package conf

import (
	"fmt"
	"io/ioutil"

	"github.com/pkg/errors"
	"gopkg.in/yaml.v3"

	"git.woa.com/kateway/kateway-server/pkg/util/ssh"
)

type DBConfig struct {
	Type  string `yaml:"type"`
	Name  string `yaml:"name"`
	Host  string `yaml:"host"`
	Port  int    `yaml:"port"`
	User  string `yaml:"user"`
	Pass  string `yaml:"pass"`
	DbUrl string `yaml:"dbUrl"`
}

type EtcdConfig struct {
	Endpoint string `yaml:"endpoint"`
	Cert     string `yaml:"cert,omitempty"`
	Key      string `yaml:"key,omitempty"`
}

type BackupEtcdConfig struct {
	Prefix     string `yaml:"prefix,omitempty"`
	EtcdConfig `yaml:",inline"`
}

type ClusterConfig struct {
	KubeConfig string `yaml:"kubeconfig"`
}

func (c *DBConfig) DBType() string {
	if c.Type == "" {
		return "mysql"
	}
	return c.Type
}

func (c *DBConfig) GormURL() string {
	if c.DbUrl != "" {
		return c.DbUrl
	}
	c.DbUrl = fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8&parseTime=True&loc=Local", c.User, c.Pass, c.Host, c.Port, c.Name)
	return c.DbUrl
}

type Config struct {
	DashboardDB map[string]DBConfig `yaml:"dashboard_db"` // key: region

	CAuth struct {
		Server map[string]string `yaml:"server"` // key: region, value: url
	} `yaml:"cauth"`

	Vpc struct {
		VpcOSS struct {
			Url string `yaml:"url"`
		} `yaml:"vpcOss"`
		APIV3 struct {
			Url string `yaml:"url"`
		} `yaml:"apiV3"`
	} `yaml:"vpc"`

	RoleCfg struct {
		TKEQCSRole struct {
			SecretId   string `yaml:"secretId"`
			SecretKey  string `yaml:"secretKey"`
			RolePrefix string `yaml:"rolePrefix"`
			RoleName   string `yaml:"roleName"`
		} `yaml:"tkeQcsRole"`
	} `yaml:"roleCfg"`

	TKE struct {
		Dashboard struct {
			Url string `yaml:"url"`
		} `yaml:"dashboard"`

		DefaultMetaCluster map[string]string `yaml:"defaultMetaCluster"`

		TKEAPIServer struct {
			Url  string `yaml:"url"`
			Cert string `yaml:"cert"`
			Key  string `yaml:"key"`
		} `yaml:"tkeApiserver"`

		TKEPlatform struct {
			Url  string `yaml:"url"`
			Cert string `yaml:"cert"`
			Key  string `yaml:"key"`
		} `yaml:"tkePlatform"`
	} `yaml:"tke"`

	EKS struct {
		CloudGwURL   string `yaml:"cloudGwUrl"`
		EKSServerURL string `yaml:"eksServerUrl"`
		EKSPlatform  struct {
			Url   string `yaml:"url"`
			Cert  string `yaml:"cert"`
			Key   string `yaml:"key"`
			Token string `yaml:"token"`
		} `yaml:"eksPlatform"`
	} `yaml:"eks"`

	Migration struct {
		Global struct {
			CVMEtcdRootClientCertFile    string `yaml:"cvmEtcdRootClientCertFile"`
			CVMEtcdRootClientKeyFile     string `yaml:"cvmEtcdRootClientKeyFile"`
			KStoneEtcdRootClientCertFile string `yaml:"kstoneEtcdRootClientCertFile"`
			KStoneEtcdRootClientKeyFile  string `yaml:"kstoneEtcdRootClientKeyFile"`

			OldCVMEtcdCACertFile string `yaml:"oldCvmEtcdCACertFile"`
			NewCVMEtcdCACertFile string `yaml:"newCvmEtcdCACertFile"`

			BackupCluster *ClusterConfig    `yaml:"backupCluster,omitempty"`
			BackupEtcd    *BackupEtcdConfig `yaml:"backupEtcd,omitempty"`
		} `yaml:"global"`
		MetaClusters map[string]struct {
			SrcMetaCluster      string      `yaml:"srcMetaCluster"`
			DstMetaCluster      string      `yaml:"dstMetaCluster"`
			SrcMetaTunnelConfig *ssh.Config `yaml:"srcMetaTunnelConfig,omitempty"`
			DstMetaTunnelConfig *ssh.Config `yaml:"dstMetaTunnelConfig,omitempty"`
			SrcMetaEtcd         *EtcdConfig `yaml:"srcMetaEtcd,omitempty"`
			DstMetaEtcd         *EtcdConfig `yaml:"dstMetaEtcd,omitempty"`
		} `yaml:"metaclusters"` // key: region, value: struct{}
	} `yaml:"migration"` // key: region, value: struct
}

func LoadConfigFromFile(file string) (*Config, error) {
	data, err := ioutil.ReadFile(file)
	if err != nil {
		return nil, errors.Wrapf(err, "read config file %s failed", file)
	}

	ret := &Config{}
	if err := yaml.Unmarshal(data, ret); err != nil {
		return nil, errors.Wrapf(err, "parse config file %s failed", file)
	}
	return ret, nil
}
