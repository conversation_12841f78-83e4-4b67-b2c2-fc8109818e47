package main

import (
	"net"
	"os"
	"strings"

	_ "github.com/jinzhu/gorm/dialects/mysql"
	"github.com/segmentio/ksuid"
	"github.com/spf13/cast"
	"github.com/spf13/cobra"

	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/api"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/clb"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/cluster"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/image"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/ingress"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/merge"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/task"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/user"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/version"
	"git.woa.com/kateway/kateway-server/pkg/tmp/wd"
)

var (
	RootCmd = &cobra.Command{
		Use:   "kops",
		Short: "接入层管理工具",
	}
)

func init() {
	RootCmd.AddCommand(version.New())
	RootCmd.AddCommand(user.New())
	RootCmd.AddCommand(cluster.New())
	RootCmd.AddCommand(clb.New())
	RootCmd.AddCommand(image.New())
	RootCmd.AddCommand(service.New())
	RootCmd.AddCommand(ingress.New())
	RootCmd.AddCommand(task.New())
	RootCmd.AddCommand(merge.New())
	setSilenceUsage(RootCmd)
}

func setSilenceUsage(cmd *cobra.Command) {
	cmd.SilenceUsage = true

	for _, subCmd := range cmd.Commands() {
		setSilenceUsage(subCmd)
	}
}

func main() {
	wd.Chdir()
	err := run()
	if err != nil {
		os.Exit(1)
	}
}

func run() (err error) {
	id := ksuid.New().String()
	fmt.Init(&fmt.Config{
		ID:         id,
		ProjectID:  "14315",
		Env:        "prod",
		EnvID:      "120539",
		ViewID:     "6422",
		Topic:      "sdk-abcg2b23e837b259",
		ServerAddr: "publiclog.zhiyan.tencent-cloud.net:11001",
		Proto:      "tcp",
	})

	err = api.CheckUpgrade()
	if err != nil {
		return fmt.Errorf("升级失败: %s", err)
	}

	if len(os.Args) == 2 {
		if strings.HasPrefix(os.Args[1], "lb-") || net.ParseIP(os.Args[1]) != nil {
			os.Args = append([]string{os.Args[0]}, "clb", os.Args[1])
		} else if strings.HasPrefix(os.Args[1], "cls-") {
			os.Args = append([]string{os.Args[0]}, "cluster", os.Args[1])
		} else if cast.ToInt64(os.Args[1]) > 0 {
			os.Args = append([]string{os.Args[0]}, "user", os.Args[1])
		}
	}

	return RootCmd.Execute()
}
