package args

import (
	"reflect"
	"strings"
	"testing"
)

func TestAddArgs(t *testing.T) {
	tests := []struct {
		name        string
		currentArgs []string
		desiredArgs []string
		want        []string
	}{
		{
			name:        "完全覆盖已有参数",
			currentArgs: []string{"--log-level=info", "listen=8080"},
			desiredArgs: []string{"--log-level=debug", "listen=9090"},
			want:        []string{"--log-level=debug", "listen=9090"},
		},
		{
			name:        "混合覆盖和新增参数",
			currentArgs: []string{"a=1", "b=2", "c=3"},
			desiredArgs: []string{"b=20", "d=4"},
			want:        []string{"a=1", "b=20", "c=3", "d=4"},
		},
		{
			name:        "混合覆盖和新增参数2",
			currentArgs: []string{"a=1", "--b=2", "c=3"},
			desiredArgs: []string{"--b=20", "--d=4", "e=5"},
			want:        []string{"a=1", "--b=20", "c=3", "--d=4", "e=5"},
		},
		{
			name:        "添加全新参数",
			currentArgs: []string{"--debug"},
			desiredArgs: []string{"--threads=4"},
			want:        []string{"--debug", "--threads=4"},
		},
		{
			name:        "当前参数为空",
			currentArgs: []string{},
			desiredArgs: []string{"--log-level=info"},
			want:        []string{"--log-level=info"},
		},
		{
			name:        "期望参数为空",
			currentArgs: []string{"--log-level=info"},
			desiredArgs: []string{},
			want:        []string{"--log-level=info"},
		},
		{
			name:        "参数不同格式处理",
			currentArgs: []string{"--verbose", "port=8080"},
			desiredArgs: []string{"--verbose=true", "port"},
			want:        []string{"--verbose=true", "port"},
		},
		{
			name:        "保留未冲突参数的顺序",
			currentArgs: []string{"first=1", "second=2", "third=3"},
			desiredArgs: []string{"second=20", "fourth=4"},
			want:        []string{"first=1", "second=20", "third=3", "fourth=4"},
		},
		{
			name:        "带空值的参数处理",
			currentArgs: []string{"--flag=", "empty"},
			desiredArgs: []string{"--flag=value", "empty=nil"},
			want:        []string{"--flag=value", "empty=nil"},
		},
		{
			name:        "多个相同key的参数覆盖",
			currentArgs: []string{"repeat=1", "repeat=2"},
			desiredArgs: []string{"repeat=3"},
			want:        []string{"repeat=3", "repeat=3"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行参数合并操作
			fn := setArgs(tt.desiredArgs)
			got := fn(tt.currentArgs)

			// 验证结果
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("参数合并结果错误:\n当前参数: %v\n期望参数: %v\n实际结果: %v\n期望结果: %v",
					tt.currentArgs, tt.desiredArgs, got, tt.want)
			}

			// 验证期望参数全部存在
			for _, darg := range tt.desiredArgs {
				found := false
				dkey := strings.SplitN(darg, "=", 2)[0]
				for _, garg := range got {
					gkey := strings.SplitN(garg, "=", 2)[0]
					if dkey == gkey {
						found = true
						// 验证值是否正确更新
						if garg != darg {
							t.Errorf("参数值未正确更新:\n期望: %s\n实际: %s", darg, garg)
						}
						break
					}
				}
				if !found {
					t.Errorf("期望参数未找到: %s\n完整参数列表: %v", darg, got)
				}
			}
		})
	}
}

func TestDelArgs(t *testing.T) {
	tests := []struct {
		name       string
		current    []string
		removeKeys []string
		want       []string
	}{
		{
			name:       "删除单个参数",
			current:    []string{"--log-level=info", "listen=8080"},
			removeKeys: []string{"--log-level"},
			want:       []string{"listen=8080"},
		},
		{
			name:       "删除多个参数",
			current:    []string{"a=1", "b=2", "c=3"},
			removeKeys: []string{"b", "c"},
			want:       []string{"a=1"},
		},
		{
			name:       "删除带不同值的相同键",
			current:    []string{"debug=true", "debug=false"},
			removeKeys: []string{"debug"},
			want:       []string{},
		},
		{
			name:       "删除不存在的键",
			current:    []string{"--verbose", "port=8080"},
			removeKeys: []string{"--log"},
			want:       []string{"--verbose", "port=8080"},
		},
		{
			name:       "混合删除带值和不带值的键",
			current:    []string{"key1", "key2=value", "key3=123"},
			removeKeys: []string{"key1", "key3"},
			want:       []string{"key2=value"},
		},
		{
			name:       "保持原有顺序",
			current:    []string{"first", "second", "third"},
			removeKeys: []string{"second"},
			want:       []string{"first", "third"},
		},
		{
			name:       "删除所有参数",
			current:    []string{"a=1", "b=2"},
			removeKeys: []string{"a", "b"},
			want:       []string{},
		},
		{
			name:       "处理空输入",
			current:    []string{},
			removeKeys: []string{"any"},
			want:       []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行删除操作
			fn := delArgs(tt.removeKeys)
			got := fn(tt.current)

			// 验证结果
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("参数删除结果错误:\n输入参数: %v\n删除键: %v\n实际结果: %v\n期望结果: %v",
					tt.current, tt.removeKeys, got, tt.want)
			}

			// 验证所有待删除键确实不存在
			for _, key := range tt.removeKeys {
				cleanKey := strings.SplitN(key, "=", 2)[0]
				for _, arg := range got {
					argKey := strings.SplitN(arg, "=", 2)[0]
					if argKey == cleanKey {
						t.Errorf("未正确删除键 %s，残留参数: %s", cleanKey, arg)
					}
				}
			}
		})
	}
}
