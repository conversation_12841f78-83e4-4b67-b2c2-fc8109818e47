package args

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/samber/lo"
	"github.com/spf13/cobra"
	"k8s.io/klog/v2"

	common2 "git.woa.com/kateway/kateway-server/cmd/kops/common"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/common"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/service/controller"
	"git.woa.com/kateway/kateway-server/pkg/dryrun"
)

const (
	ServiceDeployName    = "service-controller"
	ServiceContainerName = "service-controller"
	IngressDeployName    = "l7-lb-controller"
	IngressContainerName = "l7-lb-controller"
	argsOperationSet     = "set"
	argsOperationUnset   = "unset"

	componentService = "service"
	componentIngress = "ingress"
)

func NewServiceArgs() *cobra.Command {
	name := componentService
	cmd := &cobra.Command{
		Use:   name,
		Short: "service controller args tool",
		Long:  "service controller args tool",
	}
	cmd.PersistentFlags().StringP("cluster", "c", "", "The cluster id")
	cmd.PersistentFlags().StringSliceP("args", "a", nil, "The args")
	cmd.PersistentFlags().BoolP("force", "f", false, "force modify args")
	cmd.AddCommand(ServiceArgsSetCmd)
	cmd.AddCommand(ServiceArgsDelCmd)
	return cmd
}

func NewIngressArgs() *cobra.Command {
	name := componentIngress
	cmd := &cobra.Command{
		Use:   name,
		Short: "ingress controller args tool",
		Long:  "ingress controller args tool",
	}
	cmd.PersistentFlags().StringP("cluster", "c", "", "The cluster id")
	cmd.PersistentFlags().StringSliceP("args", "a", nil, "The args")
	cmd.PersistentFlags().BoolP("force", "f", false, "force modify args")
	cmd.AddCommand(IngressArgsSetCmd)
	cmd.AddCommand(IngressArgsDelCmd)
	return cmd
}

var ServiceArgsSetCmd = &cobra.Command{
	Use:   argsOperationSet,
	Short: "service controller args set",
	Long:  "service controller args set",
	RunE: func(cmd *cobra.Command, args []string) error {
		if err := Run(cmd, componentService, argsOperationSet, args); err != nil {
			return err
		}
		return nil
	},
}

var ServiceArgsDelCmd = &cobra.Command{
	Use:   argsOperationUnset,
	Short: "service controller args unset",
	Long:  "service controller args unset",
	RunE: func(cmd *cobra.Command, args []string) error {
		if err := Run(cmd, componentService, argsOperationUnset, args); err != nil {
			return err
		}
		return nil
	},
}

var IngressArgsSetCmd = &cobra.Command{
	Use:   argsOperationSet,
	Short: "ingress controller args set",
	Long:  "ingress controller args set",
	RunE: func(cmd *cobra.Command, args []string) error {
		if err := Run(cmd, componentIngress, argsOperationSet, args); err != nil {
			return err
		}
		return nil
	},
}

var IngressArgsDelCmd = &cobra.Command{
	Use:   argsOperationUnset,
	Short: "ingress controller args unset",
	Long:  "ingress controller args unset",
	RunE: func(cmd *cobra.Command, args []string) error {
		if err := Run(cmd, componentIngress, argsOperationUnset, args); err != nil {
			return err
		}
		return nil
	},
}

func Run(cmd *cobra.Command, components string, action string, _ []string) error {
	ctx := context.Background()
	cluster, err := cmd.Flags().GetString("cluster")
	if err != nil {
		return err
	}
	if cluster == "" {
		return errors.New("需要集群名称参数")
	}
	desiredArgs, err := cmd.Flags().GetStringSlice("args")
	if err != nil {
		return err
	}
	// clusterID -> instance
	ins, err := controller.GetByClusterID(ctx, cluster)
	if err != nil {
		return err
	}
	// dryrun(给dryrun pod添加args)
	var ctrl *controller.Controller
	switch components {
	case componentService:
		ctrl = ins.Service()
	case componentIngress:
		ctrl = ins.Ingress()
	default:
		return errors.New("非预期组件")
	}

	image, err := ctrl.GetImage(ctx)
	if err != nil {
		return err
	}
	version := image.Tag
	if !strings.HasPrefix(version, "v") {
		return errors.New("镜像tag必须以v开头")
	}
	var argsUpdater controller.DeploymentUpdater
	switch action {
	case argsOperationSet:
		// 创建updater
		argsUpdater = ctrl.BuildContainerArgsUpdater(setArgs(desiredArgs))
	case argsOperationUnset:
		argsUpdater = ctrl.BuildContainerArgsUpdater(delArgs(desiredArgs))
	default:
		return errors.New("非预期动作")
	}

	force, err := cmd.Flags().GetBool("force")
	if err != nil {
		return err
	}
	if force {
		// 强更新，直接更新（只更新args），不做dryrun检查
		if err := ctrl.UpdateDeployment(ctx, argsUpdater); err != nil {
			return err
		}
		klog.Info("强制更新结束")
		return nil
	}
	// dryrun检查，检查参数是否合法，不合法直接返回错误
	updaters := []controller.DeploymentUpdater{argsUpdater}
	// dryrun不通过不能更新
	records, err := ctrl.Dryrun(ctx, version, controller.DryrunOptions{
		Updaters: updaters,
	})
	if err != nil {
		return err
	}
	if len(records) != 0 {
		return fmt.Errorf("预检失败 错误 %w", dryrun.BuildErrorFromResult(records.ToMap()))
	}
	klog.Info("dryrun结束，预检通过")
	// dryrun结束，且没发现问题，可以变更（只更新args）
	// 变更
	if err := ctrl.UpdateDeployment(ctx, argsUpdater); err != nil {
		return err
	}
	// 变更后检
	if err := check(ctx, ctrl, image.String()); err != nil {
		klog.Infof("状态检查异常，原因：%v", err)
	}
	klog.Info("更新成功")
	return nil
}

func check(ctx context.Context, ctrl *controller.Controller, image string) error {
	//  等待5s，防止检查成老pod
	time.Sleep(5 * time.Second)
	// 只检查leader pod的状态
	// TODO:  检查所有pod的状态
	// 超时时长为300s，每3s检查一次deployment符合预期
	ctx, cancel := context.WithTimeout(ctx, 300*time.Second)
	defer cancel()
	ticker := time.NewTicker(3 * time.Second)
	defer ticker.Stop()
	// 只检查leader pod
	replicas := int32(1)

	for {
		select {
		case <-ctx.Done():
			statuses, err := getPodStatus(ctx, ctrl)
			if err != nil {
				return err
			}
			var statusString []string
			for _, status := range statuses {
				if status["Image"] == image {
					statusString = append(statusString, status["Status"])
				}
			}
			return fmt.Errorf("pod status error. %s", strings.Join(statusString, ","))
		case <-ticker.C:
			statuses, err := getPodStatus(ctx, ctrl)
			if err != nil {
				continue
			}
			var readyCount int32
			for _, status := range statuses {
				if status["Image"] == image && status["Status"] == "Running" {
					readyCount = readyCount + 1
				}
			}
			if readyCount == replicas {
				return nil
			}
		}
	}
}

func getPodStatus(ctx context.Context, ctrl *controller.Controller) ([]map[string]string, error) {
	deploy, err := ctrl.GetDeployment(ctx, controller.GetDeploymentOptions{})
	if err != nil {
		return nil, err
	}
	containerName := ServiceContainerName
	if ctrl.IsIngress() {
		containerName = IngressContainerName
	}
	deploymentImage := "<None>"
	for _, container := range deploy.Spec.Template.Spec.Containers {
		if container.Name == containerName {
			deploymentImage = container.Image
		}
	}
	pod, err := ctrl.GetLeaderPod(ctx)
	if err != nil {
		return nil, err
	}
	serviceStatuses := make([]map[string]string, 0)
	for _, container := range pod.Spec.Containers {
		if container.Name == containerName {
			restart, reason := common2.GetPodStatus(pod.Pod)
			imageID := common.GetPodSha256(pod.Pod)
			serviceStatuses = append(serviceStatuses, map[string]string{
				"Name":       pod.Name,
				"Image":      deploymentImage,
				"PodImage":   container.Image,
				"PodImageId": imageID,
				"Status":     reason,
				"Restart":    strconv.Itoa(restart),
			})
		}
	}

	return serviceStatuses, nil
}

// delArgs 参数删除处理器（保持原有参数顺序）
func delArgs(removeKeys []string) func([]string) []string {
	return func(curArgs []string) []string {
		// 构建待删除键集合（带值和不带值的情况都考虑）
		removeSet := lo.SliceToMap(removeKeys, func(k string) (string, struct{}) {
			key, _, _ := strings.Cut(k, "=")
			return key, struct{}{}
		})

		// 过滤当前参数（保持顺序）
		result := lo.Filter(curArgs, func(arg string, _ int) bool {
			k, _, _ := strings.Cut(arg, "=")
			_, exists := removeSet[k]
			return !exists
		})

		klog.Infof("args before %v after %v", curArgs, result)
		return result
	}
}

// 参数合并处理器（保持参数顺序）
func setArgs(desiredArgs []string) func([]string) []string {
	return func(curArgs []string) []string {
		// 参数解析器
		parseArg := func(arg string) (string, *string) {
			k, v, found := strings.Cut(arg, "=")
			if !found || v == "" {
				return k, nil
			}
			return k, lo.ToPtr(v)
		}

		// 构建参数映射
		newParams := lo.SliceToMap(desiredArgs, func(arg string) (string, *string) {
			return parseArg(arg)
		})
		curParams := lo.SliceToMap(curArgs, func(arg string) (string, *string) {
			return parseArg(arg)
		})

		// 计算参数差异
		existingKeys := getMapKeys(curParams)
		updateKeys := lo.Filter(getMapKeys(newParams), func(k string, _ int) bool {
			return lo.Contains(existingKeys, k)
		})

		// 合并参数（保持原有顺序）
		result := lo.FilterMap(curArgs, func(arg string, _ int) (string, bool) {
			k, _ := parseArg(arg)
			if lo.Contains(updateKeys, k) {
				if val := newParams[k]; !lo.IsNil(val) {
					return k + "=" + lo.FromPtr(val), true
				}
				return k, true
			}
			return arg, true
		})

		// 追加新参数（保持传入顺序）
		lo.ForEach(desiredArgs, func(arg string, _ int) {
			k, _ := parseArg(arg)
			if !lo.Contains(existingKeys, k) {
				result = append(result, arg)
			}
		})

		klog.Infof("args before %v after %v", curArgs, result)
		return result
	}
}

// getMapKeys 获取任意类型 map 的键列表（泛型实现）
// 参数 m: 任意键值类型的 map
// 返回值: 包含所有键的切片，保持 map 的遍历顺序（Go map 遍历顺序随机）
func getMapKeys[K comparable, V any](m map[K]V) []K {
	keys := make([]K, 0, len(m)) // 预分配容量提升性能
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}
