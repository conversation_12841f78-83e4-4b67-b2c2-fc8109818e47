package tracing

import (
	"context"
	"os"
	"os/user"

	"github.com/opentracing/opentracing-go"
	platformv1 "tkestack.io/tke/api/platform/v1"

	"git.woa.com/kateway/pkg/telemetry/jaeger"

	"git.woa.com/kateway/kateway-server/pkg/models"
)

func Cluster(span opentracing.Span, cluster *models.Cluster) {
	span.SetTag("appid", cluster.AppId)
	span.SetTag("region", cluster.Region)
	span.SetTag("clusterID", cluster.ClusterInstanceId)
	span.SetTag("metaClusterID", cluster.MetaClusterID)
	span.LogKV("cluster", jaeger.JSON(cluster))
}

func Platformv1Cluster(span opentracing.Span, cluster *platformv1.Cluster) {
	span.SetTag("appid", cluster.Spec.TenantID)
	span.SetTag("region", cluster.Annotations["eks.tke.cloud.tencent.com/region"])
	span.SetTag("clusterID", cluster.Name)
	span.SetTag("metaClusterID", cluster.Annotations["eks.tke.cloud.tencent.com/metacluster"])
	// span.LogKV("cluster", jaeger.JSON(cluster))
}

func System(ctx context.Context, span opentracing.Span, id string) {
	// 为了span快速上报，支持id查询
	span, ctx = jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	span.SetTag("id", id)

	u, _ := user.Current()
	span.LogKV("user", u.Username)
	wd, _ := os.Getwd()
	span.LogKV("workDir", wd)
	span.LogKV("env", os.Environ())
	span.LogKV("args", os.Args)
}
