package table

import (
	"context"

	"github.com/gosuri/uitable"
	"github.com/samber/lo"

	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"
)

func Print(ctx context.Context, titles []string, clusterResults []map[string]string) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	span.LogKV("clusterResults", jaeger.JSON(clusterResults))

	table := uitable.New()
	table.Separator = " "
	table.Wrap = true

	title := append([]string{"Index"}, titles...)
	table.AddRow(lo.<PERSON>(title)...)

	for rowIndex, res := range clusterResults {
		row := make([]interface{}, len(title))
		row[0] = rowIndex
		for colIndex, item := range titles {
			value, exist := res[item]
			if !exist {
				value = "nil"
			}
			row[colIndex+1] = value
		}
		table.AddRow(row...)
	}

	fmt.Println(table.String())
}
