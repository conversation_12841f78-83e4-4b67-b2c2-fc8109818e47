package main

import (
	"os"
	"os/exec"
	"strings"

	"github.com/spf13/cobra"
)

var doCmd = &cobra.Command{
	Use:  "do",
	RunE: do,
}

func init() {
	doCmd.Flags().String("exec", "./misaka_fixed", "实际执行的命令")
	doCmd.Flags().String("cmd", "getpod", "实际执行的子命令")
}

func do(cmd *cobra.Command, args []string) error {
	execCmd, _ := cmd.Flags().GetString("exec")
	doCmd, _ := cmd.Flags().GetString("cmd")
	file, _ := cmd.Flags().GetString("file")
	data := strings.Split(file, "/")
	// fmt.Println(data)
	args = []string{
		data[3],
		data[1],
		doCmd,
		"--region",
		data[4],
		"--file",
		file,
		"--imageTag",
		data[2],
	}
	configmap, _ := cmd.Flags().GetString("configmap")
	if configmap != "" {
		args = append(args, "--configmapVersion", configmap)
	}
	// fmt.Println(args)
	c := exec.Command(execCmd, args...)
	c.Stdin = os.Stdin
	c.Stdout = os.Stdout
	c.Stderr = os.Stderr
	err := c.Run()
	if err != nil {
		return err
	}

	return nil
}
