package tke

import (
	"github.com/spf13/cobra"

	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tke/ingress"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tke/merge"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tke/service"
)

var TKECmd = &cobra.Command{
	Use:   "tke",
	Short: "service controller misaka tke tool",
	Long:  "service controller misaka tke tool",
}

func init() {
	TKECmd.AddCommand(ingress.IngressCmd)
	TKECmd.AddCommand(service.ServiceCmd)
	TKECmd.AddCommand(merge.MergeCMD)
}
