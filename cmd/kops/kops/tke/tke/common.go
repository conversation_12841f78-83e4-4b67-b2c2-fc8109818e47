package tke

import (
	"fmt"
	"strconv"

	"git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
	"git.woa.com/kateway/kateway-server/pkg/models"
)

func CheckClusterStatus(cluster *models.Cluster) error {
	if cluster.LifeState == models.CLUSTER_LIFESTATE_IDLE || cluster.LifeState == models.CLUSTER_LIFESTATE_ABNORMAL ||
		cluster.LifeState == models.CLUSTER_LIFESTATE_INIT || cluster.LifeState == models.CLUSTER_LIFESTATE_DELETING ||
		cluster.LifeState == models.CLUSTER_LIFESTATE_CHARGE_ISOLATE || cluster.LifeState == models.CLUSTER_LIFESTATE_CHARGE_ISOLATED || cluster.LifeState == models.CLUSTER_LIFESTATE_ISOLATED {
		return fmt.Errorf("cluster status error: %s", cluster.LifeState)
	}
	return nil
}

func CheckTKEClusterIfNeedSkip(cls *models.Cluster) bool {
	clusterAppId := strconv.FormatUint(cls.AppId, 10)
	for _, appId := range cluster.MisakaConfig.SkipAppIds {
		if clusterAppId == appId {
			return true
		}
	}

	for _, clusterId := range cluster.MisakaConfig.SkipClusterIds {
		if cls.ClusterInstanceId == clusterId {
			return true
		}
	}

	return false
}
