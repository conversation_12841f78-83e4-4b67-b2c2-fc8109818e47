package ingress

import (
	"context"
	"strconv"
	"strings"
	"time"

	"github.com/golang/glog"
	go_version "github.com/hashicorp/go-version"
	"github.com/samber/lo"
	"github.com/spf13/cobra"
	v12 "k8s.io/api/core/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/cmd/kops/common"
	cluster2 "git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
	common2 "git.woa.com/kateway/kateway-server/cmd/kops/kops/common"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/service/controller"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tke/tke"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tracing"
	"git.woa.com/kateway/kateway-server/pkg/models"
	region2 "git.woa.com/kateway/kateway-server/pkg/util/region"
)

var SetImageCmd = &cobra.Command{
	Use:   "setimage",
	Short: "ingress controller publish tool",
	Long:  "ingress controller publish tool",
	RunE: func(cmd *cobra.Command, args []string) error {
		task := Task{
			TaskInterface: &SetImageCmdTask{},
			Title: []string{
				"Region", "Cluster", "Type", "AppId", "Version", "Error",
			},
		}
		if err := task.Run(cmd, args); err != nil {
			return err
		}
		return nil
	},
}

type SetImageCmdTask struct {
	Task
}

// eks.tke.cloud.tencent.com/metacluster: cls-g0zvpnr3
// eks.tke.cloud.tencent.com/owner-uin: "2252646423"
// eks.tke.cloud.tencent.com/product-name: tkex-csig-ziyan
// eks.tke.cloud.tencent.com/region: ap-tianjin
func (*SetImageCmdTask) Do(cmd *cobra.Command, cluster *models.Cluster) []map[string]string {
	span, ctx := jaeger.StartSpanFromContext(cmd.Context(), jaeger.WithOperationName("eks.service.setimage"))
	defer span.Finish()
	start := time.Now()

	tracing.Cluster(span, cluster)

	regionInfo := region2.Get(cluster.Region)

	errorResult := []map[string]string{
		{
			"Region":  regionInfo.Alias,
			"Cluster": cluster.ClusterInstanceId,
			"Type":    strconv.Itoa(int(cluster.ClusterType)),
			"AppId":   strconv.FormatUint(cluster.AppId, 10),
			"Version": cluster.K8sVersion,
		},
	}

	imageTag, _ := cmd.Flags().GetString("imageTag")
	if imageTag == "" {
		errorResult[0]["Error"] = fmt.Sprintf("ImageTag Not Setting\n")
		return errorResult
	}

	ccrDomain, exist := common.CcrRegionMap[regionInfo.Alias]
	if !exist {
		errorResult[0]["Error"] = fmt.Sprintf("Region %s ccr not configed", regionInfo.Alias)
		return errorResult
	}
	// [qingyangwu] 变更（更新镜像）
	user, err := cmd.Flags().GetString("user")
	if err != nil {
		user = ""
	}
	token, err := cmd.Flags().GetString("token")
	if err != nil {
		token = ""
	}
	releaseRecord := &model.ReleaseAudit{
		Date:      time.Now(),
		Region:    regionInfo.Alias,
		ClusterID: cluster.ClusterInstanceId,
		ImageTag:  imageTag,
		Component: "ingress",
		Status:    true,
		Publisher: user,
		Token:     token,
		CreatedAt: start,
		UpdatedAt: time.Now(),
	}
	defer func() {
		releaseRecord.FinishedAt = time.Now()
		// [qingyangwu] 更新数据库发布审计表
		if err := common2.ReleaseAuditService.Update(releaseRecord); err != nil {
			errorResult[0]["Error"] = err.Error()
		}
	}()
	// 执行一次健康检查，获取变更前的集群健康数据
	if skipPostCheck, err := cmd.Flags().GetBool("skipPostCheck"); err == nil && !skipPostCheck {
		if err := common2.StartOneCheckBeforeRelease(ctx, cluster.ClusterInstanceId); err != nil {
			errorResult[0]["Error"] = err.Error() // 记录错误
			releaseRecord.Status = false          // 变更失败
			return errorResult                    // 中断变更
		}
	}
	image := fmt.Sprintf("%s%s:%s", ccrDomain, common.IngressControllerImageRepo, imageTag)
	isModified, err := setImage(ctx, cluster, image, lo.Must(cmd.Flags().GetBool("force")), releaseRecord)
	releaseRecord.Status = isModified
	if err != nil {
		releaseRecord.Reason = lo.ToPtr(err.Error())
		errorResult[0]["Error"] = err.Error()
		return errorResult
	}
	if skipPostCheck, err := cmd.Flags().GetBool("skipPostCheck"); err == nil && !skipPostCheck {
		times, err := cmd.Flags().GetInt("checkTimes")
		if err != nil {
			errorResult[0]["Error"] = err.Error()
			return errorResult
		}
		interval, err := cmd.Flags().GetString("checkInterval")
		if err != nil {
			errorResult[0]["Error"] = err.Error()
			return errorResult
		}
		dur, err := time.ParseDuration(interval)
		if err != nil {
			errorResult[0]["Error"] = err.Error()
			return errorResult
		}
		taskID, err := common2.StartInspectionTaskAfterRelease(ctx, cluster.ClusterInstanceId, dur, times)
		if err != nil {
			errorResult[0]["Error"] = err.Error()
			return errorResult
		}
		releaseRecord.TaskID = taskID
	}
	return errorResult
}

func setImage(ctx context.Context, cluster *models.Cluster, image string, force bool, releaseRecord *model.ReleaseAudit) (bool, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	if err := tke.CheckClusterStatus(cluster); err != nil {
		return false, err
	}
	if tke.CheckTKEClusterIfNeedSkip(cluster) {
		return false, fmt.Errorf("skip cluster")
	}

	if !force {
		cls, err := model.NewClusterFromTKE(cluster)
		if err != nil {
			return false, err
		}

		instance, err := controller.GetByCluster(ctx, cls)
		if err != nil {
			return false, err
		}

		version := strings.Split(image, ":")[1]

		if err := controller.CheckIngressUpgrade(ctx, version, instance.Service()); err != nil {
			return false, err
		}
	}

	clientSet, err := cluster2.GetTKEClusterClientSet(cluster.Region, cluster)
	if err != nil {
		return false, err
	}

	if err = cluster2.EnsureIngressClusterRole(ctx, cluster); err != nil {
		return false, err
	}

	ingressControllerDeployment, err := clientSet.K8sCli.AppsV1().Deployments(v1.NamespaceSystem).Get(ctx, INGRESS_DEPLOYMENT_NAME, v1.GetOptions{})
	if err != nil {
		glog.Errorf("Cluster %s version %s get ingress error, err:%v", cluster.ClusterInstanceId, cluster.K8sVersion, err)
		ingressControllerDeployment, err := clientSet.K8sCli.ExtensionsV1beta1().Deployments(v1.NamespaceSystem).Get(ctx, INGRESS_DEPLOYMENT_NAME, v1.GetOptions{})
		if err != nil {
			glog.Errorf("Cluster %s version %s get ingress error, err:%v", cluster.ClusterInstanceId, cluster.K8sVersion, err)
			return false, err
		}

		// 检查发布前的组件镜像版本是否高于 v2.0.0
		semverBase, _ := go_version.NewSemver("v2.0.0")
		for index, container := range ingressControllerDeployment.Spec.Template.Spec.Containers {
			if container.Name == INGRESS_CONTAINER_NAME {
				splited := strings.Split(ingressControllerDeployment.Spec.Template.Spec.Containers[index].Image, ":")
				if len(splited) == 2 {
					if releaseRecord != nil {
						releaseRecord.SourceImageTag = splited[1]
					}
					semverCurrent, err := go_version.NewSemver(splited[1])
					if err != nil {
						return false, err
					}
					if !semverCurrent.GreaterThanOrEqual(semverBase) {
						return false, fmt.Errorf("Skip_LessThen_v2.0.0")
					}
				}
			}
		}

		isModified := false
		for index, container := range ingressControllerDeployment.Spec.Template.Spec.Containers {
			if container.Name == INGRESS_CONTAINER_NAME {
				if container.Image != image {
					ingressControllerDeployment.Spec.Template.Spec.Containers[index].Image = image
					isModified = true
				}
			}
		}

		if ingressControllerDeployment.Spec.Template.Spec.Affinity == nil {
			ingressControllerDeployment.Spec.Template.Spec.Affinity = GetAffinity()
			isModified = true
		}
		if ingressControllerDeployment.Spec.Template.Spec.Affinity.NodeAffinity == nil {
			ingressControllerDeployment.Spec.Template.Spec.Affinity.NodeAffinity = GetNodeAffinity()
			isModified = true
		}
		if ingressControllerDeployment.Spec.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution == nil {
			ingressControllerDeployment.Spec.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution = GetNodeSelector()
			isModified = true
		}
		isSetting := false
		for _, nodeSelectorTerm := range ingressControllerDeployment.Spec.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms {
			for _, matchExpression := range nodeSelectorTerm.MatchExpressions {
				if matchExpression.Key == "node.kubernetes.io/instance-type" {
					isSetting = true
					break
				}
			}
		}
		if !isSetting {
			ingressControllerDeployment.Spec.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms = append(ingressControllerDeployment.Spec.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms, GetNodeSelectorTerm())
			isModified = true
		}
		semverEKSBase, _ := go_version.NewSemver("v2.2.0")
		// v2.2.1 开始组件开始允许部署在超级节点上
		for index, container := range ingressControllerDeployment.Spec.Template.Spec.Containers {
			if container.Name == INGRESS_CONTAINER_NAME {
				splited := strings.Split(ingressControllerDeployment.Spec.Template.Spec.Containers[index].Image, ":")
				if len(splited) == 2 {
					semverCurrent, err := go_version.NewSemver(splited[1])
					if err != nil {
						return false, err
					}
					if semverCurrent.GreaterThan(semverEKSBase) {
						if ingressControllerDeployment.Spec.Template.Annotations == nil {
							ingressControllerDeployment.Spec.Template.Annotations = map[string]string{}
						}
						ingressControllerDeployment.Spec.Template.Annotations["eks.tke.cloud.tencent.com/norm"] = "true"
						for index1, nodeSelectorTerm := range ingressControllerDeployment.Spec.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms {
							for index2, matchExpression := range nodeSelectorTerm.MatchExpressions {
								if matchExpression.Key == "node.kubernetes.io/instance-type" && matchExpression.Operator == "NotIn" {
									ingressControllerDeployment.Spec.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms[index1].MatchExpressions[index2].Values = []string{"external"}
									isModified = true
								}
							}
						}
					}
				}
			}
		}

		for index, hostAlias := range ingressControllerDeployment.Spec.Template.Spec.HostAliases {
			if hostAlias.IP != "************" {
				continue
			}
			if cluster.ClusterType == models.CLUSTER_TYPE_CLUSTER_DEPLOY_ASYNC_PROCESS ||
				cluster.ClusterType == models.CLUSTER_TYPE_CLUSTER_DEPLOY_MIGRATE_TO_OPERATOR ||
				cluster.ClusterType == models.CLUSTER_TYPE_CLUSTER_DEPLOY_ON_OPERATOR {
				if len(hostAlias.Hostnames) == 7 {
					continue
				}
				ingressControllerDeployment.Spec.Template.Spec.HostAliases[index].Hostnames = []string{
					"cbs.tencentcloudapi.com",
					"cvm.tencentcloudapi.com",
					"tke.internal.tencentcloudapi.com",
					"clb.internal.tencentcloudapi.com",
					"cvm.internal.tencentcloudapi.com",
					"tag.internal.tencentcloudapi.com",
					"vpc.internal.tencentcloudapi.com",
				}
				isModified = true
			} else {
				if len(hostAlias.Hostnames) == 10 {
					continue
				}
				ingressControllerDeployment.Spec.Template.Spec.HostAliases[index].Hostnames = []string{
					"tke.internal.tencentcloudapi.com",
					"clb.internal.tencentcloudapi.com",
					"cvm.internal.tencentcloudapi.com",
					"tag.internal.tencentcloudapi.com",
					"vpc.internal.tencentcloudapi.com",
					"ssl.internal.tencentcloudapi.com",
					"as.tencentcloudapi.com",
					"cbs.tencentcloudapi.com",
					"cvm.tencentcloudapi.com",
					"vpc.tencentcloudapi.com",
				}
				isModified = true
			}
		}

		if isModified {
			if _, err := clientSet.K8sCli.ExtensionsV1beta1().Deployments(v1.NamespaceSystem).Update(ctx, ingressControllerDeployment, v1.UpdateOptions{}); err != nil {
				return false, err
			}
			return true, nil // 变更成功
		}
		return false, nil
	}

	semverBase, _ := go_version.NewSemver("v2.0.0")
	for index, container := range ingressControllerDeployment.Spec.Template.Spec.Containers {
		if container.Name == INGRESS_CONTAINER_NAME {
			splited := strings.Split(ingressControllerDeployment.Spec.Template.Spec.Containers[index].Image, ":")
			if len(splited) == 2 {
				semverCurrent, err := go_version.NewSemver(splited[1])
				if err != nil {
					return false, err
				}
				if !semverCurrent.GreaterThanOrEqual(semverBase) {
					return false, fmt.Errorf("Skip_LessThen_v2.0.0")
				}
			}
		}
	}

	isModified := false
	if ingressControllerDeployment.Spec.Template.Spec.Affinity == nil {
		ingressControllerDeployment.Spec.Template.Spec.Affinity = GetAffinity()
		isModified = true
	}
	if ingressControllerDeployment.Spec.Template.Spec.Affinity.NodeAffinity == nil {
		ingressControllerDeployment.Spec.Template.Spec.Affinity.NodeAffinity = GetNodeAffinity()
		isModified = true
	}
	if ingressControllerDeployment.Spec.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution == nil {
		ingressControllerDeployment.Spec.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution = GetNodeSelector()
		isModified = true
	}
	isSetting := false
	for _, nodeSelectorTerm := range ingressControllerDeployment.Spec.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms {
		for _, matchExpression := range nodeSelectorTerm.MatchExpressions {
			if matchExpression.Key == "node.kubernetes.io/instance-type" {
				isSetting = true
				break
			}
		}
	}
	if !isSetting {
		ingressControllerDeployment.Spec.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms = append(ingressControllerDeployment.Spec.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms, GetNodeSelectorTerm())
		isModified = true
	}
	semverEKSBase, _ := go_version.NewSemver("v2.2.0")
	for index, container := range ingressControllerDeployment.Spec.Template.Spec.Containers {
		if container.Name == INGRESS_CONTAINER_NAME {
			splited := strings.Split(ingressControllerDeployment.Spec.Template.Spec.Containers[index].Image, ":")
			if len(splited) == 2 {
				semverCurrent, err := go_version.NewSemver(splited[1])
				if err != nil {
					return false, err
				}
				if semverCurrent.GreaterThan(semverEKSBase) { // 开始支持组件部署在超级节点上
					if ingressControllerDeployment.Spec.Template.Annotations == nil {
						ingressControllerDeployment.Spec.Template.Annotations = map[string]string{}
					}
					ingressControllerDeployment.Spec.Template.Annotations["eks.tke.cloud.tencent.com/norm"] = "true"
					for index1, nodeSelectorTerm := range ingressControllerDeployment.Spec.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms {
						for index2, matchExpression := range nodeSelectorTerm.MatchExpressions {
							if matchExpression.Key == "node.kubernetes.io/instance-type" && matchExpression.Operator == "NotIn" {
								ingressControllerDeployment.Spec.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms[index1].MatchExpressions[index2].Values = []string{"external"}
								isModified = true
							}
						}
					}
				}
			}
		}
	}

	if releaseRecord != nil {
		for _, container := range ingressControllerDeployment.Spec.Template.Spec.Containers {
			if container.Name == INGRESS_CONTAINER_NAME {
				releaseRecord.SourceImageTag = common2.GetImageTag(container.Image)
			}
		}
	}
	// [qingyangwu]: 更新deployment镜像
	for index, container := range ingressControllerDeployment.Spec.Template.Spec.Containers {
		if container.Name == INGRESS_CONTAINER_NAME {
			if container.Image != image {
				ingressControllerDeployment.Spec.Template.Spec.Containers[index].Image = image
				isModified = true
			}
		}
	}

	for index, hostAlias := range ingressControllerDeployment.Spec.Template.Spec.HostAliases {
		if hostAlias.IP != "************" {
			continue
		}
		if cluster.ClusterType == models.CLUSTER_TYPE_CLUSTER_DEPLOY_ASYNC_PROCESS ||
			cluster.ClusterType == models.CLUSTER_TYPE_CLUSTER_DEPLOY_MIGRATE_TO_OPERATOR ||
			cluster.ClusterType == models.CLUSTER_TYPE_CLUSTER_DEPLOY_ON_OPERATOR {
			if len(hostAlias.Hostnames) == 7 {
				continue
			}
			ingressControllerDeployment.Spec.Template.Spec.HostAliases[index].Hostnames = []string{
				"cbs.tencentcloudapi.com",
				"cvm.tencentcloudapi.com",
				"tke.internal.tencentcloudapi.com",
				"clb.internal.tencentcloudapi.com",
				"cvm.internal.tencentcloudapi.com",
				"tag.internal.tencentcloudapi.com",
				"vpc.internal.tencentcloudapi.com",
			}
			isModified = true
		} else {
			if len(hostAlias.Hostnames) == 10 {
				continue
			}
			ingressControllerDeployment.Spec.Template.Spec.HostAliases[index].Hostnames = []string{
				"tke.internal.tencentcloudapi.com",
				"clb.internal.tencentcloudapi.com",
				"cvm.internal.tencentcloudapi.com",
				"tag.internal.tencentcloudapi.com",
				"vpc.internal.tencentcloudapi.com",
				"ssl.internal.tencentcloudapi.com",
				"as.tencentcloudapi.com",
				"cbs.tencentcloudapi.com",
				"cvm.tencentcloudapi.com",
				"vpc.tencentcloudapi.com",
			}
			isModified = true
		}
	}
	if isModified {
		if _, err := clientSet.K8sCli.AppsV1().Deployments(v1.NamespaceSystem).Update(ctx, ingressControllerDeployment, v1.UpdateOptions{}); err != nil {
			return false, err
		}
		return true, nil
	}
	return false, nil
}

func GetAffinity() *v12.Affinity {
	return &v12.Affinity{
		NodeAffinity: GetNodeAffinity(),
	}
}

func GetNodeAffinity() *v12.NodeAffinity {
	return &v12.NodeAffinity{
		RequiredDuringSchedulingIgnoredDuringExecution: GetNodeSelector(),
	}
}

func GetNodeSelector() *v12.NodeSelector {
	return &v12.NodeSelector{
		NodeSelectorTerms: []v12.NodeSelectorTerm{
			GetNodeSelectorTerm(),
		},
	}
}

func GetNodeSelectorTerm() v12.NodeSelectorTerm {
	return v12.NodeSelectorTerm{
		MatchExpressions: []v12.NodeSelectorRequirement{
			GetNodeSelectorRequirement(),
		},
	}
}

func GetNodeSelectorRequirement() v12.NodeSelectorRequirement {
	return v12.NodeSelectorRequirement{
		Key:      "node.kubernetes.io/instance-type",
		Operator: "NotIn",
		Values: []string{
			"eklet",
			"external",
		},
	}
}

func init() {
	IngressCmd.AddCommand(SetImageCmd)

	SetImageCmd.Flags().StringSliceP("cluster", "c", nil, "The cluster instance id")
	SetImageCmd.Flags().StringSliceP("appId", "a", nil, "The cluster app id")
	SetImageCmd.Flags().IntP("offset", "o", 0, "cluster offset")
	SetImageCmd.Flags().IntP("limit", "l", -1, "cluster limit")
	SetImageCmd.Flags().IntP("worker", "w", 20, "Parallelize workers")
	SetImageCmd.Flags().StringP("logpath", "p", "", "log path to save log")
	SetImageCmd.Flags().BoolP("force", "f", false, "force to set image")
}
