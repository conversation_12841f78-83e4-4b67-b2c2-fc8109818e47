package ingress

import (
	"context"
	"io"
	"strconv"
	"strings"
	"time"

	go_version "github.com/hashicorp/go-version"
	"github.com/spf13/cobra"
	appsv1 "k8s.io/api/apps/v1"
	v12 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	v1 "k8s.io/api/core/v1"
	rbac "k8s.io/api/rbac/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/cmd/kops/common"
	cluster2 "git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
	common2 "git.woa.com/kateway/kateway-server/cmd/kops/kops/common"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/common/ingress_wrapper"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tke/tke"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tracing"
	"git.woa.com/kateway/kateway-server/pkg/dryrun"
	"git.woa.com/kateway/kateway-server/pkg/models"
	region2 "git.woa.com/kateway/kateway-server/pkg/util/region"
)

const (
	MockJobName         = "ingress-mock-job"
	MockRBACName        = "lb-ingress-readonly"
	MockContainerName   = "mock"
	OtherErrorExitCode  = 255
	truncateJobExitCode = 254
	MockErrorExitCode   = 1 // found mock error
)

var MockCmd = &cobra.Command{
	Use:   "mock",
	Short: "ingress controller publish tool",
	Long:  "ingress controller publish tool",
	RunE: func(cmd *cobra.Command, args []string) error {
		task := Task{
			TaskInterface: &MockCmdTask{},
			Title: []string{
				"Region", "Cluster", "AppId", "Type", "Version", "Balance", "ServiceName", "Action", "Request", "Reason", "Analysis", "Error",
			},
		}
		if err := task.Run(cmd, args); err != nil {
			return err
		}
		return nil
	},
}

type MockCmdTask struct {
	Task

	imageTag string
	timeout  int
}

func (this *MockCmdTask) Do(cmd *cobra.Command, cluster *models.Cluster) []map[string]string {
	span, ctx := jaeger.StartSpanFromContext(cmd.Context(), jaeger.WithOperationName("tke.ingress.mock"))
	defer span.Finish()

	tracing.Cluster(span, cluster)

	regionInfo := region2.Get(cluster.Region)
	balance := common.GetUserBalance(cluster.AppId)

	errorResult := []map[string]string{
		{
			"Region":  regionInfo.Alias,
			"Cluster": cluster.ClusterInstanceId,
			"Type":    strconv.Itoa(int(cluster.ClusterType)),
			"AppId":   strconv.FormatUint(cluster.AppId, 10),
			"Balance": balance,
			"Version": cluster.K8sVersion,
		},
	}

	imageTag, _ := cmd.Flags().GetString("imageTag")
	timeout, _ := cmd.Flags().GetInt("timeout")
	this.imageTag = imageTag
	this.timeout = timeout

	if this.imageTag == "" {
		errorResult[0]["Error"] = fmt.Sprintf("ImageTag Not Setting\n")
		return errorResult
	}

	ccrRegion, exist := common.CcrRegionMap[regionInfo.Alias]
	if !exist {
		errorResult[0]["Error"] = fmt.Sprintf("Region %s ccr not configed", regionInfo.Alias)
		return errorResult
	}
	image := fmt.Sprintf("%s%s:%s", ccrRegion, common.IngressControllerImageRepo, imageTag)
	serviceStatus, err := MockRun(ctx, cluster, image, this.timeout, nil)
	if err != nil {
		errorResult[0]["Error"] = err.Error()
		return errorResult
	}

	results := make([]map[string]string, len(serviceStatus))
	if serviceStatus != nil {
		for index := range serviceStatus {
			results[index] = map[string]string{
				"Region":      regionInfo.Alias,
				"Cluster":     cluster.ClusterInstanceId,
				"Type":        strconv.Itoa(int(cluster.ClusterType)),
				"AppId":       strconv.FormatUint(cluster.AppId, 10),
				"Version":     cluster.K8sVersion,
				"Balance":     balance,
				"Error":       serviceStatus[index]["Error"],
				"ServiceName": serviceStatus[index]["ServiceName"],
				"Action":      serviceStatus[index]["Action"],
				"Request":     serviceStatus[index]["Request"],
				"Reason":      serviceStatus[index]["Reason"],
				"Analysis":    serviceStatus[index]["Analysis"],
			}
		}
	}
	span.LogKV("serviceStatus", jaeger.JSON(serviceStatus))
	return results
}

func getOperatorClusterJob(cluster *models.Cluster, deployment *v12.Deployment, image string) *batchv1.Job {
	podTemplateSpec := deployment.Spec.Template
	podTemplateSpec.Spec.RestartPolicy = v1.RestartPolicyNever
	podTemplateSpec.Spec.ServiceAccountName = MockRBACName
	for index, container := range podTemplateSpec.Spec.Containers {
		if container.Name == INGRESS_CONTAINER_NAME {
			podTemplateSpec.Spec.Containers[index].Name = MockContainerName
			podTemplateSpec.Spec.Containers[index].Image = image
			if len(podTemplateSpec.Spec.Containers[index].Args) == 0 {
				podTemplateSpec.Spec.Containers[index].Command = append(podTemplateSpec.Spec.Containers[index].Command, "--mock-run=true")
			} else {
				podTemplateSpec.Spec.Containers[index].Args = append(podTemplateSpec.Spec.Containers[index].Args, "--mock-run=true")
			}
			break
		}
	}

	semverEKSBase, _ := go_version.NewSemver("v2.2.0")
	// v2.2.1 开始组件开始允许部署在超级节点上
	for index, container := range podTemplateSpec.Spec.Containers {
		if container.Name == MockContainerName {
			splited := strings.Split(podTemplateSpec.Spec.Containers[index].Image, ":")
			if len(splited) == 2 {
				semverCurrent, err := go_version.NewSemver(splited[1])
				if err != nil {
					break
				}
				if semverCurrent.GreaterThan(semverEKSBase) {
					if podTemplateSpec.Annotations == nil {
						podTemplateSpec.Annotations = map[string]string{}
					}
					podTemplateSpec.Annotations["eks.tke.cloud.tencent.com/norm"] = "true"
					if podTemplateSpec.Spec.Affinity == nil || podTemplateSpec.Spec.Affinity.NodeAffinity == nil { // 有些老集群缺乏亲和性参数
						continue
					}
					for index1, nodeSelectorTerm := range podTemplateSpec.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms {
						for index2, matchExpression := range nodeSelectorTerm.MatchExpressions {
							if matchExpression.Key == "node.kubernetes.io/instance-type" && matchExpression.Operator == "NotIn" {
								podTemplateSpec.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms[index1].MatchExpressions[index2].Values = []string{"external"}
							}
						}
					}
				}
			}
		}
	}

	ttl := int32(30)
	backoff := int32(0)
	return &batchv1.Job{
		TypeMeta: metav1.TypeMeta{Kind: "Job"},
		ObjectMeta: metav1.ObjectMeta{
			Name:      MockJobName,
			Namespace: metav1.NamespaceSystem,
		},
		Spec: batchv1.JobSpec{
			TTLSecondsAfterFinished: &ttl,
			BackoffLimit:            &backoff,
			Template:                podTemplateSpec,
		},
	}
}

func getIndependentClusterJob(cluster *models.Cluster, deployment *v12.Deployment, image string) *batchv1.Job {
	podTemplateSpec := deployment.Spec.Template
	podTemplateSpec.Spec.RestartPolicy = v1.RestartPolicyNever
	podTemplateSpec.Spec.ServiceAccountName = MockRBACName
	for index, container := range podTemplateSpec.Spec.Containers {
		if container.Name == INGRESS_CONTAINER_NAME {
			podTemplateSpec.Spec.Containers[index].Name = MockContainerName
			podTemplateSpec.Spec.Containers[index].Image = image
			if len(podTemplateSpec.Spec.Containers[index].Args) == 0 {
				podTemplateSpec.Spec.Containers[index].Command = append(podTemplateSpec.Spec.Containers[index].Command, "--mock-run=true")
			} else {
				podTemplateSpec.Spec.Containers[index].Args = append(podTemplateSpec.Spec.Containers[index].Args, "--mock-run=true")
			}
			break
		}
	}

	semverEKSBase, _ := go_version.NewSemver("v2.2.0")
	// v2.2.1 开始组件开始允许部署在超级节点上
	for index, container := range podTemplateSpec.Spec.Containers {
		if container.Name == MockContainerName {
			splited := strings.Split(podTemplateSpec.Spec.Containers[index].Image, ":")
			if len(splited) == 2 {
				semverCurrent, err := go_version.NewSemver(splited[1])
				if err != nil {
					break
				}
				if semverCurrent.GreaterThan(semverEKSBase) {
					if podTemplateSpec.Annotations == nil {
						podTemplateSpec.Annotations = map[string]string{}
					}
					podTemplateSpec.Annotations["eks.tke.cloud.tencent.com/norm"] = "true"
					for index1, nodeSelectorTerm := range podTemplateSpec.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms {
						for index2, matchExpression := range nodeSelectorTerm.MatchExpressions {
							if matchExpression.Key == "node.kubernetes.io/instance-type" && matchExpression.Operator == "NotIn" {
								podTemplateSpec.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms[index1].MatchExpressions[index2].Values = []string{"external"}
							}
						}
					}
				}
			}
		}
	}

	ttl := int32(30)
	backoff := int32(0)
	return &batchv1.Job{
		TypeMeta: metav1.TypeMeta{Kind: "Job"},
		ObjectMeta: metav1.ObjectMeta{
			Name:      MockJobName,
			Namespace: metav1.NamespaceSystem,
		},
		Spec: batchv1.JobSpec{
			TTLSecondsAfterFinished: &ttl,
			BackoffLimit:            &backoff,
			Template:                podTemplateSpec,
		},
	}
}

func MockRun(ctx context.Context, cluster *models.Cluster, image string, timeout int, source *appsv1.Deployment) (result []map[string]string, err error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer func() {
		jaeger.LogError(span, err)
		span.LogKV("result", result)
		span.Finish()
	}()

	if err := tke.CheckClusterStatus(cluster); err != nil {
		return nil, err
	}
	if tke.CheckTKEClusterIfNeedSkip(cluster) {
		return nil, fmt.Errorf("skip cluster")
	}
	duration := time.Duration(timeout) * time.Minute
	// create the clientset
	clientSet, err := cluster2.GetTKEClusterClientSet(cluster.Region, cluster, duration)
	if err != nil {
		return nil, err
	}

	if source == nil {
		var err error
		source, err = clientSet.K8sCli.AppsV1().Deployments(metav1.NamespaceSystem).Get(ctx, "l7-lb-controller", metav1.GetOptions{})
		if err != nil {
			if errors.IsNotFound(err) {
				return nil, fmt.Errorf("skip ingress deleted cluster")
			}
			return nil, err
		}
	}

	// [qingyangwu] 检查Tag是否跨版本
	cross, err := common2.CheckImageIfCrossVersion(image, source)
	if err != nil {
		fmt.Println(err)
	}
	if cross {
		fmt.Println("warning: tke check ingress controller image tag cross version!")
	}

	if err := cleanUpJob(ctx, clientSet.K8sCli, metav1.NamespaceSystem); err != nil {
		fmt.Println(err)
		return nil, err
	}

	if err := checkOperatorCluster(ctx, clientSet.K8sCli, cluster, source); err != nil {
		return nil, err
	}

	// create rbac
	if err := createReadOnlyRBAC(ctx, clientSet.K8sCli, metav1.NamespaceSystem); err != nil {
		return nil, err
	}

	// create job
	job := getOperatorClusterJob(cluster, source, image)
	if cluster.ClusterType == models.CLUSTER_TYPE_CLUSTER_INDEPENDENT || cluster.ClusterType == models.CLUSTER_TYPE_CLUSTER_ORIGIN {
		job = getIndependentClusterJob(cluster, source, image)
	}
	// if dryRun {
	//	jobStr, _ := json.MarshalIndent(job, "", "    ")
	//	fmt.Println(string(jobStr))
	//	return nil, nil
	// }

	createdJob, err := clientSet.K8sCli.BatchV1().Jobs(metav1.NamespaceSystem).Create(ctx, job, metav1.CreateOptions{})
	if err != nil {
		fmt.Println(err)
		return nil, err
	}
	time.Sleep(5 * time.Second)
	defer func() {
		//  clean up job
		// if cleanUp {
		cleanUpJob(ctx, clientSet.K8sCli, metav1.NamespaceSystem)
		cleanUpPod(ctx, clientSet.K8sCli, metav1.NamespaceSystem)
		cleanUpRBAC(ctx, clientSet.K8sCli, metav1.NamespaceSystem)
		// }
	}()
	pod, err := waitJobRunning(ctx, clientSet.K8sCli, cluster, createdJob)
	if err != nil {
		fmt.Println("Error")
		return nil, err
	}
	// get job logs
	req := clientSet.K8sCli.CoreV1().Pods(metav1.NamespaceSystem).GetLogs(pod.Name, &v1.PodLogOptions{
		Container: MockContainerName,
		Follow:    true,
	})

	readCloser, err := req.Stream(ctx)
	if err != nil {
		fmt.Println("Error")
		return nil, err
	}
	defer readCloser.Close()
	buffer, readLogError := io.ReadAll(readCloser)
	var lines []string
	if len(buffer) > 0 {
		lines = strings.Split(string(buffer), "\n")
	}
	var truncateJob bool
	if readLogError != nil {
		if strings.Contains(readLogError.Error(), "Client.Timeout or context cancellation while reading body") && len(lines) > 10 {
			truncateJob = true
			fmt.Printf("get pod log stream timeout, try analyzing first %d lines\n", len(lines))
		} else {
			return nil, readLogError
		}
	}

	var exitCode int
	if truncateJob { // Job 还在跑，不等了, 分析前面几分钟的即可
		exitCode = truncateJobExitCode
	} else {
		exitCode, err = waitJobComplete(ctx, clientSet.K8sCli, cluster.ClusterInstanceId, pod)
		if err != nil {
			fmt.Println(err)
		}
	}

	result = make([]map[string]string, 0)
	if exitCode == 0 || exitCode == MockErrorExitCode || exitCode == truncateJobExitCode { // success or exit with mock
		for _, line := range lines {
			if strings.HasPrefix(line, "MockError") {
				split := strings.Split(line, "\t")
				if len(split) >= 5 {
					mockError := map[string]string{
						"ServiceName": split[1],
						"Action":      split[2],
						"Request":     split[3],
						"Reason":      split[4],
						"Error":       "nil",
					}
					result = append(result, mockError)
				}
			}
		}
	} else if exitCode == OtherErrorExitCode {
		mockError := map[string]string{
			"ServiceName": "<NamespacedName>",
			"Action":      "<Action>",
			"Request":     "<Request>",
			"Reason":      fmt.Sprintf("%d", OtherErrorExitCode),
		}
		result = append(result, mockError)
	} else { // failed
		for _, line := range lines {
			if strings.Contains(line, "[TencentCloudSDKError]") {
				msgs := strings.Split(line, "[TencentCloudSDKError]")
				if len(msgs) > 1 {
					return nil, fmt.Errorf(strings.Join(msgs[1:], ""))
				}
				return nil, fmt.Errorf(line)
			}
		}
	}

	result = append(result, dryrun.CollectPanicErrors(lines)...)
	result = mockeErrorAnalysis(ctx, result, clientSet.K8sCli)

	if exitCode == truncateJobExitCode {
		return nil, readLogError
	}

	return result, nil
}

func mockeErrorAnalysis(ctx context.Context, mockErrors []map[string]string, client kubernetes.Interface) []map[string]string {
MAIN:
	for _, mockError := range mockErrors {
		mockError["Analysis"] = "<None>"

		serviceName := mockError["ServiceName"]
		if !strings.Contains(serviceName, "/") {
			continue
		}
		_, namespace, name := common2.MustParseTypedName(serviceName)
		ingress, err := ingress_wrapper.IngressGetWrapper(ctx, client, namespace, name)
		if err != nil {
			continue
		}

		if mockError["Action"] == "CreateLoadBalancer" {
			if ingress.StatusLoadBalancer().Ingress == nil || len(ingress.StatusLoadBalancer().Ingress) == 0 {
				mockError["Analysis"] = "Ignore"
				continue
			}
		}

		if mockError["Action"] == "BatchModifyTargetWeight" {
			if directAccess, exist := ingress.Annotations()["ingress.cloud.tencent.com/direct-access"]; !exist || directAccess == "false" {
			OUTER:
				for _, rule := range ingress.Rules() {
					for _, path := range rule.HTTPPaths() {
						service, err := client.CoreV1().Services(namespace).Get(ctx, path.Backend().ServiceName(), metav1.GetOptions{})
						if err != nil {
							break OUTER
						}
						if service.Spec.ExternalTrafficPolicy == v1.ServiceExternalTrafficPolicyTypeLocal {
							if directAccess, exist := service.Annotations["service.cloud.tencent.com/direct-access"]; !exist || directAccess == "false" {
								mockError["Analysis"] = "Ignore"
								continue MAIN
							}
						}
					}
				}
			}
		}
	}
	return mockErrors
}

func checkOperatorCluster(ctx context.Context, client kubernetes.Interface, cluster *models.Cluster, source *appsv1.Deployment) error {
	semverBase, _ := go_version.NewSemver("v2.0.0")
	for index, container := range source.Spec.Template.Spec.Containers {
		if container.Name == INGRESS_CONTAINER_NAME {
			splited := strings.Split(source.Spec.Template.Spec.Containers[index].Image, ":")
			if len(splited) == 2 {
				semverCurrent, err := go_version.NewSemver(splited[1])
				if err != nil {
					return err
				}
				if !semverCurrent.GreaterThanOrEqual(semverBase) {
					return fmt.Errorf("Skip_LessThen_v2.0.0")
				}
			}
		}
	}

	if cluster.ClusterType != models.CLUSTER_TYPE_CLUSTER_DEPLOY_ASYNC_PROCESS && cluster.ClusterType != models.CLUSTER_TYPE_CLUSTER_DEPLOY_MIGRATE_TO_OPERATOR && cluster.ClusterType != models.CLUSTER_TYPE_CLUSTER_DEPLOY_ON_OPERATOR {
		return nil
	}

	// Skip Empty Cluster
	nodeList, err := client.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		fmt.Println(err)
		return err
	}
	if len(nodeList.Items) == 0 {
		return fmt.Errorf("skip empty cluster")
	}

	// Skip All Node Not Ready Cluster
	isAllNotReady := true
OUTER:
	for _, node := range nodeList.Items {
		for _, condition := range node.Status.Conditions {
			if condition.Type == v1.NodeReady && condition.Status == v1.ConditionTrue {
				isAllNotReady = false
				break OUTER
			}
		}
	}
	if isAllNotReady {
		return fmt.Errorf("skip not ready cluster")
	}

	return nil
}

func waitJobRunning(ctx context.Context, k8sClient kubernetes.Interface, cluster *models.Cluster, job *batchv1.Job) (*v1.Pod, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	ctx, cancel := context.WithTimeout(ctx, 10*time.Minute)
	defer cancel()
	ticker := time.NewTicker(time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			return nil, fmt.Errorf("Job Not Running In 10 Minute. %s", cluster.ClusterInstanceId)
		case <-ticker.C:
			pods, err := k8sClient.CoreV1().Pods(job.Namespace).List(ctx, metav1.ListOptions{
				LabelSelector: fmt.Sprintf("job-name=%s,controller-uid=%s", MockJobName, job.UID),
			})
			if err != nil {
				fmt.Errorf("getpod error. %s", err)
				continue
			}
			if len(pods.Items) == 0 {
				fmt.Errorf("no pod found")
				continue
			}
			phase := pods.Items[0].Status.Phase
			if phase != v1.PodRunning && phase != v1.PodSucceeded {
				fmt.Errorf("pod not running.")
				continue
			}
			return &pods.Items[0], nil
		}
	}
}

func waitJobComplete(ctx context.Context, client kubernetes.Interface, cluster string, pod *v1.Pod) (int, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	ctx, cancel := context.WithTimeout(ctx, time.Minute)
	defer cancel()
	count := 0
	ticker := time.NewTicker(time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			return OtherErrorExitCode, fmt.Errorf("Cluster time out %s", cluster)
		case <-ticker.C:
			count++
			updatedPod, err := client.CoreV1().Pods(pod.Namespace).Get(ctx, pod.Name, metav1.GetOptions{})
			if err != nil {
				return OtherErrorExitCode, err
			}
			_, status := analyzePodStatus(updatedPod)
			fmt.Printf("Check cluster %s pod %s/%s status %s\n", cluster, pod.Namespace, pod.Name, status)
			switch status {
			case "Completed":
				return 0, nil
			case "Pending", "ImagePullBackOff", "ContainerCreating": // Pending\ImagePullBackOff\ContainerCreating三分钟退出
				if count >= 18 {
					return OtherErrorExitCode, fmt.Errorf("Pod Status Error %s/%s status %s\n", pod.Namespace, pod.Name, status)
				}
			case "Failed", "Error", "OOMKilled":
				// return fmt.Errorf("pod failed")
				exitCode := OtherErrorExitCode
				for _, container := range updatedPod.Status.ContainerStatuses {
					if container.Name == MockContainerName {
						if container.State.Terminated != nil {
							exitCode = int(container.State.Terminated.ExitCode)
						}
					}
				}
				// str, _ := json.Marshal(updatedPod.Status.ContainerStatuses)
				// fmt.Println(string(str))
				return exitCode, fmt.Errorf("Pod failed %s/%s status %s exitCode %d\n", pod.Namespace, pod.Name, status, exitCode)
			}
		}
	}
}

func cleanUpJob(ctx context.Context, k8sClient kubernetes.Interface, namespace string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	_, err := k8sClient.BatchV1().Jobs(namespace).Get(ctx, MockJobName, metav1.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return err
		}
		return nil
	}

	if err := k8sClient.BatchV1().Jobs(namespace).Delete(ctx, MockJobName, metav1.DeleteOptions{}); err != nil {
		return err
	}
	time.Sleep(5 * time.Second)
	return nil
}

func cleanUpPod(ctx context.Context, k8sClient kubernetes.Interface, namespace string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	pods, err := k8sClient.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{
		// LabelSelector: "job-name=ingress-mock-job",
		LabelSelector: fmt.Sprintf("job-name=%s", MockJobName),
	})
	if err != nil {
		return err
	}
	for _, pod := range pods.Items {
		if err := k8sClient.CoreV1().Pods(namespace).Delete(ctx, pod.Name, metav1.DeleteOptions{}); err != nil {
			return err
		}
	}
	return nil
}

func genReadOnlyClusterRole() *rbac.ClusterRole {
	return &rbac.ClusterRole{
		ObjectMeta: metav1.ObjectMeta{
			Name: MockRBACName,
		},
		Rules: []rbac.PolicyRule{
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{""},
				Resources: []string{"pods", "nodes", "endpoints", "configmaps", "secrets", "services", "events"},
			},
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{"apps"},
				Resources: []string{"deployments", "replicasets"},
			},
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{"networking.k8s.io", "extensions"},
				Resources: []string{"ingresses"},
			},
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{"apiextensions.k8s.io"},
				Resources: []string{"customresourcedefinitions"},
			},
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{"discovery.k8s.io"},
				Resources: []string{"endpointslices"},
			},
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{"cloud.tencent.com"},
				Resources: []string{"tkeserviceconfigs"},
			},
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{"networking.tke.cloud.tencent.com"},
				Resources: []string{"loadbalancerresources"},
			},
		},
	}
}

func genReadOnlyClusterRoleBinding(namespace string) *rbac.ClusterRoleBinding {
	return &rbac.ClusterRoleBinding{
		ObjectMeta: metav1.ObjectMeta{
			Name: MockRBACName,
		},
		RoleRef: rbac.RoleRef{
			APIGroup: rbac.GroupName,
			Kind:     "ClusterRole",
			Name:     MockRBACName,
		},
		Subjects: []rbac.Subject{
			{
				Kind:      rbac.ServiceAccountKind,
				Name:      MockRBACName,
				Namespace: namespace,
			},
		},
	}
}

func createReadOnlyRBAC(ctx context.Context, k8sClient kubernetes.Interface, namespace string) error {
	_, err := k8sClient.RbacV1().ClusterRoles().Create(ctx, genReadOnlyClusterRole(), metav1.CreateOptions{})
	if err != nil && !apierrors.IsAlreadyExists(err) {
		return err
	}
	sa := &v1.ServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Name:      MockRBACName,
			Namespace: namespace,
		},
	}
	_, err = k8sClient.CoreV1().ServiceAccounts(namespace).Create(ctx, sa, metav1.CreateOptions{})
	if err != nil && !apierrors.IsAlreadyExists(err) {
		return err
	}
	_, err = k8sClient.RbacV1().ClusterRoleBindings().Create(ctx, genReadOnlyClusterRoleBinding(namespace), metav1.CreateOptions{})
	if err != nil && !apierrors.IsAlreadyExists(err) {
		return err
	}
	return nil
}

func cleanUpRBAC(ctx context.Context, k8sClient kubernetes.Interface, namespace string) {
	var err error
	err = k8sClient.RbacV1().ClusterRoles().Delete(ctx, MockRBACName, metav1.DeleteOptions{})
	if err != nil && !apierrors.IsNotFound(err) {
		fmt.Println(err)
	}
	err = k8sClient.RbacV1().ClusterRoleBindings().Delete(ctx, MockRBACName, metav1.DeleteOptions{})
	if err != nil && !apierrors.IsNotFound(err) {
		fmt.Println(err)
	}
	err = k8sClient.CoreV1().ServiceAccounts(namespace).Delete(ctx, MockRBACName, metav1.DeleteOptions{})
	if err != nil && !apierrors.IsNotFound(err) {
		fmt.Println(err)
	}
}

func analyzePodStatus(pod *v1.Pod) (int, string) {
	restarts := 0
	reason := string(pod.Status.Phase)
	if pod.Status.Reason != "" {
		reason = pod.Status.Reason
	}

	initializing := false
	for i := range pod.Status.InitContainerStatuses {
		container := pod.Status.InitContainerStatuses[i]
		restarts += int(container.RestartCount)
		switch {
		case container.State.Terminated != nil && container.State.Terminated.ExitCode == 0:
			continue
		case container.State.Terminated != nil:
			// initialization is failed
			if len(container.State.Terminated.Reason) == 0 {
				if container.State.Terminated.Signal != 0 {
					reason = fmt.Sprintf("Init:Signal:%d", container.State.Terminated.Signal)
				} else {
					reason = fmt.Sprintf("Init:ExitCode:%d", container.State.Terminated.ExitCode)
				}
			} else {
				reason = "Init:" + container.State.Terminated.Reason
			}
			initializing = true
		case container.State.Waiting != nil && len(container.State.Waiting.Reason) > 0 && container.State.Waiting.Reason != "PodInitializing":
			reason = "Init:" + container.State.Waiting.Reason
			initializing = true
		default:
			reason = fmt.Sprintf("Init:%d/%d", i, len(pod.Spec.InitContainers))
			initializing = true
		}
		break
	}
	if !initializing {
		restarts = 0
		hasRunning := false
		for i := len(pod.Status.ContainerStatuses) - 1; i >= 0; i-- {
			container := pod.Status.ContainerStatuses[i]
			restarts += int(container.RestartCount)
			if container.State.Waiting != nil && container.State.Waiting.Reason != "" {
				reason = container.State.Waiting.Reason
			} else if container.State.Terminated != nil && container.State.Terminated.Reason != "" {
				reason = container.State.Terminated.Reason
			} else if container.State.Terminated != nil && container.State.Terminated.Reason == "" {
				if container.State.Terminated.Signal != 0 {
					reason = fmt.Sprintf("Signal:%d", container.State.Terminated.Signal)
				} else {
					reason = fmt.Sprintf("ExitCode:%d", container.State.Terminated.ExitCode)
				}
			} else if container.Ready && container.State.Running != nil {
				hasRunning = true
			}
		}

		// change pod status back to "Running" if there is at least one container still reporting as "Running" status
		if reason == "Completed" && hasRunning {
			reason = "Running"
		}
	}

	if pod.DeletionTimestamp != nil && pod.Status.Reason == "NodeLost" {
		reason = "Unknown"
	} else if pod.DeletionTimestamp != nil {
		reason = "Terminating"
	}
	return restarts, reason
}

func init() {
	IngressCmd.AddCommand(MockCmd)

	MockCmd.Flags().StringSliceP("cluster", "c", nil, "The cluster instance id")
	MockCmd.Flags().StringSliceP("appId", "a", nil, "The cluster app id")
	MockCmd.Flags().IntP("offset", "o", 0, "cluster offset")
	MockCmd.Flags().IntP("limit", "l", -1, "cluster limit")
	MockCmd.Flags().IntP("worker", "w", 20, "Parallelize workers")
	MockCmd.Flags().Int("timeout", 15, "timeout")
	MockCmd.Flags().BoolP("dry-run", "d", false, "dry run flag")
	MockCmd.Flags().BoolP("clean-up", "u", true, "whether to clean job and pod")
	MockCmd.Flags().StringP("logpath", "p", "", "log path to save log")
}
