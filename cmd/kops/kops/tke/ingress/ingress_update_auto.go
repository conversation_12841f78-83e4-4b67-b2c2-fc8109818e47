package ingress

import (
	"context"
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"github.com/samber/lo"
	"github.com/spf13/cobra"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/cmd/kops/common"
	cluster2 "git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
	common2 "git.woa.com/kateway/kateway-server/cmd/kops/kops/common"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tke/tke"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tracing"
	"git.woa.com/kateway/kateway-server/pkg/dryrun"
	"git.woa.com/kateway/kateway-server/pkg/models"
	region2 "git.woa.com/kateway/kateway-server/pkg/util/region"
)

var UpdateAutoCmd = &cobra.Command{
	Use:   "update_auto",
	Short: "ingress controller publish tool",
	Long:  "ingress controller publish tool",
	RunE: func(cmd *cobra.Command, args []string) error {
		task := Task{
			TaskInterface: &UpdateAutoCmdTask{},
			Title: []string{
				"Region", "Cluster", "AppId", "Type", "Version", "Error",
			},
		}
		if err := task.Run(cmd, args); err != nil {
			return err
		}
		return nil
	},
}

type UpdateAutoCmdTask struct {
	Task

	ImageTag string

	Timeout    int
	Force      bool
	ForceCheck bool
}

// eks.tke.cloud.tencent.com/metacluster: cls-g0zvpnr3
// eks.tke.cloud.tencent.com/owner-uin: "2252646423"
// eks.tke.cloud.tencent.com/product-name: tkex-csig-ziyan
// eks.tke.cloud.tencent.com/region: ap-tianjin
func (t *UpdateAutoCmdTask) Do(cmd *cobra.Command, cluster *models.Cluster) []map[string]string {
	span, ctx := jaeger.StartSpanFromContext(cmd.Context(), jaeger.WithOperationName("tke.ingress.update_auto"))
	defer span.Finish()
	start := time.Now()

	tracing.Cluster(span, cluster)

	regionInfo := region2.Get(cluster.Region)

	errorResult := []map[string]string{
		{
			"Region":  regionInfo.Alias,
			"Cluster": cluster.ClusterInstanceId,
			"Type":    strconv.Itoa(int(cluster.ClusterType)),
			"AppId":   strconv.FormatUint(cluster.AppId, 10),
			"Version": cluster.K8sVersion,
		},
	}

	imageTag, _ := cmd.Flags().GetString("imageTag")
	timeout, _ := cmd.Flags().GetInt("timeout")
	force, _ := cmd.Flags().GetBool("force")
	forceCheck, _ := cmd.Flags().GetBool("force-check")
	t.ImageTag = imageTag
	t.Timeout = timeout
	t.Force = force
	t.ForceCheck = forceCheck

	if t.ImageTag == "" {
		errorResult[0]["Error"] = fmt.Sprintf("ImageTag Not Setting\n")
		return errorResult
	}

	// [qingyangwu] 变更（更新镜像）
	user, _ := cmd.Flags().GetString("user")
	token, _ := cmd.Flags().GetString("token")
	releaseRecord := &model.ReleaseAudit{
		Date:      time.Now(),
		Region:    regionInfo.Alias,
		ClusterID: cluster.ClusterInstanceId,
		ImageTag:  imageTag,
		Component: "ingress",
		Status:    true,
		Publisher: user,
		Token:     token,
		CreatedAt: start,
		UpdatedAt: time.Now(),
	}
	defer func() {
		releaseRecord.FinishedAt = time.Now()
		// [qingyangwu] 更新数据库发布审计表
		if err := common2.ReleaseAuditService.Update(releaseRecord); err != nil {
			errorResult[0]["Error"] = err.Error()
		}
	}()

	serviceStatus, isModified, err := t.updateAuto(cmd, ctx, cluster, releaseRecord)
	releaseRecord.Status = isModified
	if err != nil {
		if common2.IsErrorMessage(err) {
			releaseRecord.Status = false
		}
		releaseRecord.Reason = lo.ToPtr(err.Error())
		errorResult[0]["Error"] = err.Error()
		return errorResult
	}

	results := make([]map[string]string, len(serviceStatus))
	for index := range serviceStatus {
		results[index] = map[string]string{
			"Region":  regionInfo.Alias,
			"Cluster": cluster.ClusterInstanceId,
			"Type":    strconv.Itoa(int(cluster.ClusterType)),
			"AppId":   strconv.FormatUint(cluster.AppId, 10),
			"Version": cluster.K8sVersion,
		}
	}
	return results
}

func (t *UpdateAutoCmdTask) updateAuto(cmd *cobra.Command, ctx context.Context, cluster *models.Cluster, releaseRecord *model.ReleaseAudit) ([]map[string]string, bool, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	if err := tke.CheckClusterStatus(cluster); err != nil {
		return nil, false, err
	}
	if tke.CheckTKEClusterIfNeedSkip(cluster) {
		return nil, false, fmt.Errorf("skip cluster")
	}

	regionInfo := region2.Get(cluster.Region)
	result := make([]map[string]string, 1)
	result[0] = map[string]string{
		"Region":  regionInfo.Alias,
		"Cluster": cluster.ClusterInstanceId,
		"Type":    strconv.Itoa(int(cluster.ClusterType)),
		"AppId":   strconv.FormatUint(cluster.AppId, 10),
		"Version": cluster.K8sVersion,
	}

	// create the clientset
	clientSet, err := cluster2.GetTKEClusterClientSet(cluster.Region, cluster)
	if err != nil {
		return result, false, err
	}

	// 发布前检查
	ccrRegion, exist := common.CcrRegionMap[regionInfo.Alias]
	if !exist {
		return result, false, fmt.Errorf("Region %s ccr not configed", regionInfo.Alias)
	}
	image := fmt.Sprintf("%s%s:%s", ccrRegion, common.IngressControllerImageRepo, t.ImageTag)

	ingressControllerDeployment := cluster2.GetMixedDeployment(ctx, clientSet.K8sCli, metav1.NamespaceSystem, INGRESS_DEPLOYMENT_NAME)

	// 0. 检查集群是否已经为当前发布版本
	fmt.Printf("Cluster: %s Step PreCheck Start.\n", cluster.ClusterInstanceId)
	if ingressControllerDeployment == nil {
		return result, false, fmt.Errorf("Deployment Not Exist.")
	}
	if !t.ForceCheck && ingressControllerDeployment.GetReplicas() == 0 {
		if isModified, err := setImage(ctx, cluster, image, false, releaseRecord); err != nil {
			return result, isModified, fmt.Errorf("Error (0 Replicas), %v", err.Error())
		} else {
			return result, isModified, fmt.Errorf("Success (0 Replicas)")
		}
	}
	// deployment的镜像已经是目标镜像，无需变更
	if ingressControllerDeployment.GetContainerImage(INGRESS_CONTAINER_NAME) == image {
		return result, false, fmt.Errorf("Success (Updated)")
	}

	if t.Force {
		fmt.Printf("Cluster: %s Step Force Update Start.\n", cluster.ClusterInstanceId)
		if isModify, err := setImage(ctx, cluster, image, true, releaseRecord); err != nil {
			return result, isModify, fmt.Errorf("Error (Force Updated) %v", err.Error())
		} else {
			return result, isModify, fmt.Errorf("Success (Force Updated)")
		}
	}

	// 1. 进行预检
	// 1.2 进行预检
	fmt.Printf("Cluster: %s Step DryRun Start.\n", cluster.ClusterInstanceId)
	mockErrors, err := MockRun(ctx, cluster, image, t.Timeout, nil)
	if err != nil {
		return result, false, fmt.Errorf("Cluster: %s MockRun Error, %s", cluster.ClusterInstanceId, err.Error())
	}

	fmt.Printf("Cluster: %s Step ingress DryRun origin Result: %s", cluster.ClusterInstanceId, lo.Must(json.Marshal(mockErrors)))

	if err = dryrun.BuildErrorFromResult(mockErrors); err != nil {
		return result, false, fmt.Errorf("dryrun of cluster %q failed: %w", cluster.Name, err)
	}

	// 2. 进行发布前步骤
	// 2.1 开启静默
	// 2.2 保存日志
	// if err := saveLog(ctx, cluster, "ekslogs"); err != nil {
	// 	return result, fmt.Errorf("Cluster: %s Save Log Error. %s", cluster.ClusterInstanceId, err.Error())
	// }

	// 创建一个只执行一次的异步后检任务
	if skipPostCheck, err := cmd.Flags().GetBool("skipPostCheck"); err == nil && !skipPostCheck {
		if err := common2.StartOneCheckBeforeRelease(ctx, cluster.ClusterInstanceId); err != nil {
			return result, false, fmt.Errorf("cluster: %s Start Check Before Release Error. %s", cluster.ClusterInstanceId, err.Error())
		}
	}

	// 3. 更新镜像
	// 3.1 更新镜像
	fmt.Printf("Cluster: %s Step Update Start.\n", cluster.ClusterInstanceId)
	isModified, err := setImage(ctx, cluster, image, false, releaseRecord)
	if err != nil {
		return result, isModified, fmt.Errorf("Cluster: %s Upgrade Error. %s", cluster.ClusterInstanceId, err.Error())
	}
	// 再次开始巡检或者对已有巡检任务考虑续期
	// 查看是否已经有巡检任务
	if skipPostCheck, err := cmd.Flags().GetBool("skipPostCheck"); err == nil && !skipPostCheck {
		times, err := cmd.Flags().GetInt("checkTimes")
		if err != nil {
			return result, isModified, err
		}
		interval, err := cmd.Flags().GetString("checkInterval")
		if err != nil {
			return result, isModified, err
		}
		dur, err := time.ParseDuration(interval)
		if err != nil {
			return result, isModified, err
		}
		taskID, err := common2.StartInspectionTaskAfterRelease(ctx, cluster.ClusterInstanceId, dur, times)
		if err != nil {
			return result, isModified, fmt.Errorf("cluster: %s Start Check After Release Error. %s", cluster.ClusterInstanceId, err.Error())
		}
		releaseRecord.TaskID = taskID
	}

	// 4. 检查、清理
	time.Sleep(5 * time.Second)
	fmt.Printf("Cluster: %s Step Check Status Start.\n", cluster.ClusterInstanceId)
	if err := check(ctx, cluster, ingressControllerDeployment.GetReplicas(), image); err != nil {
		return result, isModified, fmt.Errorf("Cluster: %s Check Pod Status Error. %s", cluster.ClusterInstanceId, err.Error())
	}

	result[0]["Error"] = "Success"
	return result, isModified, nil
}

func check(ctx context.Context, cluster *models.Cluster, replicas int32, image string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	ctx, cancel := context.WithTimeout(ctx, 300*time.Second)
	defer cancel()
	ticker := time.NewTicker(3 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			statuses, err := getPodStatus(ctx, cluster)
			if err != nil {
				return err
			}
			var statusString []string
			for _, status := range statuses {
				if status["Image"] == image {
					statusString = append(statusString, status["Status"])
				}
			}
			return fmt.Errorf("pod status error. %s", strings.Join(statusString, ","))
		case <-ticker.C:
			statuses, err := getPodStatus(ctx, cluster)
			if err != nil {
				continue
			}
			var readyCount int32 = 0
			for _, status := range statuses {
				if status["Image"] == image && status["Status"] == "Running" {
					readyCount = readyCount + 1
				}
			}
			if readyCount == replicas {
				return nil
			}
		}
	}
}

func init() {
	IngressCmd.AddCommand(UpdateAutoCmd)

	UpdateAutoCmd.Flags().StringSliceP("cluster", "c", nil, "The cluster instance id")
	UpdateAutoCmd.Flags().StringSliceP("appId", "a", nil, "The cluster app id")
	UpdateAutoCmd.Flags().IntP("offset", "o", 0, "cluster offset")
	UpdateAutoCmd.Flags().IntP("limit", "l", -1, "cluster limit")
	UpdateAutoCmd.Flags().IntP("worker", "w", 20, "Parallelize workers")

	UpdateAutoCmd.Flags().Bool("force", false, "force")
	UpdateAutoCmd.Flags().Int("timeout", 15, "timeout")

	UpdateAutoCmd.Flags().Bool("force-check", false, "强制检查")
}
