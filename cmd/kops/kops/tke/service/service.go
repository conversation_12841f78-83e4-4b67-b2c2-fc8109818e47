package service

import (
	"fmt"

	"github.com/spf13/cobra"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"git.woa.com/kateway/kateway-server/pkg/models"
)

const (
	SERVICE_DEPLOYMENT_NAME = "service-controller"
	SERVICE_CONTAINER_NAME  = "service-controller"

	SERVICE_OPERATOR_LABEL = "cloud.tencent.com/tke-service-controller-config-version"

	ServiceControllerGlobalConfigKey     = "cloud.tencent.com/tke-config-name"
	ServiceControllerGlobalConfigName    = "service-controller-global-config"
	ServiceControllerGlobalConfigVersion = "cloud.tencent.com/tke-config-version"
)

var ServiceCmd = &cobra.Command{
	Use:   "service",
	Short: "service controller misaka tke tool",
	Long:  "service controller misaka tke tool",
}

func getServiceControllerDeploymentName(cluster *models.Cluster) (string, string) {
	deploymentName := SERVICE_DEPLOYMENT_NAME
	deploymentNS := metav1.NamespaceSystem
	switch cluster.ClusterType {
	case models.CLUSTER_TYPE_CLUSTER_ORIGIN: // vm
		deploymentName = SERVICE_DEPLOYMENT_NAME
	case models.CLUSTER_TYPE_CLUSTER_INDEPENDENT: // independent
		deploymentName = SERVICE_DEPLOYMENT_NAME
	case models.CLUSTER_TYPE_CLUSTER_DEPLOY_ASYNC_PROCESS, models.CLUSTER_TYPE_CLUSTER_DEPLOY_MIGRATE_TO_OPERATOR, models.CLUSTER_TYPE_CLUSTER_DEPLOY_ON_OPERATOR: // operator managed
		deploymentName = fmt.Sprintf("%s-%s", cluster.ClusterInstanceId, SERVICE_DEPLOYMENT_NAME)
		deploymentNS = cluster.ClusterInstanceId
	default: // meta cluster
		deploymentName = SERVICE_DEPLOYMENT_NAME
		deploymentNS = metav1.NamespaceSystem
	}
	return deploymentNS, deploymentName
}
