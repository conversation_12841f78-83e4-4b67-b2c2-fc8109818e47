package service

import (
	"bytes"
	"context"
	"errors"
	"io"
	"strconv"
	"strings"
	"time"

	go_version "github.com/hashicorp/go-version"
	"github.com/spf13/cobra"
	v12 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	v1 "k8s.io/api/core/v1"
	rbac "k8s.io/api/rbac/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/version"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd/api"
	"k8s.io/client-go/tools/clientcmd/api/latest"
	"k8s.io/utils/pointer"

	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/cmd/kops/common"
	cluster2 "git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
	common2 "git.woa.com/kateway/kateway-server/cmd/kops/kops/common"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tke/tke"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tracing"
	"git.woa.com/kateway/kateway-server/pkg/dryrun"
	"git.woa.com/kateway/kateway-server/pkg/merge"
	"git.woa.com/kateway/kateway-server/pkg/models"
	region2 "git.woa.com/kateway/kateway-server/pkg/util/region"
)

const (
	MockJobName         = "service-mock-job"
	MockRBACName        = "service-controller-readonly"
	MockContainerName   = "mock"
	OtherErrorExitCode  = 255
	truncateJobExitCode = 254
	MockErrorExitCode   = 1 // found mock error
)

var MockCmd = &cobra.Command{
	Use:   "mock",
	Short: "service controller publish tool",
	Long:  "service controller publish tool",
	RunE: func(cmd *cobra.Command, args []string) error {
		task := Task{
			TaskInterface: &MockCmdTask{},
			Title: []string{
				"Region", "Cluster", "AppId", "Type", "Version", "Balance", "ServiceName", "Action", "Request", "Reason", "Analysis", "Error",
			},
		}
		if err := task.Run(cmd, args); err != nil {
			return err
		}
		return nil
	},
}

type MockCmdTask struct {
	Task

	imageTag string
	timeout  int
}

func (this *MockCmdTask) Do(cmd *cobra.Command, cluster *models.Cluster) []map[string]string {
	span, ctx := jaeger.StartSpanFromContext(cmd.Context(), jaeger.WithOperationName("tke.service.mock"))
	defer span.Finish()

	tracing.Cluster(span, cluster)

	regionInfo := region2.Get(cluster.Region)
	balance := common.GetUserBalance(cluster.AppId)

	errorResult := []map[string]string{
		{
			"Region":  regionInfo.Alias,
			"Cluster": cluster.ClusterInstanceId,
			"Type":    strconv.Itoa(int(cluster.ClusterType)),
			"AppId":   strconv.FormatUint(cluster.AppId, 10),
			"Balance": balance,
			"Version": cluster.K8sVersion,
		},
	}

	imageTag, _ := cmd.Flags().GetString("imageTag")
	timeout, _ := cmd.Flags().GetInt("timeout")
	this.imageTag = imageTag
	this.timeout = timeout

	dryrunSvc, _ := cmd.Flags().GetBool("service")
	var dryrunIng *bool
	if cmd.Flags().Changed("ingress") {
		dryrun, _ := cmd.Flags().GetBool("ingress")
		dryrunIng = &dryrun
	}

	if this.imageTag == "" {
		errorResult[0]["Error"] = fmt.Sprintf("ImageTag Not Setting\n")
		return errorResult
	}

	ccrRegion, exist := common.CcrRegionMap[regionInfo.Alias]
	if !exist {
		errorResult[0]["Error"] = fmt.Sprintf("Region %s ccr not configed", regionInfo.Alias)
		return errorResult
	}
	image := fmt.Sprintf("%s%s:%s", ccrRegion, common.ServiceControllerImageRepo, imageTag)
	records, err := MockRun(ctx, cluster, image, this.timeout, dryrunSvc, dryrunIng)
	if err != nil {
		errorResult[0]["Error"] = err.Error()
		return errorResult
	}

	results := make([]map[string]string, len(records))
	for index, r := range records {
		results[index] = map[string]string{
			"Region":      regionInfo.Alias,
			"Cluster":     cluster.ClusterInstanceId,
			"Type":        strconv.Itoa(int(cluster.ClusterType)),
			"AppId":       strconv.FormatUint(cluster.AppId, 10),
			"Version":     cluster.K8sVersion,
			"Balance":     balance,
			"Error":       r.Error,
			"ServiceName": r.Resource.String(),
			"Action":      r.Action,
			"Request":     r.Request,
			"Reason":      r.Reason,
			"Analysis":    r.GetAnalysis(),
		}
	}
	return results
}

func MockRun(ctx context.Context, cluster *models.Cluster, image string, timeout int, dryrunSvc bool, expectDryrunIng *bool) (records dryrun.Records, err error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer func() {
		jaeger.LogError(span, err)
		span.LogKV("result", records)
		span.Finish()
	}()

	var (
		dryrunIng bool
		tag       = strings.Split(image, ":")[1]
	)
	readyForMerge, err := merge.IsVersionReady(tag)
	if err != nil {
		return nil, err
	}
	if expectDryrunIng != nil {
		if *expectDryrunIng {
			if !readyForMerge {
				return nil, fmt.Errorf("image version %s is not ready for ingress dryrun", tag)
			}
		}
		dryrunIng = *expectDryrunIng
	} else {
		if readyForMerge {
			cli, err := cluster2.GetTKEClusterClientSet(cluster.Region, cluster)
			if err != nil {
				return nil, err
			}
			ingressEnabled, err := merge.IsIngressControllerEnabled(ctx, cli.K8sCli)
			if err != nil {
				return nil, err
			}
			dryrunIng = ingressEnabled
		}
	}
	if !dryrunSvc && !dryrunIng {
		return nil, errors.New("either service or ingress must be true when starting a dryrun")
	}
	if dryrunIng {
		fmt.Printf("Ingress dry run need to be performed for cluster %s\n", cluster.ClusterInstanceId)
	}

	if err := tke.CheckClusterStatus(cluster); err != nil {
		return nil, err
	}
	if tke.CheckTKEClusterIfNeedSkip(cluster) {
		return nil, fmt.Errorf("skip cluster")
	}

	switch cluster.ClusterType {
	case models.CLUSTER_TYPE_CLUSTER_INDEPENDENT, models.CLUSTER_TYPE_CLUSTER_ORIGIN:
		return userClusterMockRun(ctx, cluster, image, timeout, dryrunSvc, dryrunIng)
	default:
		return metaClusterMockRun(ctx, cluster, image, timeout, dryrunSvc, dryrunIng)
	}
}

func metaClusterMockRun(ctx context.Context, cluster *models.Cluster, image string, _ int,
	dryrunSvc, dryrunIng bool) (dryrun.Records, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	metaClusterId := cluster.MetaClusterID
	if cluster.MetaClusterID == "" {
		regionInfo := region2.Get(cluster.Region)
		metaClusterId = cluster2.GetDefaultMetaCluster(regionInfo.Alias)
	}
	metaCluster := cluster2.MustGetTKECluster(cluster.Region, metaClusterId)
	metaClusterClient, err := cluster2.GetTKEClusterClientSet(cluster.Region, metaCluster)
	userClusterClient, err := cluster2.GetTKEClusterClientSet(cluster.Region, cluster)

	name := fmt.Sprintf("%s-%s", cluster.ClusterInstanceId, SERVICE_DEPLOYMENT_NAME)
	namespace := cluster.ClusterInstanceId

	// clean up job
	if err := cleanUpJob(ctx, metaClusterClient.K8sCli, namespace); err != nil {
		fmt.Println(err)
		return nil, err
	}
	cleanUpPod(ctx, metaClusterClient.K8sCli, namespace)
	cleanUpRBAC(ctx, userClusterClient.K8sCli, namespace)

	deploy, err := metaClusterClient.K8sCli.AppsV1().Deployments(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}

	// [qingyangwu] 检查Tag是否跨版本
	cross, err := common2.CheckImageIfCrossVersion(image, deploy)
	if err != nil {
		fmt.Println(err)
	}
	if cross {
		fmt.Println("warning: tke check metacluster service controller image tag cross version!")
	}

	semverBase, _ := go_version.NewSemver("v2.0.0")
	for index, container := range deploy.Spec.Template.Spec.Containers {
		if container.Name == SERVICE_CONTAINER_NAME {
			splited := strings.Split(deploy.Spec.Template.Spec.Containers[index].Image, ":")
			if len(splited) == 2 {
				semverCurrent, err := go_version.NewSemver(splited[1])
				if err != nil {
					return nil, err
				}
				if !semverCurrent.GreaterThanOrEqual(semverBase) {
					return nil, fmt.Errorf("Skip_LessThen_v2.0.0")
				}
			}
		}
	}

	err = createReadOnlyRBAC(ctx, userClusterClient.K8sCli, metav1.NamespaceSystem)
	if err != nil {
		return nil, err
	}

	err = createReadOnlySecretIfNeeded(ctx, userClusterClient.K8sCli, metav1.NamespaceSystem)
	if err != nil {
		return nil, err
	}

	time.Sleep(time.Second)

	kubeconfig, err := genReadOnlyKubeconfig(ctx, userClusterClient.K8sCli, metav1.NamespaceSystem, cluster.ClusterInstanceId)
	if err != nil {
		return nil, err
	}
	_, err = metaClusterClient.K8sCli.CoreV1().ConfigMaps(cluster.ClusterInstanceId).Create(ctx, kubeconfig, metav1.CreateOptions{})
	if err != nil && !apierrors.IsAlreadyExists(err) {
		return nil, err
	}

	args := []string{}
	if dryrunSvc {
		args = append(args, "--mock-run=true")
	}
	if dryrunIng {
		args = append(args, "--dry-run-ingress=true")
	}
	job := getOperatorClusterJob(cluster, deploy, image, args...)

	// if dryRun {
	//	jobStr, _ := json.MarshalIndent(job, "", "    ")
	//	fmt.Println(string(jobStr))
	//	return nil, nil
	// }
	//
	// if cleanUp {
	//	defer func() {
	//		cleanUpRBAC(userClusterClient, namespace)
	//		metaClusterClient.CoreV1().ConfigMaps(cluster.ClusterInstanceId).Delete(ctx, MockRBACName, metav1.DeleteOptions{})
	//	}()
	// }

	return runJob(ctx, metaClusterClient.K8sCli, userClusterClient.K8sCli, job, cluster.ClusterInstanceId, namespace)
}

func userClusterMockRun(ctx context.Context, cluster *models.Cluster, image string, timeout int, dryrunSvc, dryrunIng bool) (dryrun.Records, error) {
	duration := time.Duration(timeout) * time.Minute

	userClusterClient, err := cluster2.GetTKEClusterClientSet(cluster.Region, cluster, duration)

	name := SERVICE_DEPLOYMENT_NAME
	namespace := metav1.NamespaceSystem

	// clean up job
	if err := cleanUpJob(ctx, userClusterClient.K8sCli, namespace); err != nil {
		fmt.Println(err)
		return nil, err
	}
	cleanUpPod(ctx, userClusterClient.K8sCli, namespace)
	cleanUpRBAC(ctx, userClusterClient.K8sCli, namespace)

	deploy, err := userClusterClient.K8sCli.AppsV1().Deployments(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}

	// [qingyangwu] 检查Tag是否跨版本
	cross, err := common2.CheckImageIfCrossVersion(image, deploy)
	if err != nil {
		fmt.Println(err)
	}
	if cross {
		fmt.Println("warning: tke check user service controller image tag cross version!")
	}

	semverBase, _ := go_version.NewSemver("v2.0.0")
	for index, container := range deploy.Spec.Template.Spec.Containers {
		if container.Name == SERVICE_CONTAINER_NAME {
			splited := strings.Split(deploy.Spec.Template.Spec.Containers[index].Image, ":")
			if len(splited) == 2 {
				semverCurrent, err := go_version.NewSemver(splited[1])
				if err != nil {
					return nil, err
				}
				if !semverCurrent.GreaterThanOrEqual(semverBase) {
					return nil, fmt.Errorf("Skip_LessThen_v2.0.0")
				}
			}
		}
	}

	err = createReadOnlyRBAC(ctx, userClusterClient.K8sCli, metav1.NamespaceSystem)
	if err != nil {
		return nil, err
	}

	args := []string{}
	if dryrunSvc {
		args = append(args, "--mock-run=true")
	}
	if dryrunIng {
		args = append(args, "--dry-run-ingress=true")
	}
	job := getIndependentClusterJob(cluster, deploy, image, args...)

	// if dryRun {
	//	jobStr, _ := json.MarshalIndent(job, "", "    ")
	//	fmt.Println(string(jobStr))
	//	return nil, nil
	// }
	//
	// if cleanUp {
	//	defer cleanUpRBAC(userClusterClient, namespace)
	// }

	return runJob(ctx, userClusterClient.K8sCli, userClusterClient.K8sCli, job, cluster.ClusterInstanceId, namespace)
}

func runJob(ctx context.Context, k8sClient kubernetes.Interface, k8sClusterClient kubernetes.Interface,
	job *batchv1.Job, cluster, namespace string) (dryrun.Records, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	createdJob, err := k8sClient.BatchV1().Jobs(namespace).Create(ctx, job, metav1.CreateOptions{})
	if err != nil {
		fmt.Println(err)
		return nil, err
	}
	time.Sleep(5 * time.Second)
	defer func() {
		//  clean up job
		cleanUpJob(ctx, k8sClient, namespace)
		cleanUpPod(ctx, k8sClient, namespace)
		cleanUpRBAC(ctx, k8sClusterClient, namespace)
	}()
	pod, err := waitJobRunning(ctx, k8sClient, cluster, createdJob)
	if err != nil {
		fmt.Println("Error")
		return nil, err
	}
	// get job logs
	req := k8sClient.CoreV1().Pods(namespace).GetLogs(pod.Name, &v1.PodLogOptions{
		Container: MockContainerName,
		Follow:    true,
	})

	readCloser, err := req.Stream(ctx)
	if err != nil {
		fmt.Println("Error")
		return nil, err
	}
	defer readCloser.Close()
	buffer, readLogError := io.ReadAll(readCloser)
	var lines []string
	if len(buffer) > 0 {
		lines = strings.Split(string(buffer), "\n")
	}
	var truncateJob bool
	if readLogError != nil {
		if strings.Contains(readLogError.Error(), "Client.Timeout or context cancellation while reading body") && len(lines) > 10 {
			truncateJob = true
			fmt.Printf("get pod log stream timeout, try analyzing first %d lines\n", len(lines))
		} else {
			return nil, readLogError
		}
	}

	var exitCode int
	if truncateJob { // Job 还在跑，不等了, 分析前面几分钟的即可
		exitCode = truncateJobExitCode
	} else {
		exitCode, err = waitJobComplete(ctx, k8sClient, cluster, pod)
		if err != nil {
			fmt.Println(err)
		}
	}

	res := make([]map[string]string, 0)
	for _, line := range lines {
		if exitCode == 0 || exitCode == MockErrorExitCode || exitCode == truncateJobExitCode { // success or exit with mock
			if strings.HasPrefix(line, "MockError") {
				split := strings.Split(line, "\t")
				if len(split) >= 5 {
					mockError := map[string]string{
						"ServiceName": split[1],
						"Action":      split[2],
						"Request":     split[3],
						"Reason":      split[4],
						"Error":       "nil",
					}
					res = append(res, mockError)
				}
			}
			// } else {
			//	fmt.Println(cluster.ClusterInstanceId, line)
			// }
		} else { // failed
			if strings.Contains(line, "[TencentCloudSDKError]") {
				msgs := strings.Split(line, "[TencentCloudSDKError]")
				if len(msgs) > 1 {
					return nil, fmt.Errorf(strings.Join(msgs[1:], ""))
				}
				return nil, fmt.Errorf(line)
			}
		}
	}

	res = append(res, dryrun.CollectPanicErrors(lines)...)
	res = mockeErrorAnalysis(ctx, res, k8sClusterClient)
	if exitCode == truncateJobExitCode {
		return nil, readLogError
	}

	return dryrun.NewRecordFromMapSlice(res), nil
}

func waitJobComplete(ctx context.Context, client kubernetes.Interface, cluster string, pod *v1.Pod) (int, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	ctx, cancel := context.WithTimeout(ctx, time.Minute)
	defer cancel()
	count := 0
	ticker := time.NewTicker(time.Second * 10)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			return OtherErrorExitCode, fmt.Errorf("Cluster time out %s", cluster)
		case <-ticker.C:
			count++
			updatedPod, err := client.CoreV1().Pods(pod.Namespace).Get(ctx, pod.Name, metav1.GetOptions{})
			if err != nil {
				return OtherErrorExitCode, err
			}
			_, status := analyzePodStatus(updatedPod)
			fmt.Printf("Check cluster %s pod %s/%s status %s\n", cluster, pod.Namespace, pod.Name, status)
			switch status {
			case "Completed":
				return 0, nil
			case "Pending", "ImagePullBackOff", "ContainerCreating": // Pending\ImagePullBackOff\ContainerCreating三分钟退出
				if count >= 18 {
					return OtherErrorExitCode, fmt.Errorf("Pod Status Error %s/%s status %s\n", pod.Namespace, pod.Name, status)
				}
			case "Failed", "Error", "OOMKilled":
				// return fmt.Errorf("pod failed")
				exitCode := OtherErrorExitCode
				for _, container := range updatedPod.Status.ContainerStatuses {
					if container.Name == MockContainerName {
						if container.State.Terminated != nil {
							exitCode = int(container.State.Terminated.ExitCode)
						}
					}
				}
				// str, _ := json.Marshal(updatedPod.Status.ContainerStatuses)
				// fmt.Println(string(str))
				return exitCode, fmt.Errorf("Pod failed %s/%s status %s exitCode %d\n", pod.Namespace, pod.Name, status, exitCode)
			}
		}
	}
}

func waitJobRunning(ctx context.Context, k8sClient kubernetes.Interface, cluster string, job *batchv1.Job) (*v1.Pod, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	ctx, cancel := context.WithTimeout(ctx, 10*time.Minute)
	defer cancel()
	ticker := time.NewTicker(time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			return nil, fmt.Errorf("Job Not Running In 10 Minute. %s", cluster)
		case <-ticker.C:
			pods, err := k8sClient.CoreV1().Pods(job.Namespace).List(ctx, metav1.ListOptions{
				LabelSelector: fmt.Sprintf("job-name=%s,controller-uid=%s", MockJobName, job.UID),
			})
			if err != nil {
				fmt.Errorf("getpod error. %s", err)
				continue
			}
			if len(pods.Items) == 0 {
				fmt.Errorf("no pod found")
				continue
			}
			phase := pods.Items[0].Status.Phase
			if phase != v1.PodRunning && phase != v1.PodSucceeded {
				fmt.Errorf("pod not running.")
				continue
			}
			return &pods.Items[0], nil
		}
	}
}

func cleanUpJob(ctx context.Context, k8sClient kubernetes.Interface, namespace string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	_, err := k8sClient.BatchV1().Jobs(namespace).Get(ctx, MockJobName, metav1.GetOptions{})
	if err != nil {
		if !apierrors.IsNotFound(err) {
			return err
		}
		return nil
	}

	if err := k8sClient.BatchV1().Jobs(namespace).Delete(ctx, MockJobName, metav1.DeleteOptions{}); err != nil {
		return err
	}
	time.Sleep(5 * time.Second)
	return nil
}

func cleanUpPod(ctx context.Context, k8sClient kubernetes.Interface, namespace string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	pods, err := k8sClient.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{
		// LabelSelector: "job-name=service-controller-mock",
		LabelSelector: fmt.Sprintf("job-name=%s", MockJobName),
	})
	if err != nil {
		return err
	}
	for _, pod := range pods.Items {
		if err := k8sClient.CoreV1().Pods(namespace).Delete(ctx, pod.Name, metav1.DeleteOptions{}); err != nil {
			return err
		}
	}
	return nil
}

func genReadOnlyClusterRole() *rbac.ClusterRole {
	return &rbac.ClusterRole{
		ObjectMeta: metav1.ObjectMeta{
			Name: MockRBACName,
		},
		Rules: []rbac.PolicyRule{
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{""},
				Resources: []string{"pods", "nodes", "endpoints", "configmaps", "secrets", "services", "events"},
			},
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{"apps"},
				Resources: []string{"deployments", "replicasets"},
			},
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{"networking.k8s.io", "extensions"},
				Resources: []string{"ingresses"},
			},
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{"apiextensions.k8s.io"},
				Resources: []string{"customresourcedefinitions"},
			},
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{"discovery.k8s.io"},
				Resources: []string{"endpointslices"},
			},
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{"cloud.tencent.com"},
				Resources: []string{"tkeserviceconfigs"},
			},
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{"networking.tke.cloud.tencent.com"},
				Resources: []string{"loadbalancerresources"},
			},
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{"cloud.tencent.com"},
				Resources: []string{"multiclusterservices"},
			},
			{
				Verbs:         []string{"update"},
				APIGroups:     []string{"apiextensions.k8s.io"},
				Resources:     []string{"customresourcedefinitions"},
				ResourceNames: []string{"tkeserviceconfigs.cloud.tencent.com", "loadbalancerresources.networking.tke.cloud.tencent.com"},
			},
			{
				Verbs:         []string{"update"},
				APIGroups:     []string{""},
				Resources:     []string{"configmaps"},
				ResourceNames: []string{"tke-service-controller-config"},
			},
		},
	}
}

func genReadOnlyClusterRoleBinding(namespace string) *rbac.ClusterRoleBinding {
	return &rbac.ClusterRoleBinding{
		ObjectMeta: metav1.ObjectMeta{
			Name: MockRBACName,
		},
		RoleRef: rbac.RoleRef{
			APIGroup: rbac.GroupName,
			Kind:     "ClusterRole",
			Name:     MockRBACName,
		},
		Subjects: []rbac.Subject{
			{
				Kind:      rbac.ServiceAccountKind,
				Name:      MockRBACName,
				Namespace: namespace,
			},
		},
	}
}

func genReadOnlySecret(namespace string) *v1.Secret {
	return &v1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      MockRBACName,
			Namespace: namespace,
			Annotations: map[string]string{
				v1.ServiceAccountNameKey: MockRBACName,
			},
		},
		Type: v1.SecretTypeServiceAccountToken,
	}
}

func genReadOnlyKubeconfig(ctx context.Context, k8sClient kubernetes.Interface, namespace, cluster string) (*v1.ConfigMap, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	secretList, err := k8sClient.CoreV1().Secrets(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, err
	}

	var readOnlySecret *v1.Secret
	for i, secret := range secretList.Items {
		if secret.Type == v1.SecretTypeServiceAccountToken &&
			secret.Annotations[v1.ServiceAccountNameKey] == MockRBACName {
			readOnlySecret = &secretList.Items[i]
			break
		}
	}
	if readOnlySecret == nil {
		return nil, fmt.Errorf("%s secret not found", MockRBACName)
	}
	if readOnlySecret.Data["ca.crt"] == nil {
		return nil, fmt.Errorf("ca.crt is nil")
	}
	if readOnlySecret.Data["token"] == nil {
		return nil, fmt.Errorf("token is nil")
	}

	kubeconfig := api.NewConfig()
	kubeconfig.CurrentContext = cluster
	kubeconfig.Contexts[cluster] = &api.Context{
		Cluster:  cluster,
		AuthInfo: MockRBACName,
	}
	kubeconfig.Clusters[cluster] = &api.Cluster{
		CertificateAuthorityData: readOnlySecret.Data["ca.crt"],
		Server:                   fmt.Sprintf("https://%s-apiserver-service:60002", cluster),
	}
	kubeconfig.AuthInfos[MockRBACName] = &api.AuthInfo{
		Token: string(readOnlySecret.Data["token"]),
	}
	var data bytes.Buffer
	if err := latest.Codec.Encode(kubeconfig, &data); err != nil {
		return nil, err
	}

	return &v1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      MockRBACName,
			Namespace: cluster,
		},
		Data: map[string]string{
			"kubeconfig": data.String(),
		},
	}, nil
}

func createReadOnlyRBAC(ctx context.Context, k8sClient kubernetes.Interface, namespace string) error {
	_, err := k8sClient.RbacV1().ClusterRoles().Create(ctx, genReadOnlyClusterRole(), metav1.CreateOptions{})
	if err != nil && !apierrors.IsAlreadyExists(err) {
		return err
	}
	sa := &v1.ServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Name:      MockRBACName,
			Namespace: namespace,
		},
	}
	_, err = k8sClient.CoreV1().ServiceAccounts(namespace).Create(ctx, sa, metav1.CreateOptions{})
	if err != nil && !apierrors.IsAlreadyExists(err) {
		return err
	}
	_, err = k8sClient.RbacV1().ClusterRoleBindings().Create(ctx, genReadOnlyClusterRoleBinding(namespace), metav1.CreateOptions{})
	if err != nil && !apierrors.IsAlreadyExists(err) {
		return err
	}

	return nil
}

func createReadOnlySecretIfNeeded(ctx context.Context, k8sClient kubernetes.Interface, namespace string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	version124, _ := version.ParseGeneric("v1.24.0")
	serverVersion, err := k8sClient.Discovery().ServerVersion()
	if err != nil {
		return err
	}
	runningVersion, err := version.ParseGeneric(serverVersion.String())
	if err != nil {
		return err
	}
	if runningVersion.AtLeast(version124) {
		_, err = k8sClient.CoreV1().Secrets(namespace).Create(ctx, genReadOnlySecret(namespace), metav1.CreateOptions{})
		if err != nil && !apierrors.IsAlreadyExists(err) {
			return err
		}
	}
	return nil
}

func cleanUpRBAC(ctx context.Context, k8sClient kubernetes.Interface, namespace string) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	var err error
	err = k8sClient.RbacV1().ClusterRoles().Delete(ctx, MockRBACName, metav1.DeleteOptions{})
	if err != nil && !apierrors.IsNotFound(err) {
		fmt.Println(err)
	}
	err = k8sClient.RbacV1().ClusterRoleBindings().Delete(ctx, MockRBACName, metav1.DeleteOptions{})
	if err != nil && !apierrors.IsNotFound(err) {
		fmt.Println(err)
	}
	err = k8sClient.CoreV1().ServiceAccounts(namespace).Delete(ctx, MockRBACName, metav1.DeleteOptions{})
	if err != nil && !apierrors.IsNotFound(err) {
		fmt.Println(err)
	}
	err = k8sClient.CoreV1().Secrets(namespace).Delete(ctx, MockRBACName, metav1.DeleteOptions{})
	if err != nil && !apierrors.IsNotFound(err) {
		fmt.Println(err)
	}
}

func getIndependentClusterJob(cluster *models.Cluster, deployment *v12.Deployment, image string, args ...string) *batchv1.Job {
	oldImage := common2.GetControllerImage(*deployment)
	argMockOldVersion := fmt.Sprintf("--mock-old-version=%s", common2.GetImageTag(oldImage))
	podTemplateSpec := deployment.Spec.Template
	podTemplateSpec.Spec.RestartPolicy = v1.RestartPolicyNever
	podTemplateSpec.Spec.ServiceAccountName = MockRBACName
	podTemplateSpec.Spec.TopologySpreadConstraints = nil // mock pod 不需要指定拓扑限制，否则在小地域可能导致pod调度失败

	for index, container := range podTemplateSpec.Spec.Containers {
		if container.Name == SERVICE_CONTAINER_NAME {
			podTemplateSpec.Spec.Containers[index].Name = MockContainerName
			podTemplateSpec.Spec.Containers[index].Image = image
			if len(podTemplateSpec.Spec.Containers[index].Args) == 0 {
				podTemplateSpec.Spec.Containers[index].Command = append(podTemplateSpec.Spec.Containers[index].Command, args...)
				if common2.IsSupportArgMockOldVersion(image) {
					podTemplateSpec.Spec.Containers[index].Command = append(podTemplateSpec.Spec.Containers[index].Command, argMockOldVersion)
				}
			} else {
				length := len(podTemplateSpec.Spec.Containers[index].Args)
				podTemplateSpec.Spec.Containers[index].Args[length-1] = strings.Join(append([]string{podTemplateSpec.Spec.Containers[index].Args[length-1]}, args...), " ")
				if common2.IsSupportArgMockOldVersion(image) {
					podTemplateSpec.Spec.Containers[index].Args[length-1] = strings.Join([]string{podTemplateSpec.Spec.Containers[index].Args[length-1], argMockOldVersion}, " ")
				}
			}
			break
		}
	}

	semverEKSBase, _ := go_version.NewSemver("v2.2.0")
	// v2.2.1 开始组件开始允许部署在超级节点上
	for index, container := range podTemplateSpec.Spec.Containers {
		if container.Name == MockContainerName {
			splited := strings.Split(podTemplateSpec.Spec.Containers[index].Image, ":")
			if len(splited) == 2 {
				semverCurrent, err := go_version.NewSemver(splited[1])
				if err != nil {
					break
				}
				if semverCurrent.GreaterThan(semverEKSBase) {
					if podTemplateSpec.Annotations == nil {
						podTemplateSpec.Annotations = map[string]string{}
					}
					podTemplateSpec.Annotations["eks.tke.cloud.tencent.com/norm"] = "true"

					if podTemplateSpec.Spec.Affinity == nil || podTemplateSpec.Spec.Affinity.NodeAffinity == nil { // 有些老集群缺乏亲和性参数
						continue
					}
					for index1, nodeSelectorTerm := range podTemplateSpec.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms {
						for index2, matchExpression := range nodeSelectorTerm.MatchExpressions {
							if matchExpression.Key == "node.kubernetes.io/instance-type" && matchExpression.Operator == "NotIn" {
								podTemplateSpec.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms[index1].MatchExpressions[index2].Values = []string{"external"}
							}
						}
					}
				}
			}
		}
	}

	ttl := int32(30)
	backoff := int32(0)
	return &batchv1.Job{
		TypeMeta: metav1.TypeMeta{Kind: "Job"},
		ObjectMeta: metav1.ObjectMeta{
			Name:      MockJobName,
			Namespace: metav1.NamespaceSystem,
		},
		Spec: batchv1.JobSpec{
			TTLSecondsAfterFinished: &ttl,
			BackoffLimit:            &backoff,
			Template:                podTemplateSpec,
		},
	}
}

func getOperatorClusterJob(cluster *models.Cluster, deployment *v12.Deployment, image string, args ...string) *batchv1.Job {
	oldImage := common2.GetControllerImage(*deployment)
	argMockOldVersion := fmt.Sprintf("--mock-old-version=%s", common2.GetImageTag(oldImage))
	podTemplateSpec := deployment.Spec.Template
	podTemplateSpec.Spec.RestartPolicy = v1.RestartPolicyNever
	podTemplateSpec.Spec.TopologySpreadConstraints = nil // mock pod 不需要指定拓扑限制，否则在小地域可能导致pod调度失败
	for index, container := range podTemplateSpec.Spec.Containers {
		if container.Name == SERVICE_CONTAINER_NAME {
			podTemplateSpec.Spec.Containers[index].ImagePullPolicy = v1.PullAlways
			podTemplateSpec.Spec.Containers[index].Name = MockContainerName
			podTemplateSpec.Spec.Containers[index].Image = image
			if len(podTemplateSpec.Spec.Containers[index].Args) == 0 {
				podTemplateSpec.Spec.Containers[index].Command = append(podTemplateSpec.Spec.Containers[index].Command, args...)
				if common2.IsSupportArgMockOldVersion(image) {
					podTemplateSpec.Spec.Containers[index].Command = append(podTemplateSpec.Spec.Containers[index].Command, argMockOldVersion)
				}
			} else {
				length := len(podTemplateSpec.Spec.Containers[index].Args)
				podTemplateSpec.Spec.Containers[index].Args[length-1] = strings.Join(append([]string{podTemplateSpec.Spec.Containers[index].Args[length-1]}, args...), " ")
				if common2.IsSupportArgMockOldVersion(image) {
					podTemplateSpec.Spec.Containers[index].Args[length-1] = strings.Join([]string{podTemplateSpec.Spec.Containers[index].Args[length-1], argMockOldVersion}, " ")
				}
			}
			break
		}
	}

	for index, volume := range podTemplateSpec.Spec.Volumes {
		if volume.Name == "kubeconfig" {
			podTemplateSpec.Spec.Volumes[index] = v1.Volume{
				Name: "kubeconfig",
				VolumeSource: v1.VolumeSource{ConfigMap: &v1.ConfigMapVolumeSource{
					LocalObjectReference: v1.LocalObjectReference{
						Name: MockRBACName,
					},
					DefaultMode: pointer.Int32(420),
					Items: []v1.KeyToPath{
						{
							Key:  "kubeconfig",
							Path: "config",
						},
					},
				}},
			}
			break
		}
	}

	ttl := int32(30)
	backOff := int32(0)
	return &batchv1.Job{
		TypeMeta: metav1.TypeMeta{Kind: "Job"},
		ObjectMeta: metav1.ObjectMeta{
			Name:      MockJobName,
			Namespace: cluster.ClusterInstanceId,
		},
		Spec: batchv1.JobSpec{
			TTLSecondsAfterFinished: &ttl,
			BackoffLimit:            &backOff,
			Template:                podTemplateSpec,
		},
	}
}

func analyzePodStatus(pod *v1.Pod) (int, string) {
	restarts := 0
	reason := string(pod.Status.Phase)
	if pod.Status.Reason != "" {
		reason = pod.Status.Reason
	}

	initializing := false
	for i := range pod.Status.InitContainerStatuses {
		container := pod.Status.InitContainerStatuses[i]
		restarts += int(container.RestartCount)
		switch {
		case container.State.Terminated != nil && container.State.Terminated.ExitCode == 0:
			continue
		case container.State.Terminated != nil:
			// initialization is failed
			if len(container.State.Terminated.Reason) == 0 {
				if container.State.Terminated.Signal != 0 {
					reason = fmt.Sprintf("Init:Signal:%d", container.State.Terminated.Signal)
				} else {
					reason = fmt.Sprintf("Init:ExitCode:%d", container.State.Terminated.ExitCode)
				}
			} else {
				reason = "Init:" + container.State.Terminated.Reason
			}
			initializing = true
		case container.State.Waiting != nil && len(container.State.Waiting.Reason) > 0 && container.State.Waiting.Reason != "PodInitializing":
			reason = "Init:" + container.State.Waiting.Reason
			initializing = true
		default:
			reason = fmt.Sprintf("Init:%d/%d", i, len(pod.Spec.InitContainers))
			initializing = true
		}
		break
	}
	if !initializing {
		restarts = 0
		hasRunning := false
		for i := len(pod.Status.ContainerStatuses) - 1; i >= 0; i-- {
			container := pod.Status.ContainerStatuses[i]
			restarts += int(container.RestartCount)
			if container.State.Waiting != nil && container.State.Waiting.Reason != "" {
				reason = container.State.Waiting.Reason
			} else if container.State.Terminated != nil && container.State.Terminated.Reason != "" {
				reason = container.State.Terminated.Reason
			} else if container.State.Terminated != nil && container.State.Terminated.Reason == "" {
				if container.State.Terminated.Signal != 0 {
					reason = fmt.Sprintf("Signal:%d", container.State.Terminated.Signal)
				} else {
					reason = fmt.Sprintf("ExitCode:%d", container.State.Terminated.ExitCode)
				}
			} else if container.Ready && container.State.Running != nil {
				hasRunning = true
			}
		}

		// change pod status back to "Running" if there is at least one container still reporting as "Running" status
		if reason == "Completed" && hasRunning {
			reason = "Running"
		}
	}

	if pod.DeletionTimestamp != nil && pod.Status.Reason == "NodeLost" {
		reason = "Unknown"
	} else if pod.DeletionTimestamp != nil {
		reason = "Terminating"
	}
	return restarts, reason
}

func mockeErrorAnalysis(ctx context.Context, mockErrors []map[string]string, client kubernetes.Interface) []map[string]string {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	for _, mockError := range mockErrors {
		mockError["Analysis"] = "<None>"

		serviceName := mockError["ServiceName"]
		if !strings.Contains(serviceName, "/") {
			continue
		}
		_, namespace, name := common2.MustParseTypedName(serviceName)
		service, err := client.CoreV1().Services(namespace).Get(ctx, name, metav1.GetOptions{})
		if err != nil {
			continue
		}

		if mockError["Action"] == "CreateLoadBalancer" {
			if service.Status.LoadBalancer.Ingress == nil || len(service.Status.LoadBalancer.Ingress) == 0 {
				mockError["Analysis"] = "Ignore"
				continue
			}
		}

		if mockError["Action"] == "BatchDeregisterTargets" || mockError["Action"] == "BatchModifyTargetWeight" {
			if service.Spec.ExternalTrafficPolicy == v1.ServiceExternalTrafficPolicyTypeLocal {
				if directAccess, exist := service.Annotations["service.cloud.tencent.com/direct-access"]; !exist || directAccess == "false" {
					mockError["Analysis"] = "Ignore"
					continue
				}
			}
		}
	}
	return mockErrors
}

func init() {
	ServiceCmd.AddCommand(MockCmd)

	MockCmd.Flags().StringSliceP("cluster", "c", nil, "The cluster instance id")
	MockCmd.Flags().StringSliceP("appId", "a", nil, "The cluster app id")
	MockCmd.Flags().IntP("offset", "o", 0, "cluster offset")
	MockCmd.Flags().IntP("limit", "l", -1, "cluster limit")
	MockCmd.Flags().IntP("worker", "w", 20, "Parallelize workers")
	MockCmd.Flags().Int("timeout", 15, "timeout")
	MockCmd.Flags().StringP("logpath", "p", "", "log path to save log")
	MockCmd.Flags().Bool("service", true, "start a dry run of services")
	MockCmd.Flags().Bool("ingress", false, "start a dry run of ingresses")
}
