package service

import (
	"context"
	"errors"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/golang/glog"
	go_version "github.com/hashicorp/go-version"
	"github.com/samber/lo"
	"github.com/spf13/cobra"
	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/util/retry"

	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/cmd/kops/common"
	cluster2 "git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
	common2 "git.woa.com/kateway/kateway-server/cmd/kops/kops/common"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tke/tke"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tracing"
	"git.woa.com/kateway/kateway-server/pkg/models"
	region2 "git.woa.com/kateway/kateway-server/pkg/util/region"
)

var SetImageCmd = &cobra.Command{
	Use:   "setimage",
	Short: "service controller publish tool",
	Long:  "service controller publish tool",
	RunE: func(cmd *cobra.Command, args []string) error {
		task := Task{
			TaskInterface: &SetImageCmdTask{},
			Title: []string{
				"Region", "Cluster", "Type", "AppId", "Version", "Error",
			},
		}
		if err := task.Run(cmd, args); err != nil {
			return err
		}
		return nil
	},
}

type SetImageCmdTask struct {
	Task
}

// eks.tke.cloud.tencent.com/metacluster: cls-g0zvpnr3
// eks.tke.cloud.tencent.com/owner-uin: "2252646423"
// eks.tke.cloud.tencent.com/product-name: tkex-csig-ziyan
// eks.tke.cloud.tencent.com/region: ap-tianjin
func (*SetImageCmdTask) Do(cmd *cobra.Command, cluster *models.Cluster) (result []map[string]string) {
	span, ctx := jaeger.StartSpanFromContext(cmd.Context(), jaeger.WithOperationName("tke.service.setimage"))
	defer span.Finish()
	start := time.Now()

	tracing.Cluster(span, cluster)

	regionInfo := region2.Get(cluster.Region)

	errorResult := []map[string]string{
		{
			"Region":  regionInfo.Alias,
			"Cluster": cluster.ClusterInstanceId,
			"Type":    strconv.Itoa(int(cluster.ClusterType)),
			"AppId":   strconv.FormatUint(cluster.AppId, 10),
			"Version": cluster.K8sVersion,
		},
	}
	// [qingyangwu] 变更（更新镜像）
	user, _ := cmd.Flags().GetString("user")
	token, _ := cmd.Flags().GetString("token")
	releaseRecord := &model.ReleaseAudit{
		Date:      time.Now(),
		Region:    regionInfo.Alias,
		ClusterID: cluster.ClusterInstanceId,
		Component: "service",
		Status:    true,
		Publisher: user,
		Token:     token,
		CreatedAt: start,
		UpdatedAt: time.Now(),
	}

	defer func() {
		releaseRecord.FinishedAt = time.Now()
		// [qingyangwu] 更新数据库发布审计表
		if err := common2.ReleaseAuditService.Update(releaseRecord); err != nil {
			errorResult[0]["Error"] = err.Error()
		}
	}()

	imageTag, _ := cmd.Flags().GetString("imageTag")
	if imageTag == "" {
		errorResult[0]["Error"] = fmt.Sprintf("ImageTag Not Setting\n")
		return errorResult
	}
	releaseRecord.ImageTag = imageTag

	ccrDomain, exist := common.CcrRegionMap[regionInfo.Alias]
	if !exist {
		errorResult[0]["Error"] = fmt.Sprintf("Region %s ccr not configed", regionInfo.Alias)
		return errorResult
	}

	configmapVersion, _ := cmd.Flags().GetString("configmapVersion")
	// 执行一次健康检查，获取变更前的集群健康数据
	if skipHealtchCheck, err := cmd.Flags().GetBool("skipPostCheck"); err == nil && !skipHealtchCheck {
		if err := common2.StartOneCheckBeforeRelease(ctx, cluster.ClusterInstanceId); err != nil {
			errorResult[0]["Error"] = err.Error() // 记录错误
			releaseRecord.Status = false          // 变更失败
			return errorResult                    // 中断变更
		}
	}
	// 正式变更
	image := fmt.Sprintf("%s%s:%s", ccrDomain, common.ServiceControllerImageRepo, imageTag)
	isModified, err := setImage(ctx, cluster, image, configmapVersion, releaseRecord)
	releaseRecord.Status = isModified
	if err != nil {
		releaseRecord.Reason = lo.ToPtr(err.Error())
		errorResult[0]["Error"] = err.Error()
		return errorResult
	}
	if skipPostCheck, err := cmd.Flags().GetBool("skipPostCheck"); err == nil && !skipPostCheck {
		times, err := cmd.Flags().GetInt("checkTimes")
		if err != nil {
			errorResult[0]["Error"] = err.Error()
			return errorResult
		}
		interval, err := cmd.Flags().GetString("checkInterval")
		if err != nil {
			errorResult[0]["Error"] = err.Error()
			return errorResult
		}
		dur, err := time.ParseDuration(interval)
		if err != nil {
			errorResult[0]["Error"] = err.Error()
			return errorResult
		}
		taskID, err := common2.StartInspectionTaskAfterRelease(ctx, cluster.ClusterInstanceId, dur, times)
		if err != nil {
			errorResult[0]["Error"] = err.Error()
			return errorResult
		}
		releaseRecord.TaskID = taskID
	}

	return errorResult
}

func setImage(ctx context.Context, cluster *models.Cluster, image string, configmapVersion string, releaseRecord *model.ReleaseAudit) (bool, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	if err := tke.CheckClusterStatus(cluster); err != nil {
		return false, err
	}
	if tke.CheckTKEClusterIfNeedSkip(cluster) {
		return false, fmt.Errorf("skip cluster")
	}

	deploymentNS, deploymentName := getServiceControllerDeploymentName(cluster)

	clientSet, err := cluster2.GetTKEClusterClientSet(cluster.Region, cluster)
	// create the clientset
	if cluster.ClusterType == models.CLUSTER_TYPE_CLUSTER_DEPLOY_ASYNC_PROCESS ||
		cluster.ClusterType == models.CLUSTER_TYPE_CLUSTER_DEPLOY_MIGRATE_TO_OPERATOR ||
		cluster.ClusterType == models.CLUSTER_TYPE_CLUSTER_DEPLOY_ON_OPERATOR {
		metaClusterId := cluster.MetaClusterID
		if cluster.MetaClusterID == "" {
			regionInfo := region2.Get(cluster.Region)
			metaClusterId = cluster2.GetDefaultMetaCluster(regionInfo.Alias)
		}
		metaCluster := cluster2.MustGetTKECluster(cluster.Region, metaClusterId)
		clientSet, err = cluster2.GetTKEClusterClientSet(cluster.Region, metaCluster)
	}
	if err != nil {
		return false, err
	}

	if cluster.ClusterType == models.CLUSTER_TYPE_CLUSTER_INDEPENDENT { // 独立集群可能缺乏 nodes update 权限
		if err = cluster2.EnsureServiceClusterRole(ctx, cluster); err != nil {
			return false, err
		}
	}

	defer func() {
		// [qingyangwu] 抓取源镜像Tag
		if releaseRecord == nil {
			return
		}
		// 获取 Deployment
		if serviceControllerDeployment, err := clientSet.K8sCli.AppsV1().Deployments(deploymentNS).Get(ctx, deploymentName, v1.GetOptions{}); err == nil {
			// 查找指定容器并更新镜像标签
			for _, container := range serviceControllerDeployment.Spec.Template.Spec.Containers {
				if container.Name == SERVICE_CONTAINER_NAME {
					releaseRecord.SourceImageTag = common2.GetImageTag(container.Image)
					break
				}
			}
		}
	}()

	deploy, err := clientSet.K8sCli.AppsV1().Deployments(deploymentNS).Get(ctx, deploymentName, v1.GetOptions{})
	if err != nil {
		return false, fmt.Errorf("failed to get deployment %s/%s", deploymentNS, deploymentName)
	}
	modified := deploy.DeepCopy()
	_, index, exists := lo.FindIndexOf(modified.Spec.Template.Spec.Containers, func(c corev1.Container) bool {
		return c.Name == SERVICE_CONTAINER_NAME
	})
	if !exists {
		return false, errors.New("service controller container not exist")
	}
	modifiedContainer := &modified.Spec.Template.Spec.Containers[index]

	// 托管集群
	if cluster.ClusterType == models.CLUSTER_TYPE_CLUSTER_DEPLOY_ASYNC_PROCESS ||
		cluster.ClusterType == models.CLUSTER_TYPE_CLUSTER_DEPLOY_MIGRATE_TO_OPERATOR ||
		cluster.ClusterType == models.CLUSTER_TYPE_CLUSTER_DEPLOY_ON_OPERATOR {
		// TODO verify configmapVerson exist
		selectors := map[string]string{
			ServiceControllerGlobalConfigKey:     ServiceControllerGlobalConfigName,
			ServiceControllerGlobalConfigVersion: configmapVersion,
		}
		listOptions := v1.ListOptions{
			LabelSelector: labels.SelectorFromSet(labels.Set(selectors)).String(),
		}

		serviceConfigmap, err := clientSet.K8sCli.CoreV1().ConfigMaps(v1.NamespaceSystem).List(ctx, listOptions)
		if err != nil {
			return false, err
		}
		if len(serviceConfigmap.Items) == 0 {
			return false, fmt.Errorf("service controller configmap version: %s not exist", configmapVersion)
		}

		master, err := clientSet.MasterCli.MasterV1alpha1().Masters(cluster.ClusterInstanceId).Get(ctx, cluster.ClusterInstanceId, v1.GetOptions{})
		if err != nil {
			return false, err
		}
		image, err := getConfigMapImage(ctx, clientSet.K8sCli, configmapVersion, master.Spec.Version)
		if err != nil {
			return false, err
		}
		semverBase, _ := go_version.NewSemver("v2.0.0")
		splited := strings.Split(image, ":")
		if len(splited) == 2 {
			semverCurrent, err := go_version.NewSemver(splited[1])
			if err != nil {
				return false, err
			}
			if !semverCurrent.GreaterThanOrEqual(semverBase) {
				return false, fmt.Errorf("Skip_LessThen_v2.0.0")
			}
		}
		patchJson := fmt.Sprintf("{\"metadata\":{\"labels\":{\"%s\":\"%s\"}}}", SERVICE_OPERATOR_LABEL, configmapVersion)
		_, err = clientSet.MasterCli.MasterV1alpha1().Masters(cluster.ClusterInstanceId).Patch(ctx, cluster.ClusterInstanceId, types.MergePatchType, []byte(patchJson), v1.PatchOptions{})
		if err != nil {
			return false, err
		}

		if master.Labels["cloud.tencent.com/tke-operator-managed"] == "false" {
			modifiedContainer.Image = image
		}

	} else {
		// 独立集群或老版本托管集群
		// 检查发布前的组件镜像版本是否高于 v2.0.0
		semverBase, _ := go_version.NewSemver("v2.0.0")
		splited := strings.Split(modifiedContainer.Image, ":")
		if len(splited) == 2 {
			semverCurrent, err := go_version.NewSemver(splited[1])
			if err != nil {
				glog.Errorf("Cluster %s version %s get service controller deployment error, err:%v", cluster.ClusterInstanceId, cluster.K8sVersion, err)
				return false, err
			}
			if !semverCurrent.GreaterThanOrEqual(semverBase) {
				return false, fmt.Errorf("Skip_LessThen_v2.0.0")
			}
		}

		modifiedContainer.Image = image
		if modified.Spec.Template.Spec.Affinity == nil {
			modified.Spec.Template.Spec.Affinity = GetAffinity()
		}
		if modified.Spec.Template.Spec.Affinity.NodeAffinity == nil {
			modified.Spec.Template.Spec.Affinity.NodeAffinity = GetNodeAffinity()
		}
		if modified.Spec.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution == nil {
			modified.Spec.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution = GetNodeSelector()
		}
		isSetting := false
		for _, nodeSelectorTerm := range modified.Spec.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms {
			for _, matchExpression := range nodeSelectorTerm.MatchExpressions {
				if matchExpression.Key == "node.kubernetes.io/instance-type" {
					isSetting = true
					break
				}
			}
		}
		if !isSetting {
			modified.Spec.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms =
				append(modified.Spec.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms, GetNodeSelectorTerm())
		}

		if cluster.ClusterType == models.CLUSTER_TYPE_CLUSTER_ORIGIN || cluster.ClusterType == models.CLUSTER_TYPE_CLUSTER_INDEPENDENT {
			// v2.2.1 开始组件开始允许部署在超级节点上。托管组件不考虑部署在超级节点上。
			semverEKSBase, _ := go_version.NewSemver("v2.2.0")

			semverCurrent, err := go_version.NewSemver(splited[1])
			if err != nil {
				return false, err
			}
			if semverCurrent.GreaterThan(semverEKSBase) { // 开始支持组件部署在超级节点上
				if modified.Spec.Template.Annotations == nil {
					modified.Spec.Template.Annotations = map[string]string{}
				}
				modified.Spec.Template.Annotations["eks.tke.cloud.tencent.com/norm"] = "true"
				for index1, nodeSelectorTerm := range modified.Spec.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms {
					for index2, matchExpression := range nodeSelectorTerm.MatchExpressions {
						if matchExpression.Key == "node.kubernetes.io/instance-type" && matchExpression.Operator == "NotIn" {
							modified.Spec.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms[index1].MatchExpressions[index2].Values = []string{"external"}
						}
					}
				}
			}
		}

		for index, hostAlias := range modified.Spec.Template.Spec.HostAliases {
			if hostAlias.IP != "************" {
				continue
			}
			if len(hostAlias.Hostnames) == 10 {
				continue
			}
			modified.Spec.Template.Spec.HostAliases[index].Hostnames = []string{
				"tke.internal.tencentcloudapi.com",
				"clb.internal.tencentcloudapi.com",
				"cvm.internal.tencentcloudapi.com",
				"tag.internal.tencentcloudapi.com",
				"vpc.internal.tencentcloudapi.com",
				"ssl.internal.tencentcloudapi.com",
				"as.tencentcloudapi.com",
				"cbs.tencentcloudapi.com",
				"cvm.tencentcloudapi.com",
				"vpc.tencentcloudapi.com",
			}
		}
	}

	var updated bool
	if !reflect.DeepEqual(deploy, modified) {
		if _, err := clientSet.K8sCli.AppsV1().Deployments(deploymentNS).Update(ctx, modified, v1.UpdateOptions{}); err != nil {
			return false, fmt.Errorf("failed to update the service controller deployment: %w", err)
		}
		updated = true
	}

	enabled, err := enableReuse(ctx, clientSet.K8sCli, types.NamespacedName{
		Namespace: deploymentNS,
		Name:      deploymentName,
	})

	return updated || enabled, err
}

func enableReuse(ctx context.Context, cli kubernetes.Interface, nn types.NamespacedName) (bool, error) {
	var updated bool
	err := retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		deploy, err := cli.AppsV1().Deployments(nn.Namespace).Get(ctx, nn.Name, v1.GetOptions{})
		if err != nil {
			return err
		}
		modified := deploy.DeepCopy()
		_, index, exists := lo.FindIndexOf(modified.Spec.Template.Spec.Containers, func(c corev1.Container) bool {
			return c.Name == SERVICE_CONTAINER_NAME
		})
		if !exists {
			return errors.New("container service-controller not found")
		}
		modifiedContainer := &modified.Spec.Template.Spec.Containers[index]

		// 过滤掉--reuse-flag=false的选项（托管），或者${REUSE_FLAG}环境变量引用
		if len(modifiedContainer.Args) == 0 {
			// 托管集群逻辑
			modifiedContainer.Command = lo.Filter(modifiedContainer.Command, func(cmd string, _ int) bool {
				return !strings.HasPrefix(cmd, "--reuse-flag")
			})
		} else {
			// 独立集群逻辑
			arg, index, exists := lo.FindIndexOf(modifiedContainer.Args, func(arg string) bool {
				return strings.Contains(arg, "REUSE_FLAG") || strings.Contains(arg, "--reuse-flag")
			})
			if exists {
				segments := strings.Split(arg, " ")
				segments = lo.Filter(segments, func(seg string, _ int) bool {
					return seg != "${REUSE_FLAG}" && !strings.HasPrefix(seg, "--reuse-flag")
				})
				modifiedContainer.Args[index] = strings.Join(segments, " ")
			}
		}
		if !reflect.DeepEqual(deploy, modified) {
			_, err = cli.AppsV1().Deployments(nn.Namespace).Update(ctx, modified, v1.UpdateOptions{})
			if err == nil {
				updated = true
			}
			return err
		}
		return nil
	})
	return updated, err
}

func getConfigMapImage(ctx context.Context, k8sClient kubernetes.Interface, configmapVersion string, kubeVersion string) (string, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	selectors := map[string]string{
		ServiceControllerGlobalConfigKey:     ServiceControllerGlobalConfigName,
		ServiceControllerGlobalConfigVersion: configmapVersion,
	}
	listOptions := v1.ListOptions{
		LabelSelector: labels.SelectorFromSet(labels.Set(selectors)).String(),
	}

	serviceConfigmap, err := k8sClient.CoreV1().ConfigMaps(v1.NamespaceSystem).List(ctx, listOptions)
	if err != nil {
		return "", err
	}
	if len(serviceConfigmap.Items) == 0 {
		return "", fmt.Errorf("service controller configmap version: %s not exist", configmapVersion)
	}
	if strings.Contains(kubeVersion, "-") {
		kubeVersion = strings.Split(kubeVersion, "-")[0]
	}
	image, exist := serviceConfigmap.Items[0].Data[kubeVersion]
	if !exist {
		return "", fmt.Errorf("service controller configmap kube version: %s not exist", kubeVersion)
	}
	return strings.TrimSpace(strings.TrimPrefix(image, "image:")), nil
}

func GetAffinity() *corev1.Affinity {
	return &corev1.Affinity{
		NodeAffinity: GetNodeAffinity(),
	}
}

func GetNodeAffinity() *corev1.NodeAffinity {
	return &corev1.NodeAffinity{
		RequiredDuringSchedulingIgnoredDuringExecution: GetNodeSelector(),
	}
}

func GetNodeSelector() *corev1.NodeSelector {
	return &corev1.NodeSelector{
		NodeSelectorTerms: []corev1.NodeSelectorTerm{
			GetNodeSelectorTerm(),
		},
	}
}

func GetNodeSelectorTerm() corev1.NodeSelectorTerm {
	return corev1.NodeSelectorTerm{
		MatchExpressions: []corev1.NodeSelectorRequirement{
			GetNodeSelectorRequirement(),
		},
	}
}

func GetNodeSelectorRequirement() corev1.NodeSelectorRequirement {
	return corev1.NodeSelectorRequirement{
		Key:      "node.kubernetes.io/instance-type",
		Operator: "NotIn",
		Values: []string{
			"eklet",
			"external",
		},
	}
}

func init() {
	ServiceCmd.AddCommand(SetImageCmd)

	SetImageCmd.Flags().StringSliceP("cluster", "c", nil, "The cluster instance id")
	SetImageCmd.Flags().StringSliceP("appId", "a", nil, "The cluster app id")
	SetImageCmd.Flags().IntP("offset", "o", 0, "cluster offset")
	SetImageCmd.Flags().IntP("limit", "l", -1, "cluster limit")
	SetImageCmd.Flags().IntP("worker", "w", 20, "Parallelize workers")
	SetImageCmd.Flags().StringP("logpath", "p", "", "log path to save log")
	SetImageCmd.Flags().StringP("configmapVersion", "m", "", "The new configmap version for operator cluster")
}
