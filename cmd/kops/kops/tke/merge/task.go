package merge

import (
	"context"
	"os"
	"sort"
	"strings"
	"time"

	"github.com/spf13/cobra"
	"k8s.io/client-go/util/workqueue"

	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/helper"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/table"
	"git.woa.com/kateway/kateway-server/pkg/models"
	region2 "git.woa.com/kateway/kateway-server/pkg/util/region"
)

type Task struct {
	TaskInterface
	Title []string
}

type TaskInterface interface {
	Do(cmd *cobra.Command, cluster *models.Cluster) []map[string]string
}

func (task *Task) Run(cmd *cobra.Command, args []string) error {
	ctx, cancel := context.WithTimeout(cmd.Context(), 15*time.Minute)
	defer cancel()
	span, ctx := jaeger.StartSpanFromContext(ctx, jaeger.WithOperationName(cmd.Name()))
	defer span.Finish()
	cmd.SetContext(ctx)

	clusterIds, err := helper.InputClusters(cmd)
	if err != nil {
		return err
	}
	appIds, _ := cmd.Flags().GetStringSlice("appId")
	regionShort, _ := cmd.Flags().GetString("region")
	offset, _ := cmd.Flags().GetInt("offset")
	limit, _ := cmd.Flags().GetInt("limit")
	worker, _ := cmd.Flags().GetInt("worker")

	regionInfo := region2.Get(regionShort)

	sql := "select * from cluster where lifeState NOT IN ('abnormal', 'idle', 'deleted', 'deleting', 'charge_destroyed')"
	if len(clusterIds) != 0 {
		quotedClusterIds := make([]string, len(clusterIds))
		for index, clusterId := range clusterIds {
			quotedClusterIds[index] = fmt.Sprintf("'%s'", clusterId)
		}
		sql += fmt.Sprintf(" AND clusterInstanceId in (%s)", strings.Join(quotedClusterIds, ","))
	}
	if len(appIds) != 0 {
		quotedAppIds := make([]string, len(appIds))
		for index, appId := range appIds {
			quotedAppIds[index] = fmt.Sprintf("'%s'", appId)
		}
		sql += fmt.Sprintf(" AND appId in (%s)", strings.Join(quotedAppIds, ","))
	}

	tkeClusters := cluster.ListTKECluster(regionInfo.Name, sql)

	if limit != -1 {
		sort.Sort(ClusterModelSlice(tkeClusters))
		if offset > len(tkeClusters)-1 {
			offset = len(tkeClusters) - 1
		}

		end := offset + limit
		if offset+limit > len(tkeClusters) {
			end = len(tkeClusters)
		}
		tkeClusters = tkeClusters[offset:end]
	}

	logPath, _ := cmd.Flags().GetString("logpath")
	if logPath != "" {
		if _, err := os.Stat(logPath); os.IsNotExist(err) {
			err := os.MkdirAll(logPath, os.ModePerm)
			if err != nil {
				return err
			}
		}
	}

	fmt.Printf("exec on region: %s\n", regionShort)
	if len(tkeClusters) == 0 {
		fmt.Println("No clustetr found")
		return nil
	}

	clsIds := make([]string, len(tkeClusters))
	for i, cluster := range tkeClusters {
		clsIds[i] = cluster.ClusterInstanceId
	}
	fmt.Printf("Total %d cluster: %s\n", len(clsIds), strings.Join(clsIds, ", "))
	span.LogKV("clusters", clusterIds)

	// 执行任务
	clusterResults := make([]map[string]string, 0)
	workqueue.ParallelizeUntil(ctx, worker, len(tkeClusters), func(index int) {
		cluster := tkeClusters[index]
		defer func() {
			if r := recover(); r != nil {
				fmt.Println(cluster.ClusterInstanceId, helper.GetPanicStackTrace(r))
			}
		}()
		// if checkIfNeedSkip(cluster) {
		// clusterResults = append(clusterResults, map[string]string{
		//	"Region":  region,
		//	"Cluster": cluster.Name,
		//	"Version": cluster.Spec.Version,
		//	"Error":   "skip cluster",
		// })
		// }
		result := task.Do(cmd, &cluster)
		for index, _ := range result {
			clusterResults = append(clusterResults, result[index])
		}
	})

	// 打印输出
	task.printResult(ctx, clusterResults)

	if logPath != "" {
		logDir := fmt.Sprintf("%s/%s/%s",
			logPath,
			regionShort,
			time.Now().Format("2006-01-02"))
		err := os.MkdirAll(logDir, os.ModePerm)

		if err != nil {
			fmt.Printf("Make %s log dir failed: %v\n", regionShort, err)
		}
		log, err := os.Create(fmt.Sprintf("%s/service-controller_%s.log", logDir, regionShort))
		defer log.Close()
		if err != nil {
			return err
		}

		task.fprintResult(log, clusterResults)
	}
	fmt.Println("Done")
	return nil
}

func (task *Task) printResult(ctx context.Context, clusterResults []map[string]string) {
	table.Print(ctx, task.Title, clusterResults)
}

func (task *Task) fprintResult(log *os.File, clusterResults []map[string]string) {
	for index, item := range task.Title {
		if index == len(task.Title)-1 { // Last Item
			fmt.Printf("%s\n", item)
		} else {
			fmt.Printf("%s\t", item)
		}
	}
	for _, res := range clusterResults {
		for index, item := range task.Title {
			value, exist := res[item]
			if !exist {
				value = "nil"
			}
			if index == len(task.Title)-1 { // Last Item
				fmt.Fprintf(log, "%s\n", value)
			} else {
				fmt.Fprintf(log, "%s\t", value)
			}
		}
	}
}

type ClusterModelSlice []models.Cluster

func (a ClusterModelSlice) Len() int {
	return len(a)
}

func (a ClusterModelSlice) Swap(i, j int) {
	a[i], a[j] = a[j], a[i]
}

func (a ClusterModelSlice) Less(i, j int) bool {
	return a[i].ClusterInstanceId < a[j].ClusterInstanceId
}
