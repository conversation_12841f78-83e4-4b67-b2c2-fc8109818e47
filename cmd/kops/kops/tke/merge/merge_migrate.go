package merge

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	"github.com/spf13/cobra"

	"git.woa.com/kateway/pkg/telemetry/jaeger"

	cluster2 "git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tke/service"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tracing"
	"git.woa.com/kateway/kateway-server/pkg/dryrun"
	"git.woa.com/kateway/kateway-server/pkg/merge"
	"git.woa.com/kateway/kateway-server/pkg/models"
	"git.woa.com/kateway/kateway-server/pkg/service/cluster"
	region2 "git.woa.com/kateway/kateway-server/pkg/util/region"
)

var MigrateCMD = &cobra.Command{
	Use:   "migrate",
	Short: "start a migration which merges two controllers together",
	RunE: func(cmd *cobra.Command, args []string) error {
		task := Task{
			TaskInterface: &MigrateTask{},
			Title: []string{
				"Region", "Cluster", "Error",
			},
		}
		if err := task.Run(cmd, args); err != nil {
			return err
		}
		return nil
	},
}

func init() {
	MergeCMD.AddCommand(MigrateCMD)

	MigrateCMD.Flags().StringSliceP("cluster", "c", nil, "The cluster instance id")
	MigrateCMD.Flags().StringSliceP("appId", "a", nil, "The cluster app id")
	MigrateCMD.Flags().IntP("offset", "o", 0, "cluster offset")
	MigrateCMD.Flags().IntP("limit", "l", -1, "cluster limit")
	MigrateCMD.Flags().IntP("worker", "w", 20, "Parallelize workers")
	MigrateCMD.Flags().Int("timeout", 15, "timeout")
	MigrateCMD.Flags().StringP("logpath", "p", "", "log path to save log")
	MigrateCMD.Flags().Bool("dryrun", true, "start a dryrun before migration")
}

type MigrateTask struct {
	Task

	timeout int
	dryrun  bool

	clusterCli     cluster.ClientsSet
	metaClusterCli *cluster.ClientsSet
}

func (t *MigrateTask) migrate(ctx context.Context, c *models.Cluster) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	p := merge.NewProcessor(c.ClusterInstanceId, merge.ClientSets{
		Cluster:     t.clusterCli,
		MetaCluster: t.metaClusterCli,
	}, false)

	var fn func(context.Context, string) error
	if t.dryrun {
		fn = func(ctx context.Context, image string) error {
			records, err := service.MockRun(ctx, c, image, 15, false, lo.ToPtr(true))
			if err != nil {
				return err
			}
			if err := dryrun.BuildErrorFromResult(records.ToMap()); err != nil {
				return fmt.Errorf("dryrun failed: %w", err)
			}
			return nil
		}
	}

	return p.Migrate(ctx, fn)
}

func (t *MigrateTask) do(ctx context.Context, c *models.Cluster) error {
	cs, err := cluster2.GetTKEClusterClientSet(c.Region, c)
	if err != nil {
		return err
	}
	t.clusterCli = cs
	metaCS, err := getMetaClusterClientSet(c)
	if err != nil {
		return err
	}
	t.metaClusterCli = metaCS

	return t.migrate(ctx, c)
}

func (t *MigrateTask) Do(cmd *cobra.Command, cluster *models.Cluster) []map[string]string {
	span, ctx := jaeger.StartSpanFromContext(cmd.Context(), jaeger.WithOperationName("tke.merge.migrate"))
	defer span.Finish()

	tracing.Cluster(span, cluster)
	results := []map[string]string{
		{
			"Region":  cluster.Region,
			"Cluster": cluster.ClusterInstanceId,
			"Error":   "<nil>",
		},
	}

	t.timeout, _ = cmd.Flags().GetInt("timeout")
	t.dryrun, _ = cmd.Flags().GetBool("dryrun")
	err := t.do(ctx, cluster)
	if err != nil {
		results[0]["Error"] = err.Error()
	}
	return results
}

func getMetaClusterClientSet(c *models.Cluster) (*cluster.ClientsSet, error) {
	if c.ClusterType == models.CLUSTER_TYPE_CLUSTER_DEPLOY_ASYNC_PROCESS ||
		c.ClusterType == models.CLUSTER_TYPE_CLUSTER_DEPLOY_MIGRATE_TO_OPERATOR ||
		c.ClusterType == models.CLUSTER_TYPE_CLUSTER_DEPLOY_ON_OPERATOR {
		metaClusterId := c.MetaClusterID
		if c.MetaClusterID == "" {
			regionInfo := region2.Get(c.Region)
			metaClusterId = cluster2.GetDefaultMetaCluster(regionInfo.Alias)
		}
		metaCluster := cluster2.MustGetTKECluster(c.Region, metaClusterId)
		clientSet, err := cluster2.GetTKEClusterClientSet(c.Region, metaCluster)
		if err != nil {
			return nil, err
		}
		return &clientSet, nil
	}
	return nil, nil
}
