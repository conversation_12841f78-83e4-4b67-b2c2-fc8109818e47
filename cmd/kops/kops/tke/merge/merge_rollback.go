package merge

import (
	"context"
	"fmt"

	"github.com/spf13/cobra"
	appsv1 "k8s.io/api/apps/v1"

	"git.woa.com/kateway/pkg/telemetry/jaeger"

	cluster2 "git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tke/ingress"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tracing"
	"git.woa.com/kateway/kateway-server/pkg/dryrun"
	"git.woa.com/kateway/kateway-server/pkg/merge"
	"git.woa.com/kateway/kateway-server/pkg/models"
	region2 "git.woa.com/kateway/kateway-server/pkg/util/region"
)

var RollbackCMD = &cobra.Command{
	Use:   "rollback",
	Short: "rollback the merged controller",
	RunE: func(cmd *cobra.Command, args []string) error {
		task := Task{
			TaskInterface: &RollbackTask{},
			Title: []string{
				"Region", "Cluster", "Error",
			},
		}
		if err := task.Run(cmd, args); err != nil {
			return err
		}
		return nil
	},
}

func init() {
	MergeCMD.AddCommand(RollbackCMD)

	RollbackCMD.Flags().StringSliceP("cluster", "c", nil, "The cluster instance id")
	RollbackCMD.Flags().StringSliceP("appId", "a", nil, "The cluster app id")
	RollbackCMD.Flags().IntP("offset", "o", 0, "cluster offset")
	RollbackCMD.Flags().IntP("limit", "l", -1, "cluster limit")
	RollbackCMD.Flags().IntP("worker", "w", 20, "Parallelize workers")
	RollbackCMD.Flags().Int("timeout", 15, "timeout")
	RollbackCMD.Flags().StringP("logpath", "p", "", "log path to save log")
	RollbackCMD.Flags().Bool("dryrun", true, "start a dry run before rollback")
}

type RollbackTask struct {
	Task

	dryrun bool
}

func (t *RollbackTask) Do(cmd *cobra.Command, cluster *models.Cluster) []map[string]string {
	span, ctx := jaeger.StartSpanFromContext(cmd.Context(), jaeger.WithOperationName("eks.merge.rollback"))
	defer span.Finish()

	tracing.Cluster(span, cluster)

	regionInfo := region2.Get(cluster.Region)
	results := []map[string]string{
		{
			"Region":  regionInfo.Alias,
			"Cluster": cluster.ClusterInstanceId,
			"Error":   "<nil>",
		},
	}
	t.dryrun, _ = cmd.Flags().GetBool("dryrun")

	err := t.do(ctx, cluster)
	if err != nil {
		results[0]["Error"] = err.Error()
	}
	return results
}

func (t *RollbackTask) do(ctx context.Context, c *models.Cluster) error {
	cs, err := cluster2.GetTKEClusterClientSet(c.Region, c)
	if err != nil {
		return err
	}

	metaCS, err := getMetaClusterClientSet(c)
	if err != nil {
		return err
	}

	var dryrunFunc func(context.Context, string, *appsv1.Deployment) error
	if t.dryrun {
		dryrunFunc = func(ctx context.Context, image string, source *appsv1.Deployment) error {
			result, err := ingress.MockRun(ctx, c, image, 15, source)
			if err != nil {
				return err
			}
			if err := dryrun.BuildErrorFromResult(result); err != nil {
				return fmt.Errorf("dryrun failed: %w", err)
			}
			return nil
		}
	}

	return merge.NewProcessor(c.ClusterInstanceId, merge.ClientSets{
		Cluster:     cs,
		MetaCluster: metaCS,
	}, false).Rollback(ctx, dryrunFunc)
}
