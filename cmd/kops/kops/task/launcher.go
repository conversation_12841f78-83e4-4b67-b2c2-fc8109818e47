package task

import (
	"context"

	"github.com/samber/lo"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	clsmodel "git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/service/controller"
	"git.woa.com/kateway/kateway-server/pkg/task"
	"git.woa.com/kateway/kateway-server/pkg/task/model"
	"git.woa.com/kateway/kateway-server/pkg/tmp/telemetry/log"
	"git.woa.com/kateway/kateway-server/pkg/version"
)

type Launcher struct {
	mergeTasks []Merge
}

func (t *Launcher) Init(ctx context.Context) error {
	clusters, err := services.Get().Cluster().List(ctx)
	if err != nil {
		return err
	}
	t.mergeTasks, err = buildMergeTasks(ctx, clusters)
	if err != nil {
		return err
	}

	return nil
}

func (t *Launcher) Steps() (steps task.Steps) {
	steps.AddStep(
		buildLaunchStep("lwbowenyan", t.mergeTasks),
	)
	return
}

func (Launcher) Config() task.Config {
	cfg := task.GetDefaultConfig()
	cfg.Concurrency = 1
	return cfg
}

func buildMergeTasks(ctx context.Context, clusters []clsmodel.Cluster) ([]Merge, error) {
	internalClusters := lo.Filter(clusters, func(c clsmodel.Cluster, _ int) bool {
		return c.Appid == 1251707795
	})
	cfg := config.Get().Task.Merge
	filter := func(c *clsmodel.Cluster) (bool, error) {
		if lo.Contains(cfg.SkippedClusters, c.ClusterID) {
			return false, nil
		}

		if lo.Contains([]string{"gz", "bj", "sh"}, c.Region) {
			return false, nil
		}

		ins, err := controller.GetByCluster(ctx, c)
		if err != nil {
			return false, err
		}
		ctrl := ins.Service()
		merged, err := ctrl.MergedIngressControllerEnabled(ctx)
		if err != nil {
			return false, err
		}
		if merged {
			return false, nil
		}

		v, _, err := ctrl.GetVersion(ctx)
		if err != nil {
			return false, err
		}

		return v.Compare(version.MustParse("2.5.0")) >= 0, nil
	}

	tasks := []Merge{}

	if !cfg.Enable {
		return tasks, nil
	}

	for _, cls := range internalClusters {
		merge, err := filter(&cls)
		if err != nil {
			log.FromContext(ctx).Error(err, "filter cluster error")
			continue
		}
		if merge {
			tasks = append(tasks, Merge{
				ClusterID:   cls.ClusterID,
				Op:          MergeOpMigrate,
				AlarmOnFail: true,
			})
			if len(tasks) >= cfg.Limit {
				break
			}
		}
	}
	return tasks, nil
}

func buildLaunchStep[T any](creator string, tasks []T) task.Step {
	subTaskType := task.GetTaskType[T]()
	name := "create_" + subTaskType + "_batch"
	fn := func(ctx context.Context) error {
		svc := services.Get().Task()
		runningBatchCount, err := svc.Count(ctx,
			task.WithTypes(task.GetTaskType[task.Batch]()),
			task.WithSubTypes(subTaskType),
			task.WithStates(model.TaskStateRunning, model.TaskStatePending),
		)
		if err != nil {
			return err
		}
		if runningBatchCount > 0 {
			return nil
		}
		parent := task.FromContext(ctx)

		_, err = svc.CreateBatchTask(ctx, lo.ToAnySlice(tasks), task.WithCreator(creator), task.WithParent(parent.ID))
		return err
	}
	return task.Step{
		Name: name,
		Executor: task.StepExecutor{
			Fn: fn,
		},
	}
}
