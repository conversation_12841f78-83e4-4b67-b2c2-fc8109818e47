package task

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"

	"git.woa.com/kateway/pkg/telemetry/log"

	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
	"git.woa.com/kateway/kateway-server/pkg/alarm"
	"git.woa.com/kateway/kateway-server/pkg/tmp/types/sets"
)

const (
	alarmReleaseDuration time.Duration = 24 * time.Hour // 告警恢复时间
	RiskTimeoutInterval  time.Duration = 30 * time.Minute
)

const (
	CreateOrUpdateTaskSuccess = "Success"
	CreateOrUpdateTaskFailed  = "Failed"
)

type TaskStatus int

const (
	TaskRunning TaskStatus = iota
	TaskCompleted
	TaskError
)

type ClusterHealthResult struct {
	ClusterName   string
	ClusterRegion string
	AppID         uint64
	ClusterID     string
	ClusterType   string

	CLBList RiskCLBList
	Options RiskOptions
}

type RiskOptions struct {
	LastReleaseTime     string
	LastReleaseDuration string
	ReleaseManager      string
	CurrentVersion      string
	LastVersion         string

	Duration string
	Error    string
}

type RiskCLBList []CLBHealthResult

func (r RiskCLBList) Strings(clusterID string) []string {
	result := []string{}
	for _, risk := range r {
		result = append(result, risk.String(clusterID))
	}
	result = append(result, "\n")

	return result
}

type CLBHealthResult struct {
	CLBID       string
	RiskLevel   string
	Environment string
	RiseCount   uint
	Risks       []CLBRisk
}

func ignoreRiskLevels(risklist RiskCLBList, level ...string) RiskCLBList {
	result := RiskCLBList{}
	for _, l := range level {
		for _, risk := range risklist {
			if risk.RiskLevel != l {
				result = append(result, risk)
			}
		}
	}

	return result
}

type CLBRisk struct {
	Reason      string
	BeforeCount uint
	AfterCount  uint
}

func buildCLBRef(clusterID, clbID string) string {
	return fmt.Sprintf("[%s](http://kateway.woa.com/inspection/clb/report/get?clbID=%s&clusterID=%s&refreshCache=true)", clbID, clbID, clusterID)
}

func buildClusterRef(clusterID string) string {
	return fmt.Sprintf("[%s](http://kateway.woa.com/inspection/cluster/report/get?clusterID=%s&filter=clb)", clusterID, clusterID)
}

func (c *CLBHealthResult) String(clusterID string) string {
	if c == nil {
		return ""
	}
	var sb strings.Builder
	// CLB级别信息
	sb.WriteString(fmt.Sprintf("### %s 风险上升 ⌛️\n", buildCLBRef(clusterID, c.CLBID)))
	if c.Environment != "" {
		sb.WriteString("#### 业务: " + c.Environment + "\n")
	}
	sb.WriteString("#### 风险维度: " + c.RiskLevel + "\n")
	if c.RiseCount == 0 {
		sb.WriteString("#### 上升次数: " + "首次发生" + "\n")
	} else {
		sb.WriteString("#### 上升次数: " + fmt.Sprint(c.RiseCount+1) + "\n")
	}
	// 详细信息（Listener | Rule | RS）
	sb.WriteString("#### 风险变化:\n")

	j := 1
	for _, reason := range c.Risks {
		// TODO: 告警出前后变化
		sb.WriteString(fmt.Sprintf("- **%d. %s**: 异常数量 (%d -> %d)\n",
			j,
			reason.Reason,
			reason.BeforeCount,
			reason.AfterCount))
		j++
	}

	return sb.String()
}

// diffSlice 比较两个slice
// 返回：需要新增、删除、更新的slice
func diffSlice(old, cur []string) ([]string, []string, []string) {
	toAdd, toDel := lo.Difference(cur, old)
	toUpdate := lo.Intersect(cur, old)

	return toAdd, toDel, toUpdate
}

func createRiskMap(oldRiskRecords []model.CLBRiskRecord) map[string]model.CLBRiskRecord {
	hitMap := make(map[string]model.CLBRiskRecord)
	for _, oldRiskRecord := range oldRiskRecords {
		hitMap[oldRiskRecord.CLBID] = oldRiskRecord
	}
	return hitMap
}

func createCurRiskMap(curRiskDetails []model.CLBRiskDetail) map[string]model.CLBRiskDetail {
	hitMap := make(map[string]model.CLBRiskDetail)
	for _, curRiskDetail := range curRiskDetails {
		hitMap[curRiskDetail.CLBID] = curRiskDetail
	}
	return hitMap
}

func createCLBRiskRecord(clusterID string, appID uint64, oldV *model.CLBRiskRecord, v *model.CLBRiskDetail, now time.Time) *model.CLBRiskRecord {
	state := string(model.StateStockRisk)
	riseCount := 0
	if oldV != nil {
		state = oldV.State
		riseCount = oldV.RiseCount
	}
	return &model.CLBRiskRecord{
		AppID:          appID,
		ClusterName:    clusterID,
		CLBID:          v.CLBID,
		RiskScore:      v.RiskScore,
		RiskCount:      v.RiskCount,
		UsingResources: v.UsingResources,
		RiskLevel:      v.RiskLevel,
		RiskMetrics:    *model.ToStruct(v.Counters),
		UpdatedAt:      now,
		State:          state, // 如果当前CLB存在旧的风险，则使用旧的状态，否则使用StockRisk（存量风险）
		RiseCount:      riseCount,
	}
}

func alarmRiskCLB(ctx context.Context, cluster *model.Cluster, results RiskCLBList, recover, unrecover []string, needRise bool) (RiskOptions, error) {
	audit := RiskOptions{
		LastReleaseTime:     "暂无发布信息",
		LastReleaseDuration: "暂无发布信息",
		ReleaseManager:      "暂无发布信息",
		CurrentVersion:      "暂无发布信息",
		LastVersion:         "暂无发布信息",
	}

	// 排除指定 Level 的 CLB Risk
	riskCLBList := ignoreRiskLevels(results, "")
	alarmResults := riskCLBList.Strings(cluster.ClusterID)
	alarmResults = append(alarmResults, recoverCLBList(cluster.ClusterID, recover, "风险已恢复 ✅")...)
	alarmResults = append(alarmResults, recoverCLBList(cluster.ClusterID, unrecover, "风险未恢复 ⚠️")...)
	if len(alarmResults) == 3 {
		return audit, nil
	}
	// 构建告警内容
	content, err := buildAlertContent(cluster, len(alarmResults), alarmResults)
	if err != nil {
		log.FromContext(ctx).Error(err, "构建告警内容失败")
		return audit, err
	}
	// 构建alarm请求
	req := &alarm.Request{
		ObjType: "kateway",
		ObjName: "inspection",
		ObjID:   cluster.ClusterID,
		Content: content,
		Level:   1,
	}
	if len(results) != 0 {
		// 获取最近一次变更审计记录
		// 在一定时间区间内存在变更记录，需要升级告警等级
		err = searchReleaseAudit(ctx, cluster, req, &audit)
		if err != nil {
			return audit, err
		}
	}
	if needRise {
		// 高于RS维度的风险需要上升告警等级
		req.Level++
	}
	// 发送告警
	log.FromContext(ctx).Info("发送告警", "content", content)
	if err := alarm.Send(req); err != nil {
		return audit, err
	}
	return audit, nil
}

// 告警策略
// 1. Score 上升幅度小于10， 告警等级5
// 2. Score 上升幅度大于10小于100， 告警等级4
// 3. Score 上升幅度大于100， 告警等级3
// 4. 如果审查到24小时内有发布记录，告警等级2

// buildAlertContent 构建告警内容
func buildAlertContent(cluster *model.Cluster, metricsCnt int, contents []string) (string, error) {
	details := strings.Join(contents, "\n")
	return fmt.Sprintf(`
## 【接入层巡检异常🤔】

### 集群 %s 风险变化 🔭

名称：%v
类型：%v
地域：%v
用户：%v

风险变化CLB：%v个

%v`, buildClusterRef(cluster.ClusterID), cluster.Name, cluster.Type, cluster.Region, cluster.Appid, metricsCnt-3, details), nil
}

func recoverCLBList(clusterID string, clbList []string, state string) []string {
	results := []string{}
	clbList = sets.NewString(clbList...).List()
	// CLB级别信息
	for _, clbid := range clbList {
		var sb strings.Builder
		if info := strings.Split(clbid, "/"); len(info) == 5 {
			if strings.Contains(info[2], "prod") {
				sb.WriteString(fmt.Sprintf("### %s %s\n#### **业务: 正式环境** \n#### 风险维度: %s \n#### 上升次数: %s \n#### 持续时间: %s\n", buildCLBRef(clusterID, info[0]), state, info[1], info[3], info[4]))
			} else {
				sb.WriteString(fmt.Sprintf("### %s %s \n#### 风险维度: %s \n#### 上升次数: %s \n#### 持续时间: %s\n", buildCLBRef(clusterID, info[0]), state, info[1], info[3], info[4]))
			}
			// 详细信息（Listener | Rule | RS）
			sb.WriteString("\n")
			results = append(results, sb.String())
		} else {
			sb.WriteString(fmt.Sprintf("### %s: %s", state, buildCLBRef(clusterID, clbid)))
			// 详细信息（Listener | Rule | RS）
			sb.WriteString("\n")
			results = append(results, sb.String())
		}
	}

	results = append(results, "\n")
	return results
}

// searchReleaseAudit 处理变更审计记录
func searchReleaseAudit(ctx context.Context, cluster *model.Cluster, req *alarm.Request, audit *RiskOptions) error {
	var releaseRecords []model.ReleaseAudit
	if err := services.Get().ReleaseAudit().Select(cluster.ClusterID, &releaseRecords); err != nil {
		log.FromContext(ctx).Error(err, "获取变更审计记录失败")
		return err
	}

	if len(releaseRecords) == 0 {
		log.FromContext(ctx).Info("没有查询到变更记录")
		return nil
	}

	// 打印最后一次变更记录
	lastRecord := releaseRecords[len(releaseRecords)-1]
	log.FromContext(ctx).Info("集群: ", cluster.ClusterID, " 最近一次变更时间:", lastRecord.UpdatedAt.Format("2006-01-02 15:04:05"), "镜像版本:", lastRecord.ImageTag)
	var sb strings.Builder
	sb.WriteString(req.Content)
	// 添加分隔线
	sb.WriteString("\n----------------------------------------\n")
	// 更新告警内容
	sb.WriteString("集群相关变更记录:\n")
	sb.WriteString(fmt.Sprintf("\t1. 最近一次变更时间：%v\n", lastRecord.UpdatedAt.Format("2006-01-02 15:04:05")))
	sb.WriteString(fmt.Sprintf("\t2. 距离上一次变更：%v\n", time.Since(lastRecord.UpdatedAt).Truncate(time.Minute)))
	sb.WriteString(fmt.Sprintf("\t3. 组件发布操作人：%v\n", lastRecord.Publisher))
	sb.WriteString(fmt.Sprintf("\t4. 组件当前版本：%v\n", lastRecord.ImageTag))
	sb.WriteString(fmt.Sprintf("\t5. 组件上次版本：%v\n", lastRecord.SourceImageTag))

	audit.LastReleaseTime = lastRecord.UpdatedAt.Format("2006-01-02 15:04:05")
	audit.LastReleaseDuration = time.Since(lastRecord.UpdatedAt).Truncate(time.Minute).String()
	audit.ReleaseManager = lastRecord.Publisher
	audit.CurrentVersion = lastRecord.ImageTag
	audit.LastVersion = lastRecord.SourceImageTag

	req.Content = sb.String()

	// 检查时间间隔
	if !isIntervalExceeded(lastRecord.UpdatedAt, alarmReleaseDuration) {
		req.ObjName = "inspection"
		req.Level = 2
	}

	return nil
}

// isIntervalExceeded 检查时间间隔是否超过给定时间
func isIntervalExceeded(lastPublishedTime time.Time, duration time.Duration) bool {
	return time.Since(lastPublishedTime) > duration
}

func startInspectionTask(ctx context.Context, cluster *model.Cluster, taskid, duration string, cur, maxtimes int) error {
	return notifyInspectionTask(ctx, cluster, "启动 🚀", taskid, duration, cur, maxtimes)
}

func updateInspectionTask(ctx context.Context, cluster *model.Cluster, taskid, duration string, cur, maxtimes int) error {
	return notifyInspectionTask(ctx, cluster, "续期 🌊", taskid, duration, cur, maxtimes)
}

func stopInspectionTask(ctx context.Context, cluster *model.Cluster, taskid string, cur, maxtimes int) error {
	return notifyInspectionTask(ctx, cluster, "结束 🤚", taskid, "", cur, maxtimes)
}

func restartInspectionTask(ctx context.Context, cluster *model.Cluster, taskid, duration string, cur, maxtimes int) error {
	return notifyInspectionTask(ctx, cluster, "重启 ↩️", taskid, duration, cur, maxtimes)
}

func finishedInspectionTask(ctx context.Context, cluster *model.Cluster, taskid, duration string, cur, maxtimes int) error {
	return notifyInspectionTask(ctx, cluster, "完成 ✅", taskid, duration, cur, maxtimes)
}

func terminatedInspectionTask(ctx context.Context, cluster *model.Cluster, taskid, duration string, cur, maxtimes int, err error) error {
	return notifyInspectionTask(ctx, cluster, "退出 ⚠️", taskid, duration, cur, maxtimes, err)
}

func deniedInspectionTask(ctx context.Context, cluster *model.Cluster, taskid, duration string, cur, maxtimes int, err error) error {
	return notifyInspectionTask(ctx, cluster, "拒绝 ❌", taskid, duration, cur, maxtimes, err)
}

func notifyInspectionTask(_ context.Context, cluster *model.Cluster, state string, taskid, duration string, cur, maxtimes int, err ...error) error {
	req := &alarm.Request{
		ObjType: "kateway",
		ObjName: "inspection",
		ObjID:   cluster.ClusterID,
		Content: buildClusterContent(cluster, state, taskid, duration, cur, maxtimes, err...),
		Level:   1,
	}
	if err := alarm.Send(req); err != nil {
		return err
	}

	return nil
}

func buildClusterContent(cluster *model.Cluster, state, taskid, duration string, cur, maxtimes int, err ...error) string {
	if err != nil {
		return fmt.Sprintf(`
## 【接入层巡检周知♻️】

### %s %s 巡检任务

任务ID：%s
名称：%v
类型：%v
地域：%v
用户：%v
检查间隙：%s
当前检查次数：%d
最大检查次数：%d
异常原因：%s
`, state, buildClusterRef(cluster.ClusterID), taskid, cluster.Name, cluster.Type, cluster.Region, cluster.Appid, duration, cur, maxtimes, err[0].Error())
	}
	return fmt.Sprintf(`
## 【接入层巡检周知♻️】

### %s %s 巡检任务

任务ID：%s
名称：%v
类型：%v
地域：%v
用户：%v
检查间隙：%s
当前检查次数：%d
最大检查次数：%d
`, state, buildClusterRef(cluster.ClusterID), taskid, cluster.Name, cluster.Type, cluster.Region, cluster.Appid, duration, cur, maxtimes)
}

func generateCLBResult(_ context.Context, i int, old *model.CLBRiskRecord, cur *model.CLBRiskDetail) *CLBHealthResult {
	result := &CLBHealthResult{}

	if cur == nil {
		return nil
	}

	var metrics map[string]uint
	if old != nil {
		metrics = old.RiskMetrics.ToMap()
	}

	result.CLBID = cur.CLBID
	result.RiskLevel = cur.RiskLevel
	if strings.Contains(cur.UsingResources, "production") {
		result.Environment = "正式环境"
	}
	if old != nil {
		result.RiseCount = uint(old.RiseCount)
	}
	j := 1
	for k, v := range cur.Counters {
		riskDetail := model.GetRiskByName(k).Detail
		oldV := uint(0) // 假设旧值是 uint 类型
		if old != nil {
			if oldValue, exists := metrics[k]; exists {
				oldV = oldValue
			}
		}
		result.Risks = append(result.Risks, CLBRisk{
			Reason:      riskDetail,
			BeforeCount: oldV,
			AfterCount:  v,
		})
		// TODO: 告警出前后变化
		j++
	}

	return result
}

func filterIncrement(oldRiskRecords []model.CLBRiskRecord) []string {
	result := []string{}
	for _, risk := range oldRiskRecords {
		if risk.State == string(model.StateIncrementRisk) {
			result = append(result, risk.CLBID)
		}
	}

	return result
}
