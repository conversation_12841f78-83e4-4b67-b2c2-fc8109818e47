package task

import (
	"context"
	"fmt"
	"time"

	"git.woa.com/kateway/kateway-server/cmd/kops/kops/service/controller"
	"git.woa.com/kateway/kateway-server/pkg/task"
)

func (*PostCheck) Config() task.Config {
	return task.Config{
		Timeout: task.TimeoutConfig{
			Duration: 10 * time.Minute,
			Strategy: task.TimeoutStrategyFail,
		},
		Concurrency: 100,
	}
}

type PostCheck struct {
	ClusterID string
	Name      string
	Version   string
	User      string
	Token     string

	ctrl *controller.Controller
}

func (t *PostCheck) Init(ctx context.Context) error {
	ctrl, err := t.controller(ctx, t.ClusterID, t.Name)
	if err != nil {
		return err
	}
	t.ctrl = ctrl

	return nil
}

func (t *PostCheck) Steps() (steps task.Steps) {
	steps.AddStepFuncs(t.PostCheck)
	return
}

func (t *PostCheck) controller(ctx context.Context, cluster string, name string) (*controller.Controller, error) {
	var (
		ctrl *controller.Instance
		err  error
	)

	ctrl, err = controller.GetByClusterID(ctx, cluster)
	if err != nil {
		return nil, err
	}

	switch name {
	case "service":
		return ctrl.Service(), nil
	case "ingress":
		return ctrl.Ingress(), nil
	}

	return nil, fmt.Errorf("未找到对应的控制器")
}

func (t *PostCheck) PostCheck(_ context.Context) error {
	return nil
}
