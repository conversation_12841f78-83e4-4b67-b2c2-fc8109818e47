package task

import (
	"context"
	"time"

	"git.woa.com/kateway/kateway-server/cmd/kops/kops/service/controller"
	"git.woa.com/kateway/kateway-server/pkg/task"
)

func (Test) Config() task.Config {
	return task.Config{
		Timeout: task.TimeoutConfig{
			Duration: 10 * time.Minute,
			Strategy: task.TimeoutStrategyFail,
		},
		Concurrency: 100,
	}
}

type TestStatus struct {
	ServiceImage string
	IngressImage string
}

type Test struct {
	ClusterID string

	ins    *controller.Instance
	status TestStatus
}

func (t *Test) Init(ctx context.Context) error {
	ins, err := controller.GetByClusterID(ctx, t.ClusterID)
	if err != nil {
		return err
	}
	t.ins = ins
	return nil
}

func (t *Test) Steps() (steps task.Steps) {
	steps.AddStepFuncs(t.retrieveIngressImage, t.retrieveServiceImage)
	return
}

func (t *Test) retrieveIngressImage(ctx context.Context) error {
	image, err := t.ins.Ingress().GetImage(ctx)
	if err != nil {
		return err
	}
	t.status.IngressImage = image.String()
	return nil
}

func (t *Test) retrieveServiceImage(ctx context.Context) error {
	image, err := t.ins.Service().GetImage(ctx)
	if err != nil {
		return err
	}
	t.status.ServiceImage = image.String()
	return nil
}

func (t *Test) Status(_ context.Context) any {
	return t.status
}
