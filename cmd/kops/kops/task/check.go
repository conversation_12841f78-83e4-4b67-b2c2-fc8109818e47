package task

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"sync"
	"time"

	"github.com/Masterminds/semver"
	"github.com/go-logr/logr"
	"github.com/hashicorp/go-multierror"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/util/workqueue"

	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/telemetry/log"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	common2 "git.woa.com/kateway/kateway-server/cmd/kops/kops/common"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/helper"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/service/controller"
	"git.woa.com/kateway/kateway-server/pkg/alarm"
	"git.woa.com/kateway/kateway-server/pkg/image"
)

type Checker struct {
	date string
	task *model.Task

	log       logr.Logger
	workQueue workqueue.RateLimitingInterface
	waitGroup sync.WaitGroup

	total int
	count int64
	sync.Mutex
}

func NewChecker(task *model.Task) *Checker {
	name := "Checker"
	return &Checker{
		task: task,

		log:       log.WithName(name),
		workQueue: workqueue.NewNamedRateLimitingQueue(workqueue.DefaultControllerRateLimiter(), name),
	}
}

func (r *Checker) Run(stopCh <-chan struct{}) {
	r.log.Info("Running")
	defer r.log.Info("Done")

	go r.checkImage()

	r.list() // 列出所有集群

	r.task.State = model.TaskStateRunning.String()
	r.task.Version = config.Get().GetVersion(r.task.Type).MockVersion
	r.task.Total = r.total
	r.task.Creator = "wallaceqian"
	r.task.CreatedAt = time.Now()
	_ = services.Get().LegacyTask().Update(r.task)

	for i := 0; i < config.Get().Checker.ConcurrentSyncs; i++ {
		r.waitGroup.Add(1)
		go func() {
			defer r.waitGroup.Done()

			r.worker()
		}()
	}

	r.waitGroup.Wait()
	r.task.State = model.TaskStateDone.String()
	r.task.FinishedAt = lo.ToPtr(time.Now())
	r.task.Cost = r.task.FinishedAt.Sub(r.task.CreatedAt).Hours()
	_ = services.Get().LegacyTask().Update(r.task)
}

func (r *Checker) worker() {
	for r.processNextWorkItem() {
	}
}

func (r *Checker) list() {
	clusters, err := services.Get().Cluster().List(context.Background())
	if err != nil {
		r.log.Error(err, "list cluster error")
	}
	r.total = len(clusters)
	r.log.Info("list cluster", "size", r.total)

	for _, cluster := range clusters {
		r.workQueue.Add(&cluster)
	}

	r.workQueue.ShutDown()
}

func (r *Checker) processNextWorkItem() bool {
	startTime := time.Now()
	q := r.workQueue
	key, quit := q.Get()
	if quit {
		return false
	}
	defer q.Done(key)
	cluster := key.(*model.Cluster)

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	logger := r.log.WithValues("cluster", cluster.ClusterID)
	ctx = log.WithContext(ctx, logger)

	r.Lock()
	r.count++
	r.task.Progress = fmt.Sprintf("%d/%d %.2f%%", r.count, r.total, float64(r.count)/float64(r.total)*100)
	_ = services.Get().LegacyTask().Update(r.task)
	logger.Info(fmt.Sprintf("start syncing %s", r.task.Progress), "len", q.Len())
	r.Unlock()

	var err error
	span, ctx := jaeger.StartSpanFromContext(ctx, jaeger.WithOperationName("check"))
	span.SetTag("cluster", cluster.ClusterID)

	defer func() {
		duration := time.Since(startTime).String()

		if err != nil {
			logger.Error(err, "sync error", "duration", duration)

			jaeger.LogError(span, err)
		} else {
			logger.Info("sync done", "duration", duration)
		}

		span.LogKV("duration", duration)
		span.Finish()
	}()

	if services.Get().Cluster().NeedProcess(cluster) {
		err = r.do(ctx, cluster)
		if err != nil {
			q.AddRateLimited(key)
			logger.Error(err, "sync error", "duration", time.Since(startTime).String())
			return true
		}
	} else {
		logger.Info("skip sync")
	}

	q.Forget(key)
	logger.Info("sync ok", "duration", time.Since(startTime).String())

	return true
}

// 需要考虑过滤mock-pod
func (c *Checker) getPodImageID(ctx context.Context, ctrl *controller.Controller) (imageID string) {
	// 过滤掉 mock Pod
	imageID = "<None>"
	pods, err := ctrl.ListPod(ctx, controller.ListPodsOptions{
		Filters: []func(corev1.Pod) bool{
			func(pod corev1.Pod) bool {
				return !isMockPod(&pod)
			},
		},
	})
	if err != nil || len(pods) == 0 {
		return
	}

	imageID = common2.GetPodSha256(&pods[0])
	leaderStatus, err := ctrl.GetLeaderStatus(ctx)
	if err != nil {
		return
	}

	if p, exists := lo.Find(pods, func(p corev1.Pod) bool { return p.Name == leaderStatus.Identity }); exists {
		imageID = common2.GetPodSha256(&p)
	}

	return
}

func (c *Checker) do(ctx context.Context, cluster *model.Cluster) (err error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	span.SetTag("cluster", cluster.ClusterID)

	var errs error

	result := &model.Kateway{
		Date:        c.task.CreatedAt.Format("20060102"),
		Type:        c.task.Type,
		ClusterID:   cluster.ClusterID,
		ClusterType: cluster.Type,
		Appid:       cast.ToString(cluster.Appid),
		Region:      cluster.Region,
		CheckError:  lo.ToPtr(""),
		MockAt:      time.Now(),
		CheckAt:     time.Now(),
	}

	defer func() {
		if r := recover(); r != nil {
			// 不能返回err，否则会导致panic错误继续被重试，无意义执行
			result.CheckError = lo.ToPtr(fmt.Sprintf("ClusterID: %s\n%s", cluster.ClusterID, helper.GetPanicStackTrace(r)))
		}

		if err != nil {
			result.CheckError = lo.ToPtr(err.Error())
		}
		result.CheckCost = time.Since(result.CheckAt).String()

		if err != nil {
			if strings.Contains(err.Error(), "invalid LifeState") ||
				strings.Contains(err.Error(), "not found") {
				log.FromContext(ctx).Error(err, "skip")
				err = nil
				return

			}
		}

		err = services.Get().Kateway().Update(ctx, result)
	}()

	ins, err := controller.GetByCluster(ctx, cluster)
	if err != nil {
		return err
	}

	var (
		ctrl      = lo.Ternary(c.task.Type == "Service", ins.Service(), ins.Ingress())
		clientset = ctrl.GetClientset()
	)

	// 检查deployment状态
	deployment, err := ctrl.GetDeployment(ctx, controller.GetDeploymentOptions{})
	if err != nil {
		result.CheckError = lo.ToPtr(fmt.Sprintf("查询Deployment错误：%s", err))
		return nil
	}

	image := deployment.Spec.Template.Spec.Containers[0].Image
	version := image[strings.LastIndex(image, ":")+1:]
	if strings.Contains(image, "paas") && strings.Contains(version, "linux-amd64") { // 将临时开发打的镜像归到上一个版本，避免版本数量过多
		version = version[:strings.Index(version, "-")]
	}

	result.Replicas = deployment.Status.Replicas
	result.Image = image
	result.Version = version

	if result.Replicas == 0 {
		errs = multierror.Append(errs, fmt.Errorf("deployment副本数为0, 组件未运行"))
	} else if result.Replicas == 1 {
		errs = multierror.Append(errs, fmt.Errorf("deployment为单副本，不满足高可用"))
	}

	v, err := semver.NewVersion(version)
	if err != nil {
		errs = multierror.Append(errs, fmt.Errorf("deployment镜像版本%s为非语义版本", version))
	} else {
		if v.Prerelease() != "" {
			errs = multierror.Append(errs, fmt.Errorf("deployment版本为%s非正式版本", version))
		}
	}

	// 检查pod状态
	pods, err := ctrl.ListPod(ctx, controller.ListPodsOptions{})
	if err != nil {
		return err
	}
	for i, pod := range pods {
		if isMockPod(&pod) {
			err := fmt.Errorf("存在mock pod[%d]（%s）", i, pod.Name)
			errs = multierror.Append(errs)
			if len(pod.OwnerReferences) == 0 {
				err = fmt.Errorf("%w: OwnerReferences 不存在", err)
				err1 := clientset.K8sCli.CoreV1().Pods(pod.Namespace).Delete(ctx, pod.Name, metav1.DeleteOptions{})
				if err1 != nil {
					err = fmt.Errorf("%w: 删除Pod错误: %w", err, err1)
				}
			} else {
				job, err1 := clientset.K8sCli.BatchV1().Jobs(pod.Namespace).Get(ctx, pod.OwnerReferences[0].Name, metav1.GetOptions{})
				if err1 == nil {
					err = fmt.Errorf("%w: Job 未删除 创建时间=%s", err, job.CreationTimestamp.Time)
					if time.Since(job.CreationTimestamp.Time) > time.Hour {
						err1 := clientset.K8sCli.BatchV1().Jobs(pod.Namespace).Delete(ctx, pod.OwnerReferences[0].Name, metav1.DeleteOptions{})
						if err1 != nil {
							err = fmt.Errorf("%w: 删除Job错误: %w", err, err1)
						}

						// 删除Job可能不会删除Pod，显式调用删除
						deletePods, err1 := clientset.K8sCli.CoreV1().Pods(pod.Namespace).List(ctx, metav1.ListOptions{LabelSelector: metav1.FormatLabelSelector(job.Spec.Selector)})
						if err1 != nil {
							err = fmt.Errorf("%w: 拉取Pods错误: %w", err, err1)
						}
						for _, deletePod := range deletePods.Items {
							if isMockPod(&deletePod) {
								err1 := clientset.K8sCli.CoreV1().Pods(pod.Namespace).Delete(ctx, pod.Name, metav1.DeleteOptions{})
								if err1 != nil {
									err = fmt.Errorf("%w: 删除Pod错误: %w", err, err1)
								}
							}
						}
					}
				}
			}
			errs = multierror.Append(errs, err)
		}

		if pod.Status.Phase != corev1.PodRunning {
			errs = multierror.Append(errs, fmt.Errorf("pod[%d]（%s）状态异常（%s）", i, pod.Name, pod.Status.Phase))
		}
		podImage := pod.Spec.Containers[0].Image
		if podImage != image {
			errs = multierror.Append(errs, fmt.Errorf("pod[%d]（%s）镜像（%s）与期望（%s）不符", i, pod.Name, podImage, image))
		}

		if len(pod.Status.ContainerStatuses) > 0 {
			container := pod.Status.ContainerStatuses[0]
			if container.RestartCount > 0 && container.State.Running != nil && container.State.Running.StartedAt.After(time.Now().Add(24*time.Hour)) {
				errs = multierror.Append(errs, fmt.Errorf("pod[%d]（%s）最近24小时有重启 重启次数（%d）>0 上次启动时间： %s", i, pod.Name, container.RestartCount, container.State.Running.StartedAt))
			}
		}

		err = checkLog(clientset.K8sCli, &pod, 24)
		if err != nil {
			errs = multierror.Append(errs, fmt.Errorf("pod[%d]（%s）检查日志失败：%w", i, pod.Name, err))
		}
	}
	// [qingyangwu] 获取Pod imageID
	result.ImageID = c.getPodImageID(ctx, ctrl)

	if services.Get().Cluster().NeedMook(c.task.Type, cluster) {
		result.MockAt = time.Now()
		if c.task.Type != "Service" {
			return nil
		}
		mockResult, err := services.Get().Service().Mock(ctx, cluster, config.Get().GetVersion(c.task.Type).MockVersion)
		result.MockCost = time.Since(result.MockAt).String()
		if err != nil {
			result.MockErrorDetail = lo.ToPtr(err.Error())
		} else {
			if len(mockResult) > 0 {
				data, _ := json.Marshal(result)
				result.MockErrorDetail = lo.ToPtr(string(data))
			} else {
				result.MockErrorDetail = lo.ToPtr("")
			}
		}
		result.MockError = lo.ToPtr(services.ParseMockError(*result.MockErrorDetail))
	} else {
		log.FromContext(ctx).Info("skip %v mock", c.task.Type)
	}

	return errs
}

func isMockPod(pod *corev1.Pod) bool {
	if !strings.Contains(pod.Name, "mock-job") {
		return false
	}

	container := pod.Spec.Containers[0]
	return container.Name == "mock" && strings.Contains(container.Image, "tencentyun.com")
}

func checkLog(clientset kubernetes.Interface, pod *corev1.Pod, sinceHour int) error {
	req := clientset.CoreV1().Pods(pod.Namespace).GetLogs(pod.Name, &corev1.PodLogOptions{
		SinceSeconds: lo.ToPtr(int64(sinceHour * 60 * 60)),
	})

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	readCloser, err := req.Stream(ctx)
	if err != nil {
		return fmt.Errorf("获取日志失败: %w", err)
	}
	defer readCloser.Close()

	data, err := io.ReadAll(readCloser)
	if err != nil {
		return fmt.Errorf("读取日志失败: %w", err)
	}

	if len(data) == 0 {
		return fmt.Errorf("最近%v小时无日志输出", sinceHour)
	}

	return nil
}

func (c *Checker) checkImage() (err error) {
	startTime := time.Now()
	c.log.Info("checkImage")
	defer func() {
		c.log.Info("checkImage done", "err", err, "cost", time.Since(startTime).String())
	}()

	versions, err := services.Get().Kateway().ListVersion(c.task.Type)
	if err != nil {
		return err
	}
	c.log.Info("checkImage", "versions", versions)

	regions, err := services.Get().Kateway().ListCCRRegion()
	if err != nil {
		return err
	}
	c.log.Info("checkImage", "regions", regions)

	src := config.Get().GetImageName(c.task.Type)
	var wg wait.Group
	for _, version := range versions {
		wg.Start(func() {
			err := image.Check(src, []string{version}, regions)
			if err != nil {
				content := fmt.Sprintf("检查镜像出错：\n源镜像：%s\n错误：%s\n", src, err)
				req := &alarm.Request{
					ObjType: "kateway",
					ObjName: src,
					ObjID:   version,
					Content: content,
				}
				err = alarm.Send(req)
				if err != nil {
					c.log.Error(err, "send alarm error")
				}
			}
		})
	}
	wg.Wait()

	return nil
}
