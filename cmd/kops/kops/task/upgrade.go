package task

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/samber/lo"

	"git.woa.com/kateway/kateway-server/cmd/kops/kops/service/controller"
	"git.woa.com/kateway/kateway-server/pkg/dryrun"
	"git.woa.com/kateway/kateway-server/pkg/task"
	"git.woa.com/kateway/kateway-server/pkg/tmp/zhiyan"
)

func (*Upgrade) Config() task.Config {
	return task.Config{
		Timeout: task.TimeoutConfig{
			Duration: 10 * time.Minute,
			Strategy: task.TimeoutStrategyFail,
		},
		Concurrency: 100,
	}
}

type UpgradeStatus struct {
	Records dryrun.Records
}

type Upgrade struct {
	ClusterID string
	Name      string
	Version   string
	User      string
	Token     string

	ctrl   *controller.Controller
	status UpgradeStatus
}

func (t *Upgrade) Init(ctx context.Context) error {
	ctrl, err := t.controller(ctx, t.ClusterID, t.Name)
	if err != nil {
		return err
	}
	t.ctrl = ctrl

	return nil
}

func (t *Upgrade) Steps() (steps task.Steps) {
	steps.AddStepFuncs(t.Upgrade)
	return
}

func (t *Upgrade) controller(ctx context.Context, cluster string, name string) (*controller.Controller, error) {
	var (
		ctrl *controller.Instance
		err  error
	)

	ctrl, err = controller.GetByClusterID(ctx, cluster)
	if err != nil {
		return nil, err
	}

	switch name {
	case "service":
		return ctrl.Service(), nil
	case "ingress":
		return ctrl.Ingress(), nil
	}

	return nil, fmt.Errorf("未找到对应的控制器")
}

func (t *Upgrade) writer() func(format string, args ...any) {
	zhiyanClient := zhiyan.New(map[string]any{
		"user":    t.User,
		"token":   t.Token,
		"cluster": t.ClusterID,
	})
	return func(format string, args ...any) {
		data := fmt.Sprintf(format, args...)
		_ = zhiyanClient.Send(data)
	}
}

func (t *Upgrade) Upgrade(ctx context.Context) error {
	options := controller.UpdateOptions{
		DryrunOptions: controller.DryrunOptions{
			Writer: t.writer(),
		},
	}

	records, err := t.ctrl.Update(ctx, t.Version, options)
	if err != nil {
		return err
	}

	t.status.Records = records

	return nil
}

func (t *Upgrade) Status(_ context.Context) any {
	return string(lo.Must(json.Marshal(t.status)))
}
