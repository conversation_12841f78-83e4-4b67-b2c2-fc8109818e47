package task

import (
	"context"
	"errors"
	"strings"

	"github.com/samber/lo"
	appsv1 "k8s.io/api/apps/v1"

	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/service/controller"
	"git.woa.com/kateway/kateway-server/pkg/alarm"
	"git.woa.com/kateway/kateway-server/pkg/dryrun"
	"git.woa.com/kateway/kateway-server/pkg/merge"
	"git.woa.com/kateway/kateway-server/pkg/task"
	taskmodel "git.woa.com/kateway/kateway-server/pkg/task/model"
)

const (
	MergeOpMigrate  = "migrate"
	MergeOpRollback = "rollback"
)

type Merge struct {
	ClusterID            string
	Op                   string
	DisableDryrunIngress bool
	AlarmOnFail          bool

	ins       *controller.Instance
	cluster   *model.Cluster
	processor *merge.Processor
}

func (m *Merge) Init(ctx context.Context) error {
	cls, err := services.Get().Cluster().Get(ctx, m.ClusterID)
	if err != nil {
		return task.NewErrFailed(err)
	}
	m.cluster = cls

	ins, err := controller.GetByCluster(ctx, cls)
	if err != nil {
		return task.NewErrFailed(err)
	}
	m.ins = ins

	m.processor = merge.NewProcessor(m.ClusterID, merge.ClientSets{
		Cluster:     *ins.GetClusterClientset(),
		MetaCluster: lo.Ternary(cls.IsIndependent(), nil, ins.GetMetaClusterClientset()),
	}, cls.IsEKS())
	return nil
}

func (Merge) Config() task.Config {
	cfg := task.GetDefaultConfig()
	cfg.Concurrency = 100
	return cfg
}

func (m *Merge) DetectConflicts(ctx context.Context) error {
	tk := task.FromContext(ctx)

	total, err := services.Get().Task().Count(ctx, task.WithTypes("Merge"),
		task.WithStates(taskmodel.TaskStateRunning),
		task.WithInputLike("%"+m.ClusterID+"%"),
		task.WithoutTaskID(tk.ID),
	)
	if err != nil {
		return err
	}
	if total > 0 {
		return errors.New("other tasks are operating the cluster")
	}
	return nil
}

func (m *Merge) Steps() (steps task.Steps) {
	steps.AddStepFuncs(m.preflightCheck)
	switch m.Op {
	case MergeOpMigrate:
		steps.AddStepFuncs(m.migrate)
	case MergeOpRollback:
		steps.AddStepFuncs(m.rollback)
	default:
		panic("invalid op")
	}
	return
}

func (m *Merge) preflightCheck(_ context.Context) error {
	if m.cluster.Appid != 1251707795 {
		return task.NewErrFailed(errors.New("only support clusters with appid 1251707795"))
	}
	return nil
}

func (m *Merge) migrate(ctx context.Context) error {
	var dryrunIngress func(context.Context, string) error
	if !m.DisableDryrunIngress {
		dryrunIngress = func(ctx context.Context, image string) error {
			ctrl := m.ins.Service()
			rs, err := ctrl.Dryrun(ctx, strings.Split(image, ":")[1], controller.DryrunOptions{
				DisableDryrunService: true,
				DryrunIngress:        lo.ToPtr(true),
			})
			if err != nil {
				return err
			}
			return dryrun.BuildErrorFromResult(rs.ToMap())
		}
	}
	if err := m.processor.Migrate(ctx, dryrunIngress); err != nil {
		m.sendAlarm(ctx, err)
		return task.NewErrFailed(err)
	}
	return nil
}

func (m *Merge) rollback(ctx context.Context) error {
	var dryrunIngress func(context.Context, string, *appsv1.Deployment) error
	if !m.DisableDryrunIngress {
		dryrunIngress = func(ctx context.Context, image string, d *appsv1.Deployment) error {
			ctrl := m.ins.Ingress()
			rs, err := ctrl.Dryrun(ctx, strings.Split(image, ":")[1], controller.DryrunOptions{
				Deployment: d,
			})
			if err != nil {
				return err
			}
			return dryrun.BuildErrorFromResult(rs.ToMap())
		}
	}
	if err := m.processor.Rollback(ctx, dryrunIngress); err != nil {
		m.sendAlarm(ctx, err)
		return task.NewErrFailed(err)
	}
	return nil
}

func (m *Merge) sendAlarm(ctx context.Context, err error) {
	if !m.AlarmOnFail {
		return
	}

	req := &alarm.Request{
		ObjType: "task",
		ObjName: "融合",
		ObjID:   task.FromContext(ctx).ID,
		Content: err.Error(),
	}
	_ = alarm.Send(req)
}
