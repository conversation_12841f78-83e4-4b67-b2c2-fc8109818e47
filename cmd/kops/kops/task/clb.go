package task

import (
	"context"
	"fmt"
	"sync/atomic"
	"time"

	"github.com/go-logr/logr"
	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"golang.org/x/sync/errgroup"

	"git.woa.com/kateway/pkg/telemetry/log"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model/stat"
	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
	"git.woa.com/kateway/kateway-server/pkg/tmp/web/cloudctx"
	region2 "git.woa.com/kateway/kateway-server/pkg/util/region"
)

type CLB struct {
	date string
	task *model.Task

	log     logr.Logger
	counter atomic.Uint64
}

func NewCLB(date string) *CLB {
	task := &model.Task{
		TaskID:    date,
		Type:      "CLB",
		State:     model.TaskStateRunning.String(),
		Cost:      0,
		Total:     0,
		Progress:  "0%",
		Creator:   "wallaceqian",
		CreatedAt: time.Now(),
	}

	return &CLB{
		date: date,
		task: task,
		log:  log.WithName("CLB"),
	}
}

func (l *CLB) Run() {
	l.log.Info("Running")
	defer l.log.Info("Done")

	ctx := context.Background()
	users, err := services.Get().User().ListUsers(ctx)
	if err != nil {
		l.log.Error(err, "Get().User().ListUsers error")
		return
	}
	// var err error
	// users := []services.User{
	// 	{
	// 		Uin: "2252646423",
	// 		Appid: "1251316161",
	// 	},
	// }
	l.task.Total = len(users)
	l.task.Progress = fmt.Sprintf("%d/%d %.2f%%", l.counter.Load(), l.task.Total, float64(l.counter.Load())/float64(l.task.Total)*100)
	_ = services.Get().LegacyTask().Update(l.task)

	wg := errgroup.Group{}
	wg.SetLimit(config.Get().Task.CLB.ConcurrentSyncs)

	for _, user := range users {
		wg.Go(func() (err error) {
			defer func() {
				if r := recover(); r != nil {
					err = fmt.Errorf("panic(%s): %v", user.Uin, r)
				}

				l.counter.Add(1)
				l.task.Progress = fmt.Sprintf("%d/%d %.2f%%", l.counter.Load(), l.task.Total, float64(l.counter.Load())/float64(l.task.Total)*100)
				_ = services.Get().LegacyTask().Update(l.task)
			}()

			return l.do(ctx, user)
		})
	}

	l.task.State = model.TaskStateDone.String()
	l.task.FinishedAt = lo.ToPtr(time.Now())
	l.task.Cost = l.task.FinishedAt.Sub(l.task.CreatedAt).Hours()
	err = wg.Wait()
	if err != nil {
		l.task.Error = err.Error()
	}
	_ = services.Get().LegacyTask().Update(l.task)
}

func (l *CLB) do(ctx context.Context, user services.User) error {
	wg := errgroup.Group{}
	for _, item := range config.Get().Regions {
		wg.Go(func() (err error) {
			defer func() {
				if r := recover(); r != nil {
					err = fmt.Errorf("panic(%s %s): %v", user.Uin, item, r)
				}
			}()
			return l.statCLBForRegion(ctx, user, item)
		})
	}

	return wg.Wait()
}

func (l *CLB) statCLBForRegion(ctx context.Context, user services.User, region string) error {
	l.log.Info("statCLBForRegion", "region", region, "appid", user.Appid)

	ctx = cloudctx.WithUin(ctx, user.Uin)
	ctx = cloudctx.WithRegion(ctx, region2.MustGet(region))
	lbs, err := services.Get().CLB().List(ctx)
	if err != nil {
		return fmt.Errorf("clb.List error: %w", err)
	}

	resultMap := make(map[stat.Clb]*stat.Clb)
	for _, lb := range lbs {
		listeners, err := services.Get().CLB().ListListeners(ctx, *lb.LoadBalancerId)
		if err != nil {
			l.log.Error(err, "clb.ListListeners error")
			continue
		}
		if len(listeners) == 0 {
			continue
		}
		listenersName := lo.SliceToMap(listeners, func(item *clb.Listener) (string, string) {
			return *item.ListenerId, *item.ListenerName
		})
		listenerBackends, err := services.Get().CLB().ListListenerBackends(ctx, *lb.LoadBalancerId)
		if err != nil {
			l.log.Error(err, "clb.ListTargets error")
			continue
		}
		clusterTag, ok := lo.Find(lb.Tags, func(item *clb.TagInfo) bool {
			return *item.TagKey == "tke-clusterId"
		})
		if !ok {
			continue
		}
		serviceTag, ok := lo.Find(lb.Tags, func(item *clb.TagInfo) bool {
			return *item.TagKey == "tke-name" || *item.TagKey == "tke-lb-serviceuuid"
		})
		service := ""
		if ok {
			service = *serviceTag.TagValue
		}
		for _, listenerBackend := range listenerBackends {
			// 忽略非tke创建的监听器
			if name, ok := listenersName[*listenerBackend.ListenerId]; ok {
				if name != "TKE-DEDICATED-LISTENER" {
					continue
				}
			}

			for _, target := range listenerBackend.Targets {
				item := stat.Clb{
					Date:       l.date,
					Uin:        user.Uin,
					Appid:      user.Appid,
					Region:     region,
					ClusterID:  *clusterTag.TagValue,
					Service:    service,
					LB:         *lb.LoadBalancerId,
					Listener:   *listenerBackend.ListenerId,
					TargetType: *target.Type,
					Count:      1,
				}
				if _, ok := resultMap[item]; ok {
					resultMap[item].Count++
				} else {
					resultMap[item] = &item
				}

			}
		}
	}

	result := lo.MapToSlice(resultMap, func(k stat.Clb, v *stat.Clb) *stat.Clb {
		return v
	})

	return services.Get().Save(result)
}
