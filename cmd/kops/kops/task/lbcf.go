package task

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	lbcfclient "git.code.oa.com/tkex-teg/lb-controlling-framework/pkg/client-go/clientset/versioned"
	"github.com/go-logr/logr"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"git.woa.com/kateway/pkg/telemetry/log"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model/lbcf"
	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
)

type LBCF struct {
	date string
	task *model.Task

	db         *gorm.DB
	log        logr.Logger
	counter    atomic.Uint64
	lbRuleTolb sync.Map
	lbBgCount  sync.Map
}

func NewLBCF(date string) *LBCF {
	task := &model.Task{
		TaskID:    date,
		Type:      "LBCF",
		State:     model.TaskStateRunning.String(),
		Cost:      0,
		Total:     0,
		Progress:  "0%",
		Creator:   "wallaceqian",
		CreatedAt: time.Now(),
	}

	return &LBCF{
		db:   services.Get().DB(),
		date: date,
		task: task,
		log:  log.WithName("LBCF"),
	}
}

func (l *LBCF) Run() {
	l.log.Info("Running")
	defer l.log.Info("Done")

	ctx := context.Background()
	clusters, err := services.Get().Cluster().ListLBCF(ctx)
	if err != nil {
		l.log.Error(err, "Cluster.List error")
		return
	}
	l.task.Total = len(clusters)
	l.task.Progress = fmt.Sprintf("%d/%d %.2f%%", l.counter.Load(), l.task.Total, float64(l.counter.Load())/float64(l.task.Total)*100)
	_ = services.Get().LegacyTask().Update(l.task)

	wg := errgroup.Group{}
	wg.SetLimit(config.Get().Task.LBCF.ConcurrentSyncs)

	for _, cluster := range clusters {
		wg.Go(func() (err error) {
			defer func() {
				if r := recover(); r != nil {
					err = fmt.Errorf("panic(%s): %v", cluster.ClusterID, r)
				}

				l.counter.Add(1)
				l.task.Progress = fmt.Sprintf("%d/%d %.2f%%", l.counter.Load(), l.task.Total, float64(l.counter.Load())/float64(l.task.Total)*100)
				_ = services.Get().LegacyTask().Update(l.task)
			}()

			return l.do(ctx, &cluster)
		})
	}

	l.task.State = model.TaskStateDone.String()
	l.task.FinishedAt = lo.ToPtr(time.Now())
	l.task.Cost = l.task.FinishedAt.Sub(l.task.CreatedAt).Hours()
	err = wg.Wait()
	if err != nil {
		l.task.Error = err.Error()
	}
	_ = services.Get().LegacyTask().Update(l.task)
}

func (l *LBCF) do(ctx context.Context, cluster *model.Cluster) error {
	l.log.Info("Syncing", "cluster", cluster.ClusterID)

	lbcfClient, err := services.Get().Cluster().LBCFClient(ctx, cluster)
	if err != nil {
		return fmt.Errorf("Cluster.LBCFClient(%s) error: %w", cluster.ClusterID, err)
	}

	err = l.statSystemLB(ctx, cluster, lbcfClient)
	if err != nil {
		l.log.Error(err, "statSystemLB error", "cluster", cluster.ClusterID)
	}

	err = l.statUserLB(ctx, cluster, lbcfClient)
	if err != nil {
		l.log.Error(err, "statUserLB error", "cluster", cluster.ClusterID)
	}

	err = l.statBackendGroup(ctx, cluster, lbcfClient)
	if err != nil {
		l.log.Error(err, "statBackendGroup error", "cluster", cluster.ClusterID)
	}

	return err
}

func (l *LBCF) statSystemLB(ctx context.Context, cluster *model.Cluster, lbcfClient *lbcfclient.Clientset) error {
	lbs, err := lbcfClient.LbcfV1beta1().LoadBalancers("kube-system").List(ctx, metav1.ListOptions{})
	if err != nil {
		return fmt.Errorf("LBCFClient.LbcfV1beta1().LoadBalancers(kube-system).List(%s) error: %w", cluster.ClusterID, err)
	}

	var result []*lbcf.SystemLB
	for _, lb := range lbs.Items {
		lb.ManagedFields = nil
		projectID := lb.Spec.Attributes["projectName"]
		state := ""
		driver := lb.Spec.LBDriver
		error := ""

		item := &lbcf.SystemLB{
			Date:             l.date,
			Appid:            cast.ToString(cluster.Appid),
			Region:           cluster.Region,
			ClusterID:        cluster.ClusterID,
			ClusterName:      cluster.Name,
			ClusterType:      cluster.Type,
			ProjectID:        projectID,
			Name:             lb.Name,
			Namespace:        lb.Namespace,
			Driver:           driver,
			LoadBalancerID:   lb.Status.LBInfo["loadBalancerID"],
			LoadBalancerName: lb.Status.LBInfo["loadBalancerName"],
			LoadBalancerType: lb.Status.LBInfo["loadBalancerType"],
			VIP:              lb.Status.LBInfo["vip"],
			State:            state,
			Error:            error,
			Raw:              string(lo.Must(json.Marshal(lb))),
		}
		result = append(result, item)
	}

	return services.Get().Save(result)
}

func (l *LBCF) statUserLB(ctx context.Context, cluster *model.Cluster, lbcfClient *lbcfclient.Clientset) error {
	lbs, err := lbcfClient.LbcfV1beta1().LoadBalancers("").List(ctx, metav1.ListOptions{})
	if err != nil {
		return fmt.Errorf("LBCFClient.LbcfV1beta1().LoadBalancers().List(%s) error: %w", cluster.ClusterID, err)
	}

	var result []*lbcf.UserLB
	for _, lb := range lbs.Items {
		if lb.Namespace == "kube-system" {
			continue
		}

		lb.ManagedFields = nil
		projectID := lb.Spec.Attributes["projectName"]
		state := ""
		driver := lb.Spec.LBDriver
		error := ""

		item := &lbcf.UserLB{
			Date:           l.date,
			Appid:          cast.ToString(cluster.Appid),
			Region:         cluster.Region,
			ClusterID:      cluster.ClusterID,
			ClusterName:    cluster.Name,
			ClusterType:    cluster.Type,
			ProjectID:      projectID,
			Name:           lb.Name,
			Namespace:      lb.Namespace,
			Driver:         driver,
			LoadBalancerID: lb.Status.LBInfo["loadBalancerID"],
			ListenerID:     lb.Status.LBInfo["listenerID"],
			State:          state,
			Error:          error,
			Raw:            string(lo.Must(json.Marshal(lb))),
		}
		l.lbRuleTolb.Store(fmt.Sprintf("%s/%s", item.ClusterID, item.Name), item.LoadBalancerID)
		result = append(result, item)
	}

	return services.Get().Save(result)
}

func (l *LBCF) statBackendGroup(ctx context.Context, cluster *model.Cluster, lbcfClient *lbcfclient.Clientset) error {
	bgs, err := lbcfClient.LbcfV1beta1().BackendGroups("").List(ctx, metav1.ListOptions{})
	if err != nil {
		return err
	}

	var result []*lbcf.BackendGroup
	lbBG := make(map[string]int)
	for _, bg := range bgs.Items {
		bg.ManagedFields = nil
		projectID := ""
		if len(bg.Namespace) > 12 {
			projectID = bg.Namespace[len(bg.Namespace)-12:]
		}
		state := lbcf.MigrateStateUnknown
		driver := bg.Labels["clb.type.backendgroup"]
		driver = strings.TrimPrefix(driver, "lbcf-")
		driver = strings.TrimSuffix(driver, "-driver")

		loadBalancerIDs := make([]string, 0, len(bg.Spec.LoadBalancers))
		for _, lb := range bg.Spec.LoadBalancers {
			id, ok := l.lbRuleTolb.Load(fmt.Sprintf("%s/%s", cluster.ClusterID, lb))
			if !ok {
				continue
			}
			lbID := id.(string)
			loadBalancerIDs = append(loadBalancerIDs, lbID)
			if _, ok := lbBG[id.(string)]; ok {
				lbBG[lbID]++
			} else {
				lbBG[lbID] = 1
			}
		}
		item := &lbcf.BackendGroup{
			Date:            l.date,
			Appid:           cast.ToString(cluster.Appid),
			Region:          cluster.Region,
			ClusterID:       cluster.ClusterID,
			ClusterName:     cluster.Name,
			ClusterType:     cluster.Type,
			ProjectID:       projectID,
			Name:            bg.Name,
			Namespace:       bg.Namespace,
			Driver:          driver,
			LoadBalancers:   bg.Spec.LoadBalancers,
			LoadBalancerIDs: loadBalancerIDs,
			State:           state,
			Raw:             bg,
		}
		result = append(result, item)
	}

	// 遍历所有后端服务组
	for _, bg := range result {
		state := lbcf.MigrateStateUnsupport
		error := ""
		if bg.Driver == "" {
			state = lbcf.MigrateStateSkip
			error = "driver为空"
		} else if bg.Driver != "tkestack-clb" {
			state = lbcf.MigrateStateUnsupport
			error = fmt.Sprintf("不支持的驱动: %v", bg.Driver)
		} else {
			if len(bg.LoadBalancers) == 0 {
				state = lbcf.MigrateStateSkip
				error = "负载均衡规则为空"
			} else if len(bg.LoadBalancers) > 1 {
				state = lbcf.MigrateStateUnsupport
				error = fmt.Sprintf("不支持绑定多个负载均衡规则: %v", len(bg.LoadBalancers))
			} else {
				if len(bg.LoadBalancerIDs) == 0 {
					state = lbcf.MigrateStateSkip
					error = "负载均衡ID为空"
				} else {
					lbID := bg.LoadBalancerIDs[0]
					if lbBG[lbID] > 1 {
						state = lbcf.MigrateStateUnsupport
						error = fmt.Sprintf("不支持一个负载均衡绑定多个后端服务组: %v", lbBG[lbID])
					}
					if len(bg.Raw.Spec.Pods.ByName) > 0 {
						state = lbcf.MigrateStateUnsupport
						error = fmt.Sprintf("不支持的pod指定方式ByName: %v", bg.Raw.Spec.Pods.ByName)
					} else {
						state = lbcf.MigrateStatePending
					}
				}
			}
		}

		if bg.Raw.Annotations["dryrun"] == "true" {
			state = lbcf.MigrateStateDone
		}

		bg.State = state
		bg.Error = error
	}

	return services.Get().Save(result)
}
