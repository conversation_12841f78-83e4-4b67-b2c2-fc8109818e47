package task

import (
	"context"
	"fmt"
	"sync"

	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"
	"k8s.io/apimachinery/pkg/util/wait"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/service/controller"
	"git.woa.com/kateway/kateway-server/pkg/cron"
	"git.woa.com/kateway/kateway-server/pkg/runtime"
	"git.woa.com/kateway/kateway-server/pkg/task"
	"git.woa.com/kateway/kateway-server/pkg/tmp/telemetry/log"
)

func init() {
	task.Register(&Test{})
	task.Register(&Launcher{})
	task.Register(&Merge{})
	task.Register(&Liveness{})
	task.Register(&NginxHPA{})

	// 升级相关
	task.Register(&PreCheck{})
	task.Register(&Upgrade{})
	task.Register(&PostCheck{})
}

func RunCronTasks(ctx context.Context) {
	startLivenessCheckForMerged(ctx)

	cron := cron.New()
	cron.EveryDay(func() {
		_, err := services.Get().Task().Create(ctx, Launcher{}, task.WithCreator("system"))
		if err != nil {
			log.Error(err, "failed to create cron tasks")
		}
	})
}

func startLivenessCheckForMerged(ctx context.Context) {
	go wait.UntilWithContext(ctx, func(ctx context.Context) {
		cfg := config.Get().Task.Liveness
		clusters := cfg.Clusters

		if len(clusters) == 0 {
			cs, err := services.Get().Cluster().List(ctx)
			if err != nil {
				log.Error(err, "Failed to list clusters")
				return
			}
			if len(cfg.AppIDs) == 0 {
				clusters = lo.Map(cs, func(c model.Cluster, _ int) string { return c.ClusterID })
			} else {
				clusters = lo.FilterMap(cs, func(c model.Cluster, _ int) (string, bool) {
					return c.ClusterID, lo.Contains(cfg.AppIDs, fmt.Sprint(c.Appid))
				})
			}
		}

		mergedControllerEnabled := func(clusterID string) (bool, error) {
			ins, err := controller.GetByClusterID(ctx, clusterID)
			if err != nil {
				return false, err
			}
			enabled, err := ins.Service().MergedIngressControllerEnabled(ctx)
			if err != nil {
				return false, err
			}
			return enabled, nil
		}

		var (
			wg      errgroup.Group
			m       sync.Mutex
			victims []string
		)

		wg.SetLimit(100)
		for _, id := range clusters {
			clusterID := id
			wg.Go(func() error {
				defer func() {
					if r := recover(); r != nil {
						log.Error(runtime.GetPanicError(r), "", "clusterID", clusterID)
					}
				}()

				enabled, err := mergedControllerEnabled(clusterID)
				if err != nil {
					log.Error(err, "Failed to detect merged status", "clusterID", clusterID)
					return nil
				}
				if enabled {
					m.Lock()
					defer m.Unlock()

					victims = append(victims, clusterID)
				}
				return nil
			})
		}
		_ = wg.Wait()

		if len(victims) == 0 {
			return
		}

		_, err := services.Get().Task().CreateBatchTask(ctx,
			lo.ToAnySlice(lo.Map(victims, func(id string, _ int) Liveness {
				return Liveness{ClusterID: id}
			})))
		if err != nil {
			log.Error(err, "Failed to create batch liveness task")
		}
	}, config.Get().Task.Liveness.Interval)
}
