package task

import (
	"context"
	"fmt"
	"strings"
	"time"

	ngxv1alpha1 "git.code.oa.com/misakazhou/tke-nginx-ingress-api/pkg/apis/nginxingress/v1alpha1"
	nginxingress "git.code.oa.com/misakazhou/tke-nginx-ingress-api/pkg/client/clientset/versioned"
	"github.com/samber/lo"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
	"git.woa.com/kateway/kateway-server/pkg/task"
)

func (NginxHPA) Config() task.Config {
	return task.Config{
		Timeout: task.TimeoutConfig{
			Duration: 2 * time.Minute,
			Strategy: task.TimeoutStrategyFail,
		},
		Concurrency: 100,
	}
}

type NginxHPAStatus struct {
	Reason   string `json:"reason"`
	Version  string `json:"version"`
	Affected bool   `json:"affected"`
}

type NginxHPA struct {
	ClusterID string

	k8sCli kubernetes.Interface
	ngxCli nginxingress.Interface
	status NginxHPAStatus
}

func (h *NginxHPA) Init(ctx context.Context) error {
	svc := services.Get().Cluster()
	cls, err := svc.Get(ctx, h.ClusterID)
	if err != nil {
		return task.NewErrFailed(err)
	}
	clientset, err := svc.Clientset(ctx, cls)
	if err != nil {
		return task.NewErrFailed(err)
	}
	h.k8sCli = clientset.K8sCli

	cfg, err := svc.RESTConfig(ctx, cls)
	if err != nil {
		return task.NewErrFailed(err)
	}
	ngxCli, err := nginxingress.NewForConfig(cfg)
	if err != nil {
		return task.NewErrFailed(err)
	}
	h.ngxCli = ngxCli
	return nil
}

func (h *NginxHPA) Steps() (steps task.Steps) {
	steps.AddStepFuncs(h.scan)
	return steps
}

func (h *NginxHPA) scan(ctx context.Context) error {
	const name = "tke-ingress-nginx-controller-operator"

	deploy, err := h.k8sCli.AppsV1().Deployments(metav1.NamespaceSystem).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		if apierrors.IsNotFound(err) {
			h.status.Reason = "ngx operator does not exists"
			return nil
		}
		return err
	}

	c, exists := lo.Find(deploy.Spec.Template.Spec.Containers, func(c corev1.Container) bool {
		return c.Name == name
	})
	if !exists {
		h.status.Reason = "ngx operator container does not exists"
		return nil
	}
	version := strings.Split(c.Image, ":")[1]
	h.status.Version = version
	if lo.Contains([]string{"v1.5.1", "v1.5.0", "v1.4.1"}, version) {
		h.status.Reason = "ngx operator version is safe"
		return nil
	}

	ngxList, err := h.ngxCli.CloudV1alpha1().NginxIngresses().List(ctx, metav1.ListOptions{})
	if err != nil {
		if strings.Contains(err.Error(), "the server could not find") {
			h.status.Reason = "ngx CRD is not installed"
			return nil
		}
		return err
	}

	if len(ngxList.Items) == 0 {
		h.status.Reason = "no ngx CR resource found"
		return nil
	}

	for _, ngx := range ngxList.Items {
		hpa := ngx.Spec.WorkLoad.HPA
		if hpa != nil && !hpa.Enable {
			continue
		}
		if !h.workloadExists(ctx, &ngx) {
			h.status.Reason = "ngx workload does not exists"
			continue
		}
		if !h.hpaExists(ctx, &ngx) {
			h.status.Reason = fmt.Sprintf("hpa of ngx %s is enabled, but the hpa resource does not exist", ngx.Name)
			h.status.Affected = true
			break
		}
	}
	return nil
}

func (h *NginxHPA) workloadExists(ctx context.Context, ngx *ngxv1alpha1.NginxIngress) bool {
	name := buildNgxComponentName(ngx.Name)
	ns := metav1.NamespaceSystem

	detect := func() bool {
		return h.detectWorkloadDeploy(ctx, ns, name) || h.detectWorkloadDaemonset(ctx, ns, name)
	}

	if detect() {
		return true
	}
	if ngx.Spec.WatchNamespace != nil {
		ns = *ngx.Spec.WatchNamespace
		return detect()
	}
	return false
}

func (h *NginxHPA) hpaExists(ctx context.Context, ngx *ngxv1alpha1.NginxIngress) bool {
	name := buildNgxComponentName(ngx.Name)
	ns := metav1.NamespaceSystem

	if h.detectHPA(ctx, ns, name) {
		return true
	}
	if ngx.Spec.WatchNamespace != nil {
		ns = *ngx.Spec.WatchNamespace
		return h.detectHPA(ctx, ns, name)
	}
	return false
}

func (h *NginxHPA) detectWorkloadDaemonset(ctx context.Context, ns, name string) bool {
	_, err := h.k8sCli.ExtensionsV1beta1().DaemonSets(ns).Get(ctx, name, metav1.GetOptions{})
	if err == nil {
		return true
	}

	_, err = h.k8sCli.AppsV1().DaemonSets(ns).Get(ctx, name, metav1.GetOptions{})
	return err == nil
}

func (h *NginxHPA) detectWorkloadDeploy(ctx context.Context, ns, name string) bool {
	_, err := h.k8sCli.ExtensionsV1beta1().Deployments(ns).Get(ctx, name, metav1.GetOptions{})
	if err == nil {
		return true
	}

	_, err = h.k8sCli.AppsV1().Deployments(ns).Get(ctx, name, metav1.GetOptions{})
	return err == nil
}

func (h *NginxHPA) detectHPA(ctx context.Context, ns, name string) bool {
	_, err := h.k8sCli.AutoscalingV2beta1().HorizontalPodAutoscalers(ns).Get(ctx, name, metav1.GetOptions{})
	if err == nil {
		return true
	}

	_, err = h.k8sCli.AutoscalingV2().HorizontalPodAutoscalers(ns).Get(ctx, name, metav1.GetOptions{})
	return err == nil
}

func (h *NginxHPA) Status(_ context.Context) any {
	return h.status
}

func buildNgxComponentName(name string) string {
	return fmt.Sprintf("%s-%s-%s", name, "ingress-nginx", "controller")
}
