package task

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"strings"
	"time"

	"github.com/samber/lo"
	"github.com/tidwall/gjson"
	corev1 "k8s.io/api/core/v1"

	"git.woa.com/kateway/pkg/telemetry/log"

	"git.woa.com/kateway/kateway-server/cmd/kops/kops/service/controller"
	"git.woa.com/kateway/kateway-server/pkg/alarm"
	"git.woa.com/kateway/kateway-server/pkg/task"
)

const (
	failedReasonUnknown          = "未知错误"
	failedReasonOOM              = "OOM"
	failedReasonPanic            = "Panic退出"
	failedReasonUpdateLockFailed = "更新Leader锁失败"

	maxOutputLogsTotal = 4
	leaderLostTimeout  = 5 * time.Minute
)

type failedStatus struct {
	reason     string
	extensions map[string]string
}

func (s failedStatus) GetFormattedExtensions() string {
	return strings.Join(lo.MapToSlice(s.extensions, func(k, v string) string {
		return fmt.Sprintf("%s: %s", k, v)
	}), "\n")
}

type Liveness struct {
	ClusterID string

	ins *controller.Instance
}

func (l *Liveness) Init(ctx context.Context) error {
	ins, err := controller.GetByClusterID(ctx, l.ClusterID)
	if err != nil {
		return err
	}
	l.ins = ins
	return nil
}

func (l *Liveness) Steps() (steps task.Steps) {
	steps.AddStep(
		l.buildCheckControllerStep(l.ins.Service()),
	)
	steps.AddStep(
		l.buildCheckControllerStep(l.ins.Ingress()),
	)
	return
}

func (l *Liveness) buildCheckControllerStep(ctrl *controller.Controller) task.Step {
	cls := ctrl.GetCluster()
	fn := func(ctx context.Context) error {
		if err := l.checkController(ctx, ctrl); err != nil {
			objName := ctrl.Name()
			content := fmt.Sprintf(`检查%s失败：
		集群：%v
		名称：%v
		类型：%v
		地域：%v
		用户：%v
		错误：%v`, objName, cls.ClusterID, cls.Name, cls.Type, cls.Region, cls.Appid, err)

			req := &alarm.Request{
				ObjType: "kateway",
				ObjName: objName,
				ObjID:   cls.ClusterID,
				Content: content,
			}
			_ = alarm.Send(req)
		}
		return nil
	}
	return task.Step{
		Name: fmt.Sprintf("liveness_check_for_%s", ctrl.Kind()),
		Executor: task.StepExecutor{
			Fn: fn,
		},
	}
}

func (l *Liveness) checkController(ctx context.Context, ctrl *controller.Controller) error {
	ctx = log.WithContext(ctx, log.FromContext(ctx).WithValues("type", ctrl.Kind()))

	deployment, err := ctrl.GetDeployment(ctx, controller.GetDeploymentOptions{})
	if err != nil {
		return nil
	}
	if *deployment.Spec.Replicas == 0 {
		log.FromContext(ctx).Info("Controller is not running", "name", ctrl.Name())
		return nil
	}

	s, err := ctrl.GetLeaderStatus(ctx)
	if err != nil {
		return err
	}

	if s.Exists {
		statusBytes, err := ctrl.GetAdminStatusRaw(ctx)
		if err != nil {
			if !errors.Is(err, controller.ErrFeatureNotImplemented) {
				return fmt.Errorf("failed to get admin status: %w", err)
			}
		} else {
			status := gjson.ParseBytes(statusBytes)
			if len(status.Array()) > 0 {
				return fmt.Errorf("%s状态异常: %s", ctrl.Name(), status.Raw)
			}
		}
		return nil
	}

	if s.ExpiredDuration < leaderLostTimeout {
		return nil
	}
	fs, err := l.analyzeFailedController(ctx, ctrl)
	if err != nil {
		return err
	}

	return fmt.Errorf(`当前不存在有效Leader
已超时: %v
上一次续期时间: %s
过期时间: %s
失败原因: %s
%s`, s.ExpiredDuration, s.RenewedAt.Format(time.DateTime),
		s.ExpiredAt.Format(time.DateTime), fs.reason, fs.GetFormattedExtensions())
}

func (l *Liveness) analyzeFailedController(ctx context.Context, ctrl *controller.Controller) (*failedStatus, error) {
	d, err := ctrl.GetDeployment(ctx, controller.GetDeploymentOptions{})
	if err != nil {
		return nil, err
	}

	log.FromContext(ctx).Info("Analyze failed controller", "name", d.Name)
	containers := d.Spec.Template.Spec.Containers
	if len(containers) == 0 {
		return nil, fmt.Errorf("empty container list")
	}
	fs := &failedStatus{
		reason: failedReasonUnknown,
		extensions: map[string]string{
			"版本": strings.Split(containers[0].Image, ":")[1],
		},
	}

	pods, err := ctrl.ListPod(ctx, controller.ListPodsOptions{})
	if err != nil {
		return nil, err
	}
	restartedPods := lo.Filter(pods, func(p corev1.Pod, _ int) bool {
		css := p.Status.ContainerStatuses
		if len(css) == 0 {
			return false
		}
		cs := css[0]
		return cs.RestartCount > 0 && cs.LastTerminationState.Terminated != nil
	})
	if len(restartedPods) == 0 {
		return fs, nil
	}
	slices.SortFunc(restartedPods, func(a, b corev1.Pod) int {
		sa := a.Status.ContainerStatuses[0].LastTerminationState.Terminated
		sb := b.Status.ContainerStatuses[0].LastTerminationState.Terminated
		if sa.FinishedAt.After(sb.FinishedAt.Time) {
			return -1
		} else if sa.FinishedAt.Before(&sb.FinishedAt) {
			return 1
		}
		return 0
	})
	p := &restartedPods[0]
	state := p.Status.ContainerStatuses[0].LastTerminationState.Terminated
	if state != nil && state.Reason == "OOMKilled" {
		fs.reason = failedReasonOOM
		fs.extensions["资源配额(limits)"] = string(lo.Must(json.Marshal(p.Spec.Containers[0].Resources.Limits)))
		return fs, nil
	}
	lgs, err := ctrl.GetLogs(ctx, controller.PodLogOptions{
		PodLogOptions: corev1.PodLogOptions{
			Previous: true,
		},
		PodName: p.Name,
	})
	if err != nil {
		return nil, err
	}
	logs := lgs[p.Name]

	total := len(logs)
	if total == 0 {
		return fs, nil
	}
	_, index, exists := lo.FindLastIndexOf(logs, func(l string) bool {
		return strings.HasPrefix(l, "panic:")
	})
	finishedWithNormalLog := func() bool {
		last := logs[total-1]
		return strings.HasPrefix(last, "E") || strings.HasPrefix(last, "I")
	}
	if exists && !finishedWithNormalLog() {
		fs.reason = failedReasonPanic
		fs.extensions["调用栈"] = "\n" + parsePanicStack(logs[index:])
		return fs, nil
	}

	errorLogs := lo.Reverse(lo.Filter(logs, func(l string, _ int) bool { return strings.HasPrefix(l, "E") }))
	if _, exists = lo.Find(errorLogs, func(l string) bool {
		return strings.Contains(l, "failed to renew lease") || strings.Contains(l, "Failed to update lock")
	}); exists {
		fs.reason = failedReasonUpdateLockFailed
	}
	if len(errorLogs) > maxOutputLogsTotal {
		errorLogs = errorLogs[:maxOutputLogsTotal]
	}
	errorLogs = lo.Reverse(errorLogs)
	fs.extensions["错误日志"] = "\n" + strings.Join(errorLogs, "\n")
	return fs, nil
}

func parsePanicStack(logs []string) string {
	related := []string{logs[0]}
	for i := 1; i < len(logs); i++ {
		l := logs[i]
		if strings.HasPrefix(l, "cloud.tencent.com") || strings.HasPrefix(l, "git.woa.com") {
			related = append(related, logs[i+1])
		}
		i++
	}
	if len(related) > maxOutputLogsTotal {
		related = related[:maxOutputLogsTotal]
	}
	return strings.Join(related, "\n")
}
