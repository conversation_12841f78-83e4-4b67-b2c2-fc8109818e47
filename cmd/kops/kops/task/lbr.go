package task

import (
	"context"
	"fmt"
	"sync/atomic"
	"time"

	"github.com/go-logr/logr"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"golang.org/x/sync/errgroup"

	"git.woa.com/kateway/pkg/telemetry/log"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model/stat"
	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
)

type LBR struct {
	date string
	task *model.Task

	log     logr.Logger
	counter atomic.Uint64
}

func NewLBR(date string) *LBR {
	task := &model.Task{
		TaskID:    date,
		Type:      "LBR",
		State:     model.TaskStateRunning.String(),
		Cost:      0,
		Total:     0,
		Progress:  "0%",
		Creator:   "wallaceqian",
		CreatedAt: time.Now(),
	}

	return &LBR{
		date: date,
		task: task,
		log:  log.WithName("LBR"),
	}
}

func (l *LBR) Run() {
	l.log.Info("Running")
	defer l.log.Info("Done")

	ctx := context.Background()
	clusters, err := services.Get().Cluster().List(ctx)
	if err != nil {
		l.log.Error(err, "Cluster.List error")
		return
	}
	l.task.Total = len(clusters)
	l.task.Progress = fmt.Sprintf("%d/%d %.2f%%", l.counter.Load(), l.task.Total, float64(l.counter.Load())/float64(l.task.Total)*100)
	_ = services.Get().LegacyTask().Update(l.task)

	wg := errgroup.Group{}
	wg.SetLimit(config.Get().Task.LBR.ConcurrentSyncs)

	for _, cluster := range clusters {
		wg.Go(func() (err error) {
			defer func() {
				if r := recover(); r != nil {
					err = fmt.Errorf("panic(%s): %v", cluster.ClusterID, r)
				}

				l.counter.Add(1)
				l.task.Progress = fmt.Sprintf("%d/%d %.2f%%", l.counter.Load(), l.task.Total, float64(l.counter.Load())/float64(l.task.Total)*100)
				_ = services.Get().LegacyTask().Update(l.task)
			}()

			return l.do(ctx, &cluster)
		})
	}

	l.task.State = model.TaskStateDone.String()
	l.task.FinishedAt = lo.ToPtr(time.Now())
	l.task.Cost = l.task.FinishedAt.Sub(l.task.CreatedAt).Hours()
	err = wg.Wait()
	if err != nil {
		l.task.Error = err.Error()
	}
	_ = services.Get().LegacyTask().Update(l.task)
}

func (l *LBR) do(ctx context.Context, cluster *model.Cluster) error {
	lbrs, err := services.Get().Cluster().ListLBR(ctx, cluster)
	if err != nil {
		return fmt.Errorf("Cluster.ListLBR(%s) error: %w", cluster.ClusterID, err)
	}

	var result []*stat.LBR
	for _, lbr := range lbrs {
		item := &stat.LBR{
			Date:         l.date,
			Appid:        cast.ToString(cluster.Appid),
			Region:       cluster.Region,
			ClusterID:    cluster.ClusterID,
			ClusterType:  cluster.Type,
			LB:           lbr.Name,
			ServiceCount: len(lbr.Status.ServiceResource),
			IngressCount: len(lbr.Status.IngressResource),
		}
		result = append(result, item)
	}

	return services.Get().Save(result)
}
