package task

import (
	"context"
	"errors"
	"fmt"
	"runtime"
	"strings"
	"time"

	"github.com/go-logr/logr"
	"github.com/hashicorp/go-multierror"
	"github.com/pborman/uuid"
	"github.com/samber/lo"

	"git.woa.com/kateway/pkg/telemetry/log"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	common2 "git.woa.com/kateway/kateway-server/cmd/kops/kops/common"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
	region "git.woa.com/kateway/kateway-server/pkg/util/region"
)

type InspectionTask interface {
	Start(ctx context.Context) (err error)

	AddTask(ctx context.Context, cluster *model.Cluster, interval time.Duration, maxSyncTimes int, ignoreRiskLevel string) *model.TaskResult
	UpdateTask(ctx context.Context, taskID string, interval time.Duration, maxSyncTimes int, ignoreRiskLevel string) *model.TaskResult
	StopTask(ctx context.Context, taskID, clusterID string) *model.TaskResult
}

type Inspection struct {
	log logr.Logger
}

func NewInspection() InspectionTask {
	return &Inspection{
		log: log.WithName("Inspection"),
	}
}

func (h Inspection) Validate(ctx context.Context, cluster *model.Cluster, interval time.Duration, maxSyncTimes int, ignoreRiskLevel string) error {
	// 限制总运行的任务数量
	report, err := services.Get().LegacyTask().ListTaskByState(ctx, model.TaskStateRunning.String())
	if err != nil {
		deniedInspectionTask(ctx, cluster, "NULL", interval.String(), 0, 0, err)
		return err
	}
	if len(report)+1 > config.Get().Inspection.MaxConcurrentRunningTaskCount {
		err := fmt.Errorf("正在运行的任务总数超过最大限制: %d", config.Get().Inspection.MaxConcurrentRunningTaskCount)
		deniedInspectionTask(ctx, cluster, "NULL", interval.String(), 0, 0, err)
		return err
	}
	// 限制任务同步周期
	if interval < config.Get().Inspection.MinTaskSyncInterval {
		err := fmt.Errorf("任务同步周期小于最短同步周期: %s", config.Get().Inspection.MinTaskSyncInterval.String())
		deniedInspectionTask(ctx, cluster, "NULL", interval.String(), 0, 0, err)
		return err
	}
	// 限制最大同步次数
	if maxSyncTimes < 0 {
		err := fmt.Errorf("任务最大同步次数小于0")
		deniedInspectionTask(ctx, cluster, "NULL", interval.String(), 0, 0, err)
		return err
	}
	if !model.ValidateRiskLevel(ignoreRiskLevel) {
		err := fmt.Errorf("不支持屏蔽风险等级：%s", ignoreRiskLevel)
		deniedInspectionTask(ctx, cluster, "NULL", interval.String(), 0, 0, err)
		return err
	}

	return nil
}

// 拉起未完成的巡检任务
func (h Inspection) Start(ctx context.Context) (err error) {
	h.log.Info("Running")
	defer h.log.Info("Done")

	tasks, err := services.Get().LegacyTask().ListTaskByState(ctx, model.TaskStateRunning.String())
	if err != nil {
		log.FromContext(ctx).Error(err, "获取Task记录失败")
		return err
	}
	for _, v := range tasks {
		if v.InstanceType != "Cluster" {
			continue
		}
		cluster, err := services.Get().Cluster().Get(ctx, v.InstanceID)
		if err == nil {
			// load model cluster from clusterid
			restartInspectionTask(ctx, cluster, v.TaskID, v.SyncInterval, v.CurSyncTimes, v.MaxSyncTimes)
		}

		log.FromContext(ctx).Info("重启巡检任务", "集群ID", v.InstanceID, "当前同步次数", v.CurSyncTimes, "最大同步次数", v.MaxSyncTimes, "同步间隔", v.SyncInterval)
		h.checkClusterUntil(ctx, v)
		time.Sleep(30 * time.Second)
	}
	return nil
}

func (h Inspection) AddTask(ctx context.Context, cluster *model.Cluster, interval time.Duration, maxSyncTimes int, ignoreRiskLevel string) *model.TaskResult {
	if err := h.Validate(ctx, cluster, interval, maxSyncTimes, ignoreRiskLevel); err != nil {
		return &model.TaskResult{
			Status: CreateOrUpdateTaskFailed,
			Reason: err,
		}
	}
	log.FromContext(ctx).Info("started cluster task until", "clusterid", cluster.ClusterID, "interval", interval.String(), "times", maxSyncTimes)
	task := h.createInspectionTask(ctx, cluster, interval, maxSyncTimes, ignoreRiskLevel)
	if task != nil && task.TaskID != nil {
		if task.Reason != nil && errors.Is(task.Reason, ErrTaskOccupied) {
			// 冲突检测，已有任务占据该实例
			return task
		}
		h.runClusterTaskFromTaskID(ctx, *task.TaskID)
	}
	return task
}

func (h Inspection) UpdateTask(ctx context.Context, taskID string, interval time.Duration, maxSyncTimes int, ignoreRiskLevel string) *model.TaskResult {
	result := &model.TaskResult{
		TaskID:          lo.ToPtr(taskID),
		Status:          CreateOrUpdateTaskSuccess,
		SyncInterval:    interval.String(),
		MaxSyncTimes:    maxSyncTimes,
		IgnoreRiskLevel: ignoreRiskLevel,
	}

	// 获取巡检任务
	task, err := services.Get().LegacyTask().GetTaskByTaskID(ctx, taskID)
	if err != nil {
		result.Status = CreateOrUpdateTaskFailed
		result.Reason = err
		return result
	}

	// 获取集群信息
	cluster, err := services.Get().Cluster().Get(ctx, task.InstanceID)
	if err != nil {
		result.Status = CreateOrUpdateTaskFailed
		result.Reason = err
		return result
	}
	result.Target = cluster.ClusterID

	// 校验巡检任务
	if err := h.Validate(ctx, cluster, interval, maxSyncTimes, ignoreRiskLevel); err != nil {
		result.Status = CreateOrUpdateTaskFailed
		result.Reason = err
		return result
	}

	// 更新任务参数
	task.MaxSyncTimes = maxSyncTimes
	task.SyncInterval = interval.String()
	task.IgnoreRiskLevel = ignoreRiskLevel

	// 更新巡检任务
	if err := services.Get().LegacyTask().UpdateInspectionTask(ctx, task); err != nil {
		result.Status = CreateOrUpdateTaskFailed
		result.Reason = err
		return result
	}

	// 更新巡检任务状态
	updateInspectionTask(ctx, cluster, taskID, interval.String(), task.CurSyncTimes, task.MaxSyncTimes)

	return result
}

func (h Inspection) StopTask(ctx context.Context, taskID, clusterID string) *model.TaskResult {
	result := &model.TaskResult{
		TaskID: lo.ToPtr(taskID),
		Status: CreateOrUpdateTaskSuccess,
	}
	var task *model.InspectionTask
	var err error

	if taskID != "" {
		// 获取巡检任务
		task, err = services.Get().LegacyTask().GetTaskByTaskID(ctx, taskID)
		if err != nil {
			result.Status = CreateOrUpdateTaskFailed
			result.Reason = err
			return result
		}
	} else if clusterID != "" {
		// 获取巡检任务
		task, err = services.Get().LegacyTask().GetTaskByClusterID(ctx, clusterID)
		if err != nil {
			result.Status = CreateOrUpdateTaskFailed
			result.Reason = err
			return result
		}
	}

	// 获取集群信息
	cluster, err := services.Get().Cluster().Get(ctx, task.InstanceID)
	if err != nil {
		result.Status = CreateOrUpdateTaskFailed
		result.Reason = err
		return result
	}

	// 更新任务参数
	task.MaxSyncTimes = task.CurSyncTimes

	// 更新巡检任务
	if err := services.Get().LegacyTask().UpdateInspectionTask(ctx, task); err != nil {
		result.Status = CreateOrUpdateTaskFailed
		result.Reason = err
		return result
	}

	// 更新巡检任务状态
	stopInspectionTask(ctx, cluster, taskID, task.CurSyncTimes, task.MaxSyncTimes)

	return result
}

var (
	ErrTaskOccupied = errors.New("实例已被其他巡检任务占用")
)

// 创建巡检任务，持久化到数据库
func (h Inspection) createInspectionTask(ctx context.Context, cluster *model.Cluster, interval time.Duration, times int, ignoreRiskLevel string) (asyncTask *model.TaskResult) {
	// [qingyangwu] 在数据库InspectionTask中创建一条Task任务
	task := &model.TaskResult{
		Type:         "Cluster",
		Target:       cluster.ClusterID,
		MaxSyncTimes: times,
		SyncInterval: interval.String(),
		Status:       CreateOrUpdateTaskSuccess,
	}

	InspectionTasks, err := services.Get().LegacyTask().ListTaskByClusterState(ctx, cluster.ClusterID, model.TaskStateRunning.String())
	if err != nil {
		log.FromContext(ctx).Error(err, "获取实例Task记录失败")
		task.Reason = err
		task.Status = CreateOrUpdateTaskFailed
		return task
	}
	log.FromContext(ctx).Info("获取实例Task记录成功", "clusterid", cluster.ClusterID, "tasks", len(InspectionTasks))
	// 检查当前集群是否已经有后检任务在运行
	var latestTask *model.InspectionTask
	for _, v := range InspectionTasks {
		if v == nil {
			continue
		}
		if v.InstanceType == "Cluster" && v.State == model.TaskStateRunning.String() {
			log.FromContext(ctx).Info("已经有任务占据该实例", "clusterid", cluster.ClusterID, "taskid", v.TaskID)
			task.TaskID = lo.ToPtr(v.TaskID)        // 返回正在运行的任务ID
			task.Status = CreateOrUpdateTaskSuccess // 已经有任务占据该实例
			task.Reason = fmt.Errorf("%w, TaskID: %s", ErrTaskOccupied, v.TaskID)
			return task
		}
		if v.FinishedAt == nil || v.CurSyncTimes == 0 {
			// 没有结束时间戳或者当前同步次数为0（任务还未完成一次同步），则跳过
			continue
		}
		if latestTask == nil || latestTask.FinishedAt.Before(*v.FinishedAt) {
			// 查找最后完成的任务
			latestTask = v
		}
	}
	// 最近一次任务的完成时间距离当前时间超过一定阈值，可以认为检查数据过期，需要删除RiskCLB表中Instance对应残留数据
	if latestTask != nil && time.Since(*latestTask.FinishedAt) > RiskTimeoutInterval {
		// 清空RiskCLB表中Instance对应残留数据
		if err := services.Get().LegacyTask().DeleteModelsByClusterID(ctx, cluster.ClusterID); err != nil {
			log.FromContext(ctx).Error(err, "清空RiskCLB表中Instance对应的过期残留数据失败")
			return nil
		}
	}
	// 生成Task任务的UUID
	task.TaskID = lo.ToPtr(uuid.New())
	// 初始化Task任务
	newTask := &model.InspectionTask{
		TaskID:          *task.TaskID,
		InstanceID:      cluster.ClusterID,
		InstanceType:    "Cluster",
		State:           model.TaskStateRunning.String(),
		CurSyncTimes:    0,
		MaxSyncTimes:    times,
		SyncInterval:    interval.String(),
		IgnoreRiskLevel: ignoreRiskLevel,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}
	// 调用接口创建
	if err := services.Get().LegacyTask().CreateOrUpdateInspectionTask(ctx, newTask); err != nil {
		log.FromContext(ctx).Error(err, "创建Task记录失败")
		task.Reason = err
		task.Status = CreateOrUpdateTaskFailed
		return task
	}
	startInspectionTask(ctx, cluster, *task.TaskID, interval.String(), 0, times)
	return task
}

// 根据TaskID拉起巡检任务
func (h Inspection) runClusterTaskFromTaskID(ctx context.Context, taskID string) {
	newTask, err := services.Get().LegacyTask().GetTaskByTaskID(ctx, taskID)
	if err != nil {
		log.FromContext(ctx).Error(err, "获取Task记录失败")
		return
	}
	h.checkClusterUntil(ctx, newTask)
}

func (h Inspection) getCluster(ctx context.Context, task *model.InspectionTask) (*model.Cluster, error) {
	if task.InstanceType != "Cluster" {
		err := errors.New("实例类型不是Cluster")
		return nil, err
	}
	// 获取集群信息
	cluster, err := services.Get().Cluster().Get(ctx, task.InstanceID)
	if err != nil {
		return nil, err
	}
	return cluster, nil
}

func (h Inspection) checkClusterUntil(ctx context.Context, task *model.InspectionTask) {
	cluster, err := h.getCluster(ctx, task)
	if err != nil {
		log.FromContext(ctx).Error(err, "获取集群信息失败")
		return
	}
	go func() {
		var res error
		defer finalizeTask(ctx, cluster, task, res)
		// 循环执行巡检
		for {
			if status, err := h.checkClusterPre(ctx, cluster, task); err != nil {
				res = multierror.Append(res, err)
				break
			} else if status == TaskCompleted {
				break
			}

			log.FromContext(ctx).Info("巡检任务运行中", "clusterid", cluster.ClusterID, "任务ID", task.TaskID, "检查间隔", task.SyncInterval, "当前轮次", task.CurSyncTimes+1, "总轮次", task.MaxSyncTimes)

			// 对checkCluster进行超时控制
			if err := h.runCheckClusterWithTimeout(ctx, cluster, task); err != nil {
				res = multierror.Append(res, err)
				break
			}

			if status, err := h.checkClusterPost(ctx, cluster, task); err != nil {
				res = multierror.Append(res, err)
				break
			} else if status == TaskCompleted {
				break
			}
		}
	}()
}

func (h Inspection) checkClusterPre(ctx context.Context, cluster *model.Cluster, task *model.InspectionTask) (TaskStatus, error) {
	task, err := services.Get().LegacyTask().GetTaskByTaskID(ctx, task.TaskID)
	if err != nil {
		log.FromContext(ctx).Error(err, "获取Task记录失败")
		return TaskError, err
	}

	if task.CurSyncTimes >= task.MaxSyncTimes {
		log.FromContext(ctx).Info("任务运行完成，退出任务", "clusterid", cluster.ClusterID, "interval", task.SyncInterval, "total-times", task.MaxSyncTimes)
		return TaskCompleted, nil
	}
	return TaskRunning, nil
}

func (h Inspection) checkClusterPost(ctx context.Context, cluster *model.Cluster, task *model.InspectionTask) (TaskStatus, error) {
	task.CurSyncTimes++
	task.UpdatedAt = time.Now()
	if err := services.Get().LegacyTask().CreateOrUpdateInspectionTask(ctx, task); err != nil {
		log.FromContext(ctx).Error(err, "更新Task记录失败")
		return TaskError, err
	}

	if task.CurSyncTimes >= task.MaxSyncTimes {
		log.FromContext(ctx).Info("任务运行完成，退出任务", "clusterid", cluster.ClusterID, "interval", task.SyncInterval, "total-times", task.MaxSyncTimes)
		return TaskCompleted, nil
	}

	interval, err := time.ParseDuration(task.SyncInterval)
	if err != nil {
		log.FromContext(ctx).Error(err, "解析SyncInterval失败")
		return TaskError, err
	}
	time.Sleep(interval)
	return TaskRunning, nil
}

func (h Inspection) runCheckClusterWithTimeout(ctx context.Context, cluster *model.Cluster, task *model.InspectionTask) error {
	timeoutCtx, cancel := context.WithTimeout(ctx, 10*time.Minute)
	defer cancel() // 确保在每次循环结束时取消上下文

	if _, err := h.checkCluster(timeoutCtx, cluster, task); err != nil {
		log.FromContext(ctx).Error(err, "处理巡检数据失败")
		return err
	}
	return nil
}

func GetPanicError(r interface{}) error {
	const size = 64 << 10
	stacktrace := make([]byte, size)
	stacktrace = stacktrace[:runtime.Stack(stacktrace, false)]
	if _, ok := r.(string); !ok {
		return fmt.Errorf("observed a panic: %#v (%v)\n%s", r, r, stacktrace)
	}
	return fmt.Errorf("observed a panic: %s\n%s", r, stacktrace)
}

func finalizeTask(ctx context.Context, cluster *model.Cluster, task *model.InspectionTask, res error) {
	// 处理panic
	if r := recover(); r != nil {
		// 获取堆栈信息
		panicReason := fmt.Errorf("%v", r)
		// 记录错误和堆栈信息
		log.FromContext(ctx).Error(panicReason, "捕获到 panic，任务终止", "stack_trace", GetPanicError(r))
		res = multierror.Append(res, panicReason)
	}
	task.State = model.TaskStateDone.String()
	if res != nil {
		task.Reason = lo.ToPtr(res.Error())
		task.State = model.TaskStateTerminated.String()
		terminatedInspectionTask(ctx, cluster, task.TaskID, task.SyncInterval, task.CurSyncTimes, task.MaxSyncTimes, res)
	} else {
		finishedInspectionTask(ctx, cluster, task.TaskID, task.SyncInterval, task.CurSyncTimes, task.MaxSyncTimes)
	}
	// 设置任务完成时间和最后一次更新时间
	task.UpdatedAt = time.Now()
	task.FinishedAt = lo.ToPtr(time.Now())
	// 更新Task记录表
	if err := services.Get().LegacyTask().CreateOrUpdateInspectionTask(ctx, task); err != nil {
		log.FromContext(ctx).Error(err, "更新Task记录失败")
	}
	log.FromContext(ctx).Info("finished cluster task", "clusterid", task.InstanceID, "interval", task.SyncInterval, "total-times", task.MaxSyncTimes)
}

func (h *Inspection) checkCluster(ctx context.Context, cluster *model.Cluster, curTask *model.InspectionTask) (clusterResult *ClusterHealthResult, result error) {
	clusterResult = &ClusterHealthResult{
		ClusterName:   cluster.Name,
		ClusterRegion: region.MustGet(cluster.Region).Name,
		AppID:         cluster.Appid,
		ClusterID:     cluster.ClusterID,
		ClusterType:   cluster.Type,
	}
	clusterResult.CLBList = make(RiskCLBList, 0)
	currentTime := time.Now()
	healthCheckRecord, err := services.Get().Inspection().GetClusterRiskReport(ctx, cluster, true /*强制 refresh cache */)
	if err != nil {
		return &ClusterHealthResult{Options: RiskOptions{Error: err.Error()}}, err
	}
	log.FromContext(ctx).Info("获取 inspection report 耗时", "duration", time.Since(currentTime).String(), "clusterid", cluster.ClusterID)

	now := time.Now()

	analysis := &healthCheckRecord.Analysises
	if len(analysis.Details) == 0 {
		log.FromContext(ctx).Info("没有拉取到异常指标")
	}
	// 查询Risk Service
	oldRiskRecords, err := services.Get().LegacyTask().ListRiskCLBByCluster(ctx, cluster.ClusterID)
	if err != nil {
		log.FromContext(ctx).Error(err, "获取Risk Service失败")
		return &ClusterHealthResult{Options: RiskOptions{Error: err.Error()}}, err
	}
	// 数据处理
	oldRiskMap := createRiskMap(oldRiskRecords)
	curRiskMap := createCurRiskMap(analysis.Details)
	updatedRisks := make([]*model.CLBRiskRecord, 0)
	// 告警内容需要包含哪些service
	oldKeySlice := common2.ExtractStringKeys(oldRiskMap)
	curKeySlice := common2.ExtractStringKeys(curRiskMap)
	toAdd, toDel, toUpdate := diffSlice(oldKeySlice, curKeySlice)
	recover := []string{}
	increment := filterIncrement(oldRiskRecords)

	clbResults := make([]CLBHealthResult, 0)
	i := 1
	needRise := false
	// 处理新增Risk CLB
	for _, v := range toAdd {
		// [qingyangwu] 通过Task考虑存量还是增量，再决定是否告警
		data, exist := curRiskMap[v]
		if !exist {
			continue
		}
		if data.RiskScore == 0 {
			if data.RiskLevel != string(model.RealServerLevelRisk) {
				recover = append(recover, v)
			}
			// [qingyangwu] risk score 为 0， 说明恢复健康
			toDel = append(toDel, v)
			continue
		}
		record := createCLBRiskRecord(cluster.ClusterID, cluster.Appid, nil, &data, now)
		// [qingyangwu] 新出现的异常CLB，通过Task考虑存量还是增量，再决定是否告警（否则，第一次启动kateway-server时可能会把存量风险告警出来）
		if curTask != nil && curTask.CurSyncTimes >= 1 {
			// 生成CLB粒度的告警内容
			if res := generateCLBResult(ctx, i, nil, &data); res != nil {
				if data.RiskLevel == string(model.RealServerLevelRisk) && record.RiseCount == 0 {
					// skip
				} else {
					clbResults = append(clbResults, *res)
				}
			}
			// 考虑是否上升告警
			// RS维度风险保持原状，高于RS维度的风险需要上升告警等级
			if data.RiskLevel != string(model.RealServerLevelRisk) {
				needRise = true
			}
			i++
			// 风险为增量风险
			record.State = string(model.StateIncrementRisk)
			record.RiseCount++
		}
		updatedRisks = append(updatedRisks, record)
	}
	// 处理更新的Risk CLB
	for _, v := range toUpdate {
		curData := curRiskMap[v]
		oldData := oldRiskMap[v]
		record := createCLBRiskRecord(cluster.ClusterID, cluster.Appid, &oldData, &curData, now)

		// 风险保持不变或者降低，不告警
		if curData.RiskScore <= oldData.RiskScore {
			updatedRisks = append(updatedRisks, record)
			continue
		}

		// 生成CLB粒度的告警内容
		if res := generateCLBResult(ctx, i, &oldData, &curData); res != nil {
			if curData.RiskLevel == string(model.RealServerLevelRisk) && record.RiseCount == 0 {
				// skip
			} else {
				clbResults = append(clbResults, *res)
			}
		}

		// 检查风险级别
		if curData.RiskLevel != string(model.RealServerLevelRisk) {
			needRise = true
		}

		// 风险为增量风险
		record.State = string(model.StateIncrementRisk)
		record.RiseCount++
		updatedRisks = append(updatedRisks, record)

		i++ // 将 i 的递增放在最后，确保逻辑清晰
	}

	unrecover, _ := lo.Difference(increment, toDel)
	for index, key := range unrecover {
		obj := oldRiskMap[key]
		if strings.Contains(curRiskMap[key].UsingResources, "prod") {
			unrecover[index] = key + "/" + curRiskMap[key].RiskLevel + "/" + "prod" + "/" + fmt.Sprint(oldRiskMap[key].RiseCount) + "/" + time.Since(obj.CreatedAt).Truncate(time.Minute).String()
		} else {
			unrecover[index] = key + "/" + curRiskMap[key].RiskLevel + "/" + "dev" + "/" + fmt.Sprint(oldRiskMap[key].RiseCount) + "/" + time.Since(obj.CreatedAt).Truncate(time.Minute).String()
		}
	}
	// 发送告警
	options, err := alarmRiskCLB(ctx, cluster, clbResults, recover, unrecover, needRise)
	clusterResult.CLBList = clbResults
	clusterResult.Options = options

	if err != nil {
		log.FromContext(ctx).Error(err, "发送告警失败")
		clusterResult.Options.Error = err.Error()
		clusterResult.Options.Duration = time.Since(currentTime).String()
		return clusterResult, err
	}

	// 删除落库的RiskService数据
	if len(toDel) > 0 {
		if err := services.Get().LegacyTask().DeleteModelsForCLBRisk(ctx, cluster.ClusterID, toDel); err != nil {
			log.FromContext(ctx).Error(err, "Risk Service数据删除失败")
			clusterResult.Options.Error = err.Error()
			clusterResult.Options.Duration = time.Since(currentTime).String()
			return clusterResult, err
		}
	}

	// 更新Risk Service表
	if len(updatedRisks) > 0 {
		if err := services.Get().LegacyTask().CreateOrUpdateModelSliceForCLBRisk(ctx, updatedRisks); err != nil {
			log.FromContext(ctx).Error(err, "Risk Service落库失败")
			clusterResult.Options.Error = err.Error()
			clusterResult.Options.Duration = time.Since(currentTime).String()
			return clusterResult, err
		}
	}

	clusterResult.Options.Duration = time.Since(currentTime).String()
	clusterResult.Options.Error = "健康"
	return clusterResult, nil
}
