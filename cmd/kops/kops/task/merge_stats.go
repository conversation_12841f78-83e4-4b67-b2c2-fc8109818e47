package task

import (
	"context"
	"errors"
	"fmt"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/Masterminds/semver"
	"github.com/go-logr/logr"
	"github.com/samber/lo"
	"github.com/spf13/pflag"
	"golang.org/x/sync/errgroup"
	appsv1 "k8s.io/api/apps/v1"
	rbacv1 "k8s.io/api/rbac/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"

	"git.woa.com/kateway/pkg/telemetry/log"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model/merge"
	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
)

var (
	ErrIncompatibleStartupArgs   = errors.New("incompatible startup args")
	ErrUnexpectedRBAC            = errors.New("unexpected rbac")
	ErrSilentStart               = errors.New("silent start")
	ErrServiceControllerNotFound = errors.New("service controller not found")
	ErrControllerVersionNotReady = errors.New("controller version not ready")
)

type MergeStats struct {
	model.Task

	date           string
	logger         logr.Logger
	processedTotal int
	mutex          sync.Mutex
}

func NewMergeStats(date string) *MergeStats {
	return &MergeStats{
		Task:   model.NewTask(date, "MergeMigrationStat", "bowen"),
		date:   date,
		logger: log.WithName("MergeMigrationStat"),
	}
}

func (m *MergeStats) Run() {
	m.logger.Info("Running")
	defer m.logger.Info("Done")

	ctx := context.Background()
	if err := m.run(ctx); err != nil {
		m.logger.Error(err, "Failed to run")
	}
}

func (m *MergeStats) run(ctx context.Context) error {
	clusters, err := services.Get().Cluster().List(ctx)
	if err != nil {
		return err
	}
	providedClusters := config.Get().Task.MergeStats.ClusterIDs
	if len(providedClusters) != 0 {
		clusters = lo.Filter(clusters, func(c model.Cluster, _ int) bool {
			return lo.Contains(providedClusters, c.ClusterID)
		})
	}
	m.Total = len(clusters)
	started := time.Now()
	wg := errgroup.Group{}
	wg.SetLimit(config.Get().Task.MergeStats.Concurrency)
	wg.Go(func() error {
		return wait.PollUntilWithContext(ctx, 5*time.Second, func(ctx context.Context) (done bool, err error) {
			m.mutex.Lock()
			defer m.mutex.Unlock()

			done = m.processedTotal == m.Total
			if done {
				m.State = model.TaskStateDone.String()
				m.FinishedAt = lo.ToPtr(time.Now())
			}
			m.Cost = time.Since(started).Seconds()
			err = services.Get().LegacyTask().Update(&m.Task)
			return
		})
	})

	for _, cls := range clusters {
		cluster := cls
		wg.Go(func() error {
			defer func() {
				m.mutex.Lock()
				defer m.mutex.Unlock()

				m.processedTotal++
				m.UpdateProgress(m.processedTotal)
			}()

			m.process(ctx, &cluster)
			return nil
		})
	}
	return wg.Wait()
}

func setUnMigratable(ctrl *merge.IngressController, err error) {
	ctrl.State = merge.MigrationStateUnMigratable
	ctrl.CauseDetail = err.Error()
	if errors.Is(err, ErrIncompatibleStartupArgs) {
		ctrl.Cause = merge.CauseIncompatibleStartupArgs
	} else if errors.Is(err, ErrServiceControllerNotFound) {
		ctrl.Cause = merge.CauseServiceControllerNotFound
	} else if errors.Is(err, ErrSilentStart) {
		ctrl.Cause = merge.CauseSilentStart
	} else if errors.Is(err, ErrUnexpectedRBAC) {
		ctrl.Cause = merge.CauseUnexpectedRBAC
	} else if errors.Is(err, ErrControllerVersionNotReady) {
		ctrl.Cause = merge.CauseControllerVersionNotReady
	} else {
		ctrl.Cause = merge.CauseUnknown
	}
}

func (m *MergeStats) process(ctx context.Context, cluster *model.Cluster) {
	m.logger.Info("Processing cluster", "id", cluster.ClusterID)
	defer m.logger.Info("Cluster processed successfully", "id", cluster.ClusterID)

	ingCtrl := &merge.IngressController{
		Date:        m.date,
		ClusterID:   cluster.ClusterID,
		ClusterType: lo.Ternary(cluster.IsEKS(), "EKS", "TKE"),
	}
	var err error

	defer func() {
		if r := recover(); r != nil {
			err = parsePanicError(r)
		}

		if err != nil {
			setUnMigratable(ingCtrl, err)
		} else if ingCtrl.State != merge.MigrationStateMigrated {
			ingCtrl.State = merge.MigrationStateMigratable
		}

		if err := services.Get().Save(ingCtrl); err != nil {
			m.logger.Error(err, "Failed to save ingress controller data")
		}
	}()

	ingCli, err := services.Get().Cluster().ClientsetForIngress(ctx, cluster)
	if err != nil {
		return
	}
	svcCli, err := services.Get().Cluster().DeployClientsetForService(ctx, cluster)
	if err != nil {
		return
	}

	c := newStatCollector(cluster, ingCli.K8sCli, svcCli.K8sCli)
	migrated, err := c.IsMigrated(ctx)
	if err != nil {
		return
	}
	if migrated {
		ingCtrl.State = merge.MigrationStateMigrated
		return
	}
	err = c.PreflightCheck(ctx, cluster)
}

type statCollector struct {
	cluster                              *model.Cluster
	ingCli, svcCli                       kubernetes.Interface
	svcNamespacedName, ingNamespacedName types.NamespacedName
}

func newStatCollector(cluster *model.Cluster, ingCli, svcCli kubernetes.Interface) *statCollector {
	return &statCollector{
		cluster: cluster,
		svcNamespacedName: types.NamespacedName{
			Namespace: lo.Ternary(cluster.IsIndependent(), metav1.NamespaceSystem, cluster.ClusterID),
			Name:      lo.Ternary(cluster.IsIndependent(), "service-controller", fmt.Sprintf("%s-%s", cluster.ClusterID, "service-controller")),
		},
		ingNamespacedName: types.NamespacedName{
			Namespace: lo.Ternary(cluster.IsEKS(), cluster.ClusterID, metav1.NamespaceSystem),
			Name:      lo.Ternary(cluster.IsEKS(), fmt.Sprintf("%s-%s", cluster.ClusterID, "ingress-controller"), "l7-lb-controller"),
		},
		ingCli: ingCli,
		svcCli: svcCli,
	}
}

func (c *statCollector) IsMigrated(ctx context.Context) (bool, error) {
	d, err := c.GetServiceController(ctx)
	if err != nil {
		return false, err
	}
	_, exists := d.Annotations["cloud.tencent.com/ingress-controller-migrated"]
	return exists, nil
}

func (c *statCollector) GetServiceController(ctx context.Context) (*appsv1.Deployment, error) {
	d, err := c.svcCli.AppsV1().Deployments(c.svcNamespacedName.Namespace).Get(ctx, c.svcNamespacedName.Name, metav1.GetOptions{})
	if err != nil {
		if apierrors.IsNotFound(err) {
			return nil, ErrServiceControllerNotFound
		}
		return nil, err
	}
	return d, nil
}

func (c *statCollector) PreflightCheck(ctx context.Context, cluster *model.Cluster) error {
	var exists bool
	d, err := c.ingCli.AppsV1().Deployments(c.ingNamespacedName.Namespace).Get(ctx, c.ingNamespacedName.Name, metav1.GetOptions{})
	if err != nil {
		if !apierrors.IsNotFound(err) {
			return err
		}
	} else {
		exists = true
	}

	if exists {
		if err := c.checkStartupArgs(d); err != nil {
			return err
		}

		if err := c.checkIngCtrlConfigMap(ctx); err != nil {
			return err
		}

		if !cluster.IsEKS() {
			if err := c.checkRoleBindings(ctx, d.Spec.Template.Spec.ServiceAccountName); err != nil {
				return err
			}
		}
	}

	d, err = c.GetServiceController(ctx)
	if err != nil {
		return err
	}

	return c.checkVersion(d, config.Get().Task.MergeStats.ServiceVersion)
}

func (c *statCollector) checkVersion(d *appsv1.Deployment, targetVersion string) error {
	if len(d.Spec.Template.Spec.Containers) == 0 {
		return nil
	}
	container := d.Spec.Template.Spec.Containers[0]
	img := container.Image
	tag := strings.Split(img, ":")[1]
	index := strings.Index(tag, "-")
	if index != -1 {
		tag = tag[:index]
	}
	current, err := semver.NewVersion(tag)
	if err != nil {
		return err
	}
	target, err := semver.NewVersion(targetVersion)
	if err != nil {
		return err
	}
	if current.Compare(target) >= 0 {
		return nil
	}
	return fmt.Errorf("controller %s, current version: %s, expect: >= %s: %w", d.Name, current, target, ErrControllerVersionNotReady)
}

func (c *statCollector) checkRoleBindings(ctx context.Context, sa string) error {
	cli := c.ingCli.RbacV1()
	if sa != "lb-ingress" {
		return fmt.Errorf("the service account is not lb-ingress: %w", ErrUnexpectedRBAC)
	}
	roleBindings, err := cli.RoleBindings(metav1.NamespaceSystem).List(ctx, metav1.ListOptions{})
	if err != nil {
		return err
	}
	find := func(ss []rbacv1.Subject) bool {
		_, exists := lo.Find(ss, func(s rbacv1.Subject) bool {
			return s.Kind == "ServiceAccount" && s.Name == sa && s.Namespace == metav1.NamespaceSystem
		})
		return exists
	}
	unexpectedBindings := lo.FilterMap(roleBindings.Items, func(rb rbacv1.RoleBinding, _ int) (string, bool) {
		return rb.Name, find(rb.Subjects)
	})
	if len(unexpectedBindings) != 0 {
		return fmt.Errorf("unexpected role bindings %v related to sa %q: %w", unexpectedBindings, sa, ErrUnexpectedRBAC)
	}

	clusterBindings, err := cli.ClusterRoleBindings().List(ctx, metav1.ListOptions{})
	if err != nil {
		return err
	}
	unexpectedClusterBindings := lo.FilterMap(clusterBindings.Items, func(b rbacv1.ClusterRoleBinding, _ int) (string, bool) {
		return b.Name, find(b.Subjects) && b.Name != "lb-ingress-clusterrole-nisa-binding"
	})
	if len(unexpectedClusterBindings) != 0 {
		return fmt.Errorf("unexpected cluster role bindings %v related to sa %q: %w", unexpectedClusterBindings, sa, ErrUnexpectedRBAC)
	}
	return nil
}

func (c *statCollector) checkIngCtrlConfigMap(ctx context.Context) error {
	cm, err := c.ingCli.CoreV1().ConfigMaps(metav1.NamespaceSystem).Get(ctx, "tke-ingress-controller-config", metav1.GetOptions{})
	if err != nil {
		return err
	}
	if cm.Data["SILENT_START"] == "true" {
		return fmt.Errorf("the \"SILENT_START\" field of the tke-ingress-controller-config is true: %w", ErrSilentStart)
	}
	return nil
}

func (c *statCollector) checkStartupArgs(d *appsv1.Deployment) error {
	if len(d.Spec.Template.Spec.Containers) == 0 {
		return nil
	}

	var argsStr string

	container := d.Spec.Template.Spec.Containers[0]
	argsStr = strings.Join(append(append([]string{}, container.Command...), container.Args...), " ")
	currentArgs := strings.Split(argsStr, " ")
	if err := c.checkArgs(currentArgs); err != nil {
		return fmt.Errorf("%v: %w", err, ErrIncompatibleStartupArgs)
	}
	return nil
}

func (c *statCollector) checkArgs(curArgs []string) error {
	fs := pflag.NewFlagSet("", pflag.ContinueOnError)

	type flagSpec struct {
		name         string
		defaultValue string
	}

	acceptableSpecs := []flagSpec{
		{
			name:         "kube-config",
			defaultValue: lo.Ternary(c.cluster.IsEKS(), "/etc/kubernetes/config", ""),
		},
		{
			name:         "master",
			defaultValue: "",
		},
		{
			name:         "qps",
			defaultValue: "1000",
		},
		{
			name:         "burst",
			defaultValue: "10000",
		},
		{
			name:         "cluster-name",
			defaultValue: c.cluster.ClusterID,
		},
		{
			name:         "metric-port",
			defaultValue: "10254",
		},
		{
			name:         "backend-quota",
			defaultValue: "0",
		},
		{
			name:         "listener-quota",
			defaultValue: "0",
		},
		{
			name:         "workers",
			defaultValue: "10",
		},
		{
			name:         "rate-limit",
			defaultValue: "9",
		},
		{
			name:         "ingress-class",
			defaultValue: "qcloud",
		},
		{
			name:         "enable-tracing",
			defaultValue: "false",
		},
		{
			name:         "enable-multiclusteringress",
			defaultValue: "false",
		},
	}
	for _, f := range acceptableSpecs {
		fs.String(f.name, f.defaultValue, "")
	}
	if err := fs.Parse(curArgs); err != nil {
		return fmt.Errorf("unacceptable args of ingress-controller detected: %w", err)
	}

	var (
		errs []error
	)

	fs.Visit(func(f *pflag.Flag) {
		spec, exists := lo.Find(acceptableSpecs, func(s flagSpec) bool { return s.name == f.Name })
		if !exists {
			panic(fmt.Sprintf("unexpected flag %s", f.Name))
		}
		if f.Value.String() != spec.defaultValue {
			errs = append(errs, fmt.Errorf("the value of the flag %q is not the default, current: %v, expect: %v",
				f.Name, f.Value, spec.defaultValue))
		}
	})
	return errors.Join(errs...)
}

func parsePanicError(r any) error {
	const size = 64 << 10
	stacktrace := make([]byte, size)
	stacktrace = stacktrace[:runtime.Stack(stacktrace, false)]
	var err error
	if _, ok := r.(string); ok {
		err = fmt.Errorf("observed a panic: %s\n%s", r, stacktrace)
	} else {
		err = fmt.Errorf("observed a panic: %#v (%v)\n%s", r, r, stacktrace)
	}
	return err
}
