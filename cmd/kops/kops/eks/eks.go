package eks

import (
	"github.com/spf13/cobra"

	"git.woa.com/kateway/kateway-server/cmd/kops/kops/eks/ingress"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/eks/merge"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/eks/service"
)

var EKSCmd = &cobra.Command{
	Use:   "eks",
	Short: "service controller misaka eks tool",
	Long:  "service controller misaka eks tool",
}

func init() {
	EKSCmd.AddCommand(service.ServiceCmd)
	EKSCmd.AddCommand(ingress.IngressCmd)
	EKSCmd.AddCommand(merge.MergeCMD)
}
