package merge

import (
	"context"

	"github.com/samber/lo"
	"github.com/spf13/cobra"
	platformv1 "tkestack.io/tke/api/platform/v1"

	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	cluster2 "git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/eks/service"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tracing"
	"git.woa.com/kateway/kateway-server/pkg/dryrun"
	"git.woa.com/kateway/kateway-server/pkg/merge"
	"git.woa.com/kateway/kateway-server/pkg/service/cluster"
	region2 "git.woa.com/kateway/kateway-server/pkg/util/region"
)

var MigrateCMD = &cobra.Command{
	Use:   "migrate",
	Short: "start a migration which merges two controllers together",
	RunE: func(cmd *cobra.Command, args []string) error {
		task := Task{
			TaskInterface: &MigrateTask{},
			Title: []string{
				"Region", "Cluster", "Error",
			},
		}
		if err := task.Run(cmd, args); err != nil {
			return err
		}
		return nil
	},
}

func init() {
	MergeCMD.AddCommand(MigrateCMD)

	MigrateCMD.Flags().StringSliceP("cluster", "c", nil, "The cluster instance id")
	MigrateCMD.Flags().StringSliceP("appId", "a", nil, "The cluster app id")
	MigrateCMD.Flags().IntP("offset", "o", 0, "cluster offset")
	MigrateCMD.Flags().IntP("limit", "l", -1, "cluster limit")
	MigrateCMD.Flags().IntP("worker", "w", 20, "Parallelize workers")
	MigrateCMD.Flags().Int("timeout", 15, "timeout")
	MigrateCMD.Flags().StringP("logpath", "p", "", "log path to save log")
	MigrateCMD.Flags().Bool("dryrun", true, "start a dry run before migration")
}

type MigrateTask struct {
	Task

	timeout int
	dryrun  bool

	clusterCli, metaClusterCli cluster.ClientsSet
}

func (t *MigrateTask) migrate(ctx context.Context, c *platformv1.Cluster) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	p := merge.NewProcessor(c.Name, merge.ClientSets{
		Cluster:     t.clusterCli,
		MetaCluster: &t.metaClusterCli,
	}, true)

	var fn func(context.Context, string) error
	if t.dryrun {
		fn = func(ctx context.Context, image string) error {
			result, err := service.MockRun(ctx, c, image, 15, false, lo.ToPtr(true))
			if err != nil {
				return err
			}
			if err := dryrun.BuildErrorFromResult(result); err != nil {
				return fmt.Errorf("dryrun failed: %w", err)
			}
			return nil
		}
	}

	return p.Migrate(ctx, fn)
}

func (t *MigrateTask) do(ctx context.Context, region string, c *platformv1.Cluster) error {
	cs, err := cluster2.GetEKSClusterClientSet(region, c)
	if err != nil {
		return err
	}

	metaClusterId := c.Annotations["eks.tke.cloud.tencent.com/metacluster"]
	metaCluster := cluster2.MustGetTKECluster(region, metaClusterId)
	metaCS, err := cluster2.GetTKEClusterClientSet(region, metaCluster)
	if err != nil {
		return err
	}

	t.clusterCli = cs
	t.metaClusterCli = metaCS

	return t.migrate(ctx, c)
}

func (t *MigrateTask) Do(cmd *cobra.Command, cluster *platformv1.Cluster) []map[string]string {
	span, ctx := jaeger.StartSpanFromContext(cmd.Context(), jaeger.WithOperationName("tke.merge.migrate"))
	defer span.Finish()

	tracing.Platformv1Cluster(span, cluster)

	region, exist := cluster.Annotations["eks.tke.cloud.tencent.com/region"]
	if !exist {
		return []map[string]string{
			{
				"Error": fmt.Sprintf("%s. eks.tke.cloud.tencent.com/region not exist.\n", cluster.Name),
			},
		}
	}
	regionInfo := region2.Get(region)
	results := []map[string]string{
		{
			"Region":  regionInfo.Alias,
			"Cluster": cluster.Name,
			"Error":   "<nil>",
		},
	}

	t.timeout, _ = cmd.Flags().GetInt("timeout")
	t.dryrun, _ = cmd.Flags().GetBool("dryrun")
	err := t.do(ctx, region, cluster)
	if err != nil {
		results[0]["Error"] = err.Error()
	}
	return results
}
