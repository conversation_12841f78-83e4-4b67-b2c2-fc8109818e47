package merge

import (
	"k8s.io/apimachinery/pkg/types"
	platformv1 "tkestack.io/tke/api/platform/v1"

	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"
)

const (
	SvcCtrlDeployName    = "service-controller"
	SvcCtrlContainerName = "service-controller"

	SERVICE_OPERATOR_LABEL = "cloud.tencent.com/tke-service-controller-config-version"

	ServiceControllerGlobalConfigKey     = "cloud.tencent.com/tke-config-name"
	ServiceControllerGlobalConfigName    = "service-controller-global-config"
	ServiceControllerGlobalConfigVersion = "cloud.tencent.com/tke-config-version"
)

func buildCtrlDeployNamespacedNames(c *platformv1.Cluster) (svc, ing types.NamespacedName) {
	return types.NamespacedName{
			Namespace: c.Name,
			Name:      fmt.Sprintf("%s-%s", c.Name, SvcCtrlDeployName),
		}, types.NamespacedName{
			Namespace: c.Name,
			Name:      fmt.Sprintf("%s-%s", c.Name, "ingress-controller"),
		}
}
