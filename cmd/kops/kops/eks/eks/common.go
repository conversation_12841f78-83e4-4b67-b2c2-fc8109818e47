package eks

import (
	"context"
	"fmt"

	platformv1 "tkestack.io/tke/api/platform/v1"

	"git.woa.com/kateway/pkg/telemetry/jaeger"

	"git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
)

func CheckIfNeedSkip(ctx context.Context, cls *platformv1.Cluster) bool {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	for _, appId := range cluster.MisakaConfig.SkipAppIds {
		if cls.Spec.TenantID == appId {
			span.LogKV("cls.Spec.TenantID", cls.Spec.TenantID)
			span.LogKV("SkipAppIds", cluster.MisakaConfig.SkipAppIds)
			return true
		}
	}

	for _, clusterId := range cluster.MisakaConfig.SkipClusterIds {
		if cls.Name == clusterId {
			span.LogKV("cls.Name ", cls.Name)
			span.LogKV("SkipClusterIds", cluster.MisakaConfig.SkipClusterIds)
			return true
		}
	}

	return false
}

func CheckClusterStatus(cluster *platformv1.Cluster) error {
	if cluster.Status.Phase == "Idling" || cluster.Status.Phase == "Failed" || cluster.Status.Phase == "Initializing" || cluster.Status.Phase == "Terminating" {
		return fmt.Errorf("cluster status error: %s", string(cluster.Status.Phase))
	}
	return nil
}
