package eks

import (
	"bytes"
	"context"

	v1 "k8s.io/api/core/v1"
	rbac "k8s.io/api/rbac/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/version"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd/api"
	"k8s.io/client-go/tools/clientcmd/api/latest"

	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"
)

const MockRBACServiceName = "eks-service-readonly"

const MockRBACIngressName = "eks-ingress-readonly"

func CreateServiceReadOnlyRBAC(ctx context.Context, k8sClient kubernetes.Interface, namespace string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	return createReadOnlyRBAC(ctx, k8sClient, namespace, MockRBACServiceName)
}

func CreateIngressReadOnlyRBAC(ctx context.Context, k8sClient kubernetes.Interface, namespace string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	return createReadOnlyRBAC(ctx, k8sClient, namespace, MockRBACIngressName)
}

func createReadOnlyRBAC(ctx context.Context, k8sClient kubernetes.Interface, namespace string, name string) error {
	_, err := k8sClient.RbacV1().ClusterRoles().Create(ctx, genReadOnlyClusterRole(name), metav1.CreateOptions{})
	if err != nil && !apierrors.IsAlreadyExists(err) {
		return err
	}
	sa := &v1.ServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
		},
	}
	_, err = k8sClient.CoreV1().ServiceAccounts(namespace).Create(ctx, sa, metav1.CreateOptions{})
	if err != nil && !apierrors.IsAlreadyExists(err) {
		return err
	}
	_, err = k8sClient.RbacV1().ClusterRoleBindings().Create(ctx, genReadOnlyClusterRoleBinding(namespace, name), metav1.CreateOptions{})
	if err != nil && !apierrors.IsAlreadyExists(err) {
		return err
	}
	return nil
}

func CleanUpServiceRBAC(ctx context.Context, k8sClient kubernetes.Interface, namespace string) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cleanUpRBAC(ctx, k8sClient, namespace, MockRBACServiceName)
}

func CleanUpIngressRBAC(ctx context.Context, k8sClient kubernetes.Interface, namespace string) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cleanUpRBAC(ctx, k8sClient, namespace, MockRBACIngressName)
}

func cleanUpRBAC(ctx context.Context, k8sClient kubernetes.Interface, namespace string, name string) {
	var err error

	// kateway(wallaceqian) 错误直接输出，没有处理，可能删除失败，影响后续功能
	err = k8sClient.RbacV1().ClusterRoles().Delete(ctx, name, metav1.DeleteOptions{})
	if err != nil && !apierrors.IsNotFound(err) {
		fmt.Println(err)
	}
	err = k8sClient.RbacV1().ClusterRoleBindings().Delete(ctx, name, metav1.DeleteOptions{})
	if err != nil && !apierrors.IsNotFound(err) {
		fmt.Println(err)
	}
	err = k8sClient.CoreV1().ServiceAccounts(namespace).Delete(ctx, name, metav1.DeleteOptions{})
	if err != nil && !apierrors.IsNotFound(err) {
		fmt.Println(err)
	}
	err = k8sClient.CoreV1().Secrets(namespace).Delete(ctx, name, metav1.DeleteOptions{})
	if err != nil && !apierrors.IsNotFound(err) {
		fmt.Println(err)
	}
}

func GenServiceReadOnlyKubeconfig(ctx context.Context, k8sClient kubernetes.Interface, namespace, cluster string) (*v1.ConfigMap, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	return genReadOnlyKubeconfig(ctx, k8sClient, namespace, MockRBACServiceName, cluster)
}

func GenIngressReadOnlyKubeconfig(ctx context.Context, k8sClient kubernetes.Interface, namespace, cluster string) (*v1.ConfigMap, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	return genReadOnlyKubeconfig(ctx, k8sClient, namespace, MockRBACIngressName, cluster)
}

func genReadOnlyKubeconfig(ctx context.Context, k8sClient kubernetes.Interface, namespace, name, cluster string) (*v1.ConfigMap, error) {
	secretList, err := k8sClient.CoreV1().Secrets(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, err
	}

	var readOnlySecret *v1.Secret
	for i, secret := range secretList.Items {
		if secret.Type == v1.SecretTypeServiceAccountToken &&
			secret.Annotations[v1.ServiceAccountNameKey] == name {
			readOnlySecret = &secretList.Items[i]
			break
		}
	}
	if readOnlySecret == nil {
		return nil, fmt.Errorf("%s secret not found", name)
	}
	if readOnlySecret.Data["ca.crt"] == nil {
		return nil, fmt.Errorf("ca.crt is nil")
	}
	if readOnlySecret.Data["token"] == nil {
		return nil, fmt.Errorf("token is nil")
	}

	kubeconfig := api.NewConfig()
	kubeconfig.CurrentContext = cluster
	kubeconfig.Contexts[cluster] = &api.Context{
		Cluster:  cluster,
		AuthInfo: name,
	}
	kubeconfig.Clusters[cluster] = &api.Cluster{
		CertificateAuthorityData: readOnlySecret.Data["ca.crt"],
		Server:                   fmt.Sprintf("https://%s-apiserver-service:60002", cluster),
	}
	kubeconfig.AuthInfos[name] = &api.AuthInfo{
		Token: string(readOnlySecret.Data["token"]),
	}
	var data bytes.Buffer
	if err := latest.Codec.Encode(kubeconfig, &data); err != nil {
		return nil, err
	}

	return &v1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: cluster,
		},
		Data: map[string]string{
			"kubeconfig": data.String(),
		},
	}, nil
}

func CreateServiceReadOnlySecretIfNeeded(ctx context.Context, k8sClient kubernetes.Interface, namespace string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	return createReadOnlySecretIfNeeded(ctx, k8sClient, namespace, MockRBACServiceName)
}

func CreateIngressReadOnlySecretIfNeeded(ctx context.Context, k8sClient kubernetes.Interface, namespace string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	return createReadOnlySecretIfNeeded(ctx, k8sClient, namespace, MockRBACIngressName)
}

func createReadOnlySecretIfNeeded(ctx context.Context, k8sClient kubernetes.Interface, namespace string, name string) error {
	version124, _ := version.ParseGeneric("v1.24.0")
	serverVersion, err := k8sClient.Discovery().ServerVersion()
	if err != nil {
		return err
	}
	runningVersion, err := version.ParseGeneric(serverVersion.String())
	if err != nil {
		return err
	}
	if runningVersion.AtLeast(version124) {
		_, err = k8sClient.CoreV1().Secrets(namespace).Create(ctx, genReadOnlySecret(namespace, name), metav1.CreateOptions{})
		if err != nil && !apierrors.IsAlreadyExists(err) {
			return err
		}
	}
	return nil
}

func genReadOnlySecret(namespace string, name string) *v1.Secret {
	return &v1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
			Annotations: map[string]string{
				v1.ServiceAccountNameKey: name,
			},
		},
		Type: v1.SecretTypeServiceAccountToken,
	}
}

func genReadOnlyClusterRole(name string) *rbac.ClusterRole {
	return &rbac.ClusterRole{
		ObjectMeta: metav1.ObjectMeta{
			Name: name,
		},
		Rules: []rbac.PolicyRule{
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{""},
				Resources: []string{"pods", "nodes", "endpoints", "configmaps", "secrets", "services", "events"},
			},
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{"apps"},
				Resources: []string{"deployments", "replicasets"},
			},
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{"networking.k8s.io", "extensions"},
				Resources: []string{"ingresses"},
			},
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{"apiextensions.k8s.io"},
				Resources: []string{"customresourcedefinitions"},
			},
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{"discovery.k8s.io"},
				Resources: []string{"endpointslices"},
			},
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{"cloud.tencent.com"},
				Resources: []string{"tkeserviceconfigs"},
			},
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{"networking.tke.cloud.tencent.com"},
				Resources: []string{"loadbalancerresources"},
			},
			{
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{"cloud.tencent.com"},
				Resources: []string{"multiclusterservices"},
			}, {
				Verbs:     []string{"get", "list", "watch"},
				APIGroups: []string{"cloud.tencent.com"},
				Resources: []string{"multiclusteringresses"},
			},
			{
				Verbs:         []string{"update"},
				APIGroups:     []string{"apiextensions.k8s.io"},
				Resources:     []string{"customresourcedefinitions"},
				ResourceNames: []string{"tkeserviceconfigs.cloud.tencent.com", "loadbalancerresources.networking.tke.cloud.tencent.com", "multiclusterservices.cloud.tencent.com"},
			},
			{
				Verbs:         []string{"update"},
				APIGroups:     []string{""},
				Resources:     []string{"configmaps"},
				ResourceNames: []string{"tke-service-controller-config"},
			},
		},
	}
}

func genReadOnlyClusterRoleBinding(namespace string, name string) *rbac.ClusterRoleBinding {
	return &rbac.ClusterRoleBinding{
		ObjectMeta: metav1.ObjectMeta{
			Name: name,
		},
		RoleRef: rbac.RoleRef{
			APIGroup: rbac.GroupName,
			Kind:     "ClusterRole",
			Name:     name,
		},
		Subjects: []rbac.Subject{
			{
				Kind:      rbac.ServiceAccountKind,
				Name:      name,
				Namespace: namespace,
			},
		},
	}
}
