package service

import (
	"bytes"
	"context"
	"os"
	"sort"
	"strings"
	"time"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"k8s.io/client-go/util/workqueue"
	platformv1 "tkestack.io/tke/api/platform/v1"

	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/helper"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/table"
	region2 "git.woa.com/kateway/kateway-server/pkg/util/region"
)

type Task struct {
	TaskInterface
	Title []string
}

type TaskInterface interface {
	Do(cmd *cobra.Command, cluster *platformv1.Cluster) []map[string]string
}

func (task *Task) Run(cmd *cobra.Command, args []string) error {
	ctx, cancel := context.WithTimeout(cmd.Context(), 150*time.Minute)
	defer cancel()

	span, ctx := jaeger.StartSpanFromContext(ctx, jaeger.WithOperationName(cmd.Name()))
	defer span.Finish()
	cmd.SetContext(ctx)

	clusterIds, err := helper.InputClusters(cmd)
	if err != nil {
		return err
	}

	appIds, _ := cmd.Flags().GetStringSlice("appId")
	regionShort, _ := cmd.Flags().GetString("region")
	offset, _ := cmd.Flags().GetInt("offset")
	limit, _ := cmd.Flags().GetInt("limit")
	worker, _ := cmd.Flags().GetInt("worker")

	regionInfo := region2.MustGet(regionShort)

	// kateway(wallaceqian) 启动时全量拉取集群列表，耗时比较久
	eksClusters, err := cluster.GetAllEKSCluster(cmd.Context(), regionInfo.Name)
	if err != nil {
		return err
	}

	// kateway(wallaceqian) 校验集群列表有效性，忽略了查不到的集群，可能不符合命令执行者预期
	if len(clusterIds) != 0 {
		clusterMap := make(map[string]bool)
		for _, clusterId := range clusterIds {
			clusterMap[clusterId] = true
		}
		filterCluster := make([]*platformv1.Cluster, 0)
		for index, cluster := range eksClusters {
			if _, exist := clusterMap[cluster.Name]; exist {
				filterCluster = append(filterCluster, eksClusters[index])
			}
		}
		eksClusters = filterCluster
	}

	if len(appIds) != 0 {
		clusterMap := make(map[string]bool)
		for _, appId := range appIds {
			clusterMap[appId] = true
		}
		filterCluster := make([]*platformv1.Cluster, 0)
		for index, cluster := range eksClusters {
			if _, exist := clusterMap[cluster.Spec.TenantID]; exist {
				filterCluster = append(filterCluster, eksClusters[index])
			}
		}
		eksClusters = filterCluster
	}

	if limit != -1 {
		sort.Sort(ClusterModelSlice(eksClusters))
		if offset > len(eksClusters)-1 {
			offset = len(eksClusters) - 1
		}

		end := offset + limit
		if offset+limit > len(eksClusters) {
			end = len(eksClusters)
		}
		eksClusters = eksClusters[offset:end]
	}

	logPath, _ := cmd.Flags().GetString("logpath")
	if logPath != "" {
		if _, err := os.Stat(logPath); os.IsNotExist(err) {
			err := os.MkdirAll(logPath, os.ModePerm)
			if err != nil {
				return err
			}
		}
	}

	regionList := make([]string, 0)
	if regionShort == "all" {
		regionList = viper.GetStringSlice("regions")
		fmt.Println(regionList)
	} else {
		regionList = []string{regionShort}
	}

	for index, region := range regionList {
		fmt.Printf("exec on %dth region: %s\n", index+1, region)

		if err != nil {
			fmt.Println(err)
			os.Exit(-1)
		}
		if len(eksClusters) == 0 {
			fmt.Println("No clustetr found")
			continue
		}

		clusterIds := make([]string, len(eksClusters))
		for i, cluster := range eksClusters {
			clusterIds[i] = cluster.Name
		}
		fmt.Printf("Total %d cluster: %s\n", len(clusterIds), strings.Join(clusterIds, ", "))
		span.LogKV("clusters", clusterIds)

		// 执行任务
		clusterResults := make([]map[string]string, 0)
		workqueue.ParallelizeUntil(ctx, worker, len(eksClusters), func(index int) {
			cluster := eksClusters[index]

			// 在具体子命令中来判定是否需要忽略
			// if checkIfNeedSkip(cluster) {
			// clusterResults = append(clusterResults, map[string]string{
			//	"Region":  region,
			//	"Cluster": cluster.Name,
			//	"Version": cluster.Spec.Version,
			//	"Error":   "skip cluster",
			// })
			// }
			result := task.Do(cmd, cluster)
			for index := range result {
				clusterResults = append(clusterResults, result[index])
			}
		})

		// 打印输出
		task.printResult(ctx, clusterResults)

		// kateway(wallaceqian) 输出到智研日志汇
		if logPath != "" {
			logDir := fmt.Sprintf("%s/%s/%s",
				logPath,
				region,
				time.Now().Format("2006-01-02"))
			err = os.MkdirAll(logDir, os.ModePerm)
			if err != nil {
				fmt.Printf("Make %s log dir failed: %v\n", region, err)
			}
			log, err := os.Create(fmt.Sprintf("%s/service-controller_%s.log", logDir, region))
			defer log.Close()
			if err != nil {
				return err
			}

			task.fprintResult(log, clusterResults)
		}
		fmt.Println("Done")
	}
	return nil
}

func (task *Task) printResult(ctx context.Context, clusterResults []map[string]string) {
	table.Print(ctx, task.Title, clusterResults)
}

func (task *Task) fprintResult(log *os.File, clusterResults []map[string]string) {
	buf := bytes.NewBuffer(nil)
	for index, item := range task.Title {
		if index == len(task.Title)-1 { // Last Item
			fmt.Fprintf(buf, "%s\n", item)
			fmt.Println(buf.String())
		} else {
			fmt.Fprintf(buf, "%s\t", item)
		}
	}
	for _, res := range clusterResults {
		for index, item := range task.Title {
			value, exist := res[item]
			if !exist {
				value = "nil"
			}
			if index == len(task.Title)-1 { // Last Item
				fmt.Fprintf(log, "%s\n", value)
			} else {
				fmt.Fprintf(log, "%s\t", value)
			}
		}
	}
}

type ClusterModelSlice []*platformv1.Cluster

func (a ClusterModelSlice) Len() int {
	return len(a)
}

func (a ClusterModelSlice) Swap(i, j int) {
	a[i], a[j] = a[j], a[i]
}

func (a ClusterModelSlice) Less(i, j int) bool {
	return a[i].Name < a[j].Name
}
