package service

import (
	"context"
	"errors"
	"io"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cobra"
	v13 "k8s.io/api/apps/v1"
	v14 "k8s.io/api/batch/v1"
	v12 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/utils/pointer"
	platformv1 "tkestack.io/tke/api/platform/v1"

	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/cmd/kops/common"
	cluster2 "git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
	common2 "git.woa.com/kateway/kateway-server/cmd/kops/kops/common"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/eks/eks"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tracing"
	"git.woa.com/kateway/kateway-server/pkg/dryrun"
	"git.woa.com/kateway/kateway-server/pkg/merge"
	region2 "git.woa.com/kateway/kateway-server/pkg/util/region"
)

const (
	MockJobName         = "service-mock-job"
	MockContainerName   = "mock"
	OtherErrorExitCode  = 255
	truncateJobExitCode = 254
	MockErrorExitCode   = 1 // found mock error
)

var MockCmd = &cobra.Command{
	Use:   "mock",
	Short: "service controller publish tool",
	Long:  "service controller publish tool",
	RunE: func(cmd *cobra.Command, args []string) error {
		task := Task{
			TaskInterface: &MockCmdTask{},
			Title: []string{
				"Region", "Cluster", "AppId", "Version", "Balance", "ServiceName", "Action", "Request", "Reason", "Analysis", "Error",
			},
		}
		if err := task.Run(cmd, args); err != nil {
			return err
		}
		return nil
	},
}

type MockCmdTask struct {
	Task

	imageTag string
	timeout  int
}

// eks.tke.cloud.tencent.com/metacluster: cls-g0zvpnr3
// eks.tke.cloud.tencent.com/owner-uin: "2252646423"
// eks.tke.cloud.tencent.com/product-name: tkex-csig-ziyan
// eks.tke.cloud.tencent.com/region: ap-tianjin
func (this *MockCmdTask) Do(cmd *cobra.Command, cluster *platformv1.Cluster) []map[string]string {
	span, ctx := jaeger.StartSpanFromContext(cmd.Context(), jaeger.WithOperationName("eks.service.mock"))
	defer span.Finish()

	tracing.Platformv1Cluster(span, cluster)

	errorResult := []map[string]string{
		{
			"Region":  "<None>",
			"Cluster": cluster.Name,
			"Version": cluster.Spec.Version,
		},
	}

	region, exist := cluster.Annotations["eks.tke.cloud.tencent.com/region"]
	if !exist {
		errorResult[0]["Error"] = fmt.Sprintf("%s. eks.tke.cloud.tencent.com/region not exist.\n", cluster.Name)
		return errorResult
	}
	regionInfo := region2.Get(region)
	errorResult[0]["Region"] = regionInfo.Alias

	imageTag, _ := cmd.Flags().GetString("imageTag")
	timeout, _ := cmd.Flags().GetInt("timeout")
	this.imageTag = imageTag
	this.timeout = timeout

	dryrunSvc, _ := cmd.Flags().GetBool("service")
	var expectDryrunIng *bool
	if cmd.Flags().Changed("ingress") {
		dryrun, _ := cmd.Flags().GetBool("ingress")
		expectDryrunIng = &dryrun
	}

	ccrRegion, exist := common.CcrRegionMap[regionInfo.Alias]
	if !exist {
		errorResult[0]["Error"] = fmt.Sprintf("Region %s ccr not configed", regionInfo.Alias)
		return errorResult
	}
	image := fmt.Sprintf("%s%s:%s", ccrRegion, common.ServiceControllerImageRepo, imageTag)
	serviceStatus, err := MockRun(ctx, cluster, image, this.timeout, dryrunSvc, expectDryrunIng)
	if err != nil {
		errorResult[0]["Error"] = err.Error()
		return errorResult
	}

	results := make([]map[string]string, len(serviceStatus))
	if serviceStatus != nil {
		for index := range serviceStatus {
			results[index] = map[string]string{
				"Region":      regionInfo.Alias,
				"Cluster":     cluster.Name,
				"AppId":       cluster.Spec.TenantID,
				"Version":     cluster.Spec.Version,
				"Balance":     serviceStatus[index]["Balance"],
				"Error":       serviceStatus[index]["Error"],
				"ServiceName": serviceStatus[index]["ServiceName"],
				"Action":      serviceStatus[index]["Action"],
				"Request":     serviceStatus[index]["Request"],
				"Reason":      serviceStatus[index]["Reason"],
				"Analysis":    serviceStatus[index]["Analysis"],
			}
		}
	}
	span.LogKV("serviceStatus", jaeger.JSON(serviceStatus))
	return results
}

// kateway(wallaceqian) eks集群在metacluster集群对应的namespace创建job
func MockRun(ctx context.Context, cluster *platformv1.Cluster, image string, timeout int, dryrunSvc bool, expectDryrunIng *bool) (result []map[string]string, err error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer func() {
		jaeger.LogError(span, err)
		span.LogKV("result", result)
		span.Finish()
	}()

	var (
		dryrunIng bool
		tag       = strings.Split(image, ":")[1]
		region    = cluster.Annotations["eks.tke.cloud.tencent.com/region"]
	)
	readyForMerge, err := merge.IsVersionReady(tag)
	if err != nil {
		return nil, err
	}
	if expectDryrunIng != nil {
		if *expectDryrunIng {
			if !readyForMerge {
				return nil, fmt.Errorf("image version %s is not ready for ingress dryrun", tag)
			}
		}
		dryrunIng = *expectDryrunIng
	} else {
		if readyForMerge {
			cli, err := cluster2.GetEKSClusterClientSet(region, cluster)
			if err != nil {
				return nil, err
			}
			ingressEnabled, err := merge.IsIngressControllerEnabled(ctx, cli.K8sCli)
			if err != nil {
				return nil, err
			}
			dryrunIng = ingressEnabled
		}
	}

	if !dryrunSvc && !dryrunIng {
		return nil, errors.New("either service or ingress must be true when starting a dry run")
	}
	if dryrunIng {
		fmt.Printf("Ingress dry run need to be performed for cluster %s\n", cluster.Name)
	}

	if err := eks.CheckClusterStatus(cluster); err != nil {
		return nil, err
	}
	if eks.CheckIfNeedSkip(ctx, cluster) {
		return nil, fmt.Errorf("skip cluster")
	}

	balance := "Unknown"
	if appId, err := strconv.Atoi(cluster.Spec.TenantID); err != nil {
		balance = common.GetUserBalance(uint64(appId))
	}

	result = make([]map[string]string, 1)
	result[0] = map[string]string{
		"Balance": balance,
		"Error":   "nil",
	}

	metaClusterId := cluster.Annotations["eks.tke.cloud.tencent.com/metacluster"]
	metaCluster := cluster2.MustGetTKECluster(region, metaClusterId)
	duration := time.Duration(timeout) * time.Minute
	metaClientSet, err := cluster2.GetTKEClusterClientSet(region, metaCluster, duration)
	if err != nil {
		return result, err
	}

	eksCluster, err := cluster2.GetEKSCluster(region, cluster.Name)
	if err != nil {
		return result, err
	}
	eksClientSet, err := cluster2.GetEKSClusterClientSet(region, eksCluster, duration)
	if err != nil {
		return result, err
	}

	// kateway(wallaceqian) mock job运行在metacluster集群对应的namespace里，固定名称service-mock-job，唯一独占
	// kateway(wallaceqian) 实际运行前清理之前创建的依赖配置，可能原来老的和最新的配置不一致，确保不影响后续执行。
	common.CleanUpJob(ctx, metaClientSet.K8sCli, cluster.Name, MockJobName)
	common.CleanUpPod(ctx, metaClientSet.K8sCli, cluster.Name, MockJobName)
	eks.CleanUpServiceRBAC(ctx, eksClientSet.K8sCli, cluster.Name)
	metaClientSet.K8sCli.CoreV1().ConfigMaps(cluster.Name).Delete(ctx, eks.MockRBACServiceName, v1.DeleteOptions{})

	// Mock专用 只读RBAC，预检结束时销毁。
	if err := eks.CreateServiceReadOnlyRBAC(ctx, eksClientSet.K8sCli, v1.NamespaceSystem); err != nil {
		return result, err
	}
	err = eks.CreateServiceReadOnlySecretIfNeeded(ctx, eksClientSet.K8sCli, v1.NamespaceSystem)
	if err != nil {
		return result, err
	}
	time.Sleep(time.Second)

	kubeconfig, err := eks.GenServiceReadOnlyKubeconfig(ctx, eksClientSet.K8sCli, v1.NamespaceSystem, cluster.Name)
	if err != nil {
		return result, err
	}
	originConfig, err := metaClientSet.K8sCli.CoreV1().ConfigMaps(cluster.Name).Get(ctx, fmt.Sprintf("%s-config", cluster.Name), v1.GetOptions{})
	if err != nil {
		return result, err
	}
	if value, exist := originConfig.Data["qcloud.conf"]; !exist {
		return result, fmt.Errorf("qcloud.conf not exist.")
	} else {
		kubeconfig.Data["qcloud.conf"] = value
	}
	if _, err = metaClientSet.K8sCli.CoreV1().ConfigMaps(cluster.Name).Create(ctx, kubeconfig, v1.CreateOptions{}); err != nil && !apierrors.IsAlreadyExists(err) {
		return result, err
	}
	defer func() {
		eks.CleanUpServiceRBAC(ctx, eksClientSet.K8sCli, cluster.Name)
		metaClientSet.K8sCli.CoreV1().ConfigMaps(cluster.Name).Delete(ctx, eks.MockRBACServiceName, v1.DeleteOptions{})
	}()

	deploy, err := metaClientSet.K8sCli.AppsV1().Deployments(cluster.Name).List(ctx, v1.ListOptions{
		LabelSelector: fmt.Sprintf("cloud.tencent.com/tke-cluster-id=%s,cloud.tencent.com/tke-component=service-controller", cluster.Name),
	})
	if err != nil {
		return result, err
	}

	if deploy.Items == nil || len(deploy.Items) != 1 {
		return result, fmt.Errorf("Deployment Not One %d.", len(deploy.Items))
	}

	// [qingyangwu] 检查Tag是否跨版本
	cross, err := common2.CheckImageIfCrossVersion(image, &deploy.Items[0])
	if err != nil {
		fmt.Println(err)
	}
	if cross {
		fmt.Println("warning: eks check service controller image tag cross version!")
	}

	for index, container := range deploy.Items[0].Spec.Template.Spec.Containers {
		if container.Name == SERVICE_CONTAINER_NAME {
			mainContainer := deploy.Items[0].Spec.Template.Spec.Containers[index]

			for _, c := range mainContainer.Command { // 不要去更新老的 eks 1.0
				if c == "--enable-clusterip=true" {
					return result, fmt.Errorf("禁止更新开启了 --enable-clusterip=true 的老版本 eks")
				}
			}
		}
	}

	// create job
	fmt.Println("create job")
	if err := common.CleanUpJob(ctx, metaClientSet.K8sCli, cluster.Name, MockJobName); err != nil {
		return result, err
	}

	args := []string{}
	if dryrunSvc {
		args = append(args, "--mock-run=true")
	}
	if dryrunIng {
		args = append(args, "--dry-run-ingress=true")
	}
	job := getOperatorClusterJob(deploy.Items[0], image, args...)

	createdJob, err := metaClientSet.K8sCli.BatchV1().Jobs(cluster.Name).Create(ctx, job, v1.CreateOptions{})
	if err != nil {
		return result, err
	}
	time.Sleep(5 * time.Second)
	defer func() {
		common.CleanUpJob(ctx, metaClientSet.K8sCli, cluster.Name, MockJobName)
		common.CleanUpPod(ctx, metaClientSet.K8sCli, cluster.Name, MockJobName)
	}()
	pod, err := waitJobRunning(ctx, metaClientSet.K8sCli, cluster, createdJob)
	if err != nil {
		fmt.Println("Error")
		return result, err
	}
	// get job logs
	req := metaClientSet.K8sCli.CoreV1().Pods(cluster.Name).GetLogs(pod.Name, &v12.PodLogOptions{
		Container: MockContainerName,
		Follow:    true,
	})

	readCloser, err := req.Stream(ctx)
	if err != nil {
		fmt.Println("Error")
		return result, err
	}
	defer readCloser.Close()
	buffer, readLogError := io.ReadAll(readCloser)
	var lines []string
	var truncateJob bool
	if len(buffer) > 0 {
		lines = strings.Split(string(buffer), "\n")
	}
	if readLogError != nil {
		if strings.Contains(readLogError.Error(), "Client.Timeout or context cancellation while reading body") && len(lines) > 10 {
			truncateJob = true
			fmt.Printf("get pod log stream timeout, try analyzing first %d lines\n", len(lines))
		} else {
			return nil, readLogError
		}
	}

	var exitCode int
	if truncateJob { // Job 还在跑，不等了, 分析前面几分钟的即可
		exitCode = truncateJobExitCode
	} else {
		exitCode, err = waitJobComplete(ctx, metaClientSet.K8sCli, cluster.Name, timeout, pod)
		if err != nil {
			fmt.Println(err)
		}
	}

	res := make([]map[string]string, 0)
	for _, line := range lines {
		if exitCode == 0 || exitCode == MockErrorExitCode || exitCode == truncateJobExitCode { // success or exit with mock
			if strings.HasPrefix(line, "MockError") {
				split := strings.Split(line, "\t")
				mockError := map[string]string{
					"Balance":     balance,
					"ServiceName": split[1],
					"Action":      split[2],
					"Request":     split[3],
					"Reason":      split[4],
					"Error":       "nil",
				}
				res = append(res, mockError)
			}
		} else { // failed
			if strings.Contains(line, "[TencentCloudSDKError]") {
				msgs := strings.Split(line, "[TencentCloudSDKError]")
				if len(msgs) > 1 {
					mockError := map[string]string{
						"Balance": balance,
						"Error":   strings.Join(msgs[1:], ""),
					}
					res = append(res, mockError)
				}
			}
		}
	}

	res = append(res, dryrun.CollectPanicErrors(lines)...)
	res = mockeErrorAnalysis(ctx, res, eksClientSet.K8sCli)
	if exitCode == truncateJobExitCode {
		return nil, readLogError
	}

	return res, nil
}

func mockeErrorAnalysis(ctx context.Context, mockErrors []map[string]string, client kubernetes.Interface) []map[string]string {
	for _, mockError := range mockErrors {
		mockError["Analysis"] = "<None>"

		serviceName := mockError["ServiceName"]
		if !strings.Contains(serviceName, "/") {
			continue
		}
		_, namespace, name := common2.MustParseTypedName(serviceName)
		service, err := client.CoreV1().Services(namespace).Get(ctx, name, v1.GetOptions{})
		if err != nil {
			continue
		}

		if mockError["Action"] == "CreateLoadBalancer" {
			if service.Status.LoadBalancer.Ingress == nil || len(service.Status.LoadBalancer.Ingress) == 0 {
				mockError["Analysis"] = "Ignore"
				continue
			}
		}
	}
	return mockErrors
}

func getOperatorClusterJob(deployment v13.Deployment, image string, args ...string) *v14.Job {
	oldImage := common2.GetControllerImage(deployment)
	argMockOldVersion := fmt.Sprintf("--mock-old-version=%s", common2.GetImageTag(oldImage))
	podTemplateSpec := deployment.Spec.Template
	podTemplateSpec.Spec.RestartPolicy = v12.RestartPolicyNever
	podTemplateSpec.Spec.TopologySpreadConstraints = nil // mock pod 不需要指定拓扑限制，否则在小地域可能导致pod调度失败

	for index, container := range podTemplateSpec.Spec.Containers {
		if container.Name == SERVICE_CONTAINER_NAME {
			podTemplateSpec.Spec.Containers[index].ImagePullPolicy = v12.PullAlways
			podTemplateSpec.Spec.Containers[index].Name = MockContainerName
			podTemplateSpec.Spec.Containers[index].Image = image
			if len(podTemplateSpec.Spec.Containers[index].Args) == 0 {
				podTemplateSpec.Spec.Containers[index].Command = append(podTemplateSpec.Spec.Containers[index].Command, args...)
				if common2.IsSupportArgMockOldVersion(image) {
					podTemplateSpec.Spec.Containers[index].Command = append(podTemplateSpec.Spec.Containers[index].Command, argMockOldVersion)
				}
			} else {
				podTemplateSpec.Spec.Containers[index].Args = append(podTemplateSpec.Spec.Containers[index].Args, args...)
				if common2.IsSupportArgMockOldVersion(image) {
					podTemplateSpec.Spec.Containers[index].Args = append(podTemplateSpec.Spec.Containers[index].Args, argMockOldVersion)
				}
			}
			break
		}
	}

	for index, volume := range podTemplateSpec.Spec.Volumes {
		if volume.Name == "cloudconfig" {
			podTemplateSpec.Spec.Volumes[index] = v12.Volume{
				Name: "cloudconfig",
				VolumeSource: v12.VolumeSource{ConfigMap: &v12.ConfigMapVolumeSource{
					LocalObjectReference: v12.LocalObjectReference{
						Name: eks.MockRBACServiceName,
					},
					DefaultMode: pointer.Int32(420),
					Items: []v12.KeyToPath{
						{
							Key:  "kubeconfig",
							Path: "config",
						},
						{
							Key:  "qcloud.conf",
							Path: "qcloud.conf",
						},
					},
				}},
			}
			break
		}
	}

	ttl := int32(30)
	backOff := int32(0)
	return &v14.Job{
		TypeMeta: v1.TypeMeta{Kind: "Job"},
		ObjectMeta: v1.ObjectMeta{
			Name:      MockJobName,
			Namespace: deployment.Namespace,
		},
		Spec: v14.JobSpec{
			TTLSecondsAfterFinished: &ttl,
			BackoffLimit:            &backOff,
			Template:                podTemplateSpec,
		},
	}
}

func waitJobRunning(ctx context.Context, k8sClient kubernetes.Interface, cluster *platformv1.Cluster, job *v14.Job) (*v12.Pod, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	ctx, cancel := context.WithTimeout(ctx, 10*time.Minute)
	defer cancel()
	ticker := time.NewTicker(time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			return nil, fmt.Errorf("Job Not Running In 10 Minute. %s", cluster.Name)
		case <-ticker.C:
			pods, err := k8sClient.CoreV1().Pods(job.Namespace).List(ctx, v1.ListOptions{
				LabelSelector: fmt.Sprintf("job-name=%s,controller-uid=%s", MockJobName, job.UID),
			})
			if err != nil {
				fmt.Errorf("getpod error. %s", err)
				continue
			}
			if len(pods.Items) == 0 {
				fmt.Errorf("no pod found")
				continue
			}
			phase := pods.Items[0].Status.Phase
			if phase != v12.PodRunning && phase != v12.PodSucceeded {
				fmt.Errorf("pod not running.")
				continue
			}
			return &pods.Items[0], nil
		}
	}
}

func waitJobComplete(ctx context.Context, client kubernetes.Interface, clusterId string, timeout int, pod *v12.Pod) (int, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Minute)
	defer cancel()
	count := 0
	ticker := time.NewTicker(time.Second * 10)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			return OtherErrorExitCode, fmt.Errorf("Cluster time out %s", clusterId)
		case <-ticker.C:
			count++
			updatedPod, err := client.CoreV1().Pods(pod.Namespace).Get(ctx, pod.Name, v1.GetOptions{})
			if err != nil {
				return OtherErrorExitCode, err
			}
			_, status := common.GetPodStatus(updatedPod)
			fmt.Printf("Check cluster %s pod %s/%s status %s\n", clusterId, pod.Namespace, pod.Name, status)
			switch status {
			case "Completed":
				return 0, nil
			case "Pending", "ImagePullBackOff", "ContainerCreating": // Pending\ImagePullBackOff\ContainerCreating三分钟退出
				if count >= 18 {
					return OtherErrorExitCode, err
				}
			case "Failed", "Error", "OOMKilled":
				// return fmt.Errorf("pod failed")
				exitCode := OtherErrorExitCode
				for _, container := range updatedPod.Status.ContainerStatuses {
					if container.Name == MockContainerName {
						if container.State.Terminated != nil {
							exitCode = int(container.State.Terminated.ExitCode)
						}
					}
				}
				// str, _ := json.Marshal(updatedPod.Status.ContainerStatuses)
				// fmt.Println(string(str))
				return exitCode, fmt.Errorf("Pod failed %s/%s status %s exitCode %d\n", pod.Namespace, pod.Name, status, exitCode)
			}
		}
	}
}

func init() {
	ServiceCmd.AddCommand(MockCmd)

	MockCmd.Flags().StringSliceP("cluster", "c", nil, "The cluster instance id")
	MockCmd.Flags().StringSliceP("appId", "a", nil, "The cluster app id")
	MockCmd.Flags().IntP("offset", "o", 0, "cluster offset")
	MockCmd.Flags().IntP("limit", "l", -1, "cluster limit")
	MockCmd.Flags().IntP("worker", "w", 20, "Parallelize workers")
	MockCmd.Flags().Int("timeout", 15, "timeout")
	MockCmd.Flags().Bool("service", true, "start a dry run of services")
	MockCmd.Flags().Bool("ingress", false, "start a dry run of ingresses")
}
