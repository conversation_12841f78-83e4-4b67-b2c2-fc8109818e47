package service

import (
	"bytes"
	"context"
	"io/ioutil"
	"os"
	"time"

	"github.com/golang/glog"
	"github.com/spf13/cobra"
	v12 "k8s.io/api/core/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	platformv1 "tkestack.io/tke/api/platform/v1"

	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	cluster2 "git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/eks/eks"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tracing"
	region2 "git.woa.com/kateway/kateway-server/pkg/util/region"
)

var SaveLogCmd = &cobra.Command{
	Use:   "savelog",
	Short: "service controller publish tool",
	Long:  "service controller publish tool",
	RunE: func(cmd *cobra.Command, args []string) error {
		task := Task{
			TaskInterface: &SaveLogCmdTask{},
			Title: []string{
				"Region", "Cluster", "AppId", "Version", "Error",
			},
		}
		if err := task.Run(cmd, args); err != nil {
			return err
		}
		return nil
	},
}

type SaveLogCmdTask struct {
	Task
}

// eks.tke.cloud.tencent.com/metacluster: cls-g0zvpnr3
// eks.tke.cloud.tencent.com/owner-uin: "2252646423"
// eks.tke.cloud.tencent.com/product-name: tkex-csig-ziyan
// eks.tke.cloud.tencent.com/region: ap-tianjin
func (*SaveLogCmdTask) Do(cmd *cobra.Command, cluster *platformv1.Cluster) []map[string]string {
	span, ctx := jaeger.StartSpanFromContext(cmd.Context(), jaeger.WithOperationName("eks.service.savelog"))
	defer span.Finish()

	tracing.Platformv1Cluster(span, cluster)

	errorResult := []map[string]string{
		{
			"Region":  "<None>",
			"Cluster": cluster.Name,
			"AppId":   cluster.Spec.TenantID,
			"Version": cluster.Spec.Version,
		},
	}

	region, exist := cluster.Annotations["eks.tke.cloud.tencent.com/region"]
	if !exist {
		errorResult[0]["Error"] = fmt.Sprintf("%s. eks.tke.cloud.tencent.com/region not exist.\n", cluster.Name)
		return errorResult
	}
	regionInfo := region2.Get(region)
	errorResult[0]["Region"] = regionInfo.Alias

	logPath, _ := cmd.Flags().GetString("podlogpath")

	err := saveLog(ctx, cluster, logPath)
	if err != nil {
		errorResult[0]["Error"] = err.Error()
		return errorResult
	}
	return errorResult
}

func saveLog(ctx context.Context, cluster *platformv1.Cluster, logPath string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	if err := eks.CheckClusterStatus(cluster); err != nil {
		return err
	}

	// create the clientset
	region := cluster.Annotations["eks.tke.cloud.tencent.com/region"]
	clusterId := cluster.Name
	metaClusterId := cluster.Annotations["eks.tke.cloud.tencent.com/metacluster"]
	metaCluster := cluster2.MustGetTKECluster(region, metaClusterId)
	clientSet, err := cluster2.GetTKEClusterClientSet(region, metaCluster)
	if err != nil {
		return err
	}

	logDir := fmt.Sprintf("%s/%s/%s", logPath, region, time.Now().Format("2006-01-02"))
	if err := os.MkdirAll(logDir, os.ModePerm); err != nil {
		fmt.Printf("Cluster %s make log dir failed: %v\n", cluster.Name, err)
	}

	serviceControllerDeployment, err := clientSet.K8sCli.AppsV1().Deployments(clusterId).Get(ctx, fmt.Sprintf("%s-service-controller", clusterId), v1.GetOptions{})
	if err != nil {
		glog.Errorf("Cluster %s version %s get service controller deployment error, err:%v", cluster.Name, cluster.Spec.Version, err)
		return err
	}

	listOptions := v1.ListOptions{
		LabelSelector: labels.SelectorFromSet(serviceControllerDeployment.Spec.Template.Labels).String(),
	}
	pods, err := clientSet.K8sCli.CoreV1().Pods(clusterId).List(ctx, listOptions)
	if err != nil {
		return err
	}

	podLogOption := v12.PodLogOptions{
		Container: SERVICE_CONTAINER_NAME,
	}
	for _, pod := range pods.Items {
		req := clientSet.K8sCli.CoreV1().Pods(clusterId).GetLogs(pod.Name, &podLogOption)

		readCloser, err := req.Stream(ctx)
		if err != nil {
			return err
		}
		buf := new(bytes.Buffer)
		buf.ReadFrom(readCloser)

		logFile := fmt.Sprintf("%s/%s_%s.log", logDir, cluster.Name, pod.Name)
		if err = ioutil.WriteFile(logFile, buf.Bytes(), os.ModePerm); err != nil {
			readCloser.Close()
			return err
		}
		readCloser.Close()
	}
	return nil
}

func init() {
	// ServiceCmd.AddCommand(SaveLogCmd)

	SaveLogCmd.Flags().StringSliceP("cluster", "c", nil, "The cluster instance id")
	SaveLogCmd.Flags().StringSliceP("appId", "a", nil, "The cluster app id")
	SaveLogCmd.Flags().IntP("offset", "o", 0, "cluster offset")
	SaveLogCmd.Flags().IntP("limit", "l", -1, "cluster limit")
	SaveLogCmd.Flags().IntP("worker", "w", 20, "Parallelize workers")
	SaveLogCmd.Flags().StringP("podlogpath", "p", "ekslogs", "log path to save log")
}
