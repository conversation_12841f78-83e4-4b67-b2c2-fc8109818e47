package service

import (
	"context"
	"encoding/json"
	"strings"
	"time"

	"github.com/samber/lo"
	"github.com/spf13/cobra"
	platformv1 "tkestack.io/tke/api/platform/v1"

	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/cmd/kops/common"
	cluster2 "git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
	common2 "git.woa.com/kateway/kateway-server/cmd/kops/kops/common"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/eks/eks"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tracing"
	"git.woa.com/kateway/kateway-server/pkg/dryrun"
	region2 "git.woa.com/kateway/kateway-server/pkg/util/region"
)

var UpdateAutoCmd = &cobra.Command{
	Use:   "update_auto",
	Short: "service controller publish tool",
	Long:  "service controller publish tool",
	RunE: func(cmd *cobra.Command, args []string) error {
		task := Task{
			TaskInterface: &UpdateAutoCmdTask{},
			Title: []string{
				"Region", "Cluster", "AppId", "Version", "Error",
			},
		}
		if err := task.Run(cmd, args); err != nil {
			return err
		}
		return nil
	},
}

type UpdateAutoCmdTask struct {
	Task

	ImageTag string
	Timeout  int
	Force    bool
}

// eks.tke.cloud.tencent.com/metacluster: cls-g0zvpnr3
// eks.tke.cloud.tencent.com/owner-uin: "2252646423"
// eks.tke.cloud.tencent.com/product-name: tkex-csig-ziyan
// eks.tke.cloud.tencent.com/region: ap-tianjin
func (t *UpdateAutoCmdTask) Do(cmd *cobra.Command, cluster *platformv1.Cluster) []map[string]string {
	span, ctx := jaeger.StartSpanFromContext(cmd.Context(), jaeger.WithOperationName("eks.service.update_auto"))
	defer span.Finish()
	start := time.Now()

	tracing.Platformv1Cluster(span, cluster)

	errorResult := []map[string]string{
		{
			"Region":  "<None>",
			"Cluster": cluster.Name,
			"AppId":   cluster.Spec.TenantID,
			"Version": cluster.Spec.Version,
		},
	}

	region, exist := cluster.Annotations["eks.tke.cloud.tencent.com/region"]
	if !exist {
		errorResult[0]["Error"] = fmt.Sprintf("%s. eks.tke.cloud.tencent.com/region not exist.\n", cluster.Name)
		return errorResult
	}
	regionInfo := region2.Get(region)
	errorResult[0]["Region"] = regionInfo.Alias

	imageTag, _ := cmd.Flags().GetString("imageTag")
	timeout, _ := cmd.Flags().GetInt("timeout")
	force, _ := cmd.Flags().GetBool("force")
	t.ImageTag = imageTag
	t.Timeout = timeout
	t.Force = force

	if t.ImageTag == "" {
		errorResult[0]["Error"] = fmt.Sprintf("ImageTag Not Setting\n")
		return errorResult
	}
	// [qingyangwu] 变更（更新镜像）
	user, _ := cmd.Flags().GetString("user")
	token, _ := cmd.Flags().GetString("token")
	releaseRecord := &model.ReleaseAudit{
		Date:      time.Now(),
		Region:    region,
		ClusterID: cluster.Name,
		ImageTag:  t.ImageTag,
		Component: "service",
		Status:    true,
		Publisher: user,
		Token:     token,
		CreatedAt: start,
		UpdatedAt: time.Now(),
	}
	defer func() {
		releaseRecord.FinishedAt = time.Now()
		// [qingyangwu] 更新数据库发布审计表
		if err := common2.ReleaseAuditService.Update(releaseRecord); err != nil {
			errorResult[0]["Error"] = err.Error()
		}
	}()
	serviceStatus, isModified, err := t.updateAuto(cmd, ctx, cluster, releaseRecord)
	releaseRecord.Status = isModified
	if err != nil {
		if common2.IsErrorMessage(err) {
			releaseRecord.Status = false
		}
		releaseRecord.Reason = lo.ToPtr(err.Error())
		releaseRecord.FinishedAt = time.Now()
		// [qingyangwu] 更新数据库发布审计表
		if err2 := common2.ReleaseAuditService.Update(releaseRecord); err2 != nil {
			errorResult[0]["Error"] = err2.Error()
			return errorResult
		}
		errorResult[0]["Error"] = err.Error()
		return errorResult
	}

	results := make([]map[string]string, len(serviceStatus))
	for index := range serviceStatus {
		results[index] = map[string]string{
			"Region":   regionInfo.Alias,
			"Cluster":  cluster.Name,
			"AppId":    cluster.Spec.TenantID,
			"Version":  cluster.Spec.Version,
			"ImageTag": imageTag,
		}
	}
	return results
}

func (t *UpdateAutoCmdTask) updateAuto(cmd *cobra.Command, ctx context.Context, cluster *platformv1.Cluster, releaseRecord *model.ReleaseAudit) (result []map[string]string, isModified bool, err error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer func() {
		jaeger.LogError(span, err)
		span.LogKV("result", result)
		span.Finish()
	}()

	if err := eks.CheckClusterStatus(cluster); err != nil {
		return nil, false, err
	}
	if eks.CheckIfNeedSkip(ctx, cluster) {
		return nil, false, fmt.Errorf("skip cluster")
	}

	region := cluster.Annotations["eks.tke.cloud.tencent.com/region"]
	regionInfo := region2.Get(region)
	if regionInfo == nil {
		return nil, false, fmt.Errorf("%s. eks.tke.cloud.tencent.com/region not exist.\n", cluster.Name)
	}

	result = make([]map[string]string, 1)
	result[0] = map[string]string{
		"Region":   regionInfo.Alias,
		"Cluster":  cluster.Name,
		"AppId":    cluster.Spec.TenantID,
		"Version":  cluster.Spec.Version,
		"ImageTag": t.ImageTag,
		"Error":    "nil",
	}

	metaClusterId := cluster.Annotations["eks.tke.cloud.tencent.com/metacluster"]
	metaCluster := cluster2.MustGetTKECluster(region, metaClusterId)
	metaClientSet, err := cluster2.GetTKEClusterClientSet(region, metaCluster)
	if err != nil {
		return result, false, err
	}

	// 发布前检查
	ccrRegion, exist := common.CcrRegionMap[regionInfo.Alias]
	if !exist {
		return result, false, fmt.Errorf("Region %s ccr not configed", regionInfo.Alias)
	}
	image := fmt.Sprintf("%s%s:%s", ccrRegion, common.ServiceControllerImageRepo, t.ImageTag)

	serviceControllerDeployment := cluster2.GetMixedDeployment(ctx, metaClientSet.K8sCli, cluster.Name, fmt.Sprintf("%s-service-controller", cluster.Name))

	// 0. 检查集群是否已经为当前发布版本
	fmt.Printf("Cluster: %s Step PreCheck Start.\n", cluster.Name)
	if serviceControllerDeployment == nil {
		return result, false, fmt.Errorf("Deployment Not Exist.")
	}

	// [qingyangwu] 抓取源镜像Tag
	releaseRecord.SourceImageTag = common2.GetImageTag(serviceControllerDeployment.GetContainerImage(SERVICE_CONTAINER_NAME))

	if serviceControllerDeployment.GetReplicas() == 0 {
		if isModified, err := setImage(ctx, cluster, image, nil); err != nil {
			return result, isModified, fmt.Errorf("Error (0 Replicas), %v", err.Error())
		} else {
			return result, isModified, fmt.Errorf("Success (0 Replicas)")
		}
	}
	fmt.Printf("Cluster: %s image: %s => %s\n", cluster.Name, serviceControllerDeployment.GetContainerImage(SERVICE_CONTAINER_NAME), image)
	if serviceControllerDeployment.GetContainerImage(SERVICE_CONTAINER_NAME) == image {
		return result, false, fmt.Errorf("Success (Updated)")
	}

	if t.Force {
		fmt.Printf("Cluster: %s Step Force Update Start.\n", cluster.Name)
		if isModified, err := setImage(ctx, cluster, image, nil); err != nil {
			return result, isModified, fmt.Errorf("Error (Force Updated) %v", err.Error())
		} else {
			return result, isModified, fmt.Errorf("Success (Force Updated)")
		}
	}

	// 1. 进行预检
	// 1.2 进行预检
	fmt.Printf("Cluster: %s Step DryRun Start.\n", cluster.Name)
	mockErrors, err := MockRun(ctx, cluster, image, t.Timeout, true, nil)
	if err != nil {
		return result, false, fmt.Errorf("Cluster: %s MockRun Error, %s", cluster.Name, err.Error())
	}

	fmt.Printf("Cluster: %s Step service DryRun origin Result: %s", cluster.Name, lo.Must(json.Marshal(mockErrors)))

	if err = dryrun.BuildErrorFromResult(mockErrors); err != nil {
		return result, false, fmt.Errorf("dryrun of cluster %q failed: %w", cluster.Name, err)
	}

	// 2. 进行发布前步骤
	// 2.1 开启静默
	// 2.2 保存日志
	// if err := saveLog(ctx, cluster, "ekslogs"); err != nil {
	// 	return result, fmt.Errorf("Cluster: %s Save Log Error. %s", cluster.Name, err.Error())
	// }
	// [qingyangwu] 预检通过，开启后检
	// 创建一个只执行一次的异步后检任务
	if skipPostCheck, err := cmd.Flags().GetBool("skipPostCheck"); err == nil && !skipPostCheck {
		if err := common2.StartOneCheckBeforeRelease(ctx, cluster.Name); err != nil {
			return result, false, fmt.Errorf("cluster: %s Start Check Before Release Error. %s", cluster.Name, err.Error())
		}
	}
	// 3. 更新镜像
	// 3.1 更新镜像
	fmt.Printf("Cluster: %s Step Update Start.\n", cluster.Name)
	isModified, err = setImage(ctx, cluster, image, nil)
	if err != nil {
		return result, isModified, fmt.Errorf("Cluster: %s Upgrade Error. %s", cluster.Name, err.Error())
	}
	// 再次开始巡检或者对已有巡检任务考虑续期
	// 查看是否已经有巡检任务
	if skipPostCheck, err := cmd.Flags().GetBool("skipPostCheck"); err == nil && !skipPostCheck {
		times, err := cmd.Flags().GetInt("checkTimes")
		if err != nil {
			return result, isModified, err
		}
		interval, err := cmd.Flags().GetString("checkInterval")
		if err != nil {
			return result, isModified, err
		}
		dur, err := time.ParseDuration(interval)
		if err != nil {
			return result, isModified, err
		}
		taskID, err := common2.StartInspectionTaskAfterRelease(ctx, cluster.Name, dur, times)
		if err != nil {
			return result, isModified, fmt.Errorf("cluster: %s Start Check After Release Error. %s", cluster.Name, err.Error())
		}
		releaseRecord.TaskID = taskID
	}

	// 4. 检查、清理
	time.Sleep(5 * time.Second)
	fmt.Printf("Cluster: %s Step Check Status Start.\n", cluster.Name)
	if err := check(ctx, cluster, serviceControllerDeployment.GetReplicas(), image); err != nil {
		return result, isModified, fmt.Errorf("Cluster: %s Check Pod Status Error. %s", cluster.Name, err.Error())
	}

	result[0]["Error"] = "Success"
	return result, isModified, nil
}

func check(ctx context.Context, cluster *platformv1.Cluster, replicas int32, image string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	ctx, cancel := context.WithTimeout(ctx, 300*time.Second)
	defer cancel()
	ticker := time.NewTicker(3 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			statuses, err := getPodStatus(ctx, cluster)
			if err != nil {
				return err
			}
			var statusString []string
			for _, status := range statuses {
				if status["Image"] == image {
					statusString = append(statusString, status["Status"])
				}
			}
			return fmt.Errorf("pod status error. %s", strings.Join(statusString, ","))
		case <-ticker.C:
			statuses, err := getPodStatus(ctx, cluster)
			if err != nil {
				continue
			}
			var readyCount int32 = 0
			for _, status := range statuses {
				if status["Image"] == image && status["Status"] == "Running" {
					readyCount = readyCount + 1
				}
			}
			if readyCount == replicas {
				return nil
			}
		}
	}
}

func init() {
	ServiceCmd.AddCommand(UpdateAutoCmd)

	UpdateAutoCmd.Flags().StringSliceP("cluster", "c", nil, "The cluster instance id")
	UpdateAutoCmd.Flags().StringSliceP("appId", "a", nil, "The cluster app id")
	UpdateAutoCmd.Flags().IntP("offset", "o", 0, "cluster offset")
	UpdateAutoCmd.Flags().IntP("limit", "l", -1, "cluster limit")
	UpdateAutoCmd.Flags().IntP("worker", "w", 20, "Parallelize workers")

	UpdateAutoCmd.Flags().Bool("force", false, "force")
	UpdateAutoCmd.Flags().Int("timeout", 15, "timeout")
}
