package ingress

import (
	"context"
	"strconv"

	"github.com/golang/glog"
	"github.com/spf13/cobra"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	platformv1 "tkestack.io/tke/api/platform/v1"

	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/cmd/kops/common"
	cluster2 "git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
	common2 "git.woa.com/kateway/kateway-server/cmd/kops/kops/common"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/eks/eks"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tracing"
	region2 "git.woa.com/kateway/kateway-server/pkg/util/region"
)

var GetPodCmd = &cobra.Command{
	Use:   "getpod",
	Short: "ingress controller publish tool",
	Long:  "ingress controller publish tool",
	RunE: func(cmd *cobra.Command, args []string) error {
		task := Task{
			TaskInterface: &GetPodCmdTask{},
			Title: []string{
				"Region", "Cluster", "AppId", "Version", "Image", "Name", "PodImage", "Sha256", "Status", "Restart", "Error",
			},
		}
		if err := task.Run(cmd, args); err != nil {
			return err
		}
		return nil
	},
}

type GetPodCmdTask struct {
	Task
}

// eks.tke.cloud.tencent.com/metacluster: cls-g0zvpnr3
// eks.tke.cloud.tencent.com/owner-uin: "2252646423"
// eks.tke.cloud.tencent.com/product-name: tkex-csig-ziyan
// eks.tke.cloud.tencent.com/region: ap-tianjin
func (*GetPodCmdTask) Do(cmd *cobra.Command, cluster *platformv1.Cluster) []map[string]string {
	span, ctx := jaeger.StartSpanFromContext(cmd.Context(), jaeger.WithOperationName("eks.ingress.getpod"))
	defer span.Finish()

	tracing.Platformv1Cluster(span, cluster)

	errorResult := []map[string]string{
		{
			"Region":  "<None>",
			"Cluster": cluster.Name,
			"AppId":   cluster.Spec.TenantID,
			"Version": cluster.Spec.Version,
		},
	}

	region, exist := cluster.Annotations["eks.tke.cloud.tencent.com/region"]
	if !exist {
		errorResult[0]["Error"] = fmt.Sprintf("%s. eks.tke.cloud.tencent.com/region not exist.\n", cluster.Name)
		return errorResult
	}
	regionInfo := region2.Get(region)
	errorResult[0]["Region"] = regionInfo.Alias

	serviceStatus, err := getPodStatus(ctx, cluster)
	if err != nil {
		errorResult[0]["Error"] = err.Error()
		return errorResult
	}

	results := make([]map[string]string, len(serviceStatus))
	if serviceStatus != nil {
		for index := range serviceStatus {
			results[index] = map[string]string{
				"Region":   regionInfo.Alias,
				"Cluster":  cluster.Name,
				"AppId":    cluster.Spec.TenantID,
				"Version":  cluster.Spec.Version,
				"Name":     serviceStatus[index]["Name"],
				"Image":    common2.GetImageTag(serviceStatus[index]["Image"]),
				"PodImage": common2.GetImageTag(serviceStatus[index]["PodImage"]),
				"Sha256":   common2.GetImageTag(serviceStatus[index]["PodImageId"]),
				"Status":   serviceStatus[index]["Status"],
				"Restart":  serviceStatus[index]["Restart"],
			}
		}
	}
	return results
}

func getPodStatus(ctx context.Context, cluster *platformv1.Cluster) ([]map[string]string, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	if err := eks.CheckClusterStatus(cluster); err != nil {
		return nil, err
	}
	// create the clientset
	region := cluster.Annotations["eks.tke.cloud.tencent.com/region"]
	clusterId := cluster.Name
	metaClusterId := cluster.Annotations["eks.tke.cloud.tencent.com/metacluster"]
	metaCluster := cluster2.MustGetTKECluster(region, metaClusterId)
	clientSet, err := cluster2.GetTKEClusterClientSet(region, metaCluster)
	if err != nil {
		return nil, err
	}

	serviceControllerDeployment, err := clientSet.K8sCli.AppsV1().Deployments(clusterId).Get(ctx, fmt.Sprintf("%s-ingress-controller", clusterId), v1.GetOptions{})
	if err != nil {
		glog.Errorf("Cluster %s version %s get ingress controller deployment error, err:%v", cluster.Name, cluster.Spec.Version, err)
		return nil, err
	}
	deploymentImage := "<None>"
	for _, container := range serviceControllerDeployment.Spec.Template.Spec.Containers {
		if container.Name == INGRESS_CONTAINER_NAME {
			// return container.Image, nil
			deploymentImage = container.Image
		}
	}

	listOptions := v1.ListOptions{
		LabelSelector: labels.SelectorFromSet(serviceControllerDeployment.Spec.Template.Labels).String(),
	}
	pods, err := clientSet.K8sCli.CoreV1().Pods(clusterId).List(ctx, listOptions)
	if err != nil {
		return nil, err
	}
	serviceStatuses := make([]map[string]string, 0)
	for _, pod := range pods.Items {
		for _, container := range pod.Spec.Containers {
			if container.Name == INGRESS_CONTAINER_NAME {
				restart, reason := common.GetPodStatus(&pod)
				imageId := common2.GetPodSha256(&pod)
				serviceStatuses = append(serviceStatuses, map[string]string{
					"Name":       pod.Name,
					"Image":      deploymentImage,
					"PodImage":   container.Image,
					"PodImageId": imageId,
					"Status":     reason,
					"Restart":    strconv.Itoa(restart),
				})
			}
		}
	}
	return serviceStatuses, nil
}

func init() {
	IngressCmd.AddCommand(GetPodCmd)

	GetPodCmd.Flags().StringSliceP("cluster", "c", nil, "The cluster instance id")
	GetPodCmd.Flags().StringSliceP("appId", "a", nil, "The cluster app id")
	GetPodCmd.Flags().IntP("offset", "o", 0, "cluster offset")
	GetPodCmd.Flags().IntP("limit", "l", -1, "cluster limit")
	GetPodCmd.Flags().IntP("worker", "w", 20, "Parallelize workers")
	GetPodCmd.Flags().StringP("logpath", "p", "", "log path to save log")
}
