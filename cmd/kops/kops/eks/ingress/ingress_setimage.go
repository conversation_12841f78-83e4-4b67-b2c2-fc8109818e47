package ingress

import (
	"context"
	"strings"
	"time"

	go_version "github.com/hashicorp/go-version"
	"github.com/samber/lo"
	"github.com/spf13/cobra"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	platformv1 "tkestack.io/tke/api/platform/v1"

	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/cmd/kops/common"
	cluster2 "git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
	common2 "git.woa.com/kateway/kateway-server/cmd/kops/kops/common"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/eks/eks"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/service/controller"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tracing"
	region2 "git.woa.com/kateway/kateway-server/pkg/util/region"
)

var SetImageCmd = &cobra.Command{
	Use:   "setimage",
	Short: "ingress controller publish tool",
	Long:  "ingress controller publish tool",
	RunE: func(cmd *cobra.Command, args []string) error {
		task := Task{
			TaskInterface: &SetImageCmdTask{},
			Title: []string{
				"Region", "Cluster", "AppId", "Version", "Error",
			},
		}
		if err := task.Run(cmd, args); err != nil {
			return err
		}
		return nil
	},
}

type SetImageCmdTask struct {
	Task
}

// eks.tke.cloud.tencent.com/metacluster: cls-g0zvpnr3
// eks.tke.cloud.tencent.com/owner-uin: "2252646423"
// eks.tke.cloud.tencent.com/product-name: tkex-csig-ziyan
// eks.tke.cloud.tencent.com/region: ap-tianjin
func (*SetImageCmdTask) Do(cmd *cobra.Command, cluster *platformv1.Cluster) []map[string]string {
	span, ctx := jaeger.StartSpanFromContext(cmd.Context(), jaeger.WithOperationName("eks.ingress.setimage"))
	defer span.Finish()
	start := time.Now()

	tracing.Platformv1Cluster(span, cluster)

	errorResult := []map[string]string{
		{
			"Region":  "<None>",
			"Cluster": cluster.Name,
			"AppId":   cluster.Spec.TenantID,
			"Version": cluster.Spec.Version,
		},
	}

	region, exist := cluster.Annotations["eks.tke.cloud.tencent.com/region"]
	if !exist {
		errorResult[0]["Error"] = fmt.Sprintf("%s. eks.tke.cloud.tencent.com/region not exist.\n", cluster.Name)
		return errorResult
	}
	regionInfo := region2.Get(region)
	errorResult[0]["Region"] = regionInfo.Alias

	imageTag, _ := cmd.Flags().GetString("imageTag")
	if imageTag == "" {
		errorResult[0]["Error"] = fmt.Sprintf("ImageTag Not Setting\n")
		return errorResult
	}

	ccrDomain, exist := common.CcrRegionMap[regionInfo.Alias]
	if !exist {
		errorResult[0]["Error"] = fmt.Sprintf("Region %s ccr not configed", region)
		return errorResult
	}

	// [qingyangwu] 变更（更新镜像）
	user, _ := cmd.Flags().GetString("user")
	token, _ := cmd.Flags().GetString("token")
	releaseRecord := &model.ReleaseAudit{
		Date:      time.Now(),
		Region:    region,
		ClusterID: cluster.Name,
		ImageTag:  imageTag,
		Component: "ingress",
		Status:    true,
		Publisher: user,
		Token:     token,
		CreatedAt: start,
		UpdatedAt: time.Now(),
	}

	defer func() {
		releaseRecord.FinishedAt = time.Now()
		// [qingyangwu] 更新数据库发布审计表
		if err := common2.ReleaseAuditService.Update(releaseRecord); err != nil {
			errorResult[0]["Error"] = err.Error()
		}
	}()
	// 执行一次健康检查，获取变更前的集群健康数据
	if skipPostCheck, err := cmd.Flags().GetBool("skipPostCheck"); err == nil && !skipPostCheck {
		if err := common2.StartOneCheckBeforeRelease(ctx, cluster.Name); err != nil {
			errorResult[0]["Error"] = err.Error() // 记录错误
			releaseRecord.Status = false          // 变更失败
			return errorResult                    // 中断变更
		}
	}

	image := fmt.Sprintf("%s%s:%s", ccrDomain, common.IngressControllerImageRepo, imageTag)
	isModified, err := setImage(ctx, cluster, image, lo.Must(cmd.Flags().GetBool("force")), releaseRecord)
	releaseRecord.Status = isModified
	if err != nil {
		releaseRecord.Reason = lo.ToPtr(err.Error())
		releaseRecord.FinishedAt = time.Now()
		errorResult[0]["Error"] = err.Error()
		return errorResult
	}
	if skipPostCheck, err := cmd.Flags().GetBool("skipPostCheck"); err == nil && !skipPostCheck {
		times, err := cmd.Flags().GetInt("checkTimes")
		if err != nil {
			errorResult[0]["Error"] = err.Error()
			return errorResult
		}
		interval, err := cmd.Flags().GetString("checkInterval")
		if err != nil {
			errorResult[0]["Error"] = err.Error()
			return errorResult
		}
		dur, err := time.ParseDuration(interval)
		if err != nil {
			errorResult[0]["Error"] = err.Error()
			return errorResult
		}
		taskID, err := common2.StartInspectionTaskAfterRelease(ctx, cluster.Name, dur, times)
		if err != nil {
			errorResult[0]["Error"] = err.Error()
			return errorResult
		}
		releaseRecord.TaskID = taskID
	}
	// No Error
	return errorResult
}

func setImage(ctx context.Context, cluster *platformv1.Cluster, image string, force bool, releaseRecord *model.ReleaseAudit) (bool, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	if err := eks.CheckClusterStatus(cluster); err != nil {
		return false, err
	}

	if eks.CheckIfNeedSkip(ctx, cluster) {
		return false, fmt.Errorf("skip cluster")
	}

	if !force {
		cls, err := model.NewClusterFromEKS(cluster)
		if err != nil {
			return false, err
		}

		instance, err := controller.GetByCluster(ctx, cls)
		if err != nil {
			return false, err
		}

		version := strings.Split(image, ":")[1]

		if err := controller.CheckIngressUpgrade(ctx, version, instance.Service()); err != nil {
			return false, err
		}
	}

	// create the clientset
	region := cluster.Annotations["eks.tke.cloud.tencent.com/region"]
	clusterId := cluster.Name
	metaClusterId := cluster.Annotations["eks.tke.cloud.tencent.com/metacluster"]
	metaCluster := cluster2.MustGetTKECluster(region, metaClusterId)
	clientSet, err := cluster2.GetTKEClusterClientSet(region, metaCluster)
	if err != nil {
		return false, err
	}

	ingressControllerDeployment, err := clientSet.K8sCli.AppsV1().Deployments(clusterId).Get(ctx, fmt.Sprintf("%s-ingress-controller", clusterId), v1.GetOptions{})
	if err != nil {
		return false, fmt.Errorf("Cluster %s version %s get ingress controller deployment error, err:%v", cluster.Name, cluster.Spec.Version, err)
	}

	semverBase, _ := go_version.NewSemver("v2.0.0")
	for index, container := range ingressControllerDeployment.Spec.Template.Spec.Containers {
		if container.Name == INGRESS_CONTAINER_NAME {
			splited := strings.Split(ingressControllerDeployment.Spec.Template.Spec.Containers[index].Image, ":")
			if len(splited) == 2 {
				if releaseRecord != nil {
					releaseRecord.SourceImageTag = splited[1]
				}
				semverCurrent, err := go_version.NewSemver(splited[1])
				if err != nil {
					return false, err
				}
				if !semverCurrent.GreaterThanOrEqual(semverBase) {
					return false, fmt.Errorf("Skip_LessThen_v2.0.0")
				}
			}
		}
	}

	isModified := false
	for index, container := range ingressControllerDeployment.Spec.Template.Spec.Containers {
		if container.Name == INGRESS_CONTAINER_NAME {
			if container.Image != image {
				ingressControllerDeployment.Spec.Template.Spec.Containers[index].Image = image
				isModified = true
			}
		}
	}

	for index, hostAlias := range ingressControllerDeployment.Spec.Template.Spec.HostAliases {
		if hostAlias.IP != "************" {
			continue
		}
		if len(hostAlias.Hostnames) == 21 {
			continue
		}
		ingressControllerDeployment.Spec.Template.Spec.HostAliases[index].Hostnames = []string{
			"cbs.tencentcloudapi.com",
			"cvm.tencentcloudapi.com",
			"tke.tencentcloudapi.com",
			"cis.tencentcloudapi.com",
			"clb.tencentcloudapi.com",
			"cbs.internal.tencentcloudapi.com",
			"cvm.internal.tencentcloudapi.com",
			"tke.internal.tencentcloudapi.com",
			"cis.internal.tencentcloudapi.com",
			"clb.internal.tencentcloudapi.com",
			"tag.internal.tencentcloudapi.com",
			"vpc.internal.tencentcloudapi.com",
			"cbs.test.tencentcloudapi.com",
			"cvm.test.tencentcloudapi.com",
			"tke.test.tencentcloudapi.com",
			"cis.test.tencentcloudapi.com",
			"clb.test.tencentcloudapi.com",
			"tcb.test.tencentcloudapi.com",
			"sts.test.tencentcloudapi.com",
			"tag.test.tencentcloudapi.com",
			"vpc.test.tencentcloudapi.com",
		}
		isModified = true
	}

	if isModified {
		if _, err := clientSet.K8sCli.AppsV1().Deployments(clusterId).Update(ctx, ingressControllerDeployment, v1.UpdateOptions{}); err != nil {
			return false, err
		}
		return true, nil
	}
	return false, nil
}

func init() {
	IngressCmd.AddCommand(SetImageCmd)

	SetImageCmd.Flags().StringSliceP("cluster", "c", nil, "The cluster instance id")
	SetImageCmd.Flags().StringSliceP("appId", "a", nil, "The cluster app id")
	SetImageCmd.Flags().IntP("offset", "o", 0, "cluster offset")
	SetImageCmd.Flags().IntP("limit", "l", -1, "cluster limit")
	SetImageCmd.Flags().IntP("worker", "w", 20, "Parallelize workers")
	SetImageCmd.Flags().BoolP("force", "f", false, "force set image")
}
