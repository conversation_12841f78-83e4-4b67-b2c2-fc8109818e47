package model

import (
	"time"
)

type Kateway struct {
	Date        string `gorm:"<-:create"`
	Type        string `gorm:"<-:create"`
	ClusterID   string `gorm:"<-:create"`
	ClusterType string `gorm:"<-:create"`
	Appid       string `gorm:"<-:create"`
	Region      string `gorm:"<-:create"`

	Image   string
	ImageID string
	Version string

	State    string
	Restart  int32
	Replicas int32

	CheckAt    time.Time
	CheckCost  string
	CheckError *string

	MockAt          time.Time
	MockCost        string
	MockError       *string
	MockErrorDetail *string
}
