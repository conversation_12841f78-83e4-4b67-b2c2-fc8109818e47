package model

import (
	"time"

	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	corev1 "k8s.io/api/core/v1"
)

type LeakingClb struct {
	ID uint `gorm:"primarykey"`

	Date string `gorm:"<-:create;type:date"`

	Uin    string `gorm:"<-:create"`
	Appid  string `gorm:"<-:create"`
	Region string `gorm:"<-:create"`

	LB            string
	LBName        string
	DeleteProtect bool
	Reason        string
	ClusterID     string
	ClusterName   string
	Namespace     string
	ServiceName   string
	CanDelete     bool
	CreatedAt     time.Time

	Service          *corev1.Service        `gorm:"serializer:json"`
	LoadBalancer     *clb.LoadBalancer      `gorm:"serializer:json"`
	ListenerBackends []*clb.ListenerBackend `gorm:"serializer:json"`
}

func (LeakingClb) TableName() string {
	return "leaking_clb"
}
