package model

import (
	"fmt"
	"strconv"
	"strings"
	"sync"
	"testing"
	"time"
)

func BenchmarkBuildCompositeKey(b *testing.B) {
	region := "shanghai"
	uin := "123456"
	api := "api.login"

	b.ReportAllocs()
	b.<PERSON>set<PERSON>imer()
	for i := 0; i < b.N; i++ {
		buildCompositeKey(region, uin, api)
	}
}

func BenchmarkTakeSingleKey(b *testing.B) {
	limiter := &GlobalRateLimiter{
		RateLimit: 1000000, // 设置高频率避免阻塞
	}
	b.ReportAllocs()
	b.ResetTimer()
	b.<PERSON>(func(pb *testing.PB) {
		for pb.Next() {
			limiter.Take("region", "123", "api1")
		}
	})
}

func BenchmarkTakeMultipleKeys(b *testing.B) {
	limiter := &GlobalRateLimiter{
		RateLimit: 1000000,
	}

	keys := make([]string, 1000)
	for i := range keys {
		keys[i] = "region" + strconv.Itoa(i%10) + ":uin" + strconv.Itoa(i) + ":api" + strconv.Itoa(i%5)
	}

	var counter int
	b.ReportAllocs()
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			// 轮询使用不同的key
			k := keys[counter%len(keys)]
			parts := strings.Split(k, ":")
			uin := parts[1][3:]
			limiter.Take(parts[0], uin, parts[2])
			counter++
		}
	})
}

func BenchmarkConcurrentMixedOperations(b *testing.B) {
	limiter := &GlobalRateLimiter{
		RateLimit: 1000000,
	}

	var wg sync.WaitGroup
	workers := 100

	b.ResetTimer()
	for i := 0; i < workers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			for n := 0; n < b.N/workers; n++ {
				// 混合不同region和api
				region := "region" + strconv.Itoa(workerID%5)
				uin := strconv.Itoa(100000 + workerID + n)
				api := "api" + strconv.Itoa(n%3)
				limiter.Take(region, uin, api)
			}
		}(i)
	}
	wg.Wait()
}

func TestRateLimiter(t *testing.T) {
	t.Run("基础速率限制", func(t *testing.T) {
		t.Parallel()
		limiter := NewGlobalRateLimiter(100) // 100 requests/sec
		start := time.Now()
		var count int
		for time.Since(start) < time.Second {
			limiter.Take("ap-guangzhou", "123456", "CLB/CreateLoadBalancer")
			count++
		}
		// 允许±5%的误差
		if count < 95 || count > 105 {
			t.Errorf("Expected ~100 requests/sec, got %d", count)
		}
	})

	t.Run("并发安全", func(t *testing.T) {
		t.Parallel()
		limiter := NewGlobalRateLimiter(1000)
		var wg sync.WaitGroup
		for i := 0; i < 100; i++ {
			wg.Add(1)
			go func(uin string) {
				defer wg.Done()
				for j := 0; j < 100; j++ {
					limiter.Take("ap-shanghai", uin, "CVM/DescribeInstances")
				}
			}(strconv.Itoa(i))
		}
		wg.Wait()
	})
	t.Run("键唯一性验证", func(t *testing.T) {
		t.Parallel()
		limiter := NewGlobalRateLimiter(10)

		// 测试不同参数组合
		testCases := []struct {
			region string
			uin    string
			api    string
		}{
			{"ap-beijing", "111", "VPC/Create"},
			{"ap-beijing", "222", "VPC/Create"},
			{"ap-shanghai", "111", "VPC/Create"},
			{"ap-beijing", "111", "VPC/Delete"},
		}

		var keys sync.Map
		var wg sync.WaitGroup
		for _, tc := range testCases {
			tc := tc
			wg.Add(1)
			go func() {
				defer wg.Done()
				key := buildCompositeKey(tc.region, tc.uin, tc.api)
				if _, loaded := keys.LoadOrStore(key, struct{}{}); loaded {
					t.Errorf("Duplicate key generated: %s", key)
				}
				limiter.Take(tc.region, tc.uin, tc.api)
			}()
		}
		wg.Wait()
	})

	t.Run("边界值测试", func(t *testing.T) {
		t.Parallel()
		limiter := NewGlobalRateLimiter(1)

		// 测试空值情况
		limiter.Take("", "0", "")
		limiter.Take("ap-guangzhou", "0", "CLB/CreateLoadBalancer")
		limiter.Take("", "123456", "CVM/DescribeInstances")
	})
}

// 测试并发调用限流器是否起作用
func TestConcurrentCalls(t *testing.T) {
	testCases := []struct {
		name    string
		workers int
		in      bool
	}{
		{"低于限制_50个", 50, true},
		{"低于限制_100个", 100, true},
		{"高于限制_110个", 110, false},
		{"高于限制_120个", 120, false},
		{"超限突发_150个", 150, false}, // 15-10=5个需要等待，每个100ms
		{"两倍超限_200个", 200, false}, // 20-10=10个需要等待
	}

	for _, tc := range testCases {
		tc := tc // 捕获循环变量
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			limiter := NewGlobalRateLimiter(100)

			var (
				wg     sync.WaitGroup
				start  = time.Now()
				doneCh = make(chan struct{})
			)

			wg.Add(tc.workers)
			for i := 0; i < tc.workers; i++ {
				go func() {
					defer wg.Done()
					limiter.Take("ap-guangzhou", strconv.Itoa(10001), "CVM/DescribeInstances")
				}()
			}

			go func() {
				wg.Wait()
				close(doneCh)
			}()

			<-doneCh
			elapsed := time.Since(start)
			t.Logf("%s 实际耗时: %v", tc.name, elapsed)
			if elapsed > 1000*time.Millisecond {
				if tc.in {
					t.Errorf("不在预期时间范围: %v", elapsed)
				}
			} else {
				if !tc.in {
					t.Errorf("不在预期时间范围: %v", elapsed)
				}
			}
		})
	}
}

func TestConcurrentCallsInternalDelay(t *testing.T) {
	testCases := []struct {
		name    string
		workers int
		in      bool // true表示预期在1秒内完成
	}{
		{"低于限制_50个", 50, true},
		{"等于限制_100个", 100, true},
		{"轻微超限_110个", 110, false},
		{"明显超限_120个", 120, false},
		{"严重超限_150个", 150, false},
		{"两倍超限_200个", 200, false},
	}

	for _, tc := range testCases {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			// 设置业务接口内部延迟
			const internalDelay = 1000 * time.Millisecond
			// 设置限流器的限流速率
			const rateLimit = 100 // 100次/秒（每10ms一个令牌）
			limiter := NewGlobalRateLimiter(rateLimit)

			var (
				wg     sync.WaitGroup
				start  = time.Now()
				doneCh = make(chan struct{})
			)

			// 启动工作协程
			wg.Add(tc.workers)
			for i := 0; i < tc.workers; i++ {
				go func() {
					defer wg.Done()
					// 模拟API调用耗时（增加1ms操作耗时）
					limiter.Take("ap-guangzhou", "10001", "CVM/DescribeInstances")
					time.Sleep(internalDelay) // 模拟业务逻辑耗时
				}()
			}

			// 等待所有协程完成
			go func() {
				wg.Wait()
				close(doneCh)
			}()

			// 计算允许误差（考虑goroutine调度开销）
			const errorMargin = 50 * time.Millisecond
			// timeout := time.Second + errorMargin + internalDelay

			<-doneCh
			elapsed := time.Since(start)
			t.Logf("%s 实际耗时: %v", tc.name, elapsed)

			// 带误差范围的判断
			if tc.in && elapsed > (time.Second+errorMargin+internalDelay) {
				t.Errorf("预期在1秒内完成，实际耗时 %v（允许误差 %v）", elapsed, errorMargin)
			}
			if !tc.in && elapsed < (time.Second-errorMargin+internalDelay) {
				t.Errorf("预期需要超过1秒，实际耗时 %v（允许误差 %v）", elapsed, errorMargin)
			}

			// case <-time.After(timeout + 100*time.Millisecond): // 增加测试超时保护
			// 	if tc.in {
			// 		t.Errorf("测试超时! 预期在 %v 内完成", timeout)
			// 	} else {
			// 		t.Logf("%s 按预期超时（等待时间超过 %v）", tc.name, timeout)
			// 	}
			// }
		})
	}
}

func TestBuildCompositeKey(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name     string
		region   string
		uin      uint64
		api      string
		expected string
	}{
		{"正常情况", "ap-shanghai", 123456, "CLB/Delete", "ap-shanghai:123456:CLB/Delete"},
		{"空地域", "", 789, "VPC/Create", ":789:VPC/Create"},
		{"零账号", "ap-beijing", 0, "CVM/Update", "ap-beijing:0:CVM/Update"},
		{"特殊字符", "ap-northeast-1", 999, "API/With:Colon", "ap-northeast-1:999:API/With:Colon"},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got := buildCompositeKey(tt.region, fmt.Sprint(tt.uin), tt.api)
			if got != tt.expected {
				t.Errorf("buildCompositeKey() = %v, want %v", got, tt.expected)
			}
		})
	}
}
