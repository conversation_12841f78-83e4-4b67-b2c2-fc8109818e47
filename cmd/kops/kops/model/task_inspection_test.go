package model

import (
	"encoding/json"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTaskResult_MarshalJSON(t *testing.T) {
	testCases := []struct {
		name     string // 这里保持字段名英文
		input    TaskResult
		expected string
	}{
		{
			name: "应正确序列化包含错误原因的任务结果", // 用例描述中文化
			input: TaskResult{
				TaskID:          strPtr("task123"),
				Type:            "inspection",
				Target:          "clb-123456",
				MaxSyncTimes:    3,
				SyncInterval:    "1h",
				IgnoreRiskLevel: "ALL",
				Status:          "failed",
				Reason:          errors.New("connection timeout"),
			},
			expected: `{
				"TaskID": "task123",
				"Type": "inspection",
				"Target": "clb-123456",
				"MaxSyncTimes": 3,
				"SyncInterval": "1h",
				"IgnoreRiskLevel": "ALL",
				"Status": "failed",
				"reason": "connection timeout"
			}`,
		},
		{
			name: "当任务成功时应忽略空错误字段", // 用例描述中文化
			input: TaskResult{
				TaskID:          strPtr("task456"),
				Type:            "sync",
				Target:          "clb-789012",
				MaxSyncTimes:    5,
				SyncInterval:    "30m",
				IgnoreRiskLevel: "Rule",
				Status:          "success",
				Reason:          nil,
			},
			expected: `{
				"TaskID": "task456",
				"Type": "sync",
				"Target": "clb-789012",
				"MaxSyncTimes": 5,
				"SyncInterval": "30m",
				"IgnoreRiskLevel": "Rule",
				"Status": "success"
			}`,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := json.Marshal(tc.input)
			assert.NoError(t, err)
			assert.JSONEq(t, tc.expected, string(result))
		})
	}
}

func TestTaskResult_UnmarshalJSON(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected TaskResult
	}{
		{
			name: "应正确解析包含错误信息的JSON数据", // 中文描述
			input: `{
				"TaskID": "task789",
				"Type": "rollback",
				"Target": "clb-345678",
				"MaxSyncTimes": 2,
				"SyncInterval": "15m",
				"IgnoreRiskLevel": "Listener",
				"Status": "partial_success",
				"reason": "partial resources failed"
			}`,
			expected: TaskResult{
				TaskID:          strPtr("task789"),
				Type:            "rollback",
				Target:          "clb-345678",
				MaxSyncTimes:    2,
				SyncInterval:    "15m",
				IgnoreRiskLevel: "Listener",
				Status:          "partial_success",
				Reason:          errors.New("partial resources failed"),
			},
		},
		{
			name: "当JSON缺少错误字段时应返回空值", // 中文描述
			input: `{
				"TaskID": "task000",
				"Type": "cleanup",
				"Target": "clb-000000",
				"MaxSyncTimes": 0,
				"SyncInterval": "0s",
				"IgnoreRiskLevel": "ALL",
				"Status": "pending"
			}`,
			expected: TaskResult{
				TaskID:          strPtr("task000"),
				Type:            "cleanup",
				Target:          "clb-000000",
				MaxSyncTimes:    0,
				SyncInterval:    "0s",
				IgnoreRiskLevel: "ALL",
				Status:          "pending",
				Reason:          nil,
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			var result TaskResult
			err := json.Unmarshal([]byte(tc.input), &result)
			assert.NoError(t, err)

			assert.Equal(t, *tc.expected.TaskID, *result.TaskID)
			assert.Equal(t, tc.expected.Type, result.Type)
			assert.Equal(t, tc.expected.Target, result.Target)
			assert.Equal(t, tc.expected.MaxSyncTimes, result.MaxSyncTimes)
			assert.Equal(t, tc.expected.SyncInterval, result.SyncInterval)
			assert.Equal(t, tc.expected.IgnoreRiskLevel, result.IgnoreRiskLevel)
			assert.Equal(t, tc.expected.Status, result.Status)

			if tc.expected.Reason != nil {
				assert.EqualError(t, result.Reason, tc.expected.Reason.Error())
			} else {
				assert.Nil(t, result.Reason)
			}
		})
	}
}

func TestTaskResult_EdgeCases(t *testing.T) {
	t.Run("当输入空JSON时应初始化空结构体", func(t *testing.T) {
		var result TaskResult
		err := json.Unmarshal([]byte("{}"), &result)
		assert.NoError(t, err)
		assert.Nil(t, result.TaskID)
		assert.Empty(t, result.Type)
		assert.Empty(t, result.Target)
		assert.Zero(t, result.MaxSyncTimes)
		assert.Empty(t, result.SyncInterval)
		assert.Empty(t, result.IgnoreRiskLevel)
		assert.Empty(t, result.Status)
		assert.Nil(t, result.Reason)
	})

	t.Run("当JSON格式非法时应返回错误", func(t *testing.T) {
		var result TaskResult
		err := json.Unmarshal([]byte("{invalid}"), &result)
		assert.Error(t, err)
	})

	t.Run("当TaskID为null时应保留空指针", func(t *testing.T) {
		input := `{
			"TaskID": null,
			"Type": "test",
			"Target": "test-target"
		}`

		var result TaskResult
		err := json.Unmarshal([]byte(input), &result)
		assert.NoError(t, err)
		assert.Nil(t, result.TaskID)
	})
}

// 辅助函数保持英文命名
func strPtr(s string) *string {
	return &s
}
