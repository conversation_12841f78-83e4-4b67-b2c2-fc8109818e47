package model

import (
	"encoding/json"
	"errors"
	"time"
)

type TaskReport struct {
	Template string

	TotalTaskCount           int
	TotalRunningTaskCount    int
	TotalDoneTaskCount       int
	TotalTerminatedTaskCount int

	TaskList []*InspectionTask
}

type InspectionTask struct {
	TaskID          string  `gorm:"primarykey"`
	InstanceID      string  // 实例ID，如：clbID、clusterID，与 Type 组成联合索引
	InstanceType    string  // 类型，如：clb、cluster
	State           string  // running || finished
	Reason          *string // 原因，如：健康检查失败
	Creator         *string // 创建人
	CurSyncTimes    int
	MaxSyncTimes    int
	SyncInterval    string // 间隔时间，如 1h 1m 1s
	IgnoreRiskLevel string // 告警级别，如：ALL | RealServer | Rule | Listener | CLB
	CreatedAt       time.Time
	UpdatedAt       time.Time
	FinishedAt      *time.Time
}

type TaskResult struct {
	TaskID          *string
	Type            string
	Target          string
	MaxSyncTimes    int
	SyncInterval    string
	IgnoreRiskLevel string
	Status          string // 处理（创建、更新）任务 的执行结果状态
	Reason          error  `json:"reason,omitempty"`
}

// 自定义JSON序列化
func (tr TaskResult) MarshalJSON() ([]byte, error) {
	type Alias TaskResult
	return json.Marshal(&struct {
		Reason string `json:"reason,omitempty"`
		*Alias
	}{
		Reason: func() string {
			if tr.Reason != nil {
				return tr.Reason.Error()
			}
			return ""
		}(),
		Alias: (*Alias)(&tr),
	})
}

// 自定义反序列化
func (tr *TaskResult) UnmarshalJSON(data []byte) error {
	type Alias TaskResult
	aux := &struct {
		Reason string `json:"reason,omitempty"`
		*Alias
	}{
		Alias: (*Alias)(tr),
	}

	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	if aux.Reason != "" {
		tr.Reason = errors.New(aux.Reason)
	}
	return nil
}
