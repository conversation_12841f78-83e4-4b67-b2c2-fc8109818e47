package stat

import "reflect"

func GetUpdateFields(obj interface{}) []string {
	// 获取输入对象的类型
	t := reflect.TypeOf(obj)

	// 如果输入对象是指针，则获取指针指向的实际类型
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	// 初始化没有struct tag的字段列表
	noTagFields := []string{}

	// 遍历结构体字段
	for i := 0; i < t.NumField(); i++ {
		// 获取字段
		field := t.Field(i)

		// 检查字段是否有struct tag
		if len(field.Tag) == 0 {
			// 将没有struct tag的字段添加到列表中
			noTagFields = append(noTagFields, field.Name)
		}
	}

	// 返回没有struct tag的字段列表
	return noTagFields
}
