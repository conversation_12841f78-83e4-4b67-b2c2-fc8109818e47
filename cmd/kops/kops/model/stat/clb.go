package stat

import "time"

type Clb struct {
	ID uint `gorm:"primarykey"`

	Uin       string `gorm:"<-:create"`
	Appid     string `gorm:"<-:create"`
	Region    string `gorm:"<-:create"`
	ClusterID string `gorm:"<-:create"`
	Service   string `gorm:"<-:create"`

	Date       string `gorm:"<-:create;type:date"`
	LB         string `gorm:"<-:create"`
	Listener   string `gorm:"<-:create"`
	TargetType string `gorm:"<-:create"`

	Count int

	CreatedAt time.Time `gorm:"<-:create"`
	UpdatedAt time.Time `gorm:"<-:create"`
}

func (clb *Clb) String() string {
	return clb.Date + clb.Uin + clb.Appid + clb.Region + clb.LB + clb.Listener + clb.TargetType
}
