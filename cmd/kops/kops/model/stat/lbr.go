package stat

import (
	"time"
)

type LBR struct {
	ID uint `gorm:"primarykey"`

	Uin         string `gorm:"<-:create"`
	Appid       string `gorm:"<-:create"`
	Region      string `gorm:"<-:create"`
	ClusterID   string `gorm:"<-:create"`
	ClusterType string `gorm:"<-:create"`

	Date string `gorm:"<-:create;type:date"`
	LB   string `gorm:"<-:create"`

	ServiceCount int
	IngressCount int

	CreatedAt time.Time `gorm:"<-:create"`
	UpdatedAt time.Time `gorm:"<-:create"`
}

func (lbr *LBR) String() string {
	return lbr.Date + lbr.LB
}
