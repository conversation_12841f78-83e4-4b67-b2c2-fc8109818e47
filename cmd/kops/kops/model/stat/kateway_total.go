package stat

type KatewayTotal struct {
	Date string `gorm:"<-:create;type:date;primary_key"`
	Type string `gorm:"<-:create;primary_key"`

	Total                  int64   // 总数
	ValidClusterPencent    float64 // 有效集群百分比，实际有效处理的集群/拉取的总集群数，排除了异常状态集群
	MockCount              int64
	MockPercent            float64
	CheckCount             int64
	CheckPercent           float64
	RunningCount           int64
	RunningPercent         float64
	NotRunningCount        int64
	ExpectedVersionCount   int64
	ExpectedVersionPercent float64

	// 未使用的接入层标签总数
	UnusedTagsTotal int64
	// 接入层标签总数
	TagsTotal int64
	// 未使用的接入层标签占比
	UnusedTagsPercent float64
}
