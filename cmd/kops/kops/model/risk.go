package model

import (
	"fmt"
	"sort"
)

// 定义不同级别的健康检查风险
var (
	// RS级别风险：健康检查失败
	HasDownRSByHealth = RegisterRisk("HasDownRSByHealth", "存在后端健康检查失败的 RS", RealServerLevelRisk, 1)
	// RS级别风险：权重为0
	HasDownRSByWeight = RegisterRisk("HasDownRSByWeight", "存在后端权重为 0 的 RS", RealServerLevelRisk, 1)
)

var (
	// 规则级别风险：所有RS权重为0
	HasAllDownRuleByWeight = RegisterRisk("HasAllDownRuleByWeight", "存在后端 RS 权重全为 0 的规则", RuleLevelRisk, 10)
	// 规则级别风险：所有RS健康检查失败
	HasAlDownRuleByHealth = RegisterRisk("HasAlDownRuleByHealth", "存在后端 RS 健康检查全失败的规则", RuleLevelRisk, 10)
)

var (
	// 监听器级别风险：所有RS权重为0
	HasAllDownListenerByWeight = RegisterRisk("HasAllDownListenerByWeight", "存在后端 RS 权重全为 0 的监听器", ListenerLevelRisk, 100)
	// 监听器级别风险：所有RS健康检查失败
	HasAllDownListenerByHealth = RegisterRisk("HasAllDownListenerByHealth", "存在后端 RS 健康检查全失败的监听器", ListenerLevelRisk, 100)
)

var (
	// CLB级别风险：所有RS权重为0
	AllDownCLBByWeight = RegisterRisk("AllDownCLBByWeight", "CLB 后端 RS 权重全为 0", CLBLevelRisk, 1000)
	// CLB级别风险：所有RS健康检查失败
	AllDownCLBByHealth = RegisterRisk("AllDownCLBByHealth", "CLB 后端 RS 健康检查全失败", CLBLevelRisk, 1000)
)

// CLBRisks 结构体用于存储CLB的协议和风险信息
type CLBRisks struct {
	Protocols      string
	UsingResources string
	Risks
}

// Risks 类型用于存储风险及其出现次数
type Risks map[Risk]int

// Risk 结构体定义了风险的属性
type Risk struct {
	Name   string
	Detail string
	Level  RiskLevel
	Score  int
}

// RiskLevel 定义风险级别的枚举类型
type RiskLevel string

// 定义不同的风险级别常量
const (
	RealServerLevelRisk RiskLevel = "RealServer"
	RuleLevelRisk       RiskLevel = "Rule"
	ListenerLevelRisk   RiskLevel = "Listener"
	CLBLevelRisk        RiskLevel = "CLB"
)

var validRiskLevels = []RiskLevel{RealServerLevelRisk, RuleLevelRisk, ListenerLevelRisk, CLBLevelRisk, ""}

func ValidateRiskLevel(s string) bool {
	for _, level := range validRiskLevels {
		if RiskLevel(s) == level {
			return true
		}
	}
	return false
}

// RiskMap 用于存储所有已注册的风险
var RiskMap = Risks{}

// RegisterRisk 注册新的风险并返回Risk实例
func RegisterRisk(name, detail string, level RiskLevel, score int) Risk {
	risk := Risk{
		Name:   name,
		Detail: detail,
		Level:  level,
		Score:  score,
	}
	RiskMap[risk] = 0
	return risk
}

// GetRiskByName 根据风险名称查找对应的Risk实例
func GetRiskByName(name string) Risk {
	for risk := range RiskMap {
		if risk.Name == name {
			return risk
		}
	}
	return Risk{}
}

// RiskCount 统计不同级别风险的数量
func (r Risks) RiskCount() (int, int, int, int) {
	var clbCount, listenerCount, ruleCount, rsCount int
	for reason := range r {
		switch reason.Level {
		case CLBLevelRisk:
			clbCount++
		case ListenerLevelRisk:
			listenerCount++
		case RuleLevelRisk:
			ruleCount++
		case RealServerLevelRisk:
			rsCount++
		}
	}

	return clbCount, listenerCount, ruleCount, rsCount
}

// RiskCount 统计不同级别风险的数量
func (r Risks) TopRiskLevel() string {
	var clbCount, listenerCount, ruleCount, rsCount int
	for reason := range r {
		switch reason.Level {
		case CLBLevelRisk:
			clbCount++
		case ListenerLevelRisk:
			listenerCount++
		case RuleLevelRisk:
			ruleCount++
		case RealServerLevelRisk:
			rsCount++
		}
	}

	if clbCount > 0 {
		return string(CLBLevelRisk)
	} else if listenerCount > 0 {
		return string(ListenerLevelRisk)
	} else if ruleCount > 0 {
		return string(RuleLevelRisk)
	} else if rsCount > 0 {
		return string(RealServerLevelRisk)
	}

	return ""
}

// Strings 返回排序后的风险详情列表
func (r Risks) Strings() []string {
	details := []Risk{}
	for risk := range r {
		details = append(details, risk)
	}

	// 按风险级别降序排序
	sort.Slice(details, func(i, j int) bool {
		return details[i].Level > details[j].Level
	})

	sortedRisks := []string{}
	for _, reason := range details {
		if reason != AllDownCLBByWeight && reason != AllDownCLBByHealth {
			sortedRisks = append(sortedRisks, reason.Detail+"，"+"个数："+fmt.Sprint(r[reason]))
		} else {
			sortedRisks = append(sortedRisks, reason.Detail)
		}
	}

	return sortedRisks
}

// GetScores 计算总风险分数
func (r Risks) GetScores() int {
	var score int
	for risk, count := range r {
		score += risk.Score * count
	}

	return score
}

// Counters 返回风险名称及其出现次数的映射
func (r Risks) Counters() map[string]uint {
	sortedRisks := map[string]uint{}
	for reason, counter := range r {
		sortedRisks[reason.Name] = uint(counter)
	}

	return sortedRisks
}
