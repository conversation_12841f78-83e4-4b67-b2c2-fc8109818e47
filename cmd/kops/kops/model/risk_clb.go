package model

import (
	"bytes"
	"encoding/csv"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

type RiskState string

const (
	StateStockRisk     RiskState = "Stock"
	StateIncrementRisk RiskState = "Increment"
)

type RiskReport struct {
	Template string

	TotalRiskCount int

	TotalCLBLevelRiskCount      int
	TotalListenerLevelRiskCount int
	TotalRuleLevelRiskCount     int
	TotalRSLevelRiskCount       int

	TotalIncrementalTaskCount int
	TotalStockTaskCount       int
	TotalClusterCount         int

	Increment []CLBRiskRecord `json:",omitempty" yaml:",omitempty"`
	Stock     []CLBRiskRecord `json:",omitempty" yaml:",omitempty"`
}

func (c RiskReport) CSV() []byte {
	var b bytes.Buffer
	writer := csv.NewWriter(transform.NewWriter(&b, simplifiedchinese.GBK.NewEncoder()))

	// 写入 CSV 头部
	writer.Write([]string{"风险类型", "APPID", "集群ID", "CLBID", "风险个数", "风险等级", "上升次数", "关联资源", "风险详情", "发生时间", "持续时间", "排障链接"})

	for _, clb := range c.Increment {
		record := []string{
			"新增风险",
			strconv.Itoa(int(clb.AppID)),
			clb.ClusterName,
			clb.CLBID,
			strconv.Itoa(int(clb.RiskCount)),
			clb.RiskLevel,
			fmt.Sprint(clb.RiseCount),
			clb.UsingResources,
			clb.RiskMetrics.Strings(),
			clb.CreatedAt.Format("2006-01-02 15:04:05"),
			time.Since(clb.CreatedAt).String(),
			buildCLBRef(clb.ClusterName, clb.CLBID),
		}
		writer.Write(record)
	}

	for _, clb := range c.Stock {
		record := []string{
			"存量风险",
			strconv.Itoa(int(clb.AppID)),
			clb.ClusterName,
			clb.CLBID,
			strconv.Itoa(int(clb.RiskCount)),
			clb.RiskLevel,
			fmt.Sprint(clb.RiseCount),
			clb.UsingResources,
			clb.RiskMetrics.Strings(),
			clb.CreatedAt.Format("2006-01-02 15:04:05"),
			time.Since(clb.CreatedAt).String(),
			buildCLBRef(clb.ClusterName, clb.CLBID),
		}
		writer.Write(record)
	}

	writer.Flush()
	return b.Bytes()
}

func buildCLBRef(clusterID, clbID string) string {
	return fmt.Sprintf("[%s](http://kateway.woa.com/inspection/clb/report/get?clbID=%s&clusterID=%s&refreshCache=true)", clbID, clbID, clusterID)
}

type CLBRiskRecord struct {
	AppID       uint64
	ClusterName string      `gorm:"column:ClusterName" json:"ClusterName"`
	CLBID       string      `json:"CLBID"` // CLBID
	RiskScore   int         `json:"RiskScore"`
	RiskCount   uint        `json:"RiskCount"`
	RiskMetrics RiskMetrics `gorm:"embedded" json:"RiskMetrics"`
	RiseCount   int         `json:"RiseCount"`
	State       string      `gorm:"column:State;not null"` // 状态，如：running || finished

	UsingResources string
	RiskLevel      string

	// BlockTime   string
	// BlockAt     time.Time `gorm:"column:BlockAt;null"`
	CreatedAt time.Time `gorm:"column:CreatedAt;not null"` // 发布任务创建时间
	UpdatedAt time.Time `gorm:"column:UpdatedAt;not null"` // 发布任务更新时间

	LastDuration string `gorm:"-" json:"LastDuration"`
	RiskSummary  string `gorm:"-" json:"RiskSummary"`
}

type RiskMetrics struct {
	// Clb维度
	AllDownCLBByWeight uint
	AllDownCLBByHealth uint
	// Listener维度
	HasAllDownListenerByWeight uint
	HasAllDownListenerByHealth uint
	// Rule维度
	HasAllDownRuleByWeight uint
	HasAllDownRuleByHealth uint
	// RS维度
	HasDownRSByHealth uint
	HasDownRSByWeight uint
}

// ToMap converts the RiskMetric struct to a map
func (r *RiskMetrics) ToMap() map[string]uint {
	result := make(map[string]uint)
	val := reflect.ValueOf(r).Elem()
	for i := 0; i < val.NumField(); i++ {
		field := val.Type().Field(i)
		result[field.Name] = val.Field(i).Interface().(uint)
	}
	return result
}

// ToMap converts the RiskMetric struct to a map
func (r *RiskMetrics) ToRisks() Risks {
	result := Risks{}
	maps := r.ToMap()
	for riskName, counter := range maps {
		if counter != 0 {
			result[GetRiskByName(riskName)] = int(counter)
		}
	}
	return result
}

// ToMap converts the RiskMetric struct to a map
func (r *RiskMetrics) Strings() string {
	return strings.Join(r.ToRisks().Strings(), "; ")
}

// ToStruct converts a map to a RiskMetric struct
func ToStruct(in map[string]uint) *RiskMetrics {
	out := &RiskMetrics{}
	val := reflect.ValueOf(out).Elem()
	for k, v := range in {
		field := val.FieldByName(k)
		if field.IsValid() && field.CanSet() {
			field.SetUint(uint64(v))
		}
	}
	return out
}
