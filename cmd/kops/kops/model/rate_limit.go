package model

import (
	"strings"
	"sync"

	"go.uber.org/ratelimit"
)

// GlobalRateLimiter 全局并发安全的限流器存储
type GlobalRateLimiter struct {
	RateLimit int      // 限流速率 per second
	limiters  sync.Map // key: "region:uin:api" 格式字符串，value: ratelimit.Limiter
}

func NewGlobalRateLimiter(rateLimit int) *GlobalRateLimiter {
	return &GlobalRateLimiter{
		RateLimit: rateLimit,
	}
}

// Take 通过限流器限流
// api 为接口名称，例如：CLB/CreateLoadBalancer
func (g *GlobalRateLimiter) Take(region string, uin string, api string) {
	key := buildCompositeKey(region, uin, api)
	if rl, ok := g.limiters.LoadOrStore(key, ratelimit.New(g.RateLimit, ratelimit.WithoutSlack)); ok {
		rl.(ratelimit.Limiter).Take()
	}
}

// buildCompositeKey 构建复合键（优化内存分配版本）
func buildCompositeKey(region, uin, api string) string {
	var b strings.Builder
	b.WriteString(region)
	b.WriteByte(':')
	b.WriteString(uin)
	b.WriteByte(':')
	b.WriteString(api)
	return b.String()
}
