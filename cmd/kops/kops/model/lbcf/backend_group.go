package lbcf

import (
	"time"

	"git.code.oa.com/tkex-teg/lb-controlling-framework/pkg/apis/lbcf.tkestack.io/v1beta1"
)

type MigrateState string

const (
	MigrateStateUnknown   MigrateState = "Unknown"   // 未知
	MigrateStateUnsupport MigrateState = "Unsupport" // 不支持迁移
	MigrateStateSkip      MigrateState = "Skip"      // 忽略
	MigrateStatePending   MigrateState = "Pending"   // 待迁移
	MigrateStateDone      MigrateState = "Done"      // 迁移成功
)

type BackendGroup struct {
	ID uint `gorm:"primarykey"`

	Date string `gorm:"<-:create;type:date"`

	Appid       string `gorm:"<-:create"`
	Region      string `gorm:"<-:create"`
	ClusterID   string `gorm:"<-:create"`
	ClusterName string `gorm:"<-:create"`
	ClusterType string `gorm:"<-:create"`
	ProjectID   string `gorm:"<-:create"`

	Name            string
	Namespace       string
	Driver          string
	LoadBalancers   []string `gorm:"serializer:json"`
	LoadBalancerIDs []string `gorm:"serializer:json"`
	State           MigrateState
	Error           string
	Raw             v1beta1.BackendGroup `gorm:"serializer:json"`

	CreatedAt time.Time `gorm:"<-:create"`
	UpdatedAt time.Time `gorm:"<-:create"`
}

func (BackendGroup) TableName() string {
	return "backendgroup"
}
