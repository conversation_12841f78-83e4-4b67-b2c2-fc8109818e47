package lbcf

import (
	"time"
)

type UserLB struct {
	ID uint `gorm:"primarykey"`

	Date string `gorm:"<-:create;type:date"`

	Appid       string `gorm:"<-:create"`
	Region      string `gorm:"<-:create"`
	ClusterID   string `gorm:"<-:create"`
	ClusterName string `gorm:"<-:create"`
	ClusterType string `gorm:"<-:create"`
	ProjectID   string `gorm:"<-:create"`

	Name           string
	Namespace      string
	Driver         string
	LoadBalancerID string
	ListenerID     string
	State          string
	Error          string
	Raw            string

	CreatedAt time.Time `gorm:"<-:create"`
	UpdatedAt time.Time `gorm:"<-:create"`
}

func (UserLB) TableName() string {
	return "user_lb"
}
