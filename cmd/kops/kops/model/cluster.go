package model

import (
	"encoding/base64"
	"errors"
	"fmt"
	"strconv"
	"strings"

	platformv1 "tkestack.io/tke/api/platform/v1"

	"git.woa.com/kateway/kateway-server/pkg/models"
	region "git.woa.com/kateway/kateway-server/pkg/util/region"
)

type IANVSCluster struct {
	ClusterID     string `gorm:"column:cluster_id"`
	Type          string `gorm:"column:product"`
	SubType       string `gorm:"column:cluster_category"`
	Appid         int64  `gorm:"column:app_id"`
	Region        string `gorm:"column:region"`
	Name          string `gorm:"column:cluster_name"`
	MetaClusterID string `gorm:"column:meta_cluster_id"`
}

type Cluster struct {
	ClusterID string
	Name      string
	Type      string
	SubType   int
	Appid     uint64
	// Uin       string
	// SubUin    string
	Region   string
	RegionID int

	Description   string
	MetaClusterID string
	ServiceCIDR   string
	NetworkType   string
	KubeProxyMode string
	K8SVersion    string
	VpcID         string
	SubnetID      string
	State         string
}

func NewClusterFromTKE(cluster *models.Cluster) (*Cluster, error) {
	r := region.Get(cluster.Region)
	if r == nil {
		return nil, fmt.Errorf("region %q not found", cluster.Region)
	}

	/*
		type context struct {
			CreateCtx struct {
				Uin           string
				SubAccountUin string
			}
		}
		var c context
		_ = json.Unmarshal([]byte(cluster.Context), &c)
	*/

	cls := &Cluster{
		ClusterID: cluster.ClusterInstanceId,
		Name:      cluster.Name,
		Type:      "tke",
		SubType:   int(cluster.ClusterType),
		Appid:     cluster.AppId,
		// Uin:       c.CreateCtx.Uin,
		// SubUin:    c.CreateCtx.SubAccountUin,
		Region:   r.Alias,
		RegionID: r.ID,

		MetaClusterID: cluster.MetaClusterID,
		K8SVersion:    cluster.K8sVersion,
		Description:   cluster.Description,
		ServiceCIDR:   cluster.ServiceCIDR,
		VpcID:         cluster.UniqVpcID,
		State:         cluster.ClusterState(),
	}
	if cluster.IPVS() {
		cls.KubeProxyMode = "IPVS"
	} else {
		cls.KubeProxyMode = "IPTables"
	}

	if runtimeCfg := cluster.RuntimeConfig(); runtimeCfg != nil {
		if runtimeCfg.EniIpamdPara.EniSubnetIds != nil {
			cls.SubnetID = strings.Join(runtimeCfg.EniIpamdPara.EniSubnetIds, ",")
		}
	}

	if property := cluster.PropertyS(); property != nil {
		cls.NetworkType = property.NetworkType
	}

	return cls, nil
}

func NewClusterFromEKS(cluster *platformv1.Cluster) (*Cluster, error) {
	appID, err := strconv.Atoi(cluster.Spec.TenantID)
	if err != nil {
		return nil, err
	}
	v := cluster.Annotations["eks.tke.cloud.tencent.com/region"]
	if v == "" {
		return nil, errors.New("region annotation \"eks.tke.cloud.tencent.com/region\" not exist")
	}
	r := region.Get(v)
	if r == nil {
		return nil, fmt.Errorf("region %q not found", v)
	}
	v = cluster.Annotations["eks.tke.cloud.tencent.com/metacluster"]
	if v == "" {
		return nil, errors.New("metacluster annotation \"eks.tke.cloud.tencent.com/metacluster\" not exist")
	}
	// 使用 base64.StdEncoding.DecodeString 函数进行解码
	description, _ := base64.StdEncoding.DecodeString(cluster.GetAnnotations()["eks.tke.cloud.tencent.com/description"])
	return &Cluster{
		ClusterID: cluster.Name,
		Name:      cluster.Spec.DisplayName,
		Type:      "eks",
		Appid:     uint64(appID),
		// Uin:       cluster.Annotations["eks.tke.cloud.tencent.com/owner-uin"],
		// SubUin:    cluster.Annotations["eks.tke.cloud.tencent.com/creator-uin"],
		Region:   r.Alias,
		RegionID: r.ID,

		MetaClusterID: v,
		K8SVersion:    cluster.Status.Version,
		Description:   string(description),
		VpcID:         cluster.GetAnnotations()["eks.tke.cloud.tencent.com/vpcid"],
		ServiceCIDR:   cluster.GetAnnotations()["eks.tke.cloud.tencent.com/service-subnet-cidr"],
		SubnetID:      cluster.GetAnnotations()["eks.tke.cloud.tencent.com/subnet-ids"],
		NetworkType:   "VPC-CNI",
		KubeProxyMode: "IPVS",
		State:         string(cluster.Status.Phase),
	}, nil
}

// 是否独立集群
func (c *Cluster) IsIndependent() bool {
	return c.Type == "tke" && c.SubType == 4
}

func (c *Cluster) IsEKS() bool {
	return c.Type == "eks"
}

func (c *Cluster) GetRegion() region.Region {
	return region.MustGet(c.Region)
}

type KubeConfig struct {
	Host        string  `json:"Host"`
	BearerToken string  `json:"BearerToken"`
	QPS         float32 `json:"QPS"`
	Burst       int     `json:"Burst"`
}
