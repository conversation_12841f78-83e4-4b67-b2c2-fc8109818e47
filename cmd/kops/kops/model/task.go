package model

import (
	"fmt"
	"time"
)

type TaskState string

const (
	TaskStateRunning    TaskState = "Running"
	TaskStateDone       TaskState = "Done"
	TaskStateTerminated TaskState = "Terminated"
)

func (s TaskState) String() string {
	return string(s)
}

type Task struct {
	TaskID     string `gorm:"primarykey"`
	Type       string `gorm:"primarykey"`
	Version    string
	State      string
	Cost       float64
	Total      int
	Progress   string
	Error      string
	Creator    string
	CreatedAt  time.Time
	FinishedAt *time.Time
	UpdatedAt  time.Time
}

func NewTask(id string, taskType string, creator string) Task {
	return Task{
		TaskID:    id,
		Type:      taskType,
		State:     TaskStateRunning.String(),
		Creator:   creator,
		CreatedAt: time.Now(),
	}
}

func (t *Task) UpdateProgress(processedTotal int) {
	if t.Total == 0 {
		return
	}
	t.Progress = fmt.Sprintf("%.2f%%(%d/%d)", float64(processedTotal)/float64(t.Total)*100, processedTotal, t.Total)
}
