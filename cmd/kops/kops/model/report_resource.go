package model

import (
	"fmt"
	"strings"
)

// ResourceReport 结构体包含了资源的报告信息
type ResourceReport struct {
	Template string
	ExpireAt string

	Type       string
	Namespace  string
	Name       string
	CreateTime string
	Status     string
	CLBID      string
	Reused     bool
	ClusterID  string
	Raw        string

	AppFabric *AppFabricReference `json:",omitempty"`
	TKEx      *TKExReference      `json:",omitempty"`
}

// https://console.cloud.tencent.com/camp/app/service/info?appId=app-l85xjd7q&projectId=prj-7jcrxxrk&instanceId=tad-crdvfxsk&envName=testing&serviceType=clbservice&componentName=data-ai&serviceName=data-ai

// https://console.cloud.tencent.com/camp/ingress/detail/info?name=xiaoma-dev-v4&projectId=prj-8qcqmdvp&namespace=prj-8qcqmdvp-development&clbRegion=ap-guangzhou

type AppFabricReference struct {
	Link string

	ProjectID       string
	EnvironmentName string

	Namespace string

	AppID         string
	InstanceID    string
	ComponentName string

	CLBRegion string

	TargetType string
	TraitName  string
	TargetName string
}

func (r *AppFabricReference) BuildLink() string {
	if strings.ToLower(r.TargetType) == "multiclusterservice" {
		return fmt.Sprintf("https://console.cloud.tencent.com/camp/app/service/info?appId=%s&projectId=%s&instanceId=%s&envName=%s&serviceType=clbservice&componentName=%s&serviceName=%s",
			r.AppID,
			r.ProjectID,
			r.InstanceID,
			r.EnvironmentName,
			r.ComponentName,
			r.TraitName)
	}
	return fmt.Sprintf("https://console.cloud.tencent.com/camp/ingress/detail/info?name=%s&projectId=%s&namespace=%s&clbRegion=%s",
		r.TargetName,
		r.ProjectID,
		r.Namespace,
		r.CLBRegion)
}

// https://kubernetes.woa.com/v4/projects/prjrbnjq/services/cls-er5syccc/ns-prjrbnjq-1305257-production/tke-troubleshooting-helper
// https://kubernetes.woa.com/v4/projects/prjb8x8f/ingresses/cls-i9et79ll/ns-prjb8x8f-1305257-production/mixed-rs

type TKExReference struct {
	Link string

	ProjectID string
	ClusterID string
	Namespace string

	TargetName string
	TargetType string
}

func (r *TKExReference) BuildLink() string {
	if strings.ToLower(r.TargetType) == "service" {
		return fmt.Sprintf("https://kubernetes.woa.com/v4/projects/%s/services/%s/%s/%s",
			r.ProjectID,
			r.ClusterID,
			r.Namespace,
			r.TargetName)
	}
	return fmt.Sprintf("https://kubernetes.woa.com/v4/projects/%s/ingresses/%s/%s/%s",
		r.ProjectID,
		r.ClusterID,
		r.Namespace,
		r.TargetName)
}
