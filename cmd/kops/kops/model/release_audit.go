package model

import (
	"time"
)

// ChangeAudit 表示变更审计记录
type ReleaseAudit struct {
	ID             uint64    `gorm:"primaryKey;autoIncrement;column:ID" json:"id"`           // 主键，自增
	Date           time.Time `gorm:"type:date;column:Date;not null" json:"date"`             // 变更日期
	Region         string    `gorm:"column:Region;not null" json:"region"`                   // 集群所在地域
	ClusterID      string    `gorm:"column:ClusterID;not null" json:"cluster_id"`            // 集群ID
	Publisher      string    `gorm:"column:Publisher;not null" json:"publisher"`             // 发布人
	Component      string    `gorm:"column:Component;not null" json:"component"`             // 组件类型
	ImageTag       string    `gorm:"column:ImageTag;not null" json:"image_tag"`              // 镜像版本
	SourceImageTag string    `gorm:"column:SourceImageTag;not null" json:"source_image_tag"` // 源镜像版本
	Token          string    `gorm:"column:Token;default:''" json:"token"`                   // 可选字段
	// PreCheckStatus  *string   `gorm:"column:PreCheckStatus;default:''" json:"pre_check_status,omitempty"`    // 可选字段，预检状态
	// PreCheckReason  *string   `gorm:"column:PreCheckReason;default:''" json:"pre_check_reason,omitempty"`    // 可选字段，预检失败原因
	// PublishStatus   *string   `gorm:"column:PublishStatus;default:''" json:"publish_status,omitempty"`       // 可选字段，发布状态
	// PublishReason   *string   `gorm:"column:PublishReason;default:''" json:"publish_reason,omitempty"`       // 可选字段，发布失败原因
	// BackCheckStatus *string   `gorm:"column:BackCheckStatus;default:''" json:"back_check_status,omitempty"`  // 可选字段，后检状态
	// BackCheckReason *string   `gorm:"column:BackCheckReason;default:''" json:"back_check_reason,omitempty"`  // 可选字段，后检失败原因
	// BackCheckTaskID *string   `gorm:"column:BackCheckTaskID;default:''" json:"back_check_task_id,omitempty"` // 可选字段，后检任务ID
	Status     bool      `gorm:"column:Status;not null" json:"status"`              // 状态
	Reason     *string   `gorm:"column:Reason;default:''" json:"reason,omitempty"`  // 可选字段，失败原因
	TaskID     *string   `gorm:"column:TaskID;default:''" json:"task_id,omitempty"` // 可选字段，发布任务ID
	CreatedAt  time.Time `gorm:"column:CreatedAt;not null" json:"created_at"`       // 发布任务创建时间
	UpdatedAt  time.Time `gorm:"column:UpdatedAt;not null" json:"updated_at"`       // 变更时间
	FinishedAt time.Time `gorm:"column:FinishedAt;not null" json:"finished_at"`     // 完成时间
	// Content    *string   `gorm:"column:Content;default:''" json:"content,omitempty"` // 可选字段，发布内容
}

// TableName 返回表名
func (ReleaseAudit) TableName() string {
	return "release_audit"
}
