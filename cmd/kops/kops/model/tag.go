package model

import (
	"time"

	"gorm.io/plugin/soft_delete"
)

const (
	TagStateUsed   string = "Used"
	TagStateUnused string = "Unused"
)

const (
	ResourceStateUnknown  string = "Unknown"
	ResourceStateExist    string = "Exist"
	ResourceStateNotExist string = "NotExist"
)

type TaggedResource struct {
	Region string `json:"region"`
	Type   string `json:"type"`
	ID     string `json:"id"`
	State  string `json:"state"`
}

type Tag struct {
	ID    uint `gorm:"primarykey"`
	Appid string
	Key   string
	Value string

	Uin                string
	State              string
	Resources          []TaggedResource `gorm:"serializer:json"`
	StateDetectedTotal int
	LastTransitionTime time.Time

	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt soft_delete.DeletedAt
}
