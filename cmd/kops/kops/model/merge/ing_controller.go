package merge

import "time"

type MigrationState string

const (
	MigrationStateMigratable   MigrationState = "Migratable"
	MigrationStateUnMigratable MigrationState = "UnMigratable"
	MigrationStateMigrated     MigrationState = "Migrated"
)

type Cause string

const (
	CauseIncompatibleStartupArgs   Cause = "IncompatibleStartupArgs"
	CauseUnexpectedRBAC            Cause = "UnexpectedRBAC"
	CauseSilentStart               Cause = "SilentStart"
	CauseServiceControllerNotFound Cause = "ServiceControllerNotFound"
	CauseControllerVersionNotReady Cause = "ControllerVersionNotReady"
	CauseUnknown                   Cause = "Unknown"
)

type IngressController struct {
	ID uint `gorm:"primarykey"`

	Date string `gorm:"<-:create;type:date"`

	ClusterID   string `gorm:"<-:create"`
	ClusterType string `gorm:"<-:create"`

	State       MigrationState `gorm:"<-:create"`
	Cause       Cause          `gorm:"<-:create"`
	CauseDetail string         `gorm:"<-:create"`

	CreatedAt time.Time `gorm:"<-:create"`
	UpdatedAt time.Time `gorm:"<-:create"`
}

func (IngressController) TableName() string {
	return "ingress_controller"
}
