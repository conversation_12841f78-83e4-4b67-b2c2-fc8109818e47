package model

// CLBReport 结构体包含了负载均衡器的报告信息
type CLBReport struct {
	Template string
	ExpireAt string

	LBRInfo *LoadBalancerResourceInfo `json:",omitempty"` // 负载均衡资源信息
	CLBInfo *CloudLoadBalancerInfo    `json:",omitempty"` // 云负载均衡信息

	CLBStats *CLBHealthStats `json:",omitempty"` // 负载均衡健康统计信息
}

// CLBHealthStats 结构体包含了负载均衡的健康统计数据
type CLBHealthStats struct {
	UsingProtocols string // 使用的协议

	Stats []CLBRisk // 风险统计列表

	TotalRSCount       int // 总的RS（Real Server）数量
	TotalListenerCount int // 总的监听器数量
	TotalRuleCount     int // 总的规则数量

	// 下面几个字段统计了不同条件下的禁止RS数量和监听器/规则数量
	TotalZeroWeightForbiddenRSCount int
	TotalUnhealthForbiddenRSCount   int

	TotalAllDownlistenerCountByWeight int
	TotalAllDownRuleCountByWeight     int

	TotalAllDownRuleCountByHealth     int
	TotalAllDownlistenerCountByHealth int
}

// CloudLoadBalancerInfo 结构体包含了云负载均衡的详细信息
type CloudLoadBalancerInfo struct {
	CLBReferences

	ClusterID        string    // TKE 集群 ID
	CreatedByTKE     string    // 是否 TKE 自动创建
	LoadBalancerId   *string   // 负载均衡实例 ID
	LoadBalancerName *string   // 负载均衡实例的名称
	LoadBalancerType string    // 负载均衡实例的网络类型
	Forward          string    // 负载均衡类型标识
	LoadBalancerVips []*string // 负载均衡实例的 VIP 列表
	Status           string    // 负载均衡实例的状态
	CreateTime       *string   // 负载均衡实例的创建时间
	VpcId            *string   // 私有网络的 ID
	AddressIPVersion *string   // IP版本，ipv4 | ipv6
	SnatPro          string    // 是否开启SnatPro
}

// CLB 周边系统相关链接
type CLBReferences struct {
	DFSCLBDetail    string // DFS 实例详情
	DFSCLBMonitor   string // DFS 监控链接
	DFSCLBAccessLog string // DFS 七层访问日志链接
	DFSCLSLog       string // CLB 业务日志
	DFSCLBTask      string // DFS CLB任务日志链接
	DFSCLBHealth    string // DFS CLB健康诊断链接
}

// LoadBalancerResourceInfo 结构体包含了负载均衡的资源信息
type LoadBalancerResourceInfo struct {
	ClusterID                string
	CLBRegion                string                   // CLB所在区域
	LoadBalancerResourceLock LoadBalancerResourceLock `json:",omitempty"` // 负载均衡资源锁信息

	UsingServiceCount int // 使用的服务数量
	UsingIngressCount int // 使用的入口数量

	Resources []ObjectReference `json:",omitempty"` // 资源引用列表

	Migrate *MigrationInfo `json:",omitempty"` // 资源引用列表
}

type MigrationInfo struct {
	IsMigratedCLB      string
	MigrateFromCluster string
}

// LoadBalancerResourceLock 结构体包含了负载均衡资源的锁定状态
type LoadBalancerResourceLock struct {
	Status   string          // 锁定状态
	Resource ObjectReference // 被锁定的资源引用
}

// ObjectReference 结构体包含了资源的引用信息
type ObjectReference struct {
	PortProtocols string // 端口协议
	Namespace     string // 命名空间
	Name          string // 名称
	Kind          string // 类型
	PodCount      string // 后端 Pod 个数
	DirectAccess  string
}

// CLBRisk 结构体包含了负载均衡的风险信息
type CLBRisk struct {
	Type     string // 风险类型
	Port     string // 端口
	Protocol string // 协议
	Host     string // 主机
	Path     string // 路径
	Reason   string // 原因
}
