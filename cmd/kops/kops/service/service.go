package services

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	"gorm.io/gorm"

	"git.woa.com/kateway/pkg/telemetry/jaeger"

	"git.woa.com/kateway/kateway-server/cmd/kops/common"
	cluster2 "git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
	eksservice "git.woa.com/kateway/kateway-server/cmd/kops/kops/eks/service"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	tkeservice "git.woa.com/kateway/kateway-server/cmd/kops/kops/tke/service"
	region2 "git.woa.com/kateway/kateway-server/pkg/util/region"
)

type ServiceService struct {
	db *gorm.DB
}

func newServiceService() *ServiceService {
	return &ServiceService{
		db: services.db,
	}
}

func (s *ServiceService) Mock(ctx context.Context, cluster *model.Cluster, version string) (result []map[string]string, err error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	regionInfo := region2.Get(cluster.Region)
	ccrRegion, exist := common.CcrRegionMap[regionInfo.Alias]
	if !exist {
		return nil, fmt.Errorf("Region %s ccr not configed", regionInfo.Alias)
	}
	image := fmt.Sprintf("%s%s:%s", ccrRegion, common.ServiceControllerImageRepo, version)
	switch cluster.Type {
	case "tke":
		tkeCluster, err := Get().TKE().Get(ctx, cluster.Region, cluster.ClusterID)
		if err != nil {
			return nil, err
		}
		records, err := tkeservice.MockRun(ctx, tkeCluster, image, 15, true, lo.ToPtr(false))
		if err != nil {
			return nil, err
		}
		return records.ToMap(), nil
	case "eks":
		eksCluster, err := cluster2.GetEKSCluster(regionInfo.Name, cluster.ClusterID)
		if err != nil {
			return nil, err
		}
		return eksservice.MockRun(ctx, eksCluster, image, 15, true, lo.ToPtr(false))
	}

	return result, nil
}
