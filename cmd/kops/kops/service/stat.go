package services

import (
	"reflect"
	"time"

	"github.com/go-logr/logr"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"git.woa.com/kateway/pkg/app/version"
	"git.woa.com/kateway/pkg/telemetry/log"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model/stat"
)

type Stat struct {
	log logr.Logger
	db  *gorm.DB
}

func NewStat() *Stat {
	return &Stat{
		log: log.WithName("stat"),
		db:  services.db,
	}
}

func (s *Stat) Run(date string) {
	startAt := time.Now()
	s.log.Info("Running", "startAt", startAt)
	defer func() {
		s.log.Info("Done", "cost", time.Since(startAt).String())
	}()

	s.statClusterTotal(date)
	err := s.statKatewayTotal(date)
	if err != nil {
		s.log.Error(err, "statKatewayTotal error")
	}
}

func (s *Stat) save(data any) error {
	rvalue := reflect.ValueOf(data)
	var updateFields []string
	if rvalue.Kind() == reflect.Slice {
		if rvalue.Len() == 0 {
			return nil
		}
		updateFields = stat.GetUpdateFields(rvalue.Index(0).Interface())
	} else {
		updateFields = stat.GetUpdateFields(data)
	}

	db := s.db.Model(data).Clauses(
		clause.OnConflict{
			DoUpdates: clause.AssignmentColumns(updateFields),
		})

	return db.Create(data).Error
}

func (s *Stat) statKatewayTotal(date string) error {
	err := s.statTotal(date, "Service")
	if err != nil {
		return err
	}

	err = s.statTotal(date, "Ingress")
	if err != nil {
		return err
	}

	return nil
}

func (s *Stat) populateTagsStat(res *stat.KatewayTotal) error {
	if err := s.db.Model(&model.Tag{}).Where("`State` = ?", model.TagStateUnused).Count(&res.UnusedTagsTotal).Error; err != nil {
		return err
	}
	if err := s.db.Model(&model.Tag{}).Count(&res.TagsTotal).Error; err != nil {
		return err
	}
	if res.TagsTotal > 0 {
		res.UnusedTagsPercent = float64(res.UnusedTagsTotal) / float64(res.TagsTotal)
	}
	return nil
}

func (s *Stat) statTotal(date string, statType string) error {
	startAt := time.Now()
	s.log.Info("Running", "startAt", startAt, "job", "statTotal", "type", statType)
	defer func() {
		s.log.Info("Done", "cost", time.Since(startAt).String(), "job", "statTotal", "type", statType)
	}()

	rows, err := s.db.Raw(`select Version, Count(Version) as Count from Kateway where date=?  and type=? group by Version`, date, statType).Rows()
	if err != nil {
		panic(err)
	}
	defer rows.Close()

	result := &stat.KatewayTotal{
		Date: date,
		Type: statType,
	}
	for rows.Next() {
		row := &struct {
			Version string
			Count   int64
		}{}
		services.db.ScanRows(rows, row)
		ok, _ := version.Check(row.Version, config.Get().GetVersion(statType).ExpectedVersion)
		if ok {
			result.ExpectedVersionCount += row.Count
		}
		result.Total += row.Count
	}
	result.ExpectedVersionPercent = float64(result.ExpectedVersionCount) / float64(result.Total)

	row := s.db.Raw(`select total from Task where TaskID=?  and type=?`, date, statType).Row()
	if row.Err() != nil {
		return row.Err()
	}
	var totalCluster int64
	row.Scan(&totalCluster)
	result.ValidClusterPencent = float64(result.Total) / float64(totalCluster)

	row = s.db.Raw(`select count(*) AS count from Kateway where date=?  and type=? and MockError=''`, date, statType).Row()
	if row.Err() != nil {
		return row.Err()
	}
	row.Scan(&result.MockCount)
	result.MockPercent = float64(result.MockCount) / float64(result.Total)

	row = s.db.Raw(`select count(*) AS count from Kateway where date=?  and type=? and CheckError=''`, date, statType).Row()
	if row.Err() != nil {
		return row.Err()
	}
	row.Scan(&result.CheckCount)
	result.CheckPercent = float64(result.CheckCount) / float64(result.Total)

	row = s.db.Raw(`select count(*) AS count from Kateway where date=?  and type=? and Replicas=0`, date, statType).Row()
	if row.Err() != nil {
		return row.Err()
	}
	row.Scan(&result.NotRunningCount)

	row = s.db.Raw(`select count(*) AS count from Kateway where date=?  and type=? and Replicas>0`, date, statType).Row()
	if row.Err() != nil {
		return row.Err()
	}
	row.Scan(&result.RunningCount)
	result.RunningPercent = float64(result.RunningCount) / (float64(result.RunningCount) + float64(result.NotRunningCount))

	if err := s.populateTagsStat(result); err != nil {
		return err
	}

	return s.save(result)
}

func (s *Stat) statClusterTotal(date string) {
	startAt := time.Now()
	s.log.Info("Running", "startAt", startAt, "job", "statClusterTotal")
	defer func() {
		s.log.Info("Done", "cost", time.Since(startAt).String(), "job", "statClusterTotal")
	}()

	result := &stat.ClusterTotal{
		Date: date,
	}

	db := services.tkeossDB

	row := db.Raw(`select count(distinct appId) from tke_cluster where lifeState='normal'`).Row()
	if row.Err() != nil {
		panic(row.Err())
	}
	tkeTotal := 0
	row.Scan(&tkeTotal)

	row = db.Raw(`select count(distinct appid) from eks_cluster where phase='Running'`).Row()
	if row.Err() != nil {
		panic(row.Err())
	}
	eksTotal := 0
	row.Scan(&eksTotal)

	result.TotalUser = tkeTotal + eksTotal

	row = db.Raw(`select count(*) from tke_cluster where lifeState='normal' and isClusterDeploy!=4`).Row()
	if row.Err() != nil {
		panic(row.Err())
	}
	row.Scan(&result.TKEHosting)

	row = db.Raw(`select count(*) from tke_cluster where lifeState='normal' and isClusterDeploy=4`).Row()
	if row.Err() != nil {
		panic(row.Err())
	}
	row.Scan(&result.TKEIndependent)

	row = db.Raw(`select count(*) from eks_cluster where phase='Running'`).Row()
	if row.Err() != nil {
		panic(row.Err())
	}
	row.Scan(&result.EKS)

	result.TKE = result.TKEHosting + result.TKEIndependent
	result.TotalCluster = result.TKE + result.EKS
	result.TKEHostingPencent = float64(result.TKEHosting) / float64(result.TotalCluster)
	result.TKEIndependentPencent = float64(result.TKEIndependent) / float64(result.TotalCluster)
	result.EKSPencent = float64(result.EKS) / float64(result.TotalCluster)

	s.save(result)
}
