package services

import (
	"context"
	"fmt"

	"git.woa.com/kateway/pkg/telemetry/jaeger"

	"git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
	"git.woa.com/kateway/kateway-server/pkg/models"
)

type TKEService struct{}

func newTKEService() *TKEService {
	return &TKEService{}
}

func (s *TKEService) Get(ctx context.Context, region string, clusterID string) (*models.Cluster, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cluster := cluster.MustGetTKECluster(region, clusterID)
	if cluster == nil {
		return nil, fmt.Errorf("cluster %v not found", clusterID)
	}

	return cluster, nil
}
