package services

import (
	"gorm.io/gorm"

	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
)

type ReleaseAuditService struct {
	db *gorm.DB
}

func newReleaseAuditService(db *gorm.DB) *ReleaseAuditService {
	return &ReleaseAuditService{
		db: db,
	}
}

func (s *ReleaseAuditService) Update(item *model.ReleaseAudit) error {
	db := s.db.Model(item)

	return db.Create(item).Error
}

func (s *ReleaseAuditService) Last(clusterID string, item *model.ReleaseAudit) error {
	db := s.db.Model(item)
	return db.Where("ClusterID = ?", clusterID).Order("FinishedAt DESC").Limit(1).First(item).Error
}

func (s *ReleaseAuditService) Select(clusterID string, records *[]model.ReleaseAudit) error {
	return s.db.Model(&model.ReleaseAudit{}).Where("ClusterID = ?", clusterID).Find(records).Error
}
