package services

import (
	"context"
	"fmt"
	"time"

	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	"golang.org/x/sync/errgroup"

	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	"git.woa.com/kateway/kateway-server/pkg/service/credential"
	"git.woa.com/kateway/kateway-server/pkg/tmp/tencentcloud"
	"git.woa.com/kateway/kateway-server/pkg/tmp/web/cloudctx"
	"git.woa.com/kateway/kateway-server/pkg/util/retry"
)

// 全局 yun api 限流器
type RateLimiterService struct {
	limiter *model.GlobalRateLimiter
}

func newRateLimiterService(rateLimit int) *RateLimiterService {
	return &RateLimiterService{
		limiter: model.NewGlobalRateLimiter(rateLimit),
	}
}

func (svc *RateLimiterService) Take(region, uin, api string) {
	svc.limiter.Take(region, uin, api)
}

type CLBService struct {
	credSvc credential.Credential
}

func newCLBService(credSvc credential.Credential) *CLBService {
	return &CLBService{
		credSvc: credSvc,
	}
}

func (svc CLBService) newCLBClientFor(region, uin string) (*clb.Client, error) {
	cred, err := svc.credSvc.GetCredentials(uin)
	if err != nil {
		return nil, err
	}
	return clb.NewClient(cred, region, profile.NewClientProfile())
}

func (svc CLBService) DescribeLoadBalancer(ctx context.Context, uin, region, id string) (*clb.LoadBalancer, error) {
	cli, err := svc.newCLBClientFor(region, uin)
	if err != nil {
		return nil, err
	}
	Get().RateLimit().Take(region, uin, "CLB/"+"DescribeLoadBalancers")
	req := clb.NewDescribeLoadBalancersRequest()
	req.LoadBalancerIds = []*string{&id}
	resp, err := retry.DoIfNetError(func() (*clb.DescribeLoadBalancersResponse, error) {
		return cli.DescribeLoadBalancersWithContext(ctx, req)
	}, 1*time.Second)
	if err != nil {
		return nil, err
	}
	if len(resp.Response.LoadBalancerSet) == 0 {
		return nil, nil
	}
	return resp.Response.LoadBalancerSet[0], nil
}

func (svc CLBService) List(ctx context.Context) (result []*clb.LoadBalancer, err error) {
	request := clb.NewDescribeLoadBalancersRequest()
	request.Filters = []*clb.Filter{
		{
			Name:   lo.ToPtr("tag-key"),
			Values: []*string{lo.ToPtr("tke-clusterId")},
		},
	}
	return svc.list(ctx, request)
}

func (svc CLBService) ListAutoCreated(ctx context.Context) (result []*clb.LoadBalancer, err error) {
	var (
		tke []*clb.LoadBalancer
		eks []*clb.LoadBalancer
	)
	wg := errgroup.Group{}
	wg.Go(func() error {
		tke, err = svc.ListTKEAutoCreated(ctx)
		return err
	})
	wg.Go(func() error {
		eks, err = svc.ListEKSAutoCreated(ctx)
		return err
	})

	err = wg.Wait()
	if err != nil {
		return nil, err
	}

	return append(tke, eks...), nil
}

func (svc CLBService) ListTKEAutoCreated(ctx context.Context) (result []*clb.LoadBalancer, err error) {
	request := clb.NewDescribeLoadBalancersRequest()
	request.Filters = []*clb.Filter{
		{
			Name:   lo.ToPtr("tag-key"),
			Values: []*string{lo.ToPtr("tke-createdBy-flag")},
		},
	}
	return svc.list(ctx, request)
}

func (svc CLBService) ListEKSAutoCreated(ctx context.Context) (result []*clb.LoadBalancer, err error) {
	request := clb.NewDescribeLoadBalancersRequest()
	request.Filters = []*clb.Filter{
		{
			Name:   lo.ToPtr("tag-key"),
			Values: []*string{lo.ToPtr("tke-created")},
		},
	}
	return svc.list(ctx, request)
}

func (svc CLBService) list(ctx context.Context, request *clb.DescribeLoadBalancersRequest) (result []*clb.LoadBalancer, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic(%s): %v", cloudctx.UIN(ctx), r)
		}
	}()

	// request.LoadBalancerIds = []*string{lo.ToPtr("lb-85jm42xb"), lo.ToPtr("lb-okrfanr7")}

	var (
		offset = int64(0)
		limit  = int64(100)
		total  = int64(1000)
	)
	request.Limit = &limit
	request.Offset = &offset
	for ; offset < total; offset += limit {
		response, err := tencentcloud.Clb(ctx).DescribeLoadBalancersWithContext(ctx, request)
		if err != nil {
			continue
		}
		result = append(result, response.Response.LoadBalancerSet...)
		total = int64(*response.Response.TotalCount)
	}

	return result, nil
}

func (svc CLBService) ListListeners(ctx context.Context, loadBalancerId string) ([]*clb.Listener, error) {
	Get().RateLimit().Take(cloudctx.Region(ctx).Name, cloudctx.UIN(ctx), "CLB/"+"DescribeListeners")
	request := clb.NewDescribeListenersRequest()
	request.LoadBalancerId = lo.ToPtr(loadBalancerId)
	response, err := tencentcloud.Clb(ctx).DescribeListenersWithContext(ctx, request)
	if err != nil {
		return nil, err
	}

	return response.Response.Listeners, nil
}

func (svc CLBService) ListListenerBackends(ctx context.Context, loadBalancerId string) ([]*clb.ListenerBackend, error) {
	uin := cloudctx.UIN(ctx)
	region := cloudctx.Region(ctx).Name
	// 限流
	Get().RateLimit().Take(region, uin, "CLB/"+"DescribeTargets")

	cli, err := svc.newCLBClientFor(region, uin)
	if err != nil {
		return nil, err
	}
	request := clb.NewDescribeTargetsRequest()
	request.LoadBalancerId = lo.ToPtr(loadBalancerId)
	response, err := cli.DescribeTargetsWithContext(ctx, request)
	if err != nil {
		return nil, err
	}

	return response.Response.Listeners, nil
}

func (svc CLBService) ListListenerBackendHealth(ctx context.Context, loadBalancerId string) (*clb.LoadBalancerHealth, error) {
	uin := cloudctx.UIN(ctx)
	region := cloudctx.Region(ctx).Name
	// 限流
	Get().RateLimit().Take(region, uin, "CLB/"+"DescribeTargetHealth")

	cli, err := svc.newCLBClientFor(region, uin)
	if err != nil {
		return nil, err
	}
	request := clb.NewDescribeTargetHealthRequest()
	request.LoadBalancerIds = []*string{&loadBalancerId}
	response, err := cli.DescribeTargetHealthWithContext(ctx, request)
	if err != nil {
		return nil, err
	}

	if len(response.Response.LoadBalancers) != 0 {
		return response.Response.LoadBalancers[0], nil
	}
	return nil, nil
}

func (svc CLBService) GetLoadBalancer(ctx context.Context, loadBalancerId string) (*clb.LoadBalancer, error) {
	uin := cloudctx.UIN(ctx)
	region := cloudctx.Region(ctx).Name
	// 限流
	Get().RateLimit().Take(region, uin, "CLB/"+"DescribeLoadBalancers")

	cli, err := svc.newCLBClientFor(region, uin)
	if err != nil {
		return nil, err
	}
	request := clb.NewDescribeLoadBalancersRequest()
	request.LoadBalancerIds = []*string{lo.ToPtr(loadBalancerId)}
	response, err := cli.DescribeLoadBalancers(request)
	if err != nil {
		return nil, err
	}
	if len(response.Response.LoadBalancerSet) == 0 {
		return nil, fmt.Errorf("loadbalancer(%s) not found", loadBalancerId)
	}

	return response.Response.LoadBalancerSet[0], nil
}

func (svc CLBService) DeleteLoadBalancer(ctx context.Context, loadBalancerId string) (string, error) {
	request := clb.NewDeleteLoadBalancerRequest()
	request.LoadBalancerIds = []*string{lo.ToPtr(loadBalancerId)}
	response, err := tencentcloud.Clb(ctx).DeleteLoadBalancer(request)
	if err != nil {
		return "", err
	}

	return *response.Response.RequestId, nil
}

func (svc CLBService) GetClusterID(lb *clb.LoadBalancer) string {
	return svc.GetTagValue(lb, "tke-clusterId")
}

func (svc CLBService) IsAutoCreated(lb *clb.LoadBalancer) bool {
	return svc.IsTKEAutoCreated(lb) || svc.IsEKSAutoCreated(lb)
}

func (svc CLBService) IsTKEAutoCreated(lb *clb.LoadBalancer) bool {
	value := svc.GetTagValue(lb, "tke-createdBy-flag")

	return value == "yes"
}

func (svc CLBService) IsEKSAutoCreated(lb *clb.LoadBalancer) bool {
	value := svc.GetTagValue(lb, "tke-created")

	return value == "yes"
}

func (svc CLBService) GetTagValue(lb *clb.LoadBalancer, key string) string {
	tag, ok := lo.Find(lb.Tags, func(tag *clb.TagInfo) bool {
		return *tag.TagKey == key
	})
	if !ok {
		return ""
	}

	return *tag.TagValue
}
