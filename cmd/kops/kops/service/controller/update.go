package controller

import (
	"context"
	"fmt"

	"git.woa.com/kateway/kateway-server/pkg/dryrun"
)

func (c *Controller) Update(ctx context.Context, version string, opts UpdateOptions) (res dryrun.Records, err error) {
	writer := opts.Writer

	writer("更新开始: 集群=%s 版本=%s", c.GetCluster().ClusterID, version)

	deploy, err := c.GetDeployment(ctx, GetDeploymentOptions{BypassCache: true})
	if err != nil {
		return
	}

	needDryrun := true
	if *deploy.Spec.Replicas == 0 {
		opts.Writer("跳过预检，因为工作负载副本数为0")
		needDryrun = false
	}

	if needDryrun && opts.Force {
		writer("跳过预检，因为显式指定了强制更新")
		needDryrun = false
	}

	if needDryrun {
		res, err = c.Dryrun(ctx, version, opts.DryrunOptions)
		if err != nil {
			return
		}
		if len(res) != 0 {
			err = fmt.Errorf("更新失败")
			return
		}

		processed, err := c.processSessionAffinity(ctx, version, res, opts.DryrunOptions)
		if err != nil {
			return nil, err
		}

		if processed {
			res, err = c.Dryrun(ctx, version, opts.DryrunOptions)
			if err != nil {
				return nil, err
			}
			if len(res) != 0 {
				err = fmt.Errorf("更新失败")
				return nil, err
			}
		}
	}

	err = c.SetImage(ctx, version, SetImageOptions{
		Writer: writer,
	})
	if err != nil {
		return
	}

	writer("更新成功")

	return
}
