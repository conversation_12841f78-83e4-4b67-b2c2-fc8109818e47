package controller

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	"k8s.io/api/apps/v1"
	"k8s.io/apimachinery/pkg/util/wait"
)

// getNewImage 通过当前镜像和期望镜像，计算出新的镜像，可以兼容目标镜像为完整镜像地址和只带tag的镜像地址
func (c *Controller) getNewImage(oldImage string, expectImage string) string {
	newImage := oldImage
	// 如果expectImage是完整的镜像地址，如ccr.ccs.tencentyun.com/paas/service-controller:v2.5.1，则直接使用
	if strings.Contains(expectImage, "/") {
		newImage = expectImage
	} else {
		// 如果expectImage没有带v，如2.5.1，则加上
		if !strings.HasPrefix(expectImage, "v") {
			expectImage = "v" + expectImage
		}
		// 使用老的镜像地址，只替换tag
		images := strings.Split(oldImage, ":")
		newImage = fmt.Sprintf("%s:%s", images[0], expectImage)
	}

	return newImage
}

func (c *Controller) stopOldPods(ctx context.Context) (replicas int32, err error) {
	updaters := []DeploymentUpdater{
		func(deployment *v1.Deployment) error {
			replicas = *deployment.Spec.Replicas
			deployment.Spec.Replicas = lo.ToPtr(int32(0))
			return nil
		},
	}

	err = c.UpdateDeployment(ctx, updaters...)
	if err != nil {
		return
	}

	// 等待所有pod都停止
	err = wait.PollUntilWithContext(ctx, 5*time.Second, func(ctx context.Context) (bool, error) {
		pods, err := c.ListPod(ctx, ListPodsOptions{BypassCache: true})
		if err != nil {
			return false, fmt.Errorf("获取Pod列表失败: %s", err)
		}

		if len(pods) != 0 {
			return false, nil
		}

		return true, nil
	})

	return
}

func (c *Controller) SetImage(ctx context.Context, version string, opts SetImageOptions) error {
	writer := opts.Writer
	var (
		newImage string
	)

	if c.IsIngress() {
		if err := CheckIngressUpgrade(ctx, NewImage(version).Tag, c.ins.Service()); err != nil {
			return err
		}
	}

	writer("更新镜像: 集群=%s 版本=%s", c.GetCluster().ClusterID, version)

	deploy, err := c.GetDeployment(ctx, GetDeploymentOptions{})
	if err != nil {
		return err
	}
	skipWait := *deploy.Spec.Replicas == 0

	err = ensureRBAC(ctx, c.GetClusterClientset().K8sCli)
	if err != nil {
		return fmt.Errorf("更新RBAC失败: %w", err)
	}

	oldImage, err := c.GetImage(ctx)
	if err != nil {
		return fmt.Errorf("获取当前镜像失败: %w", err)
	}

	var replicas int32
	if !oldImage.CheckVersion(leaseVersionConstraints) && NewImage(version).CheckVersion(leaseVersionConstraints) {
		writer("检测到leader锁实现版本变化，需要先停止所有老pod，保证leader锁从ConfigMap切换到Lease时，不会同时存在两个Leader")
		replicas, err = c.stopOldPods(ctx)
		if err != nil {
			return fmt.Errorf("停止老pod失败: %w", err)
		}
		writer("停止老pod完成: replicas=%d", replicas)
	}

	updaters := []DeploymentUpdater{
		c.BuildContainerImageUpdater(func(image string) string {
			newImage = c.getNewImage(image, version)
			writer("更新镜像: %s => %s", image, newImage)
			return newImage
		}),
		c.BuildContainerArgsUpdater(func(args []string) []string {
			return lo.Filter(args, func(arg string, _ int) bool {
				return arg != "${REUSE_FLAG}" && !strings.HasPrefix(arg, "--reuse-flag")
			})
		}),
		func(deployment *v1.Deployment) error {
			if replicas > 0 {
				deployment.Spec.Replicas = &replicas
			}
			if c.IsService() {
				// 如果是独立集群，则需要确保使用独立的service account
				if c.ins.cluster.IsIndependent() {
					writer("更新独立集群的service account")
					deployment.Spec.Template.Spec.ServiceAccountName = "lb-service"
				}
			} else {
				if !c.ins.cluster.IsEKS() {
					writer("更新非EKS集群的service account")
					deployment.Spec.Template.Spec.ServiceAccountName = "lb-service"
				}
			}
			return nil
		},
	}

	err = c.UpdateDeployment(ctx, updaters...)
	if err != nil {
		return err
	}

	if skipWait {
		writer("更新完成")
		return nil
	}

	writer("等待更新完成")
	var leader *LeaderPod
	err = wait.PollUntilWithContext(ctx, 5*time.Second, func(ctx context.Context) (bool, error) {
		pods, err := c.ListPodToString(ctx, ListPodsOptions{BypassCache: true})
		if err != nil {
			writer("获取Pod列表失败: %s", err)
			return false, nil
		}
		writer("当前Pod列表:\n%s", pods)

		leader, err = c.GetLeaderPod(ctx)
		if err != nil {
			writer("暂无有效leader")
			return false, nil
		}

		if leader.ContainerStatus.Image != newImage {
			writer("新版本leader未生效: %s 当前 %s", leader.Name, leader.ContainerStatus.Image)
			image, err := c.GetImage(ctx)
			if err != nil {
				return false, err
			}
			if image.String() != newImage {
				return false, fmt.Errorf("工作负载镜像被覆盖: %s", image)
			}
			return false, nil
		}

		return true, nil
	})
	if err != nil {
		return err
	}
	writer("更新完成: leader=%s/%s image=%s", leader.Namespace, leader.Name, leader.ContainerStatus.ImageID)

	return nil
}
