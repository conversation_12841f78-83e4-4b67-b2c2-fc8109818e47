package controller

import (
	"fmt"
	"strings"

	"k8s.io/klog/v2"

	"git.woa.com/kateway/kateway-server/pkg/version"
)

type Image struct {
	Repo      string
	Namespace string
	Name      string
	Tag       string
}

// NewImage 解析镜像字符串为 Image 结构体
// 支持的格式示例：
//   - "v2.5.2" → Repo="", Namespace="", Name="", Tag="v2.5.2"
//   - "2.5.2" → Repo="", Namespace="", Name="", Tag="v2.5.2"
//   - "ccr.ccs.tencentyun.com/paas/service-controller:v2.5.2" → Repo="ccr.ccs.tencentyun.com", Namespace="paas", Name="service-controller", Tag="v2.5.2"
func NewImage(imageStr string) *Image {
	img := &Image{}

	// 分割标签部分
	parts := strings.SplitN(imageStr, ":", 2)
	if len(parts) == 1 {
		img.Tag = parts[0]
		if !strings.HasPrefix(img.Tag, "v") {
			img.Tag = "v" + img.Tag
		}
		return img
	}

	img.Tag = parts[1]

	// 分割 Repo/Namespace/Name
	pathParts := strings.Split(parts[0], "/")
	if len(pathParts) != 3 {
		panic(fmt.Sprintf("invalid image path: %s", parts[0]))
	}
	img.Repo = pathParts[0]
	img.Namespace = pathParts[1]
	img.Name = pathParts[2]

	return img
}

// 返回镜像字符串
func (img Image) String() string {
	repo := img.Repo
	if repo != "" {
		repo += "/"
	}
	namespace := img.Namespace
	if namespace != "" {
		namespace += "/"
	}
	return fmt.Sprintf("%s%s%s:%s", repo, namespace, img.Name, img.Tag)
}

func (img Image) CheckVersion(constraint string) bool {
	ok, err := version.Check(img.Tag, constraint)
	if err != nil {
		klog.Infof("tag %s constraint %s", img.Tag, constraint)
		panic(err)
	}
	return ok
}
