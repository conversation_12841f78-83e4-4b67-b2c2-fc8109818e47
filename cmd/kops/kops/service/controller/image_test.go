package controller

import (
	"reflect"
	"testing"
)

func TestNewImage(t *testing.T) {
	type args struct {
		imageStr string
	}
	tests := []struct {
		name string
		args args
		want *Image
	}{
		{
			name: "v1.0.0",
			args: args{
				imageStr: "v1.0.0",
			},
			want: &Image{
				Tag: "v1.0.0",
			},
		},
		{
			name: "1.0.0",
			args: args{
				imageStr: "1.0.0",
			},
			want: &Image{
				Tag: "v1.0.0",
			},
		},
		{
			name: "ccr.ccs.tencentyun.com/paas/service-controller:v2.5.2",
			args: args{
				imageStr: "ccr.ccs.tencentyun.com/paas/service-controller:v2.5.2",
			},
			want: &Image{
				Repo:      "ccr.ccs.tencentyun.com",
				Namespace: "paas",
				Name:      "service-controller",
				Tag:       "v2.5.2",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewImage(tt.args.imageStr); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>rf("NewImage() = %v, want %v", got, tt.want)
			}
		})
	}
}
