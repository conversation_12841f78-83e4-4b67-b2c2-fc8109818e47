package controller

import (
	"context"
	"fmt"
	"time"

	"github.com/patrickmn/go-cache"
	"github.com/samber/lo"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"

	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
	"git.woa.com/kateway/kateway-server/pkg/service/cluster"
)

type Instance struct {
	cluster, metaCluster                   *model.Cluster
	clusterClientSet, metaClusterClientSet *cluster.ClientsSet
}

func (ins Instance) List(ctx context.Context) []*Controller {
	cs := []*Controller{
		ins.Ingress(),
		ins.Service(),
	}
	return lo.Filter(cs, func(c *Controller, _ int) bool {
		enabled, _ := c.Enabled(ctx)
		return enabled
	})
}

func (ins Instance) Ingress() *Controller {
	if ins.MergedIngressControllerEnabled(context.Background()) {
		return ins.Service()
	}

	nn := types.NamespacedName{
		Name:      "l7-lb-controller",
		Namespace: metav1.NamespaceSystem,
	}
	var runningInMeta bool
	if ins.cluster.IsEKS() {
		nn.Name = fmt.Sprintf("%s-ingress-controller", ins.cluster.ClusterID)
		nn.Namespace = ins.cluster.ClusterID
		runningInMeta = true
	}
	return &Controller{
		kind:           KindIngress,
		ins:            ins,
		namespacedName: nn,
		runningInMeta:  runningInMeta,
		cache:          cache.New(5*time.Second, 10*time.Second),
	}
}

func (ins Instance) Service() *Controller {
	nn := types.NamespacedName{
		Name:      "service-controller",
		Namespace: metav1.NamespaceSystem,
	}
	var runningInMeta bool
	if !ins.cluster.IsIndependent() {
		nn.Name = fmt.Sprintf("%s-%s", ins.cluster.ClusterID, nn.Name)
		nn.Namespace = ins.cluster.ClusterID
		runningInMeta = true
	}
	return &Controller{
		kind:           KindService,
		ins:            ins,
		namespacedName: nn,
		runningInMeta:  runningInMeta,
		cache:          cache.New(5*time.Second, 10*time.Second),
	}
}

func (ins Instance) MergedIngressControllerEnabled(ctx context.Context) bool {
	cm, err := ins.GetClusterClientset().CoreV1().ConfigMaps(metav1.NamespaceSystem).Get(ctx, "tke-service-controller-config", metav1.GetOptions{})
	if err != nil {
		panic(fmt.Sprintf("get configmap tke-service-controller-config failed: %v", err))
	}

	return cm.Data["EnableIngressController"] == "true"
}

func (ins Instance) Cluster() *model.Cluster {
	return ins.cluster
}

func (ins Instance) GetClusterClientset() *cluster.ClientsSet {
	return ins.clusterClientSet
}

func (ins Instance) GetMetaClusterClientset() *cluster.ClientsSet {
	return ins.metaClusterClientSet
}

func MustGetByCluster(ctx context.Context, cls *model.Cluster) *Instance {
	return lo.Must(GetByCluster(ctx, cls))
}

func GetByCluster(ctx context.Context, cls *model.Cluster) (*Instance, error) {
	cs, err := services.Get().Cluster().Clientset(ctx, cls)
	if err != nil {
		return nil, err
	}

	metaCls, err := services.Get().Cluster().Get(ctx, cls.MetaClusterID)
	if err != nil {
		return nil, err
	}

	metaCs, err := services.Get().Cluster().Clientset(ctx, metaCls)
	if err != nil {
		return nil, err
	}

	return &Instance{
		cluster:              cls,
		clusterClientSet:     cs,
		metaCluster:          metaCls,
		metaClusterClientSet: metaCs,
	}, nil
}

func MustGetByClusterID(ctx context.Context, cluster string) *Instance {
	return lo.Must(GetByClusterID(ctx, cluster))
}

func GetByClusterID(ctx context.Context, cluster string) (*Instance, error) {
	cls, err := services.Get().Cluster().Get(ctx, cluster)
	if err != nil {
		return nil, err
	}

	return GetByCluster(ctx, cls)
}

func GetByClusterIDWithIanvs(ctx context.Context, cluster string, user string, token string) (*Instance, error) {
	cls, err := services.Get().Cluster().Get(ctx, cluster)
	if err != nil {
		return nil, err
	}

	cs, err := services.Get().Cluster().ClientsetByIanvs(ctx, cluster, user, token)
	if err != nil {
		return nil, err
	}

	metaCls, err := services.Get().Cluster().Get(ctx, cls.MetaClusterID)
	if err != nil {
		return nil, err
	}

	metaCs, err := services.Get().Cluster().ClientsetByIanvs(ctx, cls.MetaClusterID, user, token)
	if err != nil {
		return nil, err
	}

	return &Instance{
		cluster:              cls,
		clusterClientSet:     cs,
		metaCluster:          metaCls,
		metaClusterClientSet: metaCs,
	}, nil
}
