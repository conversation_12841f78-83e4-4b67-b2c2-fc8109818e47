package controller

import (
	"context"

	corev1 "k8s.io/api/core/v1"
	rbacv1 "k8s.io/api/rbac/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

var (
	dryrunServiceAccount = &corev1.ServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Name: dryrunRBACName,
		},
	}
	dryrunClusterRole = func() *rbacv1.ClusterRole {
		dryrun := &rbacv1.ClusterRole{
			ObjectMeta: metav1.ObjectMeta{
				Name: dryrunRBACName,
			},
		}
		for _, rule := range clusterRole.Rules {
			rule.Verbs = []string{"get", "list", "watch"}
			dryrun.Rules = append(dryrun.Rules, rule)
		}
		// locksmith需要token权限来启动
		dryrun.Rules = append(dryrun.Rules, rbacv1.PolicyRule{
			APIGroups: []string{""},
			Resources: []string{
				"serviceaccounts/token",
			},
			Verbs: []string{
				"*",
			},
		})

		return dryrun
	}()
)

func dryrunClusterRoleBinding(namespace string) *rbacv1.ClusterRoleBinding {
	return &rbacv1.ClusterRoleBinding{
		ObjectMeta: metav1.ObjectMeta{
			Name: dryrunRBACName,
		},
		RoleRef: rbacv1.RoleRef{
			APIGroup: rbacv1.GroupName,
			Kind:     "ClusterRole",
			Name:     dryrunRBACName,
		},
		Subjects: []rbacv1.Subject{
			{
				Kind:      rbacv1.ServiceAccountKind,
				Name:      dryrunRBACName,
				Namespace: namespace,
			},
		},
	}
}

func ensureDryrunRBAC(ctx context.Context, client kubernetes.Interface, namespace string) error {
	_, err := client.CoreV1().ServiceAccounts(namespace).Create(ctx, dryrunServiceAccount, metav1.CreateOptions{})
	if err != nil {
		if apierrors.IsAlreadyExists(err) {
			_, err = client.CoreV1().ServiceAccounts(namespace).Update(ctx, dryrunServiceAccount, metav1.UpdateOptions{})
			if err != nil {
				return err
			}
		} else {
			return err
		}
	}

	_, err = client.RbacV1().ClusterRoles().Create(ctx, dryrunClusterRole, metav1.CreateOptions{})
	if err != nil {
		if apierrors.IsAlreadyExists(err) {
			_, err = client.RbacV1().ClusterRoles().Update(ctx, dryrunClusterRole, metav1.UpdateOptions{})
			if err != nil {
				return err
			}
		} else {
			return err
		}
	}
	_, err = client.RbacV1().ClusterRoleBindings().Create(ctx, dryrunClusterRoleBinding(namespace), metav1.CreateOptions{})
	if err != nil {
		if apierrors.IsAlreadyExists(err) {
			_, err = client.RbacV1().ClusterRoleBindings().Update(ctx, dryrunClusterRoleBinding(namespace), metav1.UpdateOptions{})
			if err != nil {
				return err
			}
		} else {
			return err
		}
	}

	return nil
}

const (
	rbacName = "lb-service"
)

var (
	serviceAccount = &corev1.ServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Name:      rbacName,
			Namespace: metav1.NamespaceSystem,
		},
	}

	clusterRole = &rbacv1.ClusterRole{
		ObjectMeta: metav1.ObjectMeta{
			Name: rbacName,
		},
		Rules: []rbacv1.PolicyRule{
			{
				APIGroups: []string{"apps"},
				Resources: []string{
					"deployments",
					"replicasets",
				},
				Verbs: []string{
					"get",
					"list",
					"watch",
				},
			},
			{
				APIGroups: []string{""},
				Resources: []string{
					"nodes",
					"endpoints",
					"events",
					"services",
					"services/status",
					"pods",
					"pods/status",
					"configmaps",
					"secrets",
				},
				Verbs: []string{
					"*",
				},
			},
			{
				APIGroups: []string{"extensions", "networking.k8s.io"},
				Resources: []string{
					"ingresses",
					"ingresses/status",
				},
				Verbs: []string{
					"*",
				},
			},
			{
				APIGroups: []string{"apiextensions.k8s.io"},
				Resources: []string{
					"customresourcedefinitions",
				},
				Verbs: []string{
					"*",
				},
			},
			{
				APIGroups: []string{"cloud.tencent.com"},
				Resources: []string{
					"tkeserviceconfigs",
					"multiclusterservices",
					"multiclusterservices/status",
					"multiclusteringresses",
					"multiclusteringresses/status",
				},
				Verbs: []string{
					"*",
				},
			},
			{
				APIGroups: []string{"discovery.k8s.io"},
				Resources: []string{"endpointslices"},
				Verbs:     []string{"get", "list", "watch"},
			},
			{
				APIGroups: []string{"networking.tke.cloud.tencent.com"},
				Resources: []string{
					"loadbalancerresources",
					"loadbalancerresources/status",
				},
				Verbs: []string{
					"*",
				},
			},
			{
				APIGroups: []string{"admissionregistration.k8s.io"},
				Resources: []string{
					"mutatingwebhookconfigurations",
					"validatingwebhookconfigurations",
				},
				Verbs: []string{
					"*",
				},
			},
			{
				APIGroups: []string{"coordination.k8s.io"},
				Resources: []string{
					"leases",
				},
				Verbs: []string{
					"*",
				},
			},
		},
	}
	clusterRoleBinding = &rbacv1.ClusterRoleBinding{
		ObjectMeta: metav1.ObjectMeta{
			Name: rbacName,
		},
		RoleRef: rbacv1.RoleRef{
			APIGroup: "rbac.authorization.k8s.io",
			Kind:     "ClusterRole",
			Name:     rbacName,
		},
		Subjects: []rbacv1.Subject{
			{
				Kind:      "ServiceAccount",
				Name:      rbacName,
				Namespace: metav1.NamespaceSystem,
			},
		},
	}
)

func ensureRBAC(ctx context.Context, client kubernetes.Interface) error {
	_, err := client.CoreV1().ServiceAccounts(metav1.NamespaceSystem).Create(ctx, serviceAccount, metav1.CreateOptions{})
	if err != nil {
		if apierrors.IsAlreadyExists(err) {
			_, err = client.CoreV1().ServiceAccounts(metav1.NamespaceSystem).Update(ctx, serviceAccount, metav1.UpdateOptions{})
			if err != nil {
				return err
			}
		} else {
			return err
		}
	}

	_, err = client.RbacV1().ClusterRoles().Create(ctx, clusterRole, metav1.CreateOptions{})
	if err != nil {
		if apierrors.IsAlreadyExists(err) {
			_, err = client.RbacV1().ClusterRoles().Update(ctx, clusterRole, metav1.UpdateOptions{})
			if err != nil {
				return err
			}
		} else {
			return err
		}
	}
	_, err = client.RbacV1().ClusterRoleBindings().Create(ctx, clusterRoleBinding, metav1.CreateOptions{})
	if err != nil {
		if apierrors.IsAlreadyExists(err) {
			_, err = client.RbacV1().ClusterRoleBindings().Update(ctx, clusterRoleBinding, metav1.UpdateOptions{})
			if err != nil {
				return err
			}
		} else {
			return err
		}
	}

	return nil
}
