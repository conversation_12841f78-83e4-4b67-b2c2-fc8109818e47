package controller

import (
	"reflect"
	"strings"
	"testing"
)

func TestAddArgs(t *testing.T) {
	tests := []struct {
		name        string
		currentArgs []string
		desiredArgs []string
		want        []string
	}{
		{
			name:        "完全覆盖已有参数",
			currentArgs: []string{"--log-level=info", "listen=8080"},
			desiredArgs: []string{"--log-level=debug", "listen=9090"},
			want:        []string{"--log-level=debug", "listen=9090"},
		},
		{
			name:        "混合覆盖和新增参数",
			currentArgs: []string{"a=1", "b=2", "c=3"},
			desiredArgs: []string{"b=20", "d=4"},
			want:        []string{"a=1", "b=20", "c=3", "d=4"},
		},
		{
			name:        "混合覆盖和新增参数2",
			currentArgs: []string{"a=1", "--b=2", "c=3"},
			desiredArgs: []string{"--b=20", "--d=4", "e=5"},
			want:        []string{"a=1", "--b=20", "c=3", "--d=4", "e=5"},
		},
		{
			name:        "添加全新参数",
			currentArgs: []string{"--debug"},
			desiredArgs: []string{"--threads=4"},
			want:        []string{"--debug", "--threads=4"},
		},
		{
			name:        "当前参数为空",
			currentArgs: []string{},
			desiredArgs: []string{"--log-level=info"},
			want:        []string{"--log-level=info"},
		},
		{
			name:        "期望参数为空",
			currentArgs: []string{"--log-level=info"},
			desiredArgs: []string{},
			want:        []string{"--log-level=info"},
		},
		{
			name:        "参数不同格式处理",
			currentArgs: []string{"--verbose", "port=8080"},
			desiredArgs: []string{"--verbose=true", "port"},
			want:        []string{"--verbose=true", "port"},
		},
		{
			name:        "保留未冲突参数的顺序",
			currentArgs: []string{"first=1", "second=2", "third=3"},
			desiredArgs: []string{"second=20", "fourth=4"},
			want:        []string{"first=1", "second=20", "third=3", "fourth=4"},
		},
		{
			name:        "带空值的参数处理",
			currentArgs: []string{"--flag=", "empty"},
			desiredArgs: []string{"--flag=value", "empty=nil"},
			want:        []string{"--flag=value", "empty=nil"},
		},
		{
			name:        "多个相同key的参数覆盖",
			currentArgs: []string{"repeat=1", "repeat=2"},
			desiredArgs: []string{"repeat=3"},
			want:        []string{"repeat=3", "repeat=3"},
		},
		// 新增测试用例
		{
			name:        "环境变量参数与新参数冲突",
			currentArgs: []string{"${DB_URL}", "--port=8080"},
			desiredArgs: []string{"--db-url=mysql://localhost"},
			want:        []string{"--port=8080", "--db-url=mysql://localhost"},
		},
		{
			name:        "混合环境变量和普通参数更新",
			currentArgs: []string{"${CACHE_SIZE}", "--debug", "timeout=30"},
			desiredArgs: []string{"--cache-size=512", "--debug=true"},
			want:        []string{"--debug=true", "timeout=30", "--cache-size=512"},
		},
		{
			name:        "带特殊符号的参数值",
			currentArgs: []string{"--key=abc=123"},
			desiredArgs: []string{"--key=xyz=789"},
			want:        []string{"--key=xyz=789"},
		},
		{
			name:        "参数值包含连字符",
			currentArgs: []string{"--api-url=http://old-service"},
			desiredArgs: []string{"--api-url=https://new-service/v2"},
			want:        []string{"--api-url=https://new-service/v2"},
		},
		{
			name:        "多个环境变量参数处理",
			currentArgs: []string{"${LOG_LEVEL}", "${CACHE_ENABLED}"},
			desiredArgs: []string{"--log-level=debug", "--cache-enabled=true"},
			want:        []string{"--log-level=debug", "--cache-enabled=true"},
		},
		{
			name:        "多个环境变量参数处理",
			currentArgs: []string{"${CLUSTERNAME}", "${MASTER}", "${BACKEND_QUOTA}", "${LISTENER_QUOTA}", "${REUSE_FLAG}"},
			desiredArgs: []string{"--backend-quota=100", "--listener-quota=40"},
			want:        []string{"${CLUSTERNAME}", "${MASTER}", "${REUSE_FLAG}", "--backend-quota=100", "--listener-quota=40"},
		},
		{
			name:        "布尔参数转换",
			currentArgs: []string{"--verbose"},
			desiredArgs: []string{"--verbose=true"},
			want:        []string{"--verbose=true"},
		},
		{
			name:        "带数字后缀的参数",
			currentArgs: []string{"worker_count=4"},
			desiredArgs: []string{"worker_count=8"},
			want:        []string{"worker_count=8"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行参数合并操作
			fn := setArgs(tt.desiredArgs)
			got := fn(tt.currentArgs)

			// 验证结果
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("参数合并结果错误:\n当前参数: %v\n期望参数: %v\n实际结果: %v\n期望结果: %v",
					tt.currentArgs, tt.desiredArgs, got, tt.want)
			}

			// 验证期望参数全部存在
			for _, darg := range tt.desiredArgs {
				found := false
				dkey := strings.SplitN(darg, "=", 2)[0]
				for _, garg := range got {
					gkey := strings.SplitN(garg, "=", 2)[0]
					if dkey == gkey {
						found = true
						// 验证值是否正确更新
						if garg != darg {
							t.Errorf("参数值未正确更新:\n期望: %s\n实际: %s", darg, garg)
						}
						break
					}
				}
				if !found {
					t.Errorf("期望参数未找到: %s\n完整参数列表: %v", darg, got)
				}
			}
		})
	}
}

func TestDelArgs(t *testing.T) {
	tests := []struct {
		name       string
		current    []string
		removeKeys []string
		want       []string
	}{
		{
			name:       "删除单个参数",
			current:    []string{"--log-level=info", "listen=8080"},
			removeKeys: []string{"--log-level"},
			want:       []string{"listen=8080"},
		},
		{
			name:       "删除多个参数",
			current:    []string{"a=1", "b=2", "c=3"},
			removeKeys: []string{"b", "c"},
			want:       []string{"a=1"},
		},
		{
			name:       "删除带不同值的相同键",
			current:    []string{"debug=true", "debug=false"},
			removeKeys: []string{"debug"},
			want:       []string{},
		},
		{
			name:       "删除不存在的键",
			current:    []string{"--verbose", "port=8080"},
			removeKeys: []string{"--log"},
			want:       []string{"--verbose", "port=8080"},
		},
		{
			name:       "混合删除带值和不带值的键",
			current:    []string{"key1", "key2=value", "key3=123"},
			removeKeys: []string{"key1", "key3"},
			want:       []string{"key2=value"},
		},
		{
			name:       "保持原有顺序",
			current:    []string{"first", "second", "third"},
			removeKeys: []string{"second"},
			want:       []string{"first", "third"},
		},
		{
			name:       "删除所有参数",
			current:    []string{"a=1", "b=2"},
			removeKeys: []string{"a", "b"},
			want:       []string{},
		},
		{
			name:       "处理空输入",
			current:    []string{},
			removeKeys: []string{"any"},
			want:       []string{},
		},
		{
			name:       "多个环境变量参数处理",
			current:    []string{"${LOG_LEVEL}", "${CACHE_ENABLED}"},
			removeKeys: []string{"--log-level", "--cache-enabled"},
			want:       []string{},
		},
		{
			name:       "多个环境变量参数处理",
			current:    []string{"${CLUSTERNAME}", "${MASTER}", "${BACKEND_QUOTA}", "${LISTENER_QUOTA}", "${REUSE_FLAG}"},
			removeKeys: []string{"--backend-quota", "--listener-quota"},
			want:       []string{"${CLUSTERNAME}", "${MASTER}", "${REUSE_FLAG}"},
		},
		{
			name:       "多个环境变量参数处理",
			current:    []string{"${CLUSTERNAME}", "${MASTER}", "${BACKEND_QUOTA}", "${LISTENER_QUOTA}", "${REUSE_FLAG}", "--backend-quota=100", "--listener-quota=40"},
			removeKeys: []string{"--backend-quota", "--listener-quota"},
			want:       []string{"${CLUSTERNAME}", "${MASTER}", "${REUSE_FLAG}"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行删除操作
			fn := delArgs(tt.removeKeys)
			got := fn(tt.current)

			// 验证结果
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("参数删除结果错误:\n输入参数: %v\n删除键: %v\n实际结果: %v\n期望结果: %v",
					tt.current, tt.removeKeys, got, tt.want)
			}

			// 验证所有待删除键确实不存在
			for _, key := range tt.removeKeys {
				cleanKey := strings.SplitN(key, "=", 2)[0]
				for _, arg := range got {
					argKey := strings.SplitN(arg, "=", 2)[0]
					if argKey == cleanKey {
						t.Errorf("未正确删除键 %s，残留参数: %s", cleanKey, arg)
					}
				}
			}
		})
	}
}

func TestCheckArgs(t *testing.T) {
	// 设置操作测试用例
	t.Run("设置参数-所有参数存在应返回true", func(t *testing.T) {
		cur := []string{"--port=8080", "--log-level=debug", "--timeout=30s"}
		args := []string{"--log-level=debug", "--timeout=30s"}
		if !checkArgs(cur, ActionSetArgs, args) {
			t.Error("预期所有参数存在时应返回true")
		}
	})

	t.Run("设置参数-缺少部分参数应返回false", func(t *testing.T) {
		cur := []string{"--port=8080", "--log-level=info"}
		args := []string{"--log-level=debug", "--timeout=30s"}
		if checkArgs(cur, ActionSetArgs, args) {
			t.Error("预期缺少参数时应返回false")
		}
	})

	t.Run("设置参数-空参数列表应返回true", func(t *testing.T) {
		cur := []string{"--port=8080"}
		args := []string{}
		if !checkArgs(cur, ActionSetArgs, args) {
			t.Error("预期空参数列表应返回true")
		}
	})

	// 取消设置操作测试用例
	t.Run("取消参数-无匹配参数应返回true", func(t *testing.T) {
		cur := []string{"--port=8080", "--log-level=debug"}
		args := []string{"--timeout"}
		if !checkArgs(cur, ActionUnsetArgs, args) {
			t.Error("预期无匹配参数时应返回true")
		}
	})

	t.Run("取消参数-存在前缀匹配应返回false", func(t *testing.T) {
		cur := []string{"--timeout=30s", "--log-level=debug"}
		args := []string{"--timeout"}
		if checkArgs(cur, ActionUnsetArgs, args) {
			t.Error("预期存在前缀匹配时应返回false")
		}
	})

	t.Run("取消参数-部分匹配应返回false", func(t *testing.T) {
		cur := []string{"--log-level=debug", "--timeout=30s"}
		args := []string{"--log", "--time"}
		if checkArgs(cur, ActionUnsetArgs, args) {
			t.Error("预期部分匹配时应返回false")
		}
	})

	// 边界条件测试
	t.Run("空当前参数列表应返回false", func(t *testing.T) {
		cur := []string{}
		args := []string{"--port=8080"}
		if checkArgs(cur, ActionSetArgs, args) {
			t.Error("空当前参数列表应返回false")
		}
	})

	t.Run("带等号的参数匹配", func(t *testing.T) {
		cur := []string{"--timeout=30s"}
		args := []string{"--timeout"}
		if checkArgs(cur, ActionUnsetArgs, args) {
			t.Error("应检测到带等号的参数前缀匹配")
		}
	})

	t.Run("无效操作类型应返回false", func(t *testing.T) {
		cur := []string{"--port=8080"}
		args := []string{"--debug"}
		if checkArgs(cur, "invalid-action", args) {
			t.Error("预期无效操作类型返回false")
		}
	})
}

func TestConvertEnvToFlag(t *testing.T) {
	// 测试用例设计覆盖各种转换场景
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "标准环境变量格式",
			input:    "${BACKEND_QUOTA}",
			expected: "--backend-quota",
		},
		{
			name:     "无外壳包裹的变量名",
			input:    "LOG_LEVEL",
			expected: "--log-level",
		},
		{
			name:     "带数字的变量名",
			input:    "${API_V2_ENABLED}",
			expected: "--api-v2-enabled",
		},
		{
			name:     "全小写变量名",
			input:    "${debug_mode}",
			expected: "--debug-mode",
		},
		{
			name:     "混合大小写下划线",
			input:    "${Mixed_Case_Example}",
			expected: "--mixed-case-example",
		},
		{
			name:     "空字符串处理",
			input:    "",
			expected: "--",
		},
		{
			name:     "不完整外壳包裹",
			input:    "${UNCLOSED",
			expected: "--unclosed",
		},
		{
			name:     "包含连字符的变量名",
			input:    "${EXISTING-HYPHEN}",
			expected: "--existing-hyphen",
		},
		{
			name:     "多下划线连续",
			input:    "${DB__CONNECTION__POOL}",
			expected: "--db--connection--pool",
		},
		{
			name:     "特殊",
			input:    "--backend-quota",
			expected: "--backend-quota",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行转换
			actual := convertEnvToFlag(tt.input)

			// 验证结果
			if actual != tt.expected {
				t.Errorf("输入: %s\n期望: %s\n实际: %s", tt.input, tt.expected, actual)
			}
		})
	}
}
