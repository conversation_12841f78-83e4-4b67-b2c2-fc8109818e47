package controller

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/kateway-server/pkg/dryrun"
)

const (
	ActionSetArgs   = "set"
	ActionUnsetArgs = "unset"
)

func (c *Controller) UpdateArgs(ctx context.Context, action string, args []string, opts UpdateOptions) error {
	writer := opts.Writer

	deploy, err := c.GetDeployment(ctx, GetDeploymentOptions{BypassCache: true})
	if err != nil {
		return err
	}

	if container, err := c.findContainerInDeployment(deploy); err == nil {
		writer("更新开始: 集群=%s 动作=%s 待更新参数=%v 当前启动参数: args=%v command=%v", c.GetCluster().ClusterID, action, args, container.Args, container.Command)
	} else {
		writer("更新开始: 集群=%s 动作=%s 待更新参数=%v", c.GetCluster().ClusterID, action, args)
	}

	needDryrun := true
	setImageSkipWait := false
	if *deploy.Spec.Replicas == 0 {
		opts.Writer("跳过预检，因为工作负载副本数为0")
		needDryrun = false
		setImageSkipWait = true
	}

	if needDryrun && opts.Force {
		writer("跳过预检，因为显式指定了强制更新")
		needDryrun = false
	}

	var argsUpdater DeploymentUpdater
	switch action {
	case ActionSetArgs:
		// 创建updater
		argsUpdater = c.BuildContainerArgsUpdater(setArgs(args))
	case ActionUnsetArgs:
		argsUpdater = c.BuildContainerArgsUpdater(delArgs(args))
	default:
		return errors.New("非预期动作")
	}

	opts.DryrunOptions.Updaters = append(opts.DryrunOptions.Updaters, argsUpdater)
	if needDryrun {
		// 预检
		image, err := c.GetImage(ctx)
		if err != nil {
			return err
		}
		records, err := c.Dryrun(ctx, image.Tag, opts.DryrunOptions)
		if err != nil {
			return err
		}
		if len(records) != 0 {
			return fmt.Errorf("预检失败 错误 %w", dryrun.BuildErrorFromResult(records.ToMap()))
		}
		writer("dryrun结束，预检通过")
	}
	setArgsOpts := UpdateArgsOptions{
		SkipWait: setImageSkipWait,
		action:   action,
		args:     args,
		Writer:   writer,
	}

	updateMergeOption, err := shouldUpdateMergeOption(ctx, action, args, c)
	if err != nil {
		return err
	}

	mergeOption := "EnableIngressController"
	if updateMergeOption {
		writer("涉及到去除参数 --enable-ingress-controller-default=true，调整 configmap 中对应的选项为 false")
		if err := c.UpdateConfig(ctx, func(cm *v1.ConfigMap) error {
			if len(cm.Data) == 0 {
				return nil
			}
			v, exists := cm.Data[mergeOption]
			if exists && v == "true" {
				cm.Data[mergeOption] = "false"
			}
			return nil
		}); err != nil {
			return err
		}
	}

	// 执行启动参数变更
	if err := c.updateArgs(ctx, []DeploymentUpdater{argsUpdater}, setArgsOpts); err != nil {
		return err
	}

	if updateMergeOption {
		writer("删除 configmap 中的 EnableIngressController 选项")
		if err := c.UpdateConfig(ctx, func(cm *v1.ConfigMap) error {
			delete(cm.Data, mergeOption)
			return nil
		}); err != nil {
			return err
		}
	}

	return nil
}

func shouldUpdateMergeOption(ctx context.Context, action string, args []string, ctrl *Controller) (bool, error) {
	const argKey = "enable-ingress-controller-default"

	shouldUpdate := ctrl.IsService() && action == ActionUnsetArgs
	_, unsetMergeArg := lo.Find(args, func(s string) bool {
		return strings.Contains(s, argKey)
	})

	d, err := ctrl.GetDeployment(ctx, GetDeploymentOptions{})
	if err != nil {
		return false, err
	}

	curArgs := ctrl.GetCurArgs(ctrl.GetContainer(&d.Spec.Template.Spec))
	_, exists := lo.Find(curArgs, func(item string) bool {
		return strings.Contains(item, fmt.Sprintf("%s=true", argKey))
	})

	return shouldUpdate && unsetMergeArg && exists, nil
}

type UpdateArgsOptions struct {
	SkipWait bool
	action   string
	args     []string
	Writer   Writer
}

func (c *Controller) updateArgs(ctx context.Context, updaters []DeploymentUpdater, opts UpdateArgsOptions) error {
	writer := opts.Writer
	podList, err := c.listPod(ctx, true)
	if err != nil {
		return err
	}
	// 获取所有旧 Pod 名称的字符串切片
	podNames := lo.Map(podList, func(pod v1.Pod, _ int) string {
		return pod.Name
	})
	// 更新deploy启动参数
	err = c.UpdateDeployment(ctx, updaters...)
	if err != nil {
		return err
	}
	// 跳过等待
	if opts.SkipWait {
		writer("更新完成")
		return nil
	}
	writer("等待更新完成")
	var leader *LeaderPod
	timeoutCtx, cancel := context.WithTimeout(ctx, 5*time.Minute)
	defer cancel()
	err = wait.PollUntilWithContext(timeoutCtx, 5*time.Second, func(ctx context.Context) (bool, error) {
		leader, err = c.GetLeaderPod(ctx)
		if err != nil {
			writer("暂无有效的新leader")
			return false, nil
		}
		if !lo.Contains(podNames, leader.Name) {
			// 新leader不在pod列表中，说明这是配置了新启动参数的pod，观察是否就绪
			if leader.ContainerStatus.Ready {
				cur := c.GetCurArgs(leader.Container)
				if checkArgs(cur, opts.action, opts.args) {
					writer("新leader已就绪，启动参数符合预期")
					return true, nil
				}
				writer("新leader已就绪，但参数不符合预期, 当前参数: %v", cur)
				return false, errors.New("新leader就绪，但参数不符合预期")
			}
			writer("新leader未就绪, 当前状态: %v", leader.ContainerStatus)
		}
		return false, nil
	})
	if err != nil {
		writer("更新失败，报错: %v", err)
		return err
	}
	writer("更新完成: leader=%s/%s command=%v args=%v", leader.Namespace, leader.Name, leader.Container.Command, leader.Container.Args)

	return nil
}

func checkArgs(cur []string, action string, args []string) bool {
	switch action {
	case ActionSetArgs:
		for _, arg := range args {
			if !lo.Contains(cur, arg) {
				return false
			}
		}
		return true
	case ActionUnsetArgs:
		for _, arg := range cur {
			for _, unsetArg := range args {
				if strings.HasPrefix(arg, unsetArg) {
					return false
				}
			}
		}
		return true
	default:
		return false
	}
}

// delArgs 参数删除处理器（保持原有参数顺序）
func delArgs(removeKeys []string) func([]string) []string {
	return func(curArgs []string) []string {
		// 构建待删除键集合（带值和不带值的情况都考虑）
		removeSet := lo.SliceToMap(removeKeys, func(k string) (string, struct{}) {
			key, _, _ := strings.Cut(k, "=")
			return key, struct{}{}
		})

		// 过滤当前参数（保持顺序）
		result := lo.Filter(curArgs, func(arg string, _ int) bool {
			k, _, _ := strings.Cut(arg, "=")
			_, exists := removeSet[k]
			return !exists
		})

		// 如果存在从环境变量引入的参数，且在new args中，删掉
		result = lo.Filter(result, func(arg string, _ int) bool {
			if !strings.HasPrefix(arg, "${") {
				return true
			}
			new := convertEnvToFlag(arg)
			if _, exist := removeSet[new]; exist {
				return false
			}
			return true
		})

		klog.Infof("args 更新前 %v 更新后 %v", curArgs, result)
		return result
	}
}

// 参数合并处理器（保持参数顺序）
func setArgs(desiredArgs []string) func([]string) []string {
	return func(curArgs []string) []string {
		// 参数解析器
		parseArg := func(arg string) (string, *string) {
			k, v, found := strings.Cut(arg, "=")
			if !found || v == "" {
				return k, nil
			}
			return k, lo.ToPtr(v)
		}

		// 构建参数映射
		newParams := lo.SliceToMap(desiredArgs, func(arg string) (string, *string) {
			return parseArg(arg)
		})
		curParams := lo.SliceToMap(curArgs, func(arg string) (string, *string) {
			return parseArg(arg)
		})

		// 计算参数差异
		existingKeys := lo.Keys(curParams)
		updateKeys := lo.Filter(lo.Keys(newParams), func(k string, _ int) bool {
			return lo.Contains(existingKeys, k)
		})

		// 合并参数（保持原有顺序）
		result := lo.FilterMap(curArgs, func(arg string, _ int) (string, bool) {
			k, _ := parseArg(arg)
			if lo.Contains(updateKeys, k) {
				if val := newParams[k]; !lo.IsNil(val) {
					return k + "=" + lo.FromPtr(val), true
				}
				return k, true
			}
			return arg, true
		})

		// 追加新参数（保持传入顺序）
		lo.ForEach(desiredArgs, func(arg string, _ int) {
			k, _ := parseArg(arg)
			if !lo.Contains(existingKeys, k) {
				result = append(result, arg)
			}
		})

		// 如果存在从环境变量引入的参数，且在new args中，删掉
		result = lo.Filter(result, func(arg string, _ int) bool {
			if !strings.HasPrefix(arg, "${") {
				return true
			}
			new := convertEnvToFlag(arg)
			if _, exist := newParams[new]; exist {
				return false
			}
			return true
		})

		klog.Infof("args 更新前 %v 更新后 %v", curArgs, result)
		return result
	}
}

func convertEnvToFlag(envVar string) string {
	// 1. 去除${}外壳，获取原始变量名
	rawName := strings.TrimSuffix(strings.TrimPrefix(envVar, "${"), "}")

	// 2. 转换为全小写并替换下划线为连字符
	flagName := strings.ToLower(rawName)              // 转为小写：BACKEND_QUOTA -> backend_quota
	flagName = strings.ReplaceAll(flagName, "_", "-") // 替换分隔符：backend_quota -> backend-quota

	// 3. 添加双连字符前缀
	return "--" + flagName // 拼接为最终参数格式：--backend-quota
}
