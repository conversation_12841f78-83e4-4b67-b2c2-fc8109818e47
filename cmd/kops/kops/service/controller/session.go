package controller

import (
	"context"
	"fmt"

	"git.woa.com/kateway/kateway-server/pkg/dryrun"
	"git.woa.com/kateway/kateway-server/pkg/sessionaffinity"
)

func (ctrl *Controller) processSessionAffinity(ctx context.Context, targetVersion string,
	records dryrun.Records, opts DryrunOptions) (ok bool, err error) {
	opts.Writer("检测是否需要兼容存量会话保持")
	defer func() {
		if err == nil {
			if ok {
				opts.Writer("需要处理存量会话保持")
			} else {
				opts.Writer("不需要处理存量会话保持")
			}
		}
	}()

	if !ctrl.IsService() || ctrl.RunningInEKS() {
		return false, nil
	}

	version, _, err := ctrl.GetVersion(ctx)
	if err != nil {
		return false, err
	}
	shouldProcessSessionAffinity, err := sessionaffinity.ShouldProcess(version.String(), NewImage(targetVersion).Tag)
	if err != nil {
		return false, err
	}
	if shouldProcessSessionAffinity {
		opts.Writer("Processing session affinity for services.")

		userClusterClientset := ctrl.GetClusterClientset()
		processed, err := sessionaffinity.NewProcessor(userClusterClientset.K8sCli, opts.Writer).ProcessDryrunRecords(ctx, records)
		if err != nil {
			return false, fmt.Errorf("failed to process session affinity for services: %w", err)
		}
		return processed, nil
	}
	return false, nil
}
