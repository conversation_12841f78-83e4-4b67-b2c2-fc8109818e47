package services

import (
	"cmp"
	"context"
	"fmt"
	"slices"
	"strconv"
	"sync"
	"time"

	"github.com/patrickmn/go-cache"
	"github.com/samber/lo"
	"gorm.io/gorm"

	"git.woa.com/kateway/pkg/telemetry/jaeger"

	"git.woa.com/kateway/kateway-server/cmd/kops/common"
)

type User struct {
	Appid string
	Uin   string
	Name  string
}

type UserService struct {
	tkeossDB *gorm.DB
	m        sync.Mutex
	users    *cache.Cache
}

func newUserService(tkeossDB *gorm.DB) *UserService {
	return &UserService{
		tkeossDB: tkeossDB,
		users:    cache.New(48*time.Hour, 24*time.Hour),
	}
}

func (svc *UserService) ListUsers(ctx context.Context) ([]User, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	key := "users"
	if obj, exists := svc.users.Get(key); exists {
		return obj.([]User), nil
	}
	svc.m.Lock()
	defer svc.m.Unlock()
	if obj, exists := svc.users.Get(key); exists {
		return obj.([]User), nil
	}

	var (
		tkeUsers []int64

		eksUsers   []string
		uinByAppid = map[string]string{}
	)
	if err := svc.tkeossDB.WithContext(ctx).Table("tke_cluster").Where("appId != ?", 0).Distinct("appId").Find(&tkeUsers).Error; err != nil {
		return nil, err
	}
	if err := svc.tkeossDB.WithContext(ctx).Table("eks_cluster").Where("appid != ?", "").Distinct("appid").Find(&eksUsers).Error; err != nil {
		return nil, err
	}
	for _, id := range append(eksUsers, lo.Map(tkeUsers, func(u int64, _ int) string { return fmt.Sprint(u) })...) {
		idNum, err := strconv.Atoi(id)
		if err != nil {
			continue
		}
		if _, exists := uinByAppid[id]; !exists {
			uin, err := common.GetUinByAppid(uint64(idNum))
			if err == nil {
				uinByAppid[id] = uin
			}
		}
	}
	users := lo.MapToSlice(uinByAppid, func(k, v string) User { return User{Appid: k, Uin: v} })
	slices.SortFunc(users, func(a, b User) int { return cmp.Compare(a.Appid, b.Appid) })
	svc.users.Set(key, users, 0)
	return users, nil
}

func (svc *UserService) db(ctx context.Context) *gorm.DB {
	return svc.tkeossDB.WithContext(ctx).Table("userinfo").
		Select("Appid AS Appid, ownerUin AS Uin, name AS Name")
}

func (svc *UserService) Get(ctx context.Context, query string) (*User, error) {
	user := &User{}
	err := svc.db(ctx).Where("appid = ? OR ownerUin = ?", query, query).Find(user).Error
	return user, err
}
