package services

import (
	"strings"

	"github.com/tidwall/gjson"
)

func ParseMockError(detail string) string {
	var err string
	if detail == "" {
		err = ""
	} else if strings.HasPrefix(detail, "Job Not Running") {
		err = "JobNotRunning"
	} else if strings.HasPrefix(detail, "Malformed version") {
		err = "Malformedversion"
	} else if strings.HasPrefix(detail, "skip cluster") {
		err = "SkipCluster"
	} else if strings.HasPrefix(detail, "Skip_LessThen_v2.0.0") {
		err = "SkipLessThenV2.0.0"
	} else if strings.HasPrefix(detail, "get restConfig error") {
		err = "GetRestConfigError"
	} else if strings.HasPrefix(detail, "eks-service-readonly secret not found") {
		err = "SecretNotFound"
	} else if strings.HasPrefix(detail, "[{") {
		json := gjson.Parse(detail)
		errs := json.Array()
		if len(errs) > 1 {
			// err = json.Get("#.Action").String()
			err = "存在多个云API写操作"
		} else {
			err = errs[0].Get("Action").String()
		}
	} else {
		err = "Unknown"
	}

	return err
}
