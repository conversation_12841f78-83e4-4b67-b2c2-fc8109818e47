package services

import (
	"context"
	"fmt"

	"github.com/patrickmn/go-cache"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model/stat"
	"git.woa.com/kateway/kateway-server/pkg/util/region"
)

type KatewayService struct {
	db    *gorm.DB
	cache *cache.Cache
}

func newKatewayService() *KatewayService {
	return &KatewayService{
		db:    services.db,
		cache: cache.New(cache.NoExpiration, cache.NoExpiration),
	}
}

func (s *KatewayService) Update(ctx context.Context, kateway *model.Kateway) error {
	db := s.db.Model(kateway).Clauses(
		clause.OnConflict{
			DoUpdates: clause.AssignmentColumns(stat.GetUpdateFields(kateway)),
		})

	return db.Create(kateway).Error
}

func (s *KatewayService) ListVersion(t string) (result []string, err error) {
	switch t {
	case "Service":
		return s.listServiceVersion(t)
	case "Ingress":
		return s.listIngressVersion(t)
	default:
		panic(fmt.Errorf("unsupport %s", t))
	}
}

func (s *KatewayService) listServiceVersion(t string) (result []string, err error) {
	// var versions []string
	// err = s.db.Table("Kateway").
	// 	Select(" distinct Version").
	// 	Where("type = ? ", t).
	// 	Where("image like '%tkeimages%'").
	// 	Order("version desc").
	// 	Find(&versions).Error
	//
	// result = lo.Filter(versions, func(item string, _ int) bool {
	// 	ok, _ := version.Check(item, ">=2.3.0")
	// 	return ok
	// })
	// result = append(result, "v2.1.3")

	return config.Get().Service.Versions, nil
}

func (s *KatewayService) listIngressVersion(t string) (result []string, err error) {
	// var versions []string
	// err = s.db.Table("Kateway").
	// 	Select(" distinct Version").
	// 	Where("type = ? ", t).
	// 	Where("image like '%tkeimages%'").
	// 	Order("version desc").
	// 	Find(&versions).Error
	//
	// result = lo.Filter(versions, func(item string, _ int) bool {
	// 	ok, _ := version.Check(item, ">=2.2.5")
	// 	return ok
	// })
	// result = append(result, "v2.1.3")

	return config.Get().Ingress.Versions, nil
}

func (s *KatewayService) ListCCRRegion() (result []string, err error) {
	result = lo.Filter(config.Get().Regions, func(item string, _ int) bool {
		return region.MustGet(item).PreCCR
	})
	result = append([]string{""}, result...)

	return
}
