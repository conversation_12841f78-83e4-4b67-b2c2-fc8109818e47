package services

import (
	"fmt"
	"reflect"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"

	"git.woa.com/kateway/pkg/database/mysql"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	common "git.woa.com/kateway/kateway-server/cmd/kops/kops/common"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model/stat"
	"git.woa.com/kateway/kateway-server/pkg/service/credential"
	"git.woa.com/kateway/kateway-server/pkg/task"
)

type Services struct {
	db       *gorm.DB
	tkeossDB *gorm.DB

	kateway      *KatewayService
	cluster      *ClusterService
	tke          *TKEService
	service      *ServiceService
	legacyTask   *LegacyTask
	clb          *CLBService
	tag          *TagService
	user         *UserService
	releaseAudit *ReleaseAuditService
	task         *task.Service

	healthCheck Inspection
	rateLimiter *RateLimiterService
}

var services Services

func Get() *Services {
	return &services
}

// nolint
func Init() {
	db, err := mysql.New(config.Get().MySQL)
	if err != nil {
		panic(err)
	}
	services.db, err = db.OpenGORM(gorm.Config{
		// DryRun: true,
		Logger: logger.Default.LogMode(logger.Warn),
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true,
			NoLowerCase:   true,
		},
		CreateBatchSize: 1000,
	})
	if err != nil {
		panic(fmt.Errorf("OpenGORM error: %w", err))
	}
	services.kateway = newKatewayService()
	services.service = newServiceService()
	services.legacyTask = newLegacyTask()

	services.tke = newTKEService()

	db, err = mysql.New(config.Get().TKEOSS)
	if err != nil {
		panic(err)
	}
	services.tkeossDB, err = db.OpenGORM(gorm.Config{
		Logger: logger.Default.LogMode(logger.Warn),
	})
	if err != nil {
		panic(fmt.Errorf("OpenGORM error: %w", err))
	}

	sts := config.Get().STS
	credSvc := credential.NewCredentialService(sts.SecretID, sts.SecretKey, "roleName", sts.RoleName)
	services.clb = newCLBService(credSvc)
	services.cluster = newClusterService()
	services.tag = newTagService(services.db, credSvc)
	services.user = newUserService(services.tkeossDB)
	services.releaseAudit = newReleaseAuditService(services.db)
	common.ReleaseAuditService.Update = services.ReleaseAudit().Update // [qingyangwu] 临时解决方案，解决cycle import问题
	common.ReleaseAuditService.Last = services.ReleaseAudit().Last
	common.ReleaseAuditService.Select = services.ReleaseAudit().Select
	services.healthCheck = newInspectionService(services.db)
	services.rateLimiter = newRateLimiterService(config.Get().Inspection.RateLimit)
	services.task = task.NewService(services.db)
}

func (s *Services) DB() *gorm.DB {
	return s.db
}

func (s *Services) Save(data any) error {
	rvalue := reflect.ValueOf(data)
	var updateFields []string
	if rvalue.Kind() == reflect.Slice {
		if rvalue.Len() == 0 {
			return nil
		}
		updateFields = stat.GetUpdateFields(rvalue.Index(0).Interface())
	} else {
		updateFields = stat.GetUpdateFields(data)
	}

	db := s.db.Model(data).Clauses(
		clause.OnConflict{
			DoUpdates: clause.AssignmentColumns(updateFields),
		})

	return db.Create(data).Error
}

func (s *Services) Kateway() *KatewayService {
	return s.kateway
}

func (s *Services) Cluster() *ClusterService {
	return s.cluster
}

func (s *Services) Service() *ServiceService {
	return s.service
}

func (s *Services) TKE() *TKEService {
	return s.tke
}

// Deprecated: use Task instead
func (s *Services) LegacyTask() *LegacyTask {
	return s.legacyTask
}

func (s *Services) Tag() *TagService {
	return s.tag
}

func (s *Services) CLB() *CLBService {
	return s.clb
}

func (s *Services) User() *UserService {
	return s.user
}

func (s *Services) ReleaseAudit() *ReleaseAuditService {
	return s.releaseAudit
}

func (s *Services) Inspection() Inspection {
	return s.healthCheck
}

func (s *Services) RateLimit() *RateLimiterService {
	return s.rateLimiter
}

func (s *Services) Task() *task.Service {
	return s.task
}
