package services

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/samber/lo"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	tag "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tag/v20180813"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"k8s.io/apimachinery/pkg/util/wait"

	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/telemetry/log"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	"git.woa.com/kateway/kateway-server/cmd/kops/common"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	"git.woa.com/kateway/kateway-server/pkg/service/credential"
	"git.woa.com/kateway/kateway-server/pkg/util/retry"
)

type TagService struct {
	db *gorm.DB

	credSvc    credential.Credential
	uinByAppID map[string]string
	runnable   sync.Mutex

	deletionCounterByUser sync.Map // map[string]*atomic.Uint64
}

func newTagService(db *gorm.DB, credSvc credential.Credential) *TagService {
	return &TagService{
		db:         db,
		credSvc:    credSvc,
		uinByAppID: map[string]string{},
	}
}

func (svc *TagService) newTagClientFor(appID string) (*tag.Client, error) {
	cred, err := svc.credSvc.GetCredentials(svc.uinByAppID[appID])
	if err != nil {
		return nil, err
	}
	return tag.NewClient(cred, "ap-guangzhou", profile.NewClientProfile())
}

func (svc *TagService) GetModel(ctx context.Context, tag *model.Tag) (*model.Tag, error) {
	if err := svc.db.WithContext(ctx).Where("`Appid` = ? AND `Key` = ? AND `Value` = ?", tag.Appid, tag.Key, tag.Value).First(tag).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return tag, nil
}

func (svc *TagService) CreateOrUpdateModel(ctx context.Context, tag *model.Tag) error {
	db := svc.db.WithContext(ctx).Clauses(clause.OnConflict{
		DoUpdates: clause.AssignmentColumns([]string{"State", "Resources", "StateDetectedTotal", "LastTransitionTime"}),
	})
	return db.Create(tag).Error
}

func (svc *TagService) DeleteModel(ctx context.Context, tag *model.Tag) error {
	return svc.db.WithContext(ctx).Delete(tag).Error
}

func (svc *TagService) loadUsers(ctx context.Context) ([]string, error) {
	specifiedAppids := config.Get().Checker.Tag.Appids
	if len(specifiedAppids) > 0 {
		for _, id := range specifiedAppids {
			if _, exists := svc.uinByAppID[id]; !exists {
				svc.uinByAppID[id] = common.MustGetUINByAppID(uint64(lo.Must(strconv.Atoi(id))))
			}
		}
		return specifiedAppids, nil
	}

	us, err := services.User().ListUsers(ctx)
	if err != nil {
		return nil, err
	}
	for _, u := range us {
		svc.uinByAppID[u.Appid] = u.Uin
	}

	return lo.Map(us, func(u User, _ int) string { return u.Appid }), nil
}

func (svc *TagService) StartProcessing(pctx context.Context) {
	done := make(chan struct{})
	defer close(done)

	ctx, cancel := context.WithCancel(context.Background())

	go func() {
		defer cancel()

		select {
		case <-pctx.Done():
		case <-done:
		}
	}()

	// 防止重复执行
	if !svc.runnable.TryLock() {
		return
	}
	defer svc.runnable.Unlock()

	if err := svc.processTags(ctx); err != nil {
		log.Error(err, "Failed to execute task")
	}
}

func (svc *TagService) syncProgress(ctx context.Context, counter *atomic.Uint64, total int) (chan struct{}, error) {
	done := make(chan struct{})
	formatProgress := func() string {
		return fmt.Sprintf("%d/%d %.2f%%", counter.Load(), total, float64(counter.Load())/float64(total)*100)
	}
	startedAt := time.Now()
	taskID := time.Now().Format("20060102")
	task := &model.Task{
		TaskID:   taskID,
		Type:     "Tag",
		State:    model.TaskStateRunning.String(),
		Total:    total,
		Creator:  "bowen",
		Progress: formatProgress(),
	}
	if err := services.db.WithContext(ctx).Create(task).Error; err != nil && !errors.Is(err, gorm.ErrDuplicatedKey) {
		return nil, err
	}
	go func() {
		defer close(done)

		wait.PollImmediateUntilWithContext(ctx, time.Second, func(ctx context.Context) (done bool, err error) {
			done = counter.Load() == uint64(total)
			task.Cost = float64(time.Since(startedAt).Hours())
			task.Progress = formatProgress()
			if done {
				task.State = model.TaskStateDone.String()
				task.FinishedAt = lo.ToPtr(time.Now())
			}
			if err := services.db.WithContext(ctx).Updates(task).Error; err != nil {
				log.Error(err, "Update task error", "id", taskID)
			}
			return
		})
	}()

	return done, nil
}

func (svc *TagService) processTags(ctx context.Context) error {
	log.Info("Start processing tags")

	users, err := svc.loadUsers(ctx)
	if err != nil {
		return err
	}
	counter := &atomic.Uint64{}
	done, err := svc.syncProgress(ctx, counter, len(users))
	if err != nil {
		return err
	}

	wg := errgroup.Group{}
	wg.SetLimit(config.Get().Checker.Tag.Concurrency)
	for _, u := range users {
		user := u
		wg.Go(func() error {
			defer counter.Add(1)
			return svc.processTagsFor(ctx, user)
		})
	}
	err = wg.Wait()
	<-done
	return err
}

func (svc *TagService) processTagsFor(ctx context.Context, appID string) (err error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	defer func() {
		if err != nil {
			jaeger.LogError(span, err)
		}
	}()

	span.SetTag("user", appID)
	log.Info("Processing tags for user", "user", appID)

	svc.deletionCounterByUser.Store(appID, &atomic.Uint64{})

	tags, err := svc.listTags(ctx, appID, config.Get().Checker.Tag.TargetKeys)
	if err != nil {
		return err
	}
	span.LogKV("totalTags", len(tags))
	wg := errgroup.Group{}
	wg.SetLimit(10)
	for _, t := range tags {
		tag := t
		wg.Go(func() error {
			return svc.processTag(ctx, appID, tag)
		})
	}
	return wg.Wait()
}

func (svc *TagService) processTag(ctx context.Context, appID string, t *tag.TagWithDelete) error {
	resources, err := svc.listTaggedResources(ctx, appID, t)
	if err != nil {
		return err
	}
	rs := lo.Map(resources, func(r *tag.ResourceTag, _ int) model.TaggedResource {
		return model.TaggedResource{
			Region: *r.ResourceRegion,
			Type:   *r.ServiceType,
			ID:     *r.ResourceId,
			State:  model.ResourceStateUnknown,
		}
	})
	for i := range rs {
		r := &rs[i]
		if r.Type != "clb" || !strings.HasPrefix(r.ID, "lb-") {
			continue
		}
		lb, err := services.CLB().DescribeLoadBalancer(ctx, svc.uinByAppID[appID], r.Region, r.ID)
		if err == nil {
			if lb != nil {
				r.State = model.ResourceStateExist
			} else {
				r.State = model.ResourceStateNotExist
			}
		}
	}
	state := lo.Ternary(len(rs) == 0, model.TagStateUnused, model.TagStateUsed)
	current, err := svc.GetModel(ctx, &model.Tag{
		Appid: appID,
		Key:   *t.TagKey,
		Value: *t.TagValue,
	})
	if err != nil {
		return err
	}
	if current == nil {
		current = &model.Tag{
			Appid:              appID,
			Uin:                svc.uinByAppID[appID],
			Key:                *t.TagKey,
			Value:              *t.TagValue,
			State:              state,
			Resources:          rs,
			StateDetectedTotal: 1,
			LastTransitionTime: time.Now(),
		}
	} else {
		if current.State != state {
			current.State = state
			current.StateDetectedTotal = 1
			current.LastTransitionTime = time.Now()
		} else {
			current.StateDetectedTotal += 1
		}
		current.Resources = rs
	}
	if err := svc.CreateOrUpdateModel(ctx, current); err != nil {
		return fmt.Errorf("failed to create or update the model: %w", err)
	}
	return svc.deleteTagIfUnused(ctx, appID, current)
}

func (svc *TagService) deleteTagIfUnused(ctx context.Context, appID string, t *model.Tag) error {
	if !config.Get().Checker.Tag.DeletionEnabled {
		return nil
	}

	wd := time.Now().Weekday()
	if lo.Contains([]time.Weekday{time.Saturday, time.Sunday}, wd) {
		return nil
	}

	obj, _ := svc.deletionCounterByUser.Load(appID)
	counter := obj.(*atomic.Uint64)

	if counter.Load() >= uint64(config.Get().Checker.Tag.MaxDeletionNumPerUser) {
		log.Info("The number of deletions exceeded the maximum, skip deleting", "user", appID,
			"maximum", config.Get().Checker.Tag.MaxDeletionNumPerUser)
		return nil
	}

	if t.State != model.TagStateUnused ||
		t.StateDetectedTotal < config.Get().Checker.Tag.DeletionThreshold ||
		time.Since(t.LastTransitionTime) < time.Duration(config.Get().Checker.Tag.DeletionThreshold)*24*time.Hour {
		return nil
	}

	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	span.LogKV("key", t.Key, "value", t.Value)

	if err := svc.db.Transaction(func(tx *gorm.DB) error {
		if err := tx.WithContext(ctx).Delete(t).Error; err != nil {
			return fmt.Errorf("failed to delete tag model: %w", err)
		}
		cli, err := svc.newTagClientFor(appID)
		if err != nil {
			return err
		}
		req := tag.NewDeleteTagsRequest()
		req.Tags = []*tag.Tag{
			{
				TagKey:   &t.Key,
				TagValue: &t.Value,
			},
		}
		if _, err = cli.DeleteTagsWithContext(ctx, req); err != nil {
			return fmt.Errorf("failed to delete tag %s:%s: %w", t.Key, t.Value, err)
		}
		return nil
	}); err != nil {
		return err
	}
	counter.Add(1)
	return nil
}

func (svc *TagService) listTaggedResources(ctx context.Context, appID string, t *tag.TagWithDelete) ([]*tag.ResourceTag, error) {
	cli, err := svc.newTagClientFor(appID)
	if err != nil {
		return nil, err
	}
	var (
		resources = []*tag.ResourceTag{}
		total     = uint64(1001)
		offset    = uint64(0)
		limit     = uint64(1000)
		req       = tag.NewDescribeResourcesByTagsRequest()
	)
	req.TagFilters = []*tag.TagFilter{
		{
			TagKey:   t.TagKey,
			TagValue: []*string{t.TagValue},
		},
	}
	req.Limit = &limit
	req.Offset = &offset
	for ; offset < total; offset += total {
		resp, err := retry.DoIfNetError(func() (*tag.DescribeResourcesByTagsResponse, error) {
			return cli.DescribeResourcesByTagsWithContext(ctx, req)
		}, 1*time.Second)
		if err != nil {
			return nil, err
		}
		total = *resp.Response.TotalCount
		resources = append(resources, resp.Response.Rows...)
	}
	return resources, nil
}

func (svc *TagService) listTags(ctx context.Context, appID string, keys []string) ([]*tag.TagWithDelete, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli, err := svc.newTagClientFor(appID)
	if err != nil {
		return nil, err
	}
	var (
		tags   = make([]*tag.TagWithDelete, 0, 10000)
		total  = uint64(2001)
		offset = uint64(0)
		limit  = uint64(2000)
		req    = tag.NewDescribeTagsRequest()
	)
	req.TagKeys = lo.ToSlicePtr(keys)
	req.Limit = &limit
	req.Offset = &offset
	for ; offset < total; offset += limit {
		resp, err := retry.DoIfNetError(func() (*tag.DescribeTagsResponse, error) {
			return cli.DescribeTagsWithContext(ctx, req)
		}, 1*time.Second)
		if err != nil {
			return nil, err
		}
		total = *resp.Response.TotalCount
		tags = append(tags, resp.Response.Tags...)
	}
	type tagMeta struct {
		k, v string
	}
	return lo.UniqBy(tags, func(t *tag.TagWithDelete) tagMeta { return tagMeta{k: *t.TagKey, v: *t.TagValue} }), nil
}
