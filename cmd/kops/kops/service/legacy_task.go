package services

import (
	"context"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model/stat"
	"git.woa.com/kateway/kateway-server/pkg/tmp/types/sets"
)

type LegacyTask struct {
	db *gorm.DB
}

func newLegacyTask() *LegacyTask {
	return &LegacyTask{
		db: services.db,
	}
}

func (s *LegacyTask) Update(item *model.Task) error {
	db := s.db.Model(item).Clauses(
		clause.OnConflict{
			DoUpdates: clause.AssignmentColumns(stat.GetUpdateFields(item)),
		})

	return db.Create(item).Error
}

func (s *LegacyTask) UpdateInspectionTask(ctx context.Context, item *model.InspectionTask) error {
	return s.CreateOrUpdateInspectionTask(ctx, item)
}

func (s *LegacyTask) CreateOrUpdateInspectionTask(ctx context.Context, item *model.InspectionTask) error {
	db := s.db.WithContext(ctx).Model(item).Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "TaskID"}},
		DoUpdates: clause.AssignmentColumns([]string{
			"State", // 状态
			"Reason",
			"Creator",
			"IgnoreRiskLevel", // 报警级别
			"CurSyncTimes",    // 当前同步次数
			"MaxSyncTimes",    // 最大同步次数
			"UpdatedAt",       // 更新时间戳
			"FinishedAt",      // 完成时间戳
		}),
	})
	return db.Create(item).Error
}

func (s *LegacyTask) GetTaskByTaskID(ctx context.Context, taskID string) (*model.InspectionTask, error) {
	var task model.InspectionTask

	// 查询匹配的记录，使用 First 方法更合适
	result := s.db.WithContext(ctx).Where("TaskID = ?", taskID).First(&task)

	// 处理查询结果
	if result.Error != nil {
		return nil, result.Error
	}

	return &task, nil
}

func (s *LegacyTask) GetTaskByClusterID(ctx context.Context, clusterID string) (*model.InspectionTask, error) {
	var task model.InspectionTask

	// 查询匹配的记录，使用 First 方法更合适
	result := s.db.WithContext(ctx).Where("InstanceID = ? AND State = ?", clusterID, model.TaskStateRunning).First(&task)

	// 处理查询结果
	if result.Error != nil {
		return nil, result.Error
	}

	return &task, nil
}

func (s *LegacyTask) ListTask(ctx context.Context) ([]*model.InspectionTask, error) {
	var task []*model.InspectionTask
	// 查询匹配的记录，使用 First 方法更合适
	result := s.db.WithContext(ctx).Find(&task)

	// 处理查询结果
	if result.Error != nil {
		return nil, result.Error
	}

	return task, nil
}

func (s *LegacyTask) ListTaskByClusterID(ctx context.Context, clusterID string) ([]*model.InspectionTask, error) {
	var task []*model.InspectionTask

	// 查询匹配的记录，使用 First 方法更合适
	result := s.db.WithContext(ctx).Where("InstanceID = ?", clusterID).Find(&task)

	// 处理查询结果
	if result.Error != nil {
		return nil, result.Error
	}

	return task, nil
}

func (s *LegacyTask) ListTaskByState(ctx context.Context, state string) ([]*model.InspectionTask, error) {
	var task []*model.InspectionTask
	// 查询匹配的记录，使用 First 方法更合适
	result := s.db.WithContext(ctx).Where("State = ?", state).Find(&task)

	// 处理查询结果
	if result.Error != nil {
		return nil, result.Error
	}

	return task, nil
}

func (s *LegacyTask) ListTaskByClusterState(ctx context.Context, clusterID, state string) ([]*model.InspectionTask, error) {
	if clusterID == "" && state == "" {
		return nil, nil
	}
	if clusterID != "" && state == "" {
		return s.ListTaskByClusterID(ctx, clusterID)
	}
	if clusterID == "" && state != "" {
		return s.ListTaskByState(ctx, state)
	}

	var task []*model.InspectionTask
	// 查询匹配的记录，使用 First 方法更合适
	result := s.db.WithContext(ctx).Where("State = ? AND InstanceID = ?", state, clusterID).Find(&task)

	// 处理查询结果
	if result.Error != nil {
		return nil, result.Error
	}

	return task, nil
}

func (s *LegacyTask) GetTaskReport(ctx context.Context, clusterID, state string) (*model.TaskReport, error) {
	var report = &model.TaskReport{Template: "report_cluster_task"}
	var err error

	tasks, err := s.ListTaskByClusterState(ctx, clusterID, model.TaskStateRunning.String())
	if err != nil {
		return nil, err
	}
	report.TotalRunningTaskCount = len(tasks)
	report.TotalDoneTaskCount = len(tasks)
	if state == model.TaskStateRunning.String() {
		report.TaskList = tasks
	}

	tasks, err = s.ListTaskByClusterState(ctx, clusterID, model.TaskStateDone.String())
	if err != nil {
		return nil, err
	}
	report.TotalDoneTaskCount = len(tasks)
	if state == model.TaskStateDone.String() {
		report.TaskList = tasks
	}

	tasks, err = s.ListTaskByClusterState(ctx, clusterID, model.TaskStateTerminated.String())
	if err != nil {
		return nil, err
	}
	report.TotalTerminatedTaskCount = len(tasks)
	if state == model.TaskStateTerminated.String() {
		report.TaskList = tasks
	}

	tasks, err = s.ListTask(ctx)
	if err != nil {
		return nil, err
	}
	report.TotalTaskCount = len(tasks)
	if state == "" || state == "ALL" {
		if clusterID == "" {
			report.TaskList = tasks
		} else {
			tasks, err := s.ListTaskByClusterID(ctx, clusterID)
			if err != nil {
				return nil, err
			}
			report.TaskList = tasks
		}
	}

	return report, nil
}

func (s *LegacyTask) GetRiskReport(ctx context.Context) (*model.RiskReport, error) {
	var report = &model.RiskReport{Template: "report_risk"}
	var err error

	increment, err := s.ListIncrementalRiskCLB(ctx)
	if err != nil {
		return nil, err
	}
	stock, err := s.ListStockRiskCLB(ctx)
	if err != nil {
		return nil, err
	}

	clustersets := sets.NewString()
	var (
		clbRiskCount, listenerRiskCount, ruleRiskCount, rsRiskCount int
	)
	for _, risk := range increment {
		if risk.RiskLevel == string(model.CLBLevelRisk) {
			clbRiskCount++
		}
		if risk.RiskLevel == string(model.ListenerLevelRisk) {
			listenerRiskCount++
		}
		if risk.RiskLevel == string(model.RuleLevelRisk) {
			ruleRiskCount++
		}
		if risk.RiskLevel == string(model.RealServerLevelRisk) {
			rsRiskCount++
		}
		clustersets.Insert(risk.ClusterName)
	}
	for _, risk := range stock {
		if risk.RiskLevel == string(model.CLBLevelRisk) {
			clbRiskCount++
		}
		if risk.RiskLevel == string(model.ListenerLevelRisk) {
			listenerRiskCount++
		}
		if risk.RiskLevel == string(model.RuleLevelRisk) {
			ruleRiskCount++
		}
		if risk.RiskLevel == string(model.RealServerLevelRisk) {
			rsRiskCount++
		}
		clustersets.Insert(risk.ClusterName)
	}
	report.TotalRiskCount = len(increment) + len(stock)
	report.TotalCLBLevelRiskCount = clbRiskCount
	report.TotalListenerLevelRiskCount = listenerRiskCount
	report.TotalRuleLevelRiskCount = ruleRiskCount
	report.TotalRSLevelRiskCount = rsRiskCount

	report.TotalIncrementalTaskCount = len(increment)
	report.TotalStockTaskCount = len(stock)
	report.Stock = stock
	report.Increment = increment
	report.TotalClusterCount = clustersets.Len()

	return report, nil
}

func (s *LegacyTask) ListRiskCLBByCluster(ctx context.Context, clusterName string) ([]model.CLBRiskRecord, error) {
	var records []model.CLBRiskRecord

	// 查询所有匹配的记录
	result := s.db.WithContext(ctx).Where("ClusterName = ?", clusterName).Order("RiskScore DESC").Find(&records)

	// 处理查询结果
	if result.Error != nil {
		return nil, result.Error
	}

	return records, nil
}

func (s *LegacyTask) ListRiskCLBByState(ctx context.Context, state string) ([]model.CLBRiskRecord, error) {
	var records []model.CLBRiskRecord
	var completes []model.CLBRiskRecord

	// 查询所有匹配的记录
	result := s.db.WithContext(ctx).Where("State = ?", state).Order("RiskScore DESC").Find(&records)
	// 处理查询结果
	if result.Error != nil {
		return nil, result.Error
	}

	for _, record := range records {
		record.RiskSummary = record.RiskMetrics.Strings()
		record.LastDuration = time.Since(record.CreatedAt).String()
		completes = append(completes, record)
	}

	return completes, nil
}

func (s *LegacyTask) ListRiskCLBByClusterState(ctx context.Context, clusterName, state string) ([]model.CLBRiskRecord, error) {
	var records []model.CLBRiskRecord

	// 查询所有匹配的记录
	result := s.db.WithContext(ctx).Where("ClusterName = ? AND State = ?", clusterName, state).Find(&records)

	// 处理查询结果
	if result.Error != nil {
		return nil, result.Error
	}

	return records, nil
}

func (s *LegacyTask) ListIncrementalRiskCLB(ctx context.Context) ([]model.CLBRiskRecord, error) {
	return s.ListRiskCLBByState(ctx, string(model.StateIncrementRisk))
}

func (s *LegacyTask) ListStockRiskCLB(ctx context.Context) ([]model.CLBRiskRecord, error) {
	return s.ListRiskCLBByState(ctx, string(model.StateStockRisk))
}

// CreateOrUpdateModelsForServiceRisk 批量创建或更新模型
func (s *LegacyTask) CreateOrUpdateModelSliceForCLBRisk(ctx context.Context, records []*model.CLBRiskRecord) error {
	if len(records) == 0 {
		return nil // 如果没有记录，直接返回
	}

	db := s.db.WithContext(ctx).Clauses(clause.OnConflict{
		DoUpdates: clause.AssignmentColumns([]string{
			"RiskCount",
			"RiskScore",
			"AllDownCLBByWeight",
			"AllDownCLBByHealth",
			"HasAllDownListenerByWeight",
			"HasAllDownListenerByHealth",
			"HasAllDownRuleByWeight",
			"HasAllDownRuleByHealth",
			"HasDownRSByHealth",
			"HasDownRSByWeight",
			"UsingResources",
			"RiskLevel",
			"RiseCount",
			"State",
			"UpdatedAt", // 更新时间戳
		}),
	})

	// 批量创建或更新
	return db.Create(records).Error
}

// DeleteModelsForServiceRisk 批量删除模型
func (s *LegacyTask) DeleteModelsForCLBRisk(ctx context.Context, clusterName string, serviceNames []string) error {
	if len(serviceNames) == 0 {
		return nil // 如果没有服务名称，直接返回
	}

	// 使用事务确保操作的原子性
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback() // 如果发生恐慌，回滚事务
		}
	}()

	// 批量删除
	result := tx.Where("ClusterName = ? AND CLBID IN ?", clusterName, serviceNames).Delete(&model.CLBRiskRecord{})
	if result.Error != nil {
		tx.Rollback() // 如果删除失败，回滚事务
		return result.Error
	}

	return tx.Commit().Error // 提交事务
}

func (s *LegacyTask) DeleteModelsByClusterID(ctx context.Context, clusterName string) error {
	// 使用事务确保操作的原子性
	// 删除记录
	// 使用事务确保操作的原子性
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback() // 如果发生恐慌，回滚事务
		}
	}()
	if err := tx.Where("ClusterName = ?", clusterName).Delete(&model.CLBRiskRecord{}).Error; err != nil {
		tx.Rollback() // 如果删除失败，回滚事务
		return err
	}
	return tx.Commit().Error // 提交事务
}

// CreateOrUpdateModelsForServiceRisk 批量创建或更新模型
func (s *LegacyTask) CreateOrUpdateClusterStats(ctx context.Context, id string, records model.ClusterHealthStatistics) error {
	stats := model.ConvertClusterStats(id, records)
	db := s.db.WithContext(ctx).Clauses(clause.OnConflict{
		DoUpdates: clause.AssignmentColumns([]string{
			"ClusterName",
			"CLBTotalCount",
			"CLBTotalZeroWeightCount",
			"CLBTotalUnhealthCount",
			"CLBTotalNoRealServerCount",
			"CLBTotalNotExistedCount",
			"ListenerTotalCount",
			"ListenerTotalZeroWeightCount",
			"ListenerTotalUnhealthCount",
			"RuleTotalCount",
			"RuleTotalZeroWeightCount",
			"RuleTotalUnhealthCount",
			"RealServerTotalCount",
			"RealServerTotalZeroWeightCount",
			"RealServerTotalUnhealthCount",
			"UpdatedAt",
		}),
	})

	// 批量创建或更新
	return db.Create(&stats).Error
}

// CreateOrUpdateClusterInfo 创建或更新集群信息
func (s *LegacyTask) CreateOrUpdateClusterInfo(ctx context.Context, info model.ClusterInfo) error {
	// 设置时间字段
	now := time.Now()
	info.UpdatedAt = now

	// 如果是新记录，设置创建时间
	var existingRecord model.ClusterInfo
	result := s.db.WithContext(ctx).Where("ClusterID = ?", info.ClusterID).First(&existingRecord)
	if result.Error != nil {
		// 记录不存在，设置创建时间
		info.CreatedAt = now
	}

	// 设置更新字段
	db := s.db.WithContext(ctx).Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "ClusterID"}}, // 使用ClusterID作为唯一约束
		DoUpdates: clause.AssignmentColumns([]string{
			// 集群基本信息
			"ClusterName",
			"ClusterRegion",
			"ClusterType",
			"AppID",
			"Description",
			"MetaClusterID",
			"State",

			// 网络信息
			"ServiceCIDR",
			"NetworkType",
			"KubeProxyMode",
			"K8SVersion",
			"VpcID",
			"SubnetID",

			// 集群基本统计信息
			"TotalCLBCount",
			"TotalServiceCount",
			"TotalIngressCount",
			"TotalTKEServiceConfigCount",

			// 组件信息
			"ServiceControllerImage",
			"ServiceControllerVersion",
			"ServiceAvailableReplicas",
			"ServiceExpectReplicas",
			"ServiceArgs",
			"ServiceImagePullPolicy",
			"ServiceResource",
			"ServiceConfigMap",

			"IngressControllerImage",
			"IngressControllerVersion",
			"IngressAvailableReplicas",
			"IngressExpectReplicas",
			"IngressArgs",
			"IngressImagePullPolicy",
			"IngressResource",
			"IngressConfigMap",

			// 时间戳
			"UpdatedAt",
		}),
	})

	// 创建或更新
	return db.Create(&info).Error
}
