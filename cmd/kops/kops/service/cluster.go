package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	masterclient "cloud.tencent.com/tke/master-operator/pkg/client/clientset/versioned"
	lbcfclient "git.code.oa.com/tkex-teg/lb-controlling-framework/pkg/client-go/clientset/versioned"
	"github.com/patrickmn/go-cache"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gorm.io/gorm"
	appv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/api/core/v1"
	extensionsv1beta1 "k8s.io/api/extensions/v1beta1"
	networkingv1 "k8s.io/api/networking/v1"
	crdclient "k8s.io/apiextensions-apiserver/pkg/client/clientset/clientset"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	restclient "k8s.io/client-go/rest"
	"k8s.io/client-go/tools/leaderelection/resourcelock"

	multiclusteringress "git.woa.com/kateway/multi-cluster-ingress-api/apis/multiclusteringress/v1alpha1"
	mciClient "git.woa.com/kateway/multi-cluster-ingress-api/client/clientset/versioned"
	multiclusterservice "git.woa.com/kateway/multi-cluster-service-api/apis/multiclusterservice/v1alpha1"
	mcsClient "git.woa.com/kateway/multi-cluster-service-api/client/clientset/versioned"
	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/telemetry/log"
	tkeserviceconfig "git.woa.com/kateway/tke-service-config/pkg/apis/tkeservice/v1alpha1"
	tscClient "git.woa.com/kateway/tke-service-config/pkg/client/clientset/versioned"
	lbrv1alpha1 "git.woa.com/misakazhou/loadbalancer-resource-api/pkg/apis/loadbalancerresource/v1alpha1"
	loadbalancerresource "git.woa.com/misakazhou/loadbalancer-resource-api/pkg/client/clientset/versioned"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	cluster2 "git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	"git.woa.com/kateway/kateway-server/pkg/errno"
	"git.woa.com/kateway/kateway-server/pkg/ianvs"
	"git.woa.com/kateway/kateway-server/pkg/models"
	cluster3 "git.woa.com/kateway/kateway-server/pkg/service/cluster"
	"git.woa.com/kateway/kateway-server/pkg/util/region"
)

type ClusterService struct {
	db       *gorm.DB
	tkeossDB *gorm.DB

	clientsetCache     *cache.Cache
	metaClientsetCache *cache.Cache
}

func newClusterService() *ClusterService {
	return &ClusterService{
		db:       services.db,
		tkeossDB: services.tkeossDB,

		clientsetCache:     cache.New(24*time.Hour, time.Hour),
		metaClientsetCache: cache.New(24*time.Hour, time.Hour),
	}
}

func (s *ClusterService) Get(ctx context.Context, clusterID string) (*model.Cluster, error) {
	var cluster model.Cluster

	err := s.tkeossDB.Table("tke_cluster").
		Select("'tke' as Type", "isClusterDeploy as SubType", "clusterInstanceId as ClusterID ", "name as Name", "appId as Appid", "region_id as RegionID", "metaClusterId as MetaClusterID").
		Where(`region_id not in (6, 24)`).Where(`clusterInstanceId = ?`, clusterID).Find(&cluster).Error
	if err != nil {
		return nil, err
	}

	err = s.tkeossDB.Table("eks_cluster").
		Select("'eks' as Type", "clusterId as ClusterID ", "spec_display_name as Name", "appid as Appid", "region_id as RegionID", "meta_cluster_id as MetaClusterID").
		Where(`region_id not in (6, 24)`).Where(`clusterId = ?`, clusterID).Find(&cluster).Error
	if err != nil {
		return nil, err
	}

	r := region.Get(cluster.RegionID)
	if r == nil {
		return nil, fmt.Errorf("region id %q not found", cluster.RegionID)
	}

	cluster.Region = r.Alias

	return &cluster, err
}

func (s *ClusterService) List(ctx context.Context, opts ...ListClusterOptions) ([]model.Cluster, error) {
	tkeClusters, err := s.ListTKE(ctx, opts...)
	if err != nil {
		return nil, err
	}

	eksClusters, err := s.ListEKS(ctx, opts...)
	if err != nil {
		return nil, err
	}

	return append(tkeClusters, eksClusters...), nil
}

func (s *ClusterService) ListLBCF(ctx context.Context) ([]model.Cluster, error) {
	// db := s.tkeDB().Where("clusterInstanceId in ?", []string{"cls-7m2mnrz0", "cls-ciyxgivo"})
	db := s.tkeDB().Where("clusterInstanceId in ?", config.Get().LBCF.Clusters)
	tkeClusters, err := s.list(db)
	if err != nil {
		return nil, err
	}

	return tkeClusters, nil
}

type ListClusterOptions func(db *gorm.DB) *gorm.DB

func WithAppID(appID string) ListClusterOptions {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("Appid = ?", appID)
	}
}

func (s *ClusterService) ListTKE(_ context.Context, opts ...ListClusterOptions) ([]model.Cluster, error) {
	db := s.tkeDB().Where("lifeState in ?", []string{"normal"})

	for _, opt := range opts {
		db = opt(db)
	}

	return s.list(db)
}

func (s *ClusterService) ListEKS(_ context.Context, opts ...ListClusterOptions) ([]model.Cluster, error) {
	db := s.eksDB().Where("phase in ?", []string{"Running"})

	for _, opt := range opts {
		db = opt(db)
	}

	return s.list(db)
}

func (s *ClusterService) tkeDB() *gorm.DB {
	return s.tkeossDB.Table("tke_cluster").
		Select("'tke' as Type", "isClusterDeploy as SubType", "clusterInstanceId as ClusterID ", "name as Name", "appId as Appid", "region_id as RegionID", "metaClusterId as MetaClusterID").
		Where(`region_id not in (6, 24)`)
}

func (s *ClusterService) eksDB() *gorm.DB {
	return s.tkeossDB.Table("eks_cluster").
		Select("'eks' as Type", "clusterId as ClusterID ", "spec_display_name as Name", "appid as Appid", "region_id as RegionID", "meta_cluster_id as MetaClusterID").
		Where(`region_id not in (6, 24)`)
}

func (s *ClusterService) list(db *gorm.DB) ([]model.Cluster, error) {
	var clusters []model.Cluster
	err := db.Find(&clusters).Error
	if err != nil {
		return nil, err
	}

	for i, cluster := range clusters {
		if cluster.Region == "" && cluster.RegionID != 0 {
			r := region.Get(cluster.RegionID)
			if r == nil {
				continue
			}
			clusters[i].Region = r.Alias
		}
	}

	return clusters, nil
}

func (s *ClusterService) NeedProcess(cluster *model.Cluster) bool {
	if config.Get().SkipClusterByID(cluster.ClusterID) {
		return false
	}

	if config.Get().SkipClusterByName(cluster.Name) {
		return false
	}

	return true
}

// NeedMook 暂时为了避免依赖用户集群环境运行mock job，防止执行失败和用户可见后质疑
func (s *ClusterService) NeedMook(mockType string, cluster *model.Cluster) bool {
	// 暂时关闭mock，因为ingress错误和权限问题
	return false

	// Service 独立集群不做mock
	if mockType == "Service" {
		return !cluster.IsIndependent()
	}

	// Ingress 只有eks集群mock
	return cluster.IsEKS()
}

func (s *ClusterService) ClientsetByIanvs(ctx context.Context, clusterID string, user string, token string) (clientset *cluster3.ClientsSet, err error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	set := &cluster3.ClientsSet{}

	cfg, err := ianvs.GetRestConfig(clusterID, user, token)
	if err != nil {
		return nil, errors.Wrapf(err, "get restConfig from ianvs")
	}

	set.K8sCli, err = kubernetes.NewForConfig(cfg)
	if err != nil {
		return set, err
	}

	set.Interface = set.K8sCli

	set.CRDCli, err = crdclient.NewForConfig(cfg)
	if err != nil {
		return set, err
	}

	set.MasterCli, err = masterclient.NewForConfig(cfg)
	if err != nil {
		return set, err
	}

	set.LoadbalancerCli, err = loadbalancerresource.NewForConfig(cfg)
	if err != nil {
		return set, err
	}

	set.MCICli, err = mciClient.NewForConfig(cfg)
	if err != nil {
		return set, errno.Cause(errno.KUBE_CLIENT_CREATE_ERROR, err)
	}

	set.MCSCli, err = mcsClient.NewForConfig(cfg)
	if err != nil {
		return set, errno.Cause(errno.KUBE_CLIENT_CREATE_ERROR, err)
	}

	set.TSCCli, err = tscClient.NewForConfig(cfg)
	if err != nil {
		return set, errno.Cause(errno.KUBE_CLIENT_CREATE_ERROR, err)
	}

	return set, nil
}

func (s *ClusterService) Clientset(ctx context.Context, cluster *model.Cluster) (clientset *cluster3.ClientsSet, err error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	if data, ok := s.clientsetCache.Get(cluster.ClusterID); ok {
		return data.(*cluster3.ClientsSet), nil
	}
	defer func() {
		if err == nil {
			s.clientsetCache.SetDefault(cluster.ClusterID, clientset)
		}
	}()
	var clientsSet cluster3.ClientsSet
	if cluster.Type == "tke" {
		tkeCluster := cluster2.MustGetTKECluster(cluster.Region, cluster.ClusterID)
		if tkeCluster == nil {
			return nil, fmt.Errorf("cluster %v not found", cluster.ClusterID)
		}
		if tkeCluster.LifeState != "normal" {
			return nil, fmt.Errorf("invalid LifeState %q", tkeCluster.LifeState)
		}

		clientsSet, err = cluster2.GetTKEClusterClientSet(cluster.Region, tkeCluster)
		if err != nil {
			return nil, err
		}
	} else if cluster.Type == "eks" {
		eksCluster, err := cluster2.GetEKSCluster(cluster.Region, cluster.ClusterID)
		if err != nil {
			return nil, err
		}
		if eksCluster == nil {
			return nil, fmt.Errorf("cluster %v not found", cluster.ClusterID)
		}
		clientsSet, err = cluster2.GetEKSClusterClientSet(cluster.Region, eksCluster)
		if err != nil {
			return nil, err
		}
	} else {
		panic(fmt.Sprintf("unknown %v type %s", cluster.ClusterID, cluster.Type))
	}

	return &clientsSet, nil
}

func (s *ClusterService) RESTConfig(ctx context.Context, cluster *model.Cluster) (*restclient.Config, error) {
	if cluster.IsEKS() {
		return cluster2.GetEKSClusterRestConfig(cluster.Region, cluster.Appid, cluster.ClusterID)
	} else {
		return cluster2.GetTKEClusterRestConfig(cluster.Region, cluster.Appid, cluster.ClusterID)
	}
}

func (s *ClusterService) LBCFClient(ctx context.Context, cluster *model.Cluster) (*lbcfclient.Clientset, error) {
	restConfig, err := s.RESTConfig(ctx, cluster)
	if err != nil {
		return nil, err
	}

	return lbcfclient.NewForConfig(restConfig)
}

func (s *ClusterService) ListLBR(ctx context.Context, cluster *model.Cluster) ([]lbrv1alpha1.LoadBalancerResource, error) {
	clientset, _ := s.Clientset(ctx, cluster)

	result, err := clientset.LoadbalancerCli.NetworkingV1alpha1().LoadBalancerResources().List(ctx, metav1.ListOptions{})

	return result.Items, err
}

func (s *ClusterService) ListService(ctx context.Context, cluster *model.Cluster) ([]v1.Service, error) {
	clientset, _ := s.Clientset(ctx, cluster)
	result, err := clientset.K8sCli.CoreV1().Services(metav1.NamespaceAll).List(ctx, metav1.ListOptions{})

	return result.Items, err
}

func (s *ClusterService) ListIngress(ctx context.Context, cluster *model.Cluster) ([]networkingv1.Ingress, error) {
	clientset, _ := s.Clientset(ctx, cluster)
	result, err := clientset.K8sCli.NetworkingV1().Ingresses(metav1.NamespaceAll).List(ctx, metav1.ListOptions{})

	return result.Items, err
}

func (s *ClusterService) ListTSC(ctx context.Context, cluster *model.Cluster) ([]tkeserviceconfig.TkeServiceConfig, error) {
	clientset, _ := s.Clientset(ctx, cluster)
	result, err := clientset.TSCCli.CloudV1alpha1().TkeServiceConfigs(metav1.NamespaceAll).List(ctx, metav1.ListOptions{})

	return result.Items, err
}

func (s *ClusterService) IsDirectAccessService(cluster *model.Cluster, svc *corev1.Service) string {
	if cluster.IsEKS() {
		return "直连"
	}
	if svc == nil {
		return "直连"
	}
	value, ok := svc.GetAnnotations()["service.cloud.tencent.com/direct-access"]
	if ok {
		if cast.ToBool(value) {
			return "直连"
		}
		return "非直连"
	}
	return "非直连"
}

func (s *ClusterService) IsDirectAccessIngress(cluster *model.Cluster, ing *networkingv1.Ingress) string {
	if cluster.IsEKS() {
		return "直连"
	}
	if ing == nil {
		return "直连"
	}
	value, ok := ing.GetAnnotations()["ingress.cloud.tencent.com/direct-access"]
	if ok {
		if cast.ToBool(value) {
			return "直连"
		}
		return "非直连"
	}
	return "非直连"
}

func (s *ClusterService) ListPodByService(ctx context.Context, cluster *model.Cluster, namespace, name string) (*corev1.Service, []corev1.Pod, error) {
	svc, err := s.GetService(ctx, cluster, namespace, name)
	if err != nil {
		return nil, nil, err
	}
	pods, err := s.ListPod(ctx, cluster, namespace, buildLabelSelector(svc.Spec.Selector))
	return svc, pods, err
}

func (s *ClusterService) ListPod(ctx context.Context, cluster *model.Cluster, namespace, labelselector string) ([]corev1.Pod, error) {
	clientset, _ := s.Clientset(ctx, cluster)
	podlist, err := clientset.K8sCli.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{LabelSelector: labelselector})
	return podlist.Items, err
}

func (s *ClusterService) GetLoadBalancerResource(ctx context.Context, cluster *model.Cluster, lbr string) (*lbrv1alpha1.LoadBalancerResource, error) {
	clientset, _ := s.Clientset(ctx, cluster)

	result, err := clientset.LoadbalancerCli.NetworkingV1alpha1().LoadBalancerResources().Get(ctx, lbr, metav1.GetOptions{})

	return result, err
}

func (s *ClusterService) GetService(ctx context.Context, cluster *model.Cluster, namespace, name string) (*corev1.Service, error) {
	clientset, _ := s.Clientset(ctx, cluster)

	result, err := clientset.K8sCli.CoreV1().Services(namespace).Get(ctx, name, metav1.GetOptions{})

	return result, err
}

func (s *ClusterService) GetConfigMap(ctx context.Context, cluster *model.Cluster, namespace, name string) (*corev1.ConfigMap, error) {
	clientset, _ := s.Clientset(ctx, cluster)

	result, err := clientset.K8sCli.CoreV1().ConfigMaps(namespace).Get(ctx, name, metav1.GetOptions{})

	return result, err
}

func (s *ClusterService) GetNetworkingIngress(ctx context.Context, cluster *model.Cluster, namespace, name string) (*networkingv1.Ingress, error) {
	clientset, _ := s.Clientset(ctx, cluster)
	return clientset.K8sCli.NetworkingV1().Ingresses(namespace).Get(ctx, name, metav1.GetOptions{})
}

func (s *ClusterService) GetExtensionIngress(ctx context.Context, cluster *model.Cluster, namespace, name string) (*extensionsv1beta1.Ingress, error) {
	clientset, _ := s.Clientset(ctx, cluster)
	return clientset.K8sCli.ExtensionsV1beta1().Ingresses(namespace).Get(ctx, name, metav1.GetOptions{})
}

func (s *ClusterService) GetMCS(ctx context.Context, cluster *model.Cluster, namespace, name string) (*multiclusterservice.MultiClusterService, error) {
	clientset, _ := s.Clientset(ctx, cluster)

	result, err := clientset.MCSCli.CloudV1alpha1().MultiClusterServices(namespace).Get(ctx, name, metav1.GetOptions{})

	return result, err
}

func (s *ClusterService) GetMCI(ctx context.Context, cluster *model.Cluster, namespace, name string) (*multiclusteringress.MultiClusterIngress, error) {
	clientset, _ := s.Clientset(ctx, cluster)

	result, err := clientset.MCICli.CloudV1alpha1().MultiClusterIngresses(namespace).Get(ctx, name, metav1.GetOptions{})
	return result, err
}

func (s *ClusterService) GetLBR(ctx context.Context, clusterID string, lbr string) (*lbrv1alpha1.LoadBalancerResource, error) {
	clientset, err := s.ClientsetByIanvs(ctx, clusterID, "TODO", "TOKEN") // TODO: 暂时没用使用，等未来有需要再补充
	if err != nil {
		return nil, err
	}

	result, err := clientset.LoadbalancerCli.NetworkingV1alpha1().LoadBalancerResources().Get(ctx, lbr, metav1.GetOptions{})

	return result, err
}

func (s *ClusterService) ClientsetForIngress(ctx context.Context, cluster *model.Cluster) (clientset *cluster3.ClientsSet, err error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	var clientsSet cluster3.ClientsSet
	if cluster.Type == "tke" {
		tkeCluster := cluster2.MustGetTKECluster(cluster.Region, cluster.ClusterID)
		if tkeCluster == nil {
			return nil, fmt.Errorf("cluster %v not found", cluster.ClusterID)
		}
		if tkeCluster.LifeState != "normal" {
			return nil, fmt.Errorf("invalid LifeState %q", tkeCluster.LifeState)
		}

		clientsSet, err = cluster2.GetTKEClusterClientSet(cluster.Region, tkeCluster)
		if err != nil {
			return nil, err
		}
	} else if cluster.Type == "eks" {
		eksCluster, err := cluster2.GetEKSCluster(cluster.Region, cluster.ClusterID)
		if err != nil {
			return nil, err
		}
		if eksCluster == nil {
			return nil, fmt.Errorf("cluster %v not found", cluster.ClusterID)
		}
		metaClusterId, ok := eksCluster.Annotations["eks.tke.cloud.tencent.com/metacluster"]
		if !ok {
			panic(eksCluster)
		}
		metaCluster := cluster2.MustGetTKECluster(cluster.Region, metaClusterId)
		if metaCluster == nil {
			return nil, fmt.Errorf("cluster %v not found", cluster.ClusterID)
		}
		clientsSet, err = cluster2.GetTKEClusterClientSet(cluster.Region, metaCluster)
		if err != nil {
			return nil, err
		}
	} else {
		panic(fmt.Sprintf("unknown %v type %s", cluster.ClusterID, cluster.Type))
	}

	return &clientsSet, nil
}

func (s *ClusterService) GetComponentInfo(ctx context.Context, cluster *model.Cluster) model.ComponentInfo {
	info := model.ComponentInfo{}

	svcClients, err := s.DeployClientsetForService(ctx, cluster)
	if err != nil {
		return info
	}
	svcDeployment, err := GetServiceDeployment(ctx, svcClients.K8sCli, cluster)
	if err != nil {
		return info
	}
	svcCm, err := s.GetConfigMap(ctx, cluster, "kube-system", "tke-service-controller-config")
	if err != nil {
		return info
	}
	info.ServiceConfigMap = mapToString(svcCm.Data)

	ingClients, err := s.ClientsetForIngress(ctx, cluster)
	if err != nil {
		return info
	}
	ingDeployment, err := GetIngressDeployment(ctx, ingClients.K8sCli, cluster)
	if err != nil {
		return info
	}
	ingCm, err := s.GetConfigMap(ctx, cluster, "kube-system", "tke-ingress-controller-config")
	if err != nil {
		return info
	}
	info.IngressConfigMap = mapToString(ingCm.Data)

	info.ServiceControllerImage = svcDeployment.Spec.Template.Spec.Containers[0].Image
	info.ServiceControllerVersion = strings.Split(svcDeployment.Spec.Template.Spec.Containers[0].Image, ":")[1]
	svcArgs := []string{}
	svcArgs = append(svcArgs, svcDeployment.Spec.Template.Spec.Containers[0].Command...)
	svcArgs = append(svcArgs, svcDeployment.Spec.Template.Spec.Containers[0].Args...)
	info.ServiceArgs = strings.Join(svcArgs, ";")
	info.ServiceExpectReplicas = int(svcDeployment.Status.Replicas)
	info.ServiceAvailableReplicas = int(svcDeployment.Status.AvailableReplicas)
	info.ServiceImagePullPolicy = string(svcDeployment.Spec.Template.Spec.Containers[0].ImagePullPolicy)
	info.ServiceResource = svcDeployment.Spec.Template.Spec.Containers[0].Resources.String()

	info.IngressControllerImage = ingDeployment.Spec.Template.Spec.Containers[0].Image
	info.IngressControllerVersion = strings.Split(ingDeployment.Spec.Template.Spec.Containers[0].Image, ":")[1]
	ingArgs := []string{}
	ingArgs = append(ingArgs, ingDeployment.Spec.Template.Spec.Containers[0].Command...)
	ingArgs = append(ingArgs, ingDeployment.Spec.Template.Spec.Containers[0].Args...)
	info.IngressArgs = strings.Join(ingArgs, ";")
	info.IngressExpectReplicas = int(ingDeployment.Status.Replicas)
	info.IngressAvailableReplicas = int(ingDeployment.Status.AvailableReplicas)
	info.IngressImagePullPolicy = string(ingDeployment.Spec.Template.Spec.Containers[0].ImagePullPolicy)
	info.IngressResource = ingDeployment.Spec.Template.Spec.Containers[0].Resources.String()

	return info
}

func mapToString(data map[string]string) string {
	var result []string
	for k, v := range data {
		result = append(result, k+":"+v)
	}
	return strings.Join(result, ";")
}

func (s *ClusterService) DeployClientsetForService(ctx context.Context, cluster *model.Cluster) (clientset *cluster3.ClientsSet, err error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	if data, ok := s.metaClientsetCache.Get(cluster.ClusterID); ok {
		return data.(*cluster3.ClientsSet), nil
	}
	defer func() {
		if err == nil {
			s.metaClientsetCache.SetDefault(cluster.ClusterID, clientset)
		}
	}()

	var clientsSet cluster3.ClientsSet
	if cluster.Type == "tke" {
		tkeCluster := cluster2.MustGetTKECluster(cluster.Region, cluster.ClusterID)
		if cluster == nil {
			return nil, fmt.Errorf("cluster %v not found", cluster.ClusterID)
		}
		if tkeCluster.LifeState != "normal" {
			return nil, fmt.Errorf("invalid LifeState %q", tkeCluster.LifeState)
		}
		if tkeCluster.ClusterType == models.CLUSTER_TYPE_CLUSTER_DEPLOY_ASYNC_PROCESS ||
			tkeCluster.ClusterType == models.CLUSTER_TYPE_CLUSTER_DEPLOY_MIGRATE_TO_OPERATOR ||
			tkeCluster.ClusterType == models.CLUSTER_TYPE_CLUSTER_DEPLOY_ON_OPERATOR {
			metaClusterId := tkeCluster.MetaClusterID
			if tkeCluster.MetaClusterID == "" {
				regionInfo := region.Get(tkeCluster.Region)
				metaClusterId = cluster2.GetDefaultMetaCluster(regionInfo.Alias)
			}
			metaCluster := cluster2.MustGetTKECluster(tkeCluster.Region, metaClusterId)
			clientsSet, err = cluster2.GetTKEClusterClientSet(tkeCluster.Region, metaCluster)
			if err != nil {
				return nil, err
			}
		} else {
			clientsSet, err = cluster2.GetTKEClusterClientSet(cluster.Region, tkeCluster)
			if err != nil {
				return nil, err
			}
		}
	} else if cluster.Type == "eks" {
		eksCluster, err := cluster2.GetEKSCluster(cluster.Region, cluster.ClusterID)
		if err != nil {
			return nil, err
		}
		if eksCluster == nil {
			return nil, fmt.Errorf("cluster %v not found", cluster.ClusterID)
		}
		metaClusterId, ok := eksCluster.Annotations["eks.tke.cloud.tencent.com/metacluster"]
		if !ok {
			panic(eksCluster)
		}
		metaCluster := cluster2.MustGetTKECluster(cluster.Region, metaClusterId)
		if metaCluster == nil {
			return nil, fmt.Errorf("cluster %v not found", cluster.ClusterID)
		}
		clientsSet, err = cluster2.GetTKEClusterClientSet(cluster.Region, metaCluster)
		if err != nil {
			return nil, err
		}
	} else {
		panic(fmt.Sprintf("unknown %v type %s", cluster.ClusterID, cluster.Type))
	}

	return &clientsSet, nil
}

func (s *ClusterService) GetLeaderRecord(ctx context.Context, client kubernetes.Interface, namespace string, name string) (*resourcelock.LeaderElectionRecord, error) {
	cm, err := client.CoreV1().ConfigMaps(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("对应的leader配置（%s/%s）不存在", namespace, name)
	}

	recordStr, found := cm.Annotations[resourcelock.LeaderElectionRecordAnnotationKey]
	if !found {
		return nil, fmt.Errorf("没有找到leader")
	}

	recordBytes := []byte(recordStr)
	record := &resourcelock.LeaderElectionRecord{}

	err = json.Unmarshal(recordBytes, &record)
	if err != nil {
		return nil, fmt.Errorf("解析leader配置失败: %w", err)
	}

	return record, nil
}

const (
	SERVICE_DEPLOYMENT_NAME = "service-controller"
)

func GetServiceDeployment(ctx context.Context, client kubernetes.Interface, cluster *model.Cluster) (*appv1.Deployment, error) {
	namespace, name := getDeploymentName("Service", cluster)

	return client.AppsV1().Deployments(namespace).Get(ctx, name, metav1.GetOptions{})
}

func GetIngressDeployment(ctx context.Context, client kubernetes.Interface, cluster *model.Cluster) (*appv1.Deployment, error) {
	namespace, name := getDeploymentName("Ingress", cluster)
	log.FromContext(ctx).Info("get ingress deployment", "namespace", namespace, "name", name)

	return client.AppsV1().Deployments(namespace).Get(ctx, name, metav1.GetOptions{})
}

func getDeploymentName(taskType string, cluster *model.Cluster) (string, string) {
	if taskType == "Service" {
		return getServiceControllerDeploymentName(cluster)
	}

	return getIngressControllerDeploymentName(cluster)
}

func getServiceControllerDeploymentName(cluster *model.Cluster) (string, string) {
	switch cluster.Type {
	case "tke":
		tkeCluster, err := Get().TKE().Get(context.TODO(), cluster.Region, cluster.ClusterID)
		if err != nil {
			panic(err)
		}
		// cluster.SubType = (fmt.Sprint(tkeCluster.ClusterType))
		return getTKEServiceControllerDeploymentName(tkeCluster)
	case "eks":
		return getEKSServiceControllerDeploymentName(cluster)
	default:
		panic(fmt.Errorf("%s unknown type %s", cluster.ClusterID, cluster.Type))
	}
}

func getIngressControllerDeploymentName(cluster *model.Cluster) (string, string) {
	switch cluster.Type {
	case "tke":
		return "kube-system", "l7-lb-controller"
	case "eks":
		return cluster.ClusterID, fmt.Sprintf("%s-ingress-controller", cluster.ClusterID)
	default:
		panic(fmt.Errorf("%s unknown type %s", cluster.ClusterID, cluster.Type))
	}
}

func getEKSServiceControllerDeploymentName(cluster *model.Cluster) (string, string) {
	return cluster.ClusterID, fmt.Sprintf("%s-service-controller", cluster.ClusterID)
}

func getTKEServiceControllerDeploymentName(cluster *models.Cluster) (string, string) {
	deploymentName := SERVICE_DEPLOYMENT_NAME
	deploymentNS := metav1.NamespaceSystem
	switch cluster.ClusterType {
	case models.CLUSTER_TYPE_CLUSTER_ORIGIN: // vm
		deploymentName = SERVICE_DEPLOYMENT_NAME
	case models.CLUSTER_TYPE_CLUSTER_INDEPENDENT: // independent
		deploymentName = SERVICE_DEPLOYMENT_NAME
	case models.CLUSTER_TYPE_CLUSTER_DEPLOY_ASYNC_PROCESS, models.CLUSTER_TYPE_CLUSTER_DEPLOY_MIGRATE_TO_OPERATOR, models.CLUSTER_TYPE_CLUSTER_DEPLOY_ON_OPERATOR: // operator managed
		deploymentName = fmt.Sprintf("%s-%s", cluster.ClusterInstanceId, SERVICE_DEPLOYMENT_NAME)
		deploymentNS = cluster.ClusterInstanceId
	default: // meta cluster
		deploymentName = SERVICE_DEPLOYMENT_NAME
		deploymentNS = metav1.NamespaceSystem
	}
	return deploymentNS, deploymentName
}

func buildLabelSelector(labels map[string]string) string {
	var parts []string
	for key, value := range labels {
		part := fmt.Sprintf("%s=%s", key, value)
		parts = append(parts, part)
	}
	return strings.Join(parts, ",")
}
