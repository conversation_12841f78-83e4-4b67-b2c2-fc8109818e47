package services

import (
	"bytes"
	"context"
	"strings"

	"github.com/samber/lo"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/client-go/kubernetes"
)

func GetPodsLogs(ctx context.Context, cli kubernetes.Interface, pods []corev1.Pod, opts *corev1.PodLogOptions) ([]string, error) {
	logs := []string{}
	for _, p := range pods {
		log, err := GetPodLog(ctx, cli, &p, opts)
		if err != nil {
			return nil, err
		}
		logs = append(logs, log...)
	}
	return logs, nil
}

func GetPodLog(ctx context.Context, cli kubernetes.Interface, pod *corev1.Pod, opts *corev1.PodLogOptions) ([]string, error) {
	var b bytes.Buffer
	req := cli.CoreV1().Pods(pod.Namespace).GetLogs(pod.Name, opts)
	rc, err := req.Stream(ctx)
	if err != nil {
		return nil, err
	}
	defer rc.Close()
	b.ReadFrom(rc)
	return lo.Map(strings.Split(b.String(), "\n"), func(s string, _ int) string { return strings.Trim(s, "\t\n") }), nil
}
