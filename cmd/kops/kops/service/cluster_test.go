package services

import (
	"testing"
)

func TestClusterService_PrepareForTask(t *testing.T) {
	// readonlyConfig, err := conf.LoadConfigFromFile("conf/tops-readonly.yaml")
	// if err != nil {
	// 	panic(err)
	// }
	// cluster2.Config = readonlyConfig
	// common2.InitUserService(readonlyConfig)
	// err = kopsconfig.Init("conf/kops.yaml")
	// if err != nil {
	// 	panic(err)
	// }

	// Init()

	// services.Cluster().PrepareForTask(1)
}
