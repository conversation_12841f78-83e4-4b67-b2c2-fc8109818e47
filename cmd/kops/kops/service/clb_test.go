package services

import (
	"context"
	"testing"

	"git.woa.com/kateway/kateway-server/pkg/tmp/tencentcloud/sts"
	"git.woa.com/kateway/kateway-server/pkg/tmp/web/cloudctx"
	"git.woa.com/kateway/kateway-server/pkg/util/region"
)

func TestCLBService_List(t *testing.T) {
	sts.Init(sts.Config{
		RoleName:    "TKE_QCSRole",
		SecretID:    "AKIDXco1EUjCkIuZK5qfaXWbKQTWcq3JdyOq",
		SecretKey:   "Y6X2Z7kO6sGe2VXZXVQNOQUZYTXZdkaQ",
		ServiceRole: false,
	})
	ctx := cloudctx.WithRegion(context.TODO(), region.MustGet("ap-guangzhou"))
	ctx = cloudctx.WithUin(ctx, "2252646423")
	svc := CLBService{}
	svc.List(ctx)
	// svc.ListListeners(ctx, "lb-6pq02fwh")
	// svc.ListTargets(ctx, "lb-hnd5ui5l")
}
