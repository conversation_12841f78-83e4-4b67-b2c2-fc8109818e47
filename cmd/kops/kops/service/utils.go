package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	multiclusteringress "git.woa.com/kateway/multi-cluster-ingress-api/apis/multiclusteringress/v1alpha1"
	multiclusterservice "git.woa.com/kateway/multi-cluster-service-api/apis/multiclusterservice/v1alpha1"
	lbrv1alpha1 "git.woa.com/misakazhou/loadbalancer-resource-api/pkg/apis/loadbalancerresource/v1alpha1"

	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	"git.woa.com/kateway/kateway-server/pkg/tmp/telemetry/log"
	"git.woa.com/kateway/kateway-server/pkg/tmp/types/sets"
)

// CompleteDFS 填充CLB相关的DFS信息
func CompleteDFS(lbid string, appid uint64, clbregionid int, clbinfo *model.CloudLoadBalancerInfo) {
	clbinfo.DFSCLBDetail = BuildDFSCLBDetail(appid, clbregionid, lbid)
	clbinfo.DFSCLBMonitor = BuildDFSCLBMonitor(clbregionid, lbid)
	clbinfo.DFSCLBTask = BuildDFSCLBTask(appid, clbregionid, lbid)
	clbinfo.DFSCLBAccessLog = BuildDFSCLBAccessLog(appid, clbregionid, lbid)
	clbinfo.DFSCLBHealth = BuildDFSCLBHealth(clbregionid, lbid)
	clbinfo.DFSCLSLog = BuildCLSLog()
}

// 托管组件监控
func GetMetaComponentMonitorURL(cluster *model.Cluster) string {
	if cluster.IsEKS() {
		return GetEksControlPlaneMonitorURL(*cluster)
	}
	return GetTkeControlPlaneMonitorURL(*cluster)
}

func GetEksControlPlaneMonitorURL(cluster model.Cluster) string {
	set := "public"
	target := cluster.GetRegion().Alias
	if cluster.Appid == ********** {
		set = "ziyan"
		target += "-ziyan"
	}
	return fmt.Sprintf(
		"http://panel.woa.com/d/LXZ2vScnz/dan-ji-qun-tuo-guan-zu-jian?orgId=5&var-product=eks&var-cluster=%s&var-appid=&var-region=%s&var-target=eks-%s&var-DataSource=global-prometheus-eks-%s&var-region_prometheus=&var-user_apiserver_datasource=&var-ece=ETCD_ALL_REGION&var-myinterval=1m&var-day_duration=24h&var-scheduler_instance=All&var-query0=All&var-etcdSource=ETCD_ALL_REGION&var-set=%s",
		cluster.ClusterID, target, target, target, set)
}

func GetTkeControlPlaneMonitorURL(cluster model.Cluster) string {
	set := "public"
	target := cluster.GetRegion().Alias
	if cluster.Appid == ********** {
		set = "ziyan"
		target += "-ziyan"
	}
	return fmt.Sprintf(
		"http://panel.woa.com/d/LXZ2vScnz/dan-ji-qun-tuo-guan-zu-jian?orgId=5&var-product=tke&var-cluster=%s&var-appid=%d&var-region=%s&var-target=%s&var-DataSource=global-prometheus-%s&var-region_prometheus=tke-user-prometheus-%s&var-user_apiserver_datasource=tke-user-apiserver-%s&var-etcdSource=ETCD_ALL_REGION&var-myinterval=1m&var-day_duration=24h&var-scheduler_instance=All&var-query0=All&var-set=%s",
		cluster.ClusterID, cluster.Appid, target, target, target, target, target, set)
}

func GetServiceControllerMonitorURL(cluster *model.Cluster) string {
	set := "public"
	target := cluster.GetRegion().Alias
	if cluster.Appid == ********** {
		set = "ziyan"
		target += "-ziyan"
	}
	if cluster.IsEKS() {
		return fmt.Sprintf(`https://panel.woa.com/d/PGkJbAp7k/eks-user-service-controller?orgId=5&var-regionname=%s&var-target=eks-%s&var-region=region-prometheus-eks-%s&var-cluster=%s&var-time_granularity=5m&var-type=All&var-apiversion=All&var-namespace=&var-set=%s`, target, target, target, cluster.ClusterID, set)
	}
	return fmt.Sprintf(`https://panel.woa.com/d/tAziUFyMz/tke-user-service-controller?orgId=5&var-regionname=%s&var-target=%s&var-region=tke-user-prometheus-%s&var-cluster=%s&var-time_granularity=5m&var-type=All&var-apiversion=All&var-namespace=&var-set=%s`, target, target, target, cluster.ClusterID, set)
}

func BuildTKEOSSCluster(appId uint64, regId int, clusterid, uin string) string {
	return fmt.Sprintf("https://tke.woa.com/oss/tke/control-center/resource/cluster/info?region=%d&clusterId=%s&appId=%d&uin=%s", regId, clusterid, appId, uin)
}

func BuildTKEOSSUser(appId uint64, uin string) string {
	return fmt.Sprintf("https://tke.woa.com/oss/tke/control-center/resource/userinfo?appid=%d&name=&ownerUin=%s", appId, uin)
}

// BuildDFSQuota 构建DFS配额管理URL
func BuildDFSQuota(appId uint64, regId int) string {
	return fmt.Sprintf("https://dfs.woa.com/clb/quota-manage?rid=%d&AppId=%d", regId, appId)
}

// BuildDFSCLBDetail 构建DFS CLB详情URL
func BuildDFSCLBDetail(appId uint64, regId int, clbId string) string {
	return fmt.Sprintf("https://dfs.woa.com/clb/resource-overview-detail?id=%s&rid=%d&appId=%d&tab=info", clbId, regId, appId)
}

// BuildDFSCLBMonitor 构建DFS CLB监控URL
func BuildDFSCLBMonitor(regId int, clbId string) string {
	return fmt.Sprintf("https://defensor.woa.com/clb/resource-overview?rid=%d&LoadBalancerIds=%s&Limit=10&Offset=0", regId, clbId)
}

// BuildDFSCLBTask 构建DFS CLB任务URL
func BuildDFSCLBTask(appId uint64, regId int, clbId string) string {
	return fmt.Sprintf("https://dfs.woa.com/clb/operate-log?clbId=%s&rid=%d&appId=%d", clbId, regId, appId)
}

// BuildDFSCLBHealth 构建DFS CLB健康检查URL
func BuildDFSCLBHealth(regId int, clbId string) string {
	return fmt.Sprintf("https://dfs.woa.com/clb/trouble-shoot?clbId=%s&rid=%d", clbId, regId)
}

// BuildDFSCLBAccessLog 构建DFS CLB访问日志URL
// 默认查询时间范围为昨天到现在
func BuildDFSCLBAccessLog(appId uint64, regId int, clbId string) string {
	now := time.Now()
	yesterday := now.AddDate(0, 0, -1)
	return fmt.Sprintf("https://dfs.woa.com/clb/l7-log?rid=%d&LoadBalancerId=%s&EndTime=%d&StartTime=%d", regId, clbId, now.Unix(), yesterday.Unix())
}

// BuildCLSLog 构建CLS日志URL
func BuildCLSLog() string {
	return "https://defensor.woa.com/service/dfs-cls"
}

// GenerateCLBInfo 根据CLB信息生成CloudLoadBalancerInfo
func GenerateCLBInfo(clbinfo clb.LoadBalancer) model.CloudLoadBalancerInfo {
	info := model.CloudLoadBalancerInfo{
		LoadBalancerId:   clbinfo.LoadBalancerId,
		LoadBalancerName: clbinfo.LoadBalancerName,
		LoadBalancerVips: clbinfo.LoadBalancerVips,
		CreateTime:       clbinfo.CreateTime,
		VpcId:            clbinfo.VpcId,
		AddressIPVersion: clbinfo.AddressIPVersion,
		ClusterID:        Get().CLB().GetClusterID(&clbinfo),
	}

	// 设置负载均衡器类型
	if clbinfo.LoadBalancerType != nil {
		if *clbinfo.LoadBalancerType == "INTERNAL" {
			info.LoadBalancerType = "内网CLB"
		} else {
			info.LoadBalancerType = "公网CLB"
		}
	}

	// 设置负载均衡器转发类型
	if clbinfo.Forward != nil {
		if *clbinfo.Forward == 1 {
			info.Forward = "应用型CLB"
		} else {
			info.Forward = "传统型CLB"
		}
	}

	// 设置负载均衡器状态
	if clbinfo.Status != nil {
		if *clbinfo.Status == 1 {
			info.Status = "正常"
		} else {
			info.Status = "异常"
		}
	}

	// 设置SNAT Pro状态
	if clbinfo.SnatPro != nil {
		if *clbinfo.SnatPro {
			info.SnatPro = "开启"
		} else {
			info.SnatPro = "关闭"
		}
	}

	// 设置CLB创建方式
	if Get().CLB().IsAutoCreated(&clbinfo) {
		info.CreatedByTKE = "TKE创建"
	} else {
		info.CreatedByTKE = "使用已有"
	}

	return info
}

// GenerateLBRInfo 根据LoadBalancerResource生成LoadBalancerResourceInfo
func GenerateLBRInfo(lbr lbrv1alpha1.LoadBalancerResource, cluster *model.Cluster, includePodLists bool) model.LoadBalancerResourceInfo {
	info := model.LoadBalancerResourceInfo{}
	info.CLBRegion = lbr.Spec.Region

	if lbr.GetAnnotations() != nil {
		from, ok := lbr.Annotations["cloud.tencent.com/clbmigration-from-cluster"]
		if ok {
			migrateInfo := &model.MigrationInfo{}
			migrateInfo.IsMigratedCLB = "是"
			migrateInfo.MigrateFromCluster = from
			info.Migrate = migrateInfo
		}
	}

	// 设置负载均衡资源锁信息
	if lock := lbr.Status.Lock; lock != nil {
		if rs := lock.Resource; rs != nil {
			info.LoadBalancerResourceLock.Status = "同步中"
			info.LoadBalancerResourceLock.Resource.Name = rs.Name
			info.LoadBalancerResourceLock.Resource.Namespace = rs.Namespace
			info.LoadBalancerResourceLock.Resource.Kind = rs.Kind
		} else {
			info.LoadBalancerResourceLock.Status = "未同步"
			info.LoadBalancerResourceLock.Resource.Name = "暂未被占用"
			info.LoadBalancerResourceLock.Resource.Namespace = "暂未被占用"
			info.LoadBalancerResourceLock.Resource.Kind = "暂未被占用"
		}
	} else {
		info.LoadBalancerResourceLock.Status = "未同步"
		info.LoadBalancerResourceLock.Resource.Name = "暂未被占用"
		info.LoadBalancerResourceLock.Resource.Namespace = "暂未被占用"
		info.LoadBalancerResourceLock.Resource.Kind = "暂未被占用"
	}

	// 构建资源矩阵，用于统计每个资源引用的监听器信息
	resourceMatrix := map[string]sets.String{}
	for _, listener := range lbr.Spec.Listeners {
		for _, ref := range listener.References {
			key := ref.Kind + "/" + ref.Namespace + "/" + ref.Name
			if _, ok := resourceMatrix[key]; !ok {
				resourceMatrix[key] = sets.NewString(listener.Protocol + "-" + fmt.Sprint(listener.Port))
			} else {
				resourceMatrix[key].Insert(listener.Protocol + "-" + fmt.Sprint(listener.Port))
			}
		}
	}

	// 填充资源信息
	for resource, matrix := range resourceMatrix {
		keys := strings.Split(resource, "/")
		if includePodLists {
			switch strings.ToLower(keys[0]) {
			case "service":
				{
					var count = 0
					svc, podList, _ := services.Cluster().ListPodByService(context.Background(), cluster, keys[1], keys[2])
					count = len(podList)
					time.Sleep(1 * time.Second)
					info.Resources = append(info.Resources, model.ObjectReference{
						Kind:          keys[0],
						Namespace:     keys[1],
						Name:          keys[2],
						PortProtocols: strings.Join(matrix.List(), " "),
						PodCount:      fmt.Sprint(count),
						DirectAccess:  services.Cluster().IsDirectAccessService(cluster, svc),
					})
				}
			case "ingress":
				info.Resources = append(info.Resources, model.ObjectReference{
					Kind:          "Ingress",
					Namespace:     keys[1],
					Name:          keys[2],
					PortProtocols: strings.Join(matrix.List(), " "),
				})

				ing, err := services.Cluster().GetNetworkingIngress(context.Background(), cluster, keys[1], keys[2])
				if err == nil {
					backends := sets.NewString()
					for _, rule := range ing.Spec.Rules {
						for _, path := range rule.HTTP.Paths {
							backends.Insert(path.Backend.Service.Name)
						}
					}
					for _, bk := range backends.List() {
						var count = 0
						_, podList, _ := services.Cluster().ListPodByService(context.Background(), cluster, keys[1], bk)
						count = len(podList)
						log.Info("fetch service backend", "count", count, "service", bk)
						info.Resources = append(info.Resources, model.ObjectReference{
							Kind:         "Service",
							Namespace:    keys[1],
							Name:         bk,
							PodCount:     fmt.Sprint(count),
							DirectAccess: services.Cluster().IsDirectAccessIngress(cluster, ing),
						})
						time.Sleep(1 * time.Second)
					}
				}
			default:
				info.Resources = append(info.Resources, model.ObjectReference{
					Kind:          keys[0],
					Namespace:     keys[1],
					Name:          keys[2],
					PortProtocols: strings.Join(matrix.List(), " "),
					PodCount:      "未扫描",
					DirectAccess:  "直连",
				})
			}
		} else {
			switch strings.ToLower(keys[0]) {
			case "service":
				svc, _ := services.Cluster().GetService(context.Background(), cluster, keys[1], keys[2])
				info.Resources = append(info.Resources, model.ObjectReference{
					Kind:          keys[0],
					Namespace:     keys[1],
					Name:          keys[2],
					PortProtocols: strings.Join(matrix.List(), " "),
					PodCount:      "未扫描",
					DirectAccess:  services.Cluster().IsDirectAccessService(cluster, svc),
				})
			case "ingress":
				ing, _ := services.Cluster().GetNetworkingIngress(context.Background(), cluster, keys[1], keys[2])
				info.Resources = append(info.Resources, model.ObjectReference{
					Kind:          keys[0],
					Namespace:     keys[1],
					Name:          keys[2],
					PortProtocols: strings.Join(matrix.List(), " "),
					PodCount:      "未扫描",
					DirectAccess:  services.Cluster().IsDirectAccessIngress(cluster, ing),
				})
			default:
				info.Resources = append(info.Resources, model.ObjectReference{
					Kind:          keys[0],
					Namespace:     keys[1],
					Name:          keys[2],
					PortProtocols: strings.Join(matrix.List(), " "),
					PodCount:      "未扫描",
					DirectAccess:  "直连",
				})
			}
		}
	}

	// 设置使用的Ingress和Service数量
	info.UsingIngressCount = len(lbr.Status.IngressResource)
	info.UsingServiceCount = len(lbr.Status.ServiceResource)

	return info
}

// 定义常量，表示不同的协议类型
var (
	PROTOCOL_TCP     = "TCP"
	PROTOCOL_UDP     = "UDP"
	PROTOCOL_TCP_SSL = "TCP_SSL"
	PROTOCOL_QUIC    = "QUIC"
	PROTOCOL_HTTP    = "HTTP"
	PROTOCOL_HTTPS   = "HTTPS"
)

// BuildWeightStats 构建权重统计信息
// 输入参数为两组监听器和规则的集合
// 返回一个包含CLBRisk结构体的切片，表示每个监听器和规则的权重风险信息
func BuildWeightStats(listener, rule, plistener, prule sets.String) []model.CLBRisk {
	// 初始化一个空的CLBRisk切片
	targets := []model.CLBRisk{}
	// 遍历所有监听器，构建风险键并添加到targets切片中
	for _, l := range listener.List() {
		port, protocol, hostname, path := buildRiskKey(l)
		f := model.CLBRisk{
			Type:     "Listener",
			Port:     port,
			Protocol: protocol,
			Host:     hostname,
			Path:     path,
			Reason:   "全部RS权重为0",
		}
		targets = append(targets, f)
	}
	// 遍历部分监听器，构建风险键并添加到targets切片中
	for _, l := range plistener.List() {
		port, protocol, hostname, path := buildRiskKey(l)
		f := model.CLBRisk{
			Type:     "Listener",
			Port:     port,
			Protocol: protocol,
			Host:     hostname,
			Path:     path,
			Reason:   "部分RS权重为0",
		}
		targets = append(targets, f)
	}
	// 遍历所有规则，构建风险键并添加到targets切片中
	for _, r := range rule.List() {
		port, protocol, hostname, path := buildRiskKey(r)
		f := model.CLBRisk{
			Type:     "Rule",
			Port:     port,
			Protocol: protocol,
			Host:     hostname,
			Path:     path,
			Reason:   "全部RS权重为0",
		}
		targets = append(targets, f)
	}
	// 遍历部分规则，构建风险键并添加到targets切片中
	for _, r := range prule.List() {
		port, protocol, hostname, path := buildRiskKey(r)
		f := model.CLBRisk{
			Type:     "Rule",
			Port:     port,
			Protocol: protocol,
			Host:     hostname,
			Path:     path,
			Reason:   "部分RS权重为0",
		}
		targets = append(targets, f)
	}
	// 返回包含所有风险信息的切片
	return targets
}

// BuildHealthStats 构建健康检查统计信息
// 输入参数为两组监听器和规则的集合
// 返回一个包含CLBRisk结构体的切片，表示每个监听器和规则的健康检查风险信息
func BuildHealthStats(listener, rule, plistener, prule sets.String) []model.CLBRisk {
	// 初始化一个空的CLBRisk切片
	targets := []model.CLBRisk{}
	// 遍历所有监听器，构建风险键并添加到targets切片中
	for _, l := range listener.List() {
		port, protocol, hostname, path := buildRiskKey(l)
		f := model.CLBRisk{
			Type:     "Listener",
			Port:     port,
			Protocol: protocol,
			Host:     hostname,
			Path:     path,
			Reason:   "全部RS健康检查失败",
		}
		targets = append(targets, f)
	}
	// 遍历部分监听器，构建风险键并添加到targets切片中
	for _, l := range plistener.List() {
		port, protocol, hostname, path := buildRiskKey(l)
		f := model.CLBRisk{
			Type:     "Listener",
			Port:     port,
			Protocol: protocol,
			Host:     hostname,
			Path:     path,
			Reason:   "部分RS健康检查失败",
		}
		targets = append(targets, f)
	}
	// 遍历所有规则，构建风险键并添加到targets切片中
	for _, r := range rule.List() {
		port, protocol, hostname, path := buildRiskKey(r)
		f := model.CLBRisk{
			Type:     "Rule",
			Port:     port,
			Protocol: protocol,
			Host:     hostname,
			Path:     path,
			Reason:   "全部RS健康检查失败",
		}
		targets = append(targets, f)
	}
	// 遍历部分规则，构建风险键并添加到targets切片中
	for _, r := range prule.List() {
		port, protocol, hostname, path := buildRiskKey(r)
		f := model.CLBRisk{
			Type:     "Rule",
			Port:     port,
			Protocol: protocol,
			Host:     hostname,
			Path:     path,
			Reason:   "部分RS健康检查失败",
		}
		targets = append(targets, f)
	}
	// 返回包含所有风险信息的切片
	return targets
}

// SplitLBRGroups 将资源列表分割成多个小组
// 输入参数为资源列表和每个小组的大小
// 返回一个二维切片，每个元素为一个小组的资源列表
func SplitLBRGroups(resources []lbrv1alpha1.LoadBalancerResource, groupSize int) [][]lbrv1alpha1.LoadBalancerResource {
	// 初始化一个空的二维切片
	var groups [][]lbrv1alpha1.LoadBalancerResource
	// 遍历资源列表，按照groupSize大小进行分割
	for i := 0; i < len(resources); i += groupSize {
		end := i + groupSize
		if end > len(resources) {
			end = len(resources)
		}
		// 将分割后的小组添加到groups切片中
		groups = append(groups, resources[i:end])
	}
	// 返回包含所有小组的二维切片
	return groups
}

const (
	serviceConditionAnnotation = "service.cloud.tencent.com/status.conditions"
	ingressConditionAnnotation = "ingress.cloud.tencent.com/status.conditions"
)

func GetIngressConditions(annotations map[string]string) bool {
	if annotation, exist := annotations[ingressConditionAnnotation]; exist {
		var conditions []metav1.Condition
		if err := json.Unmarshal([]byte(annotation), &conditions); err != nil {
			return false
		}
		return IsHealth(conditions)
	}
	return false
}

func GetServiceConditions(svc *v1.Service) bool {
	annotations := svc.GetAnnotations()
	if annotation, exist := annotations[serviceConditionAnnotation]; exist {
		var conditions []metav1.Condition
		if err := json.Unmarshal([]byte(annotation), &conditions); err != nil {
			return false
		}
		return IsHealth(conditions)
	} else {
		return IsHealth(svc.Status.Conditions)
	}
}

func GetMCIConditions(ingress *multiclusteringress.MultiClusterIngress) bool {
	return IsHealth(ingress.Status.Conditions)
}

func GetMCSConditions(svc *multiclusterservice.MultiClusterService) bool {
	return IsHealth(svc.Status.Conditions)
}

func IsHealth(conditions []metav1.Condition) bool {
	if len(conditions) == 0 {
		return false
	} else {
		for _, condition := range conditions {
			if condition.Status == metav1.ConditionFalse {
				return false
			}
		}
	}
	return true
}

type IngressAnnotations map[string]string

func (i IngressAnnotations) IsReused() bool {
	return i.GetExistLbId() != ""
}

func (i IngressAnnotations) LBID() string {
	if i.IsReused() {
		return i.GetExistLbId()
	}
	return i.GetLbId()
}

func (i IngressAnnotations) GetLbId() string {
	return i["kubernetes.io/ingress.qcloud-loadbalance-id"]
}

func (i IngressAnnotations) GetExistLbId() string {
	return i["kubernetes.io/ingress.existLbId"]
}

type ServiceAnnotations map[string]string

func (s ServiceAnnotations) LBID() string {
	if s.IsReused() {
		return s.GetExistLbId()
	}
	return s.GetLbId()
}

func (s ServiceAnnotations) IsReused() bool {
	return s.GetExistLbId() != ""
}

// CLB ID
func (s ServiceAnnotations) GetLbId() string {
	return s["service.kubernetes.io/loadbalance-id"]
}

// CLB 复用
func (s ServiceAnnotations) GetExistLbId() string {
	return s["service.kubernetes.io/tke-existed-lbid"]
}

// buildRiskKey 根据传入的key构建风险键
// 输入参数为一个字符串key
// 返回值为端口、协议、主机名和路径四个字符串
func buildRiskKey(key string) (port, protocol, hostname, path string) {
	// 如果key包含"="，则按照"="分割成两部分
	if strings.Contains(key, "=") {
		l4key, l7key := strings.Split(key, "=")[0], strings.Split(key, "=")[1]
		port, protocol = strings.Split(l4key, "_")[0], strings.Split(l4key, "_")[1]
		hostname, path = strings.Split(l7key, "_")[0], strings.Split(l7key, "_")[1]
	} else {
		// 否则直接按照"_"分割
		port, protocol = strings.Split(key, "_")[0], strings.Split(key, "_")[1]
	}
	// 返回端口、协议、主机名和路径四个字符串
	return
}

// isL4Protocol 判断协议是否为四层协议
// 输入参数为一个字符串protocol
// 返回值为布尔值，表示是否为四层协议
func isL4Protocol(protocol string) bool {
	return protocol == PROTOCOL_TCP || protocol == PROTOCOL_UDP || protocol == PROTOCOL_TCP_SSL || protocol == PROTOCOL_QUIC
}

// getListenerKey 根据端口和协议构建监听器键
// 输入参数为端口号和协议字符串
// 返回值为监听器键字符串
func getListenerKey(port int64, protocol string) string {
	return fmt.Sprintf("%d_%s", port, strings.ToUpper(protocol))
}

// getRuleKey 根据主机名和路径构建规则键
// 输入参数为主机名和路径字符串
// 返回值为规则键字符串
func getRuleKey(host string, path string) string {
	return fmt.Sprintf("%s_%s", host, path)
}

// 判断注册时间是否与当前时间的差距在5分钟内
func ignorableZeroWeightRS(ctx context.Context, lbid string, registeredTimeStr *string) bool {
	if registeredTimeStr == nil {
		log.FromContext(ctx).Info("[warnning] zero-weight rs", "lbid", lbid)
		return false
	}
	// 定义时间格式
	const layout = "2006-01-02 15:04:05"
	// 解析注册时间
	registeredTime, err := time.Parse(layout, *registeredTimeStr)
	if err != nil {
		log.FromContext(ctx).Error(err, "zero-weight rs error", "lbid", lbid)
		return false
	}

	currentTime := time.Now()
	duration := currentTime.Sub(registeredTime)

	if duration < 0 {
		duration = -duration
	}
	if duration <= 5*time.Minute {
		log.FromContext(ctx).Info("[ignorable] zero-weight rs", "lbid", lbid, "registertime", *registeredTimeStr, "diff", duration.String())
		return true
	}

	log.FromContext(ctx).Info("[warnning] zero-weight rs", "lbid", lbid, "registertime", *registeredTimeStr, "diff", duration.String())
	return false
}

func UsingProtocols(lbr lbrv1alpha1.LoadBalancerResource) string {
	protocols := sets.NewString()
	for _, listener := range lbr.Spec.Listeners {
		protocols.Insert(listener.Protocol)
	}

	return strings.Join(protocols.List(), ",")
}
