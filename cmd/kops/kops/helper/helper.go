package helper

import (
	"net/http"
	"os"
	"runtime"
	"strings"

	"github.com/spf13/cobra"

	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"
)

func InputClusters(cmd *cobra.Command) ([]string, error) {
	var clusters []string

	file, _ := cmd.Flags().GetString("file")
	if file != "" {
		data, err := os.ReadFile(file)
		if err != nil {
			return nil, err
		}
		clusters = strings.Fields(string(data))
	} else {
		clusters, _ = cmd.Flags().GetStringSlice("cluster")
		if len(clusters) == 0 {
			return nil, fmt.Errorf("cluster required")
		}
	}
	fmt.Printf("input cluster: %v\n", clusters)
	// for _, clusterID := range clusters {
	// 	cluster := &model.Cluster{
	// 		ClusterID: sqldb.NewColumn(clusterID),
	// 	}
	// 	err := services.Get().Cluster().Create(cmd.Context(), cluster)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// }

	return clusters, nil
}

func GetPanicStackTrace(r interface{}) string {
	if r == http.ErrAbortHandler {
		// honor the http.ErrAbortHandler sentinel panic value:
		//   ErrAbortHandler is a sentinel panic value to abort a handler.
		//   While any panic from ServeHTTP aborts the response to the client,
		//   panicking with ErrAbortHandler also suppresses logging of a stack trace to the server's error log.
		return ""
	}

	// Same as stdlib http server code. Manually allocate stack trace buffer size
	// to prevent excessively large logs
	const size = 64 << 10
	stacktrace := make([]byte, size)
	stacktrace = stacktrace[:runtime.Stack(stacktrace, false)]
	return fmt.Sprintf("Observed a panic: %#v (%v)\n%s", r, r, stacktrace)
}
