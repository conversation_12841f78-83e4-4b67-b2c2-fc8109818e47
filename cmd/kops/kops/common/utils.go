package common

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/hashicorp/go-version"
	"github.com/samber/lo"
	v1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/leaderelection/resourcelock"
	"k8s.io/klog"

	version2 "git.woa.com/kateway/pkg/app/version"

	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	"git.woa.com/kateway/kateway-server/pkg/tmp/telemetry/log"
)

var (
	DefaultVersionCnt = 3
)

func ParseTypedName(fullName string) (resource string, nn types.NamespacedName, err error) {
	splits := strings.Split(fullName, "/")
	switch len(splits) {
	case 1:
		nn.Namespace = "default"
		nn.Name = splits[0]
	case 2:
		nn.Namespace = splits[0]
		nn.Name = splits[1]
	case 3:
		resource = splits[0]
		nn.Namespace = splits[1]
		nn.Name = splits[2]
	default:
		err = fmt.Errorf("invalid TypedName: %s", fullName)
	}
	return
}

func MustParseTypedName(fullName string) (string, string, string) {
	resource, nn, err := ParseTypedName(fullName)
	if err != nil {
		panic(err)
	}
	return resource, nn.Namespace, nn.Name
}

func GetPodSha256(pod *corev1.Pod) string {
	if pod.Status.ContainerStatuses != nil && len(pod.Status.ContainerStatuses) != 0 {
		split := strings.Split(pod.Status.ContainerStatuses[0].ImageID, "@")
		if len(split) == 2 {
			return split[1]
		}
	}
	return "None"
}

func GetImageTag(image string) string {
	parts := strings.Split(image, ":")
	if len(parts) == 2 {
		return parts[1]
	}
	return image
}

// IsSupportArgMockOldVersion
// service/ingress controller 在 v2.4.1 支持了 新参数 MockOldVersion
func IsSupportArgMockOldVersion(image string) bool {
	base, _ := version.NewSemver("v2.4.0")
	v, err := version.NewSemver(GetImageTag(image))
	if err != nil {
		return false
	}

	return v.GreaterThan(base)
}

func GetControllerImage(deploy v1.Deployment) string {
	if len(deploy.Spec.Template.Spec.Containers) == 1 {
		return deploy.Spec.Template.Spec.Containers[0].Image
	}

	for _, container := range deploy.Spec.Template.Spec.Containers {
		if container.Name == "l7-lb-controller" || container.Name == "service-controller" {
			return container.Image
		}
	}

	return ""
}

// InitIngressVersionLimit initializes the ingress version limits
func InitIngressVersionLimit() map[int]map[string]int {
	return map[int]map[string]int{
		1: {"max": 8, "min": 0},
		2: {"max": 5, "min": 0},
	}
}

var IngressVersionlimits = InitIngressVersionLimit()

func GetTagFromImage(image string) ([]int, error) {
	parts := strings.Split(image, ":")
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid image format: %s", image)
	}
	version, err := version.NewVersion(parts[1])
	if err != nil {
		return nil, err
	}
	if len(version.Segments()) < DefaultVersionCnt {
		return version.Segments(), nil
	}
	return version.Segments()[:DefaultVersionCnt], nil
}

func IsCrossVersion(curTagSlice, tarTagSlice []int) bool {
	// 如果主版本相同，检查次版本
	if curTagSlice[0] == tarTagSlice[0] {
		return curTagSlice[1]+1 < tarTagSlice[1]
	}

	// 如果当前主版本大于目标主版本
	if curTagSlice[0] > tarTagSlice[0] {
		return false
	}
	// 如果当前主版本小于目标主版本
	// 检查主版本差异是否大于1
	if tarTagSlice[0]-curTagSlice[0] > 1 {
		return true
	}

	// 获取当前和目标版本的限制
	upper, curOk := IngressVersionlimits[curTagSlice[0]]["max"]
	lower, tarOk := IngressVersionlimits[tarTagSlice[0]]["min"]

	// 检查是否存在版本限制并与当前次版本进行比较
	return !curOk || upper != curTagSlice[1] || !tarOk || lower != tarTagSlice[1]
}

func CheckImageIfCrossVersion(image string, deploy *v1.Deployment) (bool, error) {
	if deploy == nil {
		return false, fmt.Errorf("deployment is nil")
	}
	curImage := GetControllerImage(*deploy)

	curTagSlice, err := GetTagFromImage(curImage)
	if err != nil {
		return false, err
	}
	if len(curTagSlice) != DefaultVersionCnt {
		return false, fmt.Errorf("invalid current image: %s", curImage)
	}

	tarTagSlice, err := GetTagFromImage(image)
	if err != nil {
		return false, err
	}
	if len(tarTagSlice) != DefaultVersionCnt {
		return false, fmt.Errorf("invalid target image: %s", image)
	}

	if IsCrossVersion(curTagSlice, tarTagSlice) {
		return true, fmt.Errorf("check whether cross image version: yes")
	}

	return false, nil
}

type LeaderStatus struct {
	Exists          bool
	Identity        string
	ExpiredAt       time.Time
	RenewedAt       time.Time
	ExpiredDuration time.Duration
}

func GetLeaderStatus(ctx context.Context, client kubernetes.Interface, namespace string, name string) (*LeaderStatus, error) {
	cm, err := client.CoreV1().ConfigMaps(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		if apierrors.IsNotFound(err) {
			return nil, fmt.Errorf("对应的leader配置（%s/%s）不存在", namespace, name)
		}
		return nil, fmt.Errorf("获取leader配置(%s/%s)失败: %w", namespace, name, err)
	}

	recordStr, found := cm.Annotations[resourcelock.LeaderElectionRecordAnnotationKey]
	if !found {
		return nil, fmt.Errorf("没有找到leader")
	}

	recordBytes := []byte(recordStr)
	record := &resourcelock.LeaderElectionRecord{}

	err = json.Unmarshal(recordBytes, &record)
	if err != nil {
		return nil, fmt.Errorf("解析leader配置失败: %w", err)
	}

	now := time.Now()
	expiredAt := record.RenewTime.Add(time.Duration(record.LeaseDurationSeconds) * time.Second)
	diff := now.Sub(expiredAt).Round(time.Second)
	log.FromContext(ctx).Info("checkLeader", "expiredAt", expiredAt, "now", now, "diff", diff.String())
	return &LeaderStatus{
		Exists:          diff <= 0,
		Identity:        record.HolderIdentity,
		ExpiredAt:       expiredAt,
		RenewedAt:       record.RenewTime.Time,
		ExpiredDuration: lo.Ternary(diff > 0, diff, 0),
	}, nil
}

func GetComponentLeader(clusterType string, image string, component string) string {
	switch component {
	case "Service":
		return GetServiceLeader(clusterType, image)
	case "Ingress":
		return "qcloud-ingress-controller"
	default:
		return fmt.Sprintf("unknown component: %s", component) // 处理未知组件
	}
}

func GetServiceLeader(clusterType, image string) string {
	v := image[strings.LastIndex(image, ":")+1:]
	if clusterType == "eks" && version2.CheckOrDie(v, "<=v2.3.0") {
		return "tke-eks-vpcgw-controller-group-leader"
	}

	return "qcloud-service-controller"
}

type ReleaseAuditServiceTool struct {
	Update func(record *model.ReleaseAudit) error
	Last   func(clusterID string, item *model.ReleaseAudit) error
	Select func(clusterID string, records *[]model.ReleaseAudit) error
}

var ReleaseAuditService ReleaseAuditServiceTool

// isErrorMessage 判断字符串是否包含错误的标识
func IsErrorMessage(err error) bool {
	// 使用 strings.Split 切割字符串
	parts := strings.Split(err.Error(), " ")

	// 检查切割后的部分是否包含 "Error"
	for _, part := range parts {
		if part == "Error" {
			return true
		}
	}
	return false
}

func JoinKeyStrings(separator string, parts ...string) string {
	return strings.Join(parts, separator)
}

// ExtractStringKeys 从给定的 map 中提取键（仅限 string 类型）并返回一个切片
func ExtractStringKeys[T any](m map[string]T) []string {
	keys := make([]string, 0, len(m)) // 预分配切片的容量
	for key := range m {
		keys = append(keys, key) // 直接将键添加到切片中
	}
	return keys
}

type InspectionTaskResult struct {
	TaskID       *string
	Type         string
	Target       string
	MaxSyncTimes int
	SyncInterval string
	Status       string
	Reason       *string
}

// Generic function to make HTTP requests
func DoRequest[T any](ctx context.Context, method, url string, body interface{}) (*T, error) {
	client := &http.Client{}

	var reqBody io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("error marshalling request body: %w", err)
		}
		reqBody = bytes.NewBuffer(jsonBody)
	}

	// Create request with the specified method
	req, err := http.NewRequestWithContext(ctx, method, url, reqBody)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	// Send the request
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body) // Ignore error
		return nil, fmt.Errorf("error: received status code %d, response: %s", resp.StatusCode, string(body))
	}

	// Read and parse the response body
	var result T
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("error unmarshalling response: %w", err)
	}

	return &result, nil
}

func UpdateClusterTask(ctx context.Context, taskID string, maxSyncTimes int, syncInterval time.Duration) (*model.TaskResult, error) {
	baseURL := "http://kateway.woa.com/inspection/cluster/task/update"
	params := url.Values{}
	params.Add("taskID", taskID)
	params.Add("maxSyncTimes", strconv.Itoa(maxSyncTimes))
	params.Add("syncInterval", syncInterval.String())
	requestURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())

	// 使用 DoRequest 函数
	result, err := DoRequest[model.TaskResult](ctx, http.MethodGet, requestURL, nil)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// ListClusterTasks 列出集群任务
func ListClusterTasks(ctx context.Context, clusterID, state string) (*model.TaskReport, error) {
	baseURL := "http://kateway.woa.com/inspection/task/report/get"
	params := url.Values{}
	params.Add("clusterID", clusterID)
	params.Add("state", state)
	params.Add("raw", "true")
	requestURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())

	// 使用 DoRequest 函数
	result, err := DoRequest[model.TaskReport](ctx, http.MethodGet, requestURL, nil)
	if err != nil {
		return nil, fmt.Errorf("error making request: %w", err)
	}

	return result, nil
}

func CreateInspectionTask(ctx context.Context, instanceID string, maxSyncTimes int, syncInterval time.Duration) (*model.TaskResult, error) {
	// 构建请求的 URL
	baseURL := "http://kateway.woa.com/inspection/cluster/task/run"
	params := url.Values{}
	params.Add("clusterID", instanceID)
	params.Add("maxSyncTimes", strconv.Itoa(maxSyncTimes))
	params.Add("syncInterval", syncInterval.String())
	requestURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())

	// 使用 DoRequest 函数
	result, err := DoRequest[model.TaskResult](ctx, http.MethodGet, requestURL, nil)
	if err != nil {
		return nil, fmt.Errorf("error making request: %w", err)
	}

	return result, nil
}

func GetClusterTaskByTaskID(ctx context.Context, taskID string) (*model.InspectionTask, error) {
	// 构建请求的 URL
	baseURL := "http://kateway.woa.com/inspection/cluster/task/get"
	params := url.Values{}
	params.Add("taskID", taskID)
	// 完整的请求 URL
	requestURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())

	// 使用 DoRequest 函数
	result, err := DoRequest[model.InspectionTask](ctx, http.MethodGet, requestURL, nil)
	if err != nil {
		return nil, fmt.Errorf("error making request: %w", err)
	}
	// 返回结果
	return result, nil
}

// WaitForTaskSync 等待任务至少同步一次
func WaitForTaskSyncOnce(ctx context.Context, taskID string, checkInterval time.Duration) error {
	ticker := time.NewTicker(checkInterval)
	defer ticker.Stop() // 确保在函数退出时停止 ticker

	for {
		select {
		case <-ctx.Done():
			return ctx.Err() // 如果上下文被取消，返回错误
		case <-ticker.C:
			task, err := GetClusterTaskByTaskID(ctx, taskID)
			if err != nil {
				return fmt.Errorf("failed to get task status: %w", err)
			}
			// 检查同步次数
			if task.CurSyncTimes >= 1 {
				fmt.Printf("Task %s synced at least once\n", taskID)
				return nil // 如果同步次数大于或等于 1，返回成功
			}
		}
	}
}

func StartOneCheckBeforeRelease(ctx context.Context, clusterID string) error {
	klog.Infof("StartOneCheckBeforeRelease: clusterID: %s", clusterID)
	maxSyncTimes := 1
	syncInterval := 10 * time.Minute

	// Get当前集群的健康检查任务
	runningTasksList, err := ListClusterTasks(ctx, clusterID, model.TaskStateRunning.String())
	if err != nil {
		return err
	}
	var taskID string
	if len(runningTasksList.TaskList) == 0 {
		// 创建健康检查任务（如果已存在，且至少sync一次直接跳过这一步，因为已经存在变更前的健康检查数据）
		task, err := CreateInspectionTask(ctx, clusterID, maxSyncTimes, syncInterval)
		if err != nil {
			return err
		}
		if task.Status != "Success" {
			return fmt.Errorf("create health check task failed: %w", task.Reason)
		}
		taskID = *task.TaskID
	} else {
		// task 已经存在，直接获取
		taskID = runningTasksList.TaskList[0].TaskID
	}

	// 设置超时和查询间隔
	timeoutCtx, cancel := context.WithTimeout(ctx, 8*time.Minute)
	defer cancel()

	// 通过 taskID 反复查询任务状态，确保在 SetImage 之前任务已经至少 sync 一次
	if err := WaitForTaskSyncOnce(timeoutCtx, taskID, 10*time.Second); err != nil {
		return err
	}
	return nil
}

func StartInspectionTaskAfterRelease(ctx context.Context, clusterID string, interval time.Duration, times int) (*string, error) {
	klog.Infof("StartInspectionTaskAfterRelease: clusterID: %s", clusterID)
	timeoutCtx, cancel := context.WithTimeout(ctx, 1*time.Minute)
	defer cancel()
	// 再次开始巡检或者对已有巡检任务考虑续期
	// 查看是否已经有巡检任务
	runningTasksList, err := ListClusterTasks(ctx, clusterID, model.TaskStateRunning.String())
	if err != nil {
		return nil, err
	}
	runningTasks := runningTasksList.TaskList
	switch len(runningTasks) {
	case 0:
		// 巡检任务不存在，创建巡检任务
		if newTask, err := CreateInspectionTask(timeoutCtx, clusterID, times, interval); err != nil {
			// API调用失败
			return newTask.TaskID, fmt.Errorf("cluster: %s List Running Task Error. %s", clusterID, err.Error())
		} else if newTask.Status != "Success" {
			// API接口返回错误
			return nil, fmt.Errorf("cluster: %s Create New Task Error. %w", clusterID, newTask.Reason)
		} else {
			// 创建成功
			return newTask.TaskID, nil
		}
	case 1:
		runningTask := runningTasks[0]
		// 判断是否续期： maxSyncTimes += curSyncTimes
		if runningTask.CurSyncTimes > 0 {
			// 续期任务
			if updatedTask, err := UpdateClusterTask(timeoutCtx, runningTask.TaskID, max(runningTask.MaxSyncTimes, times+runningTask.CurSyncTimes), interval); err != nil {
				return nil, fmt.Errorf("cluster: %s Update Running Task Error. %s", clusterID, err.Error())
			} else if updatedTask.Status != "Success" {
				return nil, fmt.Errorf("cluster: %s Update Running Task Error. %w", clusterID, updatedTask.Reason)
			} else {
				return updatedTask.TaskID, nil
			}
		}
	default:
		// 正在运行的巡检任务存在多个，直接返回错误(通常情况下不会出现，后端会校验限制)
		return nil, fmt.Errorf("cluster: %s List Running Tasks more than one", clusterID)
	}
	return nil, nil
}
