package common

import (
	"testing"

	v12 "k8s.io/api/apps/v1"
	v1 "k8s.io/api/core/v1"
)

const (
	INGRESS_DEPLOYMENT_NAME = "l7-lb-controller"
	INGRESS_CONTAINER_NAME  = "l7-lb-controller"
)

func TestGetTagFromImage(t *testing.T) {
	tests := []struct {
		image    string
		expected []int
		err      bool
	}{
		{"nginx:v", nil, true},
		{"nginx:v1", []int{1, 0, 0}, false},
		{"nginx:v1.2.3", []int{1, 2, 3}, false},
		{"nginx:v1.2", []int{1, 2, 0}, false},
		{"nginx:v0", []int{0, 0, 0}, false},
		{"nginx:1.2.3", []int{1, 2, 3}, false},
		{"nginx:v1.2.3-alpha", []int{1, 2, 3}, false},
		{"nginx:v1.2.3.4", []int{1, 2, 3}, false},
		{"nginx:v1.2.3.4-beta", []int{1, 2, 3}, false},
		{"nginx:v1.2.3.4-rc", []int{1, 2, 3}, false},
		{"nginx:v1.2.3.4-rc1", []int{1, 2, 3}, false},
		{"nginx:v1.2.3.4.5", []int{1, 2, 3}, false},
		{"nginx:v1.2.3.4.5.6", []int{1, 2, 3}, false},
		{"nginx:v1.2.3.4.5.6-rc", []int{1, 2, 3}, false},
		{"nginx:v1.2.3.4.5.6-rc1", []int{1, 2, 3}, false},
		{"nginx:v1.2.3.4.5.6-rc1.2", []int{1, 2, 3}, false},
		{"nginx:v1.2.3.4.5.6-rc1.2.3", []int{1, 2, 3}, false},
		{"nginx:v1.2.3.4.5.6-rc.3.4", []int{1, 2, 3}, false},
		{"nginx:v1.2.3.4.5.6-rc.2.3.4.5", []int{1, 2, 3}, false},
	}

	for _, test := range tests {
		result, err := GetTagFromImage(test.image)
		if (err != nil) != test.err {
			t.Errorf("GetTagFromImage(%s) error = %v, expected error = %v", test.image, err, test.err)
		}
		if !test.err && !equal(result, test.expected) {
			t.Errorf("GetTagFromImage(%s) = %v, expected %v", test.image, result, test.expected)
		}
	}
}

func TestCheckImageIfCrossVersion(t *testing.T) {
	tests := []struct {
		image  string
		deploy *v12.Deployment
		cross  bool
		err    bool
	}{
		{
			"nginx:v1.2.3",
			&v12.Deployment{
				Spec: v12.DeploymentSpec{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{
								{Name: INGRESS_CONTAINER_NAME, Image: "nginx:v1.2.2"},
							},
						},
					},
				},
			},
			false,
			false,
		},
		{
			"nginx:v2.0.0",
			&v12.Deployment{
				Spec: v12.DeploymentSpec{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{
								{Name: INGRESS_CONTAINER_NAME, Image: "nginx:v1.8.0"},
							},
						},
					},
				},
			},
			false,
			false,
		},
		{
			"nginx:v1.9.1-7",
			&v12.Deployment{
				Spec: v12.DeploymentSpec{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{
								{Name: INGRESS_CONTAINER_NAME, Image: "nginx:v1.8.0-6"},
							},
						},
					},
				},
			},
			false,
			false,
		},
		{
			"nginx:v2.0.1-7",
			&v12.Deployment{
				Spec: v12.DeploymentSpec{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{
								{Name: INGRESS_CONTAINER_NAME, Image: "nginx:v1.8.0-6"},
							},
						},
					},
				},
			},
			false,
			false,
		},
		{
			"nginx:v1.3.0",
			&v12.Deployment{
				Spec: v12.DeploymentSpec{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{
								{Name: INGRESS_CONTAINER_NAME, Image: "nginx:v1.1.0"},
							},
						},
					},
				},
			},
			true,
			true,
		},
		{
			"nginx:v2.5.0",
			&v12.Deployment{
				Spec: v12.DeploymentSpec{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{
								{Name: INGRESS_CONTAINER_NAME, Image: "nginx:v2.4.2"},
							},
						},
					},
				},
			},
			false,
			false,
		},
		{
			"nginx:v2.5",
			&v12.Deployment{
				Spec: v12.DeploymentSpec{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{
								{Name: INGRESS_CONTAINER_NAME, Image: "nginx:v2.4.2"},
							},
						},
					},
				},
			},
			false,
			false,
		},
		{
			"nginx:v2.5.0",
			&v12.Deployment{
				Spec: v12.DeploymentSpec{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{
								{Name: INGRESS_CONTAINER_NAME, Image: "nginx:v2.4"},
							},
						},
					},
				},
			},
			false,
			false,
		},
		{
			"nginx:v2.5.0-7",
			&v12.Deployment{
				Spec: v12.DeploymentSpec{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{
								{Name: INGRESS_CONTAINER_NAME, Image: "nginx:v2.4.3-4"},
							},
						},
					},
				},
			},
			false,
			false,
		},
		{
			"nginx:v2.5",
			&v12.Deployment{
				Spec: v12.DeploymentSpec{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{
								{Name: "service-controller", Image: "nginx:v2.4.2"},
							},
						},
					},
				},
			},
			false,
			false,
		},
		{
			"nginx:v2.5",
			&v12.Deployment{
				Spec: v12.DeploymentSpec{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{
								{Name: "service-controller", Image: "nginx:v2.4.2.14"},
							},
						},
					},
				},
			},
			false,
			false,
		},
		{
			"nginx:v2.5.2.67",
			&v12.Deployment{
				Spec: v12.DeploymentSpec{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{
								{Name: "service-controller", Image: "nginx:v2.4.2.14"},
							},
						},
					},
				},
			},
			false,
			false,
		},
	}

	for _, test := range tests {
		cross, err := CheckImageIfCrossVersion(test.image, test.deploy)
		if (err != nil) != test.err {
			t.Errorf("checkImageIfCrossVersion(%s, %v) error = %v, expected error = %v", test.image, test.deploy, err, test.err)
		}
		if cross != test.cross {
			t.Errorf("checkImageIfCrossVersion(%s, %v) = %v, expected %v", test.image, test.deploy, cross, test.cross)
		}
	}
}

func TestIsCrossVersion(t *testing.T) {
	tests := []struct {
		curTagSlice []int
		tarTagSlice []int
		expected    bool
	}{
		{[]int{1, 2, 3}, []int{1, 3, 0}, false},
		{[]int{1, 2, 3}, []int{2, 0, 0}, true},
		{[]int{1, 8, 0}, []int{2, 0, 0}, false},
		{[]int{1, 7, 0}, []int{2, 0, 0}, true},
		{[]int{1, 8, 0}, []int{1, 9, 0}, false},
		{[]int{1, 8, 0}, []int{1, 8, 1}, false},
		{[]int{2, 0, 0}, []int{2, 1, 0}, false},
		{[]int{2, 0, 0}, []int{3, 0, 0}, true},
		{[]int{1, 0, 0}, []int{3, 0, 0}, true},
	}

	for _, test := range tests {
		result := IsCrossVersion(test.curTagSlice, test.tarTagSlice)
		if result != test.expected {
			t.Errorf("isCrossVersion(%v, %v) = %v, expected %v", test.curTagSlice, test.tarTagSlice, result, test.expected)
		}
	}
}

// Helper function to compare two slices of integers
func equal(a, b []int) bool {
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}
