package ingress_wrapper

import (
	"context"

	go_version "github.com/hashicorp/go-version"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/client-go/kubernetes"

	"git.woa.com/kateway/pkg/telemetry/jaeger"

	ingress_wrapper2 "git.woa.com/kateway/kateway-server/pkg/ingress_wrapper"
)

type IngressWrapper interface {
	Namespace() string
	Name() string
	UID() string
	IngressClassName() *string
	Annotations() map[string]string
	RuntimeObject() runtime.Object
	ObjectReference() v1.ObjectReference
	TLS() []IngressTLSWrapper
	Rules() []IngressRuleWrapper
	StatusLoadBalancer() v1.LoadBalancerStatus
	DeletionTimestamp() *metav1.Time

	UpdateAnnotation(kubeClient *kubernetes.Clientset, update map[string]string, delete []string) (IngressWrapper, error)
	UpdateLoadBalancerStatus(kubeClient *kubernetes.Clientset, loadBalancerStatus v1.LoadBalancerStatus) error
	AddFinalizer(kubeClient *kubernetes.Clientset, finalizer string) error
	RemoveFinalizer(kubeClient *kubernetes.Clientset, finalizer string) error
}

type IngressTLSWrapper interface {
	Hosts() []string
	SecretName() string
}

type IngressRuleWrapper interface {
	Host() string
	HTTPPaths() []HTTPIngressPathWrapper
}

type HTTPIngressPathWrapper interface {
	Path() string
	PathType() *string
	Backend() IngressBackendWrapper
}

type IngressBackendWrapper interface {
	ServiceName() string
	ServicePort() intstr.IntOrString
}

func IngressGetWrapper(ctx context.Context, client kubernetes.Interface, namespace string, name string) (ingress_wrapper2.IngressWrapper, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	semverBase, _ := go_version.NewSemver("v1.22") // 直绑服务的基本版本要求
	serverVersion, err := client.Discovery().ServerVersion()
	if err != nil {
		return nil, err
	}
	serverSemver, err := go_version.NewSemver(serverVersion.GitVersion)
	if err != nil {
		return nil, err
	}

	if serverSemver.Compare(semverBase) < 0 {
		ingress, err := client.ExtensionsV1beta1().Ingresses(namespace).Get(ctx, name, metav1.GetOptions{})
		if err != nil {
			return nil, err
		}
		return ingress_wrapper2.NewIngressWrapperExtensions(ingress), nil
	} else {
		ingress, err := client.NetworkingV1().Ingresses(namespace).Get(ctx, name, metav1.GetOptions{})
		if err != nil {
			return nil, err
		}
		return ingress_wrapper2.NewIngressWrapperNetworking(ingress), nil
	}
}
