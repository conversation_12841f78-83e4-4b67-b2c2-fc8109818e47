package ingress_wrapper

import (
	"context"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	v1 "k8s.io/api/core/v1"
	extensions "k8s.io/api/extensions/v1beta1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/client-go/kubernetes"
)

type IngressWrapperExtensions struct {
	extensionsIngress *extensions.Ingress
}

type IngressTLSWrapperExtensions struct {
	extensionsIngress *extensions.IngressTLS
}

type IngressRuleWrapperExtensions struct {
	extensionsIngress *extensions.IngressRule
}

type HTTPIngressPathWrapperExtensions struct {
	extensionsIngress *extensions.HTTPIngressPath
}

type IngressBackendWrapperExtensions struct {
	extensionsIngress *extensions.IngressBackend
}

func NewIngressWrapperExtensions(ingress *extensions.Ingress) IngressWrapper {
	return &IngressWrapperExtensions{
		extensionsIngress: ingress,
	}
}

func NewIngressWrapperExtensionsList(ingresses []*extensions.Ingress) []IngressWrapper {
	ingressWrapperExtensionss := make([]IngressWrapper, len(ingresses))
	for index, ingress := range ingresses {
		ingressWrapperExtensionss[index] = NewIngressWrapperExtensions(ingress)
	}
	return ingressWrapperExtensionss
}

func (this *IngressWrapperExtensions) UpdateAnnotation(kubeClient *kubernetes.Clientset, update map[string]string, deleteAnnotation []string) (IngressWrapper, error) {
	currentIngress, err := kubeClient.ExtensionsV1beta1().Ingresses(this.Namespace()).Get(context.Background(), this.Name(), metav1.GetOptions{})
	if err != nil {
		return NewIngressWrapperExtensions(currentIngress), err
	}
	if currentIngress.Annotations == nil {
		currentIngress.Annotations = make(map[string]string, 0)
	}
	isUpdated := false
	if update != nil && len(update) != 0 {
		for key, value := range update {
			if oldvalue, exist := currentIngress.Annotations[key]; !exist || oldvalue != value {
				currentIngress.Annotations[key] = value
				isUpdated = true
			}
		}
	}
	if deleteAnnotation != nil && len(deleteAnnotation) != 0 {
		for _, key := range deleteAnnotation {
			if _, exist := currentIngress.Annotations[key]; exist {
				delete(currentIngress.Annotations, key)
				isUpdated = true
			}
		}
	}
	if isUpdated {
		updatedIngress, err := kubeClient.ExtensionsV1beta1().Ingresses(this.Namespace()).Update(context.Background(), currentIngress, metav1.UpdateOptions{})
		if err != nil {
			return NewIngressWrapperExtensions(currentIngress), err
		}
		return NewIngressWrapperExtensions(updatedIngress), nil
	}
	return NewIngressWrapperExtensions(currentIngress), nil
}

func (this *IngressWrapperExtensions) UpdateLoadBalancerStatus(kubeClient *kubernetes.Clientset, loadBalancerStatus v1.LoadBalancerStatus) error {
	currentIngress, err := kubeClient.ExtensionsV1beta1().Ingresses(this.Namespace()).Get(context.Background(), this.Name(), metav1.GetOptions{})
	if err != nil {
		return err
	}
	currentIngress.Status = extensions.IngressStatus{
		LoadBalancer: loadBalancerStatus,
	}
	if _, err := kubeClient.ExtensionsV1beta1().Ingresses(this.Namespace()).UpdateStatus(context.Background(), currentIngress, metav1.UpdateOptions{}); err != nil {
		return err
	}
	return nil
}

func (this *IngressWrapperExtensions) HasFinalizer(kubeClient *kubernetes.Clientset, finalizer string) (bool, error) {
	currentIngress, err := kubeClient.ExtensionsV1beta1().Ingresses(this.Namespace()).Get(context.Background(), this.Name(), metav1.GetOptions{})
	if err != nil {
		return false, err
	}
	for _, f := range currentIngress.Finalizers {
		if f == finalizer {
			return true, nil
		}
	}
	return false, nil
}

func (this *IngressWrapperExtensions) AddFinalizer(kubeClient *kubernetes.Clientset, finalizer string) error {
	currentIngress, err := kubeClient.ExtensionsV1beta1().Ingresses(this.Namespace()).Get(context.Background(), this.Name(), metav1.GetOptions{})
	if err != nil {
		return err
	}
	hasFinalizer, err := this.HasFinalizer(kubeClient, finalizer)
	if err != nil {
		return err
	}
	if !hasFinalizer {
		currentIngress.Finalizers = append(currentIngress.Finalizers, finalizer)
		if _, err = kubeClient.ExtensionsV1beta1().Ingresses(this.Namespace()).Update(context.Background(), currentIngress, metav1.UpdateOptions{}); err != nil {
			return err
		}
	}
	return nil
}

func (this *IngressWrapperExtensions) RemoveFinalizer(kubeClient *kubernetes.Clientset, finalizer string) error {
	currentIngress, err := kubeClient.ExtensionsV1beta1().Ingresses(this.Namespace()).Get(context.Background(), this.Name(), metav1.GetOptions{})
	if err != nil {
		return err
	}
	needsUpdate := false
	for i := 0; i < len(currentIngress.Finalizers); i++ {
		if currentIngress.Finalizers[i] == finalizer {
			currentIngress.Finalizers = append(currentIngress.Finalizers[:i], currentIngress.Finalizers[i+1:]...)
			i--
			needsUpdate = true
		}
	}
	if needsUpdate {
		if _, err = kubeClient.ExtensionsV1beta1().Ingresses(this.Namespace()).Update(context.Background(), currentIngress, metav1.UpdateOptions{}); err != nil {
			return err
		}
	}
	return nil
}

func (this *IngressWrapperExtensions) RuntimeObject() runtime.Object {
	return this.extensionsIngress
}

func (this *IngressWrapperExtensions) ObjectReference() v1.ObjectReference {
	return v1.ObjectReference{
		Kind:            "Ingress",
		APIVersion:      extensions.SchemeGroupVersion.String(),
		Namespace:       this.extensionsIngress.Namespace,
		Name:            this.extensionsIngress.Name,
		ResourceVersion: this.extensionsIngress.ResourceVersion,
		UID:             this.extensionsIngress.UID,
	}
}

func (this *IngressWrapperExtensions) Namespace() string {
	return this.extensionsIngress.Namespace
}

func (this *IngressWrapperExtensions) Name() string {
	return this.extensionsIngress.Name
}

func (this *IngressWrapperExtensions) Annotations() map[string]string {
	return this.extensionsIngress.Annotations
}

func (this *IngressWrapperExtensions) IngressClassName() *string {
	return this.extensionsIngress.Spec.IngressClassName
}

func (this *IngressWrapperExtensions) TLS() []IngressTLSWrapper {
	ingressTLSWrapperExtensions := make([]IngressTLSWrapper, len(this.extensionsIngress.Spec.TLS))
	for index, _ := range this.extensionsIngress.Spec.TLS {
		ingressTLSWrapperExtensions[index] = &IngressTLSWrapperExtensions{
			extensionsIngress: &this.extensionsIngress.Spec.TLS[index],
		}
	}
	return ingressTLSWrapperExtensions
}

func (this *IngressTLSWrapperExtensions) Hosts() []string {
	return this.extensionsIngress.Hosts
}

func (this *IngressTLSWrapperExtensions) SecretName() string {
	return this.extensionsIngress.SecretName
}

func (this *IngressWrapperExtensions) Rules() []IngressRuleWrapper {
	ingressRuleWrapperExtensionss := make([]IngressRuleWrapper, len(this.extensionsIngress.Spec.Rules))
	for index, _ := range this.extensionsIngress.Spec.Rules {
		ingressRuleWrapperExtensionss[index] = &IngressRuleWrapperExtensions{
			extensionsIngress: &this.extensionsIngress.Spec.Rules[index],
		}
	}
	return ingressRuleWrapperExtensionss
}

func (this *IngressRuleWrapperExtensions) Host() string {
	return this.extensionsIngress.Host
}

func (this *IngressRuleWrapperExtensions) HTTPPaths() []HTTPIngressPathWrapper {
	httpIngressPathWrapperExtensionss := make([]HTTPIngressPathWrapper, 0)
	if this.extensionsIngress.HTTP == nil {
		return httpIngressPathWrapperExtensionss
	}
	httpIngressPathWrapperExtensionss = make([]HTTPIngressPathWrapper, len(this.extensionsIngress.HTTP.Paths))
	for index, _ := range this.extensionsIngress.HTTP.Paths {
		httpIngressPathWrapperExtensionss[index] = &HTTPIngressPathWrapperExtensions{
			extensionsIngress: &this.extensionsIngress.HTTP.Paths[index],
		}
	}
	return httpIngressPathWrapperExtensionss
}

func (this *HTTPIngressPathWrapperExtensions) Path() string {
	return this.extensionsIngress.Path
}

func (this *HTTPIngressPathWrapperExtensions) PathType() *string {
	if this.extensionsIngress.PathType == nil {
		return nil
	}
	return common.StringPtr(string(*this.extensionsIngress.PathType))
}

func (this *HTTPIngressPathWrapperExtensions) Backend() IngressBackendWrapper {
	return &IngressBackendWrapperExtensions{
		extensionsIngress: &this.extensionsIngress.Backend,
	}
}

func (this *IngressBackendWrapperExtensions) ServiceName() string {
	return this.extensionsIngress.ServiceName
}

func (this *IngressBackendWrapperExtensions) ServicePort() intstr.IntOrString {
	return this.extensionsIngress.ServicePort
}

func (this *IngressWrapperExtensions) StatusLoadBalancer() v1.LoadBalancerStatus {
	return this.extensionsIngress.Status.LoadBalancer
}

func (this *IngressWrapperExtensions) UID() string {
	return string(this.extensionsIngress.UID)
}

func (this *IngressWrapperExtensions) DeletionTimestamp() *metav1.Time {
	return this.extensionsIngress.DeletionTimestamp
}
