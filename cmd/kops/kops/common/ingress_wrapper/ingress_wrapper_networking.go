package ingress_wrapper

import (
	"context"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	v1 "k8s.io/api/core/v1"
	networking "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/client-go/kubernetes"
)

type IngressWrapperNetworking struct {
	networkingIngress *networking.Ingress
}

type IngressTLSWrapperNetworking struct {
	networkingIngress *networking.IngressTLS
}

type IngressRuleWrapperNetworking struct {
	networkingIngress *networking.IngressRule
}

type HTTPIngressPathWrapperNetworking struct {
	networkingIngress *networking.HTTPIngressPath
}

type IngressBackendWrapperNetworking struct {
	networkingIngress *networking.IngressBackend
}

func NewIngressWrapperNetworking(ingress *networking.Ingress) IngressWrapper {
	return &IngressWrapperNetworking{
		networkingIngress: ingress,
	}
}

func NewIngressWrapperNetworkingList(ingresses []*networking.Ingress) []IngressWrapper {
	ingressWrapperNetworkings := make([]IngressWrapper, len(ingresses))
	for index, ingress := range ingresses {
		ingressWrapperNetworkings[index] = NewIngressWrapperNetworking(ingress)
	}
	return ingressWrapperNetworkings
}

func (this *IngressWrapperNetworking) RuntimeObject() runtime.Object {
	return this.networkingIngress
}

func (this *IngressWrapperNetworking) ObjectReference() v1.ObjectReference {
	return v1.ObjectReference{
		Kind:            "Ingress",
		APIVersion:      networking.SchemeGroupVersion.String(),
		Namespace:       this.networkingIngress.Namespace,
		Name:            this.networkingIngress.Name,
		ResourceVersion: this.networkingIngress.ResourceVersion,
		UID:             this.networkingIngress.UID,
	}
}

func (this *IngressWrapperNetworking) Namespace() string {
	return this.networkingIngress.Namespace
}

func (this *IngressWrapperNetworking) Name() string {
	return this.networkingIngress.Name
}

func (this *IngressWrapperNetworking) Annotations() map[string]string {
	return this.networkingIngress.Annotations
}

func (this *IngressWrapperNetworking) IngressClassName() *string {
	return this.networkingIngress.Spec.IngressClassName
}

func (this *IngressWrapperNetworking) TLS() []IngressTLSWrapper {
	ingressTLSWrapperNetworking := make([]IngressTLSWrapper, len(this.networkingIngress.Spec.TLS))
	for index, _ := range this.networkingIngress.Spec.TLS {
		ingressTLSWrapperNetworking[index] = &IngressTLSWrapperNetworking{
			networkingIngress: &this.networkingIngress.Spec.TLS[index],
		}
	}
	return ingressTLSWrapperNetworking
}

func (this *IngressWrapperNetworking) StatusLoadBalancer() v1.LoadBalancerStatus {
	return this.networkingIngress.Status.LoadBalancer
}

func (this *IngressWrapperNetworking) UID() string {
	return string(this.networkingIngress.UID)
}

func (this *IngressWrapperNetworking) UpdateAnnotation(kubeClient *kubernetes.Clientset, update map[string]string, deleteAnnotation []string) (IngressWrapper, error) {
	currIng, err := kubeClient.NetworkingV1().Ingresses(this.Namespace()).Get(context.Background(), this.Name(), metav1.GetOptions{})
	if err != nil {
		return NewIngressWrapperNetworking(currIng), err
	}
	if currIng.Annotations == nil {
		currIng.Annotations = make(map[string]string, 0)
	}
	isUpdated := false
	if update != nil && len(update) != 0 {
		for key, value := range update {
			if oldvalue, exist := currIng.Annotations[key]; !exist || oldvalue != value {
				currIng.Annotations[key] = value
				isUpdated = true
			}
		}
	}
	if deleteAnnotation != nil && len(deleteAnnotation) != 0 {
		for _, key := range deleteAnnotation {
			if _, exist := currIng.Annotations[key]; exist {
				delete(currIng.Annotations, key)
				isUpdated = true
			}
		}
	}
	if isUpdated {
		updatedIngress, err := kubeClient.NetworkingV1().Ingresses(this.Namespace()).Update(context.Background(), currIng, metav1.UpdateOptions{})
		if err != nil {
			return NewIngressWrapperNetworking(currIng), err
		}
		return NewIngressWrapperNetworking(updatedIngress), err
	}
	return NewIngressWrapperNetworking(currIng), nil
}

func (this *IngressWrapperNetworking) UpdateLoadBalancerStatus(kubeClient *kubernetes.Clientset, loadBalancerStatus v1.LoadBalancerStatus) error {
	currentIngress, err := kubeClient.NetworkingV1().Ingresses(this.Namespace()).Get(context.Background(), this.Name(), metav1.GetOptions{})
	if err != nil {
		return err
	}
	currentIngress.Status = networking.IngressStatus{
		LoadBalancer: loadBalancerStatus,
	}
	if _, err := kubeClient.NetworkingV1().Ingresses(this.Namespace()).UpdateStatus(context.Background(), currentIngress, metav1.UpdateOptions{}); err != nil {
		return err
	}
	return nil
}

func (this *IngressWrapperNetworking) HasFinalizer(kubeClient *kubernetes.Clientset, finalizer string) (bool, error) {
	currentIngress, err := kubeClient.NetworkingV1().Ingresses(this.Namespace()).Get(context.Background(), this.Name(), metav1.GetOptions{})
	if err != nil {
		return false, err
	}
	for _, f := range currentIngress.Finalizers {
		if f == finalizer {
			return true, nil
		}
	}
	return false, nil
}

func (this *IngressWrapperNetworking) AddFinalizer(kubeClient *kubernetes.Clientset, finalizer string) error {
	currentIngress, err := kubeClient.NetworkingV1().Ingresses(this.Namespace()).Get(context.Background(), this.Name(), metav1.GetOptions{})
	if err != nil {
		return err
	}
	hasFinalizer, err := this.HasFinalizer(kubeClient, finalizer)
	if err != nil {
		return err
	}
	if !hasFinalizer {
		currentIngress.Finalizers = append(currentIngress.Finalizers, finalizer)
		if _, err = kubeClient.NetworkingV1().Ingresses(this.Namespace()).Update(context.Background(), currentIngress, metav1.UpdateOptions{}); err != nil {
			return err
		}
	}
	return nil
}

func (this *IngressWrapperNetworking) RemoveFinalizer(kubeClient *kubernetes.Clientset, finalizer string) error {
	currentIngress, err := kubeClient.NetworkingV1().Ingresses(this.Namespace()).Get(context.Background(), this.Name(), metav1.GetOptions{})
	if err != nil {
		return err
	}
	needsUpdate := false
	for i := 0; i < len(currentIngress.Finalizers); i++ {
		if currentIngress.Finalizers[i] == finalizer {
			currentIngress.Finalizers = append(currentIngress.Finalizers[:i], currentIngress.Finalizers[i+1:]...)
			i--
			needsUpdate = true
		}
	}
	if needsUpdate {
		if _, err = kubeClient.NetworkingV1().Ingresses(this.Namespace()).Update(context.Background(), currentIngress, metav1.UpdateOptions{}); err != nil {
			return err
		}
	}
	return nil
}

func (this *IngressTLSWrapperNetworking) Hosts() []string {
	return this.networkingIngress.Hosts
}

func (this *IngressTLSWrapperNetworking) SecretName() string {
	return this.networkingIngress.SecretName
}

func (this *IngressWrapperNetworking) Rules() []IngressRuleWrapper {
	ingressRuleWrapperNetworkings := make([]IngressRuleWrapper, len(this.networkingIngress.Spec.Rules))
	for index, _ := range this.networkingIngress.Spec.Rules {
		ingressRuleWrapperNetworkings[index] = &IngressRuleWrapperNetworking{
			networkingIngress: &this.networkingIngress.Spec.Rules[index],
		}
	}
	return ingressRuleWrapperNetworkings
}

func (this *IngressWrapperNetworking) DeletionTimestamp() *metav1.Time {
	return this.networkingIngress.DeletionTimestamp
}

func (this *IngressRuleWrapperNetworking) Host() string {
	return this.networkingIngress.Host
}

func (this *IngressRuleWrapperNetworking) HTTPPaths() []HTTPIngressPathWrapper {
	httpIngressPathWrapperNetworkings := make([]HTTPIngressPathWrapper, 0)
	if this.networkingIngress.HTTP == nil {
		return httpIngressPathWrapperNetworkings
	}
	httpIngressPathWrapperNetworkings = make([]HTTPIngressPathWrapper, len(this.networkingIngress.HTTP.Paths))
	for index, _ := range this.networkingIngress.HTTP.Paths {
		httpIngressPathWrapperNetworkings[index] = &HTTPIngressPathWrapperNetworking{
			networkingIngress: &this.networkingIngress.HTTP.Paths[index],
		}
	}
	return httpIngressPathWrapperNetworkings
}

func (this *HTTPIngressPathWrapperNetworking) Path() string {
	return this.networkingIngress.Path
}

func (this *HTTPIngressPathWrapperNetworking) PathType() *string {
	if this.networkingIngress.PathType == nil {
		return nil
	}
	return common.StringPtr(string(*this.networkingIngress.PathType))
}

func (this *HTTPIngressPathWrapperNetworking) Backend() IngressBackendWrapper {
	return &IngressBackendWrapperNetworking{
		networkingIngress: &this.networkingIngress.Backend,
	}
}

func (this *IngressBackendWrapperNetworking) ServiceName() string {
	return this.networkingIngress.Service.Name
}

func (this *IngressBackendWrapperNetworking) ServicePort() intstr.IntOrString {
	if this.networkingIngress.Service.Port.Number != 0 {
		return intstr.FromInt(int(this.networkingIngress.Service.Port.Number))
	} else {
		return intstr.FromString(this.networkingIngress.Service.Port.Name)
	}
}
