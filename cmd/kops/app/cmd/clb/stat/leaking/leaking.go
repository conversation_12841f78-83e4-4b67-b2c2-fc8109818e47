package leaking

import (
	"time"

	"github.com/spf13/cobra"
	"k8s.io/klog/v2"
)

func New() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "leaking",
		Short: "统计clb实例泄漏",
		Long:  "统计clb实例泄漏",
		RunE: func(cmd *cobra.Command, args []string) error {
			defer klog.Flush()

			appid, _ := cmd.Flags().GetString("appid")
			clb := NewCLB(time.Now().Format("20060102"), appid)
			clb.Run()

			return nil
		},
	}

	cmd.Flags().String("appid", "", "appid")

	return cmd
}
