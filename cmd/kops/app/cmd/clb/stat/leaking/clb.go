package leaking

import (
	"context"
	"fmt"
	"strings"
	"sync/atomic"

	"github.com/go-logr/logr"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"golang.org/x/sync/errgroup"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"git.woa.com/kateway/pkg/telemetry/log"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
	"git.woa.com/kateway/kateway-server/pkg/tmp/web/cloudctx"
	region2 "git.woa.com/kateway/kateway-server/pkg/util/region"
)

type CLB struct {
	date    string
	appid   string
	log     logr.Logger
	counter atomic.Uint64
}

func NewCLB(date string, appid string) *CLB {
	return &CLB{
		date:  date,
		appid: appid,
		log:   log.WithName("CLB"),
	}
}

func (l *CLB) Run() {
	l.log.Info("Running")
	defer l.log.Info("Done")

	ctx := context.Background()
	// users, err := services.Get().User().ListUsers(ctx)
	// if err != nil {
	// 	l.log.Error(err, "Get().User().ListUsers error")
	// 	return
	// }
	users := []services.User{
		{
			Uin:   "2783211945",
			Appid: "1253687700",
		},
		{
			Uin:   "2025368773",
			Appid: "1258638997",
		},
	}
	var err error
	if l.appid != "" {
		users = lo.Filter(users, func(user services.User, _ int) bool {
			return user.Appid == l.appid
		})
	}
	l.log.Info("users", "users", users)

	wg := errgroup.Group{}
	wg.SetLimit(config.Get().Task.CLB.ConcurrentSyncs)

	for _, user := range users {
		wg.Go(func() (err error) {
			defer func() {
				if r := recover(); r != nil {
					err = fmt.Errorf("panic(%s): %v", user.Uin, r)
				}
			}()

			return l.do(ctx, user)
		})
	}

	err = wg.Wait()
	l.log.Error(err, "run error")
}

func (l *CLB) do(ctx context.Context, user services.User) error {
	wg := errgroup.Group{}
	for _, item := range config.Get().Regions {
		wg.Go(func() (err error) {
			defer func() {
				if r := recover(); r != nil {
					err = fmt.Errorf("panic(%s %s): %v", user.Uin, item, r)
				}
			}()
			return l.statCLBForRegion(ctx, user, item)
		})
	}

	return wg.Wait()
}

// [【Andon反馈】排查接入层创建的clb，历史上可能存在泄露的实例，周期性监控-容器接入层-TAPD平台](https://tapd.woa.com/tapd_fe/70108010/story/detail/1070108010119585585)
func (l *CLB) statCLBForRegion(ctx context.Context, user services.User, region string) error {
	l.log.Info("statCLBForRegion", "region", region, "appid", user.Appid)

	ctx = cloudctx.WithUin(ctx, user.Uin)
	ctx = cloudctx.WithRegion(ctx, region2.MustGet(region))
	// 筛选所有接入层自动创建的clb，标签带有tke-createdBy-flag(tke)、tke-created(eks)
	lbs, err := services.Get().CLB().ListAutoCreated(ctx)
	if err != nil {
		return fmt.Errorf("clb.List error: %w", err)
	}

	var result []model.LeakingClb
	for _, lb := range lbs {
		deleteProtect := lo.ContainsBy(lb.AttributeFlags, func(item *string) bool {
			return *item == "DeleteProtect"
		})

		lbID := *lb.LoadBalancerId
		item := model.LeakingClb{
			Date:          l.date,
			Uin:           user.Uin,
			Appid:         user.Appid,
			Region:        region,
			LB:            lbID,
			LBName:        *lb.LoadBalancerName,
			DeleteProtect: deleteProtect,
			LoadBalancer:  lb,
			CanDelete:     true,
			CreatedAt:     cast.ToTime(lb.CreateTime),
		}
		names := strings.Split(*lb.LoadBalancerName, "_")
		if len(names) == 3 {
			item.Namespace = names[1]
			item.ServiceName = names[2]
		}
		// 判断是否存在tke-clusterId标签
		tag, ok := lo.Find(lb.Tags, func(tag *clb.TagInfo) bool {
			return *tag.TagKey == "tke-clusterId"
		})
		if !ok {
			item.Reason = "集群标签不存在"
			result = append(result, item)
			continue
		}
		item.ClusterID = *tag.TagValue

		// 判断集群是否存在
		cluster, err := services.Get().Cluster().Get(ctx, item.ClusterID)
		if err != nil {
			item.Reason = "集群不存在"
			result = append(result, item)
			continue
		}
		item.ClusterName = cluster.Name

		client, err := services.Get().Cluster().Clientset(ctx, cluster)
		if err == nil {
			svc, err := client.K8sCli.CoreV1().Services(item.Namespace).Get(ctx, item.ServiceName, metav1.GetOptions{})
			if err == nil {
				item.Service = svc
			}
		}

		// 判断是否存在对应的lbr
		_, err = services.Get().Cluster().GetLBR(ctx, cluster.ClusterID, lbID)
		if err != nil && apierrors.IsNotFound(err) {
			item.Reason = "LBR不存在"
			result = append(result, item)
			continue
		}
	}

	for i, r := range result {
		listenerBackends, err := services.Get().CLB().ListListenerBackends(ctx, r.LB)
		if err != nil {
			log.Error(err, "ListListeners", "lb", r.LB)
			continue
		}
		result[i].ListenerBackends = listenerBackends
		if len(listenerBackends) > 0 {
			result[i].Reason = fmt.Sprintf("%s, 存在监听器", r.Reason)
			// 判断是否存在后端绑定，若存在，则不可删除
			for _, listenerBackend := range listenerBackends {
				if len(listenerBackend.Targets) > 0 {
					result[i].Reason = fmt.Sprintf("%s, 存在4层后端(不可删除)", r.Reason)
					result[i].CanDelete = false
					break
				}

				for _, rule := range listenerBackend.Rules {
					if len(rule.Targets) > 0 {
						result[i].Reason = fmt.Sprintf("%s(7层), 存在7层后端(不可删除)", r.Reason)
						result[i].CanDelete = false
						break
					}
				}

				// 如果已经标记不可删除，则不再继续判断
				if result[i].CanDelete == false {
					break
				}
			}

			if result[i].CanDelete {
				result[i].Reason = fmt.Sprintf("%s, 不存在后端(可删除)", r.Reason)
			}

		} else {
			result[i].Reason = fmt.Sprintf("%s, 不存在监听器(可删除)", r.Reason)
		}
	}

	return services.Get().Save(result)
}
