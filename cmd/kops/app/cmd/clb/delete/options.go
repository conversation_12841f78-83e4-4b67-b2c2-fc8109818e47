package delete

import (
	"errors"
	"fmt"

	"github.com/samber/lo"
	"github.com/spf13/pflag"

	"git.woa.com/kateway/kateway-server/pkg/util/region"
)

type options struct {
	lb     string
	uin    string
	region string
	user   string
	token  string
}

func newOptions() *options {
	return &options{}
}

func (o *options) AddFlags(fs *pflag.FlagSet) {
	fs.StringVar(&o.uin, "uin", o.uin, "[必填] 主账号uin")
	fs.StringVar(&o.region, "region", o.region, "[必填] 地域标识，支持id、短名称、长名称")
}

func (o *options) Validate() (errs []error) {
	if o.uin == "" {
		errs = append(errs, errors.New("--uin 主账号必填"))
	}
	validUINs := []string{"3321337994", "2783211945"}
	if !lo.Contains(validUINs, o.uin) {
		errs = append(errs, fmt.Errorf("只允许指定以下账号: %v, --uin=%s 不在范围内", validUINs, o.uin))
	}

	if o.region == "" {
		errs = append(errs, errors.New("--region 必填"))
	} else {
		if r := region.Get(o.region); r == nil {
			errs = append(errs, fmt.Errorf("地域(%s)非法", o.region))
		}
	}

	return
}
