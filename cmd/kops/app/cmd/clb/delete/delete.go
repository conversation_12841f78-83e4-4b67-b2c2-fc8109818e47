package delete

import (
	"context"
	stdfmt "fmt"
	"time"

	"github.com/fatih/color"
	"github.com/go-logr/logr"
	"github.com/samber/lo"
	"github.com/spf13/cobra"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/json"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"sigs.k8s.io/yaml"

	"git.woa.com/kateway/pkg/telemetry/log"
	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
	"git.woa.com/kateway/kateway-server/pkg/tmp/types/errors"
	"git.woa.com/kateway/kateway-server/pkg/tmp/web/cloudctx"
	region2 "git.woa.com/kateway/kateway-server/pkg/util/region"
)

const (
	example = `
# 删除指定clb https://iwiki.woa.com/p/4013110918
kops clb delete --uin=<uin> --region=<region> --token=<token> --user=<user> lb-xxx
`
)

func New() *cobra.Command {
	o := newOptions()
	cmd := &cobra.Command{
		Use:           "delete",
		Short:         "删除clb数据",
		Long:          "删除clb数据",
		Example:       example,
		SilenceErrors: true,
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(args) != 1 {
				return cmd.Help()
			}
			o.lb = args[0]
			o.user = lo.Must(cmd.Flags().GetString("user"))
			o.token = lo.Must(cmd.Flags().GetString("token"))

			errs := o.Validate()
			if len(errs) > 0 {
				return errors.NewAggregate(errs)
			}

			h := newHandler(o)
			h.Run(cmd.Context())

			return nil
		},
	}

	o.AddFlags(cmd.Flags())

	return cmd
}

type handler struct {
	*options
	logr.Logger
	checkCount int
}

func newHandler(o *options) *handler {
	return &handler{
		options: o,
		Logger:  log.WithName("clb-delete").WithValues("lb", o.lb),
	}
}

func (h *handler) Run(ctx context.Context) {
	start := time.Now()

	ctx = cloudctx.WithUin(ctx, h.uin)
	ctx = cloudctx.WithRegion(ctx, region2.MustGet(h.region))

	err := h.check(ctx)
	if err != nil {
		fmt.Println(color.RedString("\n检测失败: %s", err))
		return
	} else {
		fmt.Println(color.GreenString("\n检测通过"))
	}

	var input string
	fmt.Println()
	fmt.Println(color.RedString("注意:"))
	fmt.Printf(color.GreenString("确认删除 yes 或 no："))
	stdfmt.Scanln(&input)

	if input != "yes" {
		fmt.Println(color.RedString("取消删除"))
		return
	}

	requestID, err := services.Get().CLB().DeleteLoadBalancer(ctx, h.lb)
	if err != nil {
		fmt.Println(color.RedString("删除失败: %s", err))
		return
	}

	fmt.Println(color.GreenString("删除成功 耗时: %s 请求ID: %s", time.Since(start).Round(time.Second).String(), requestID))

	return
}

func (h *handler) prompt(s string) {
	h.checkCount++
	fmt.Printf("%d. %s ", h.checkCount, s)
}

func (h *handler) check(ctx context.Context) error {
	fmt.Println("开始检测是否可以删除\n")

	h.prompt("是否存在? ")
	lb, err := services.Get().CLB().GetLoadBalancer(ctx, h.lb)
	if err != nil {
		fmt.Println(color.RedString("否"))
		return err
	}
	fmt.Println(color.GreenString("是"))

	h.prompt("是否接入层创建的? ")
	if services.Get().CLB().IsAutoCreated(lb) {
		fmt.Println(color.GreenString("是"))

		if services.Get().CLB().IsTKEAutoCreated(lb) {
			fmt.Println(color.YellowString("TKE 创建于 %s", *lb.CreateTime))
		} else {
			fmt.Println(color.YellowString("EKS 创建于 %s", *lb.CreateTime))
		}
	} else {
		fmt.Println(color.RedString("否"))

		return fmt.Errorf("不能删除非接入层创建的!")
	}

	// 非3321337994账号，需要确认创建时间，防止误删
	if h.uin != "3321337994" {
		// 为了tkeoss或ianvs集群同步延时，只允许删除一个月以上的lb
		h.prompt("创建时间是否大于一个月? ")
		createTime, err := time.Parse("2006-01-02 15:04:05", *lb.CreateTime)
		if err != nil {
			return fmt.Errorf("解析创建时间失败 %s", err)
		}
		createTime = createTime.Add(-time.Hour * 8)
		if time.Since(createTime) > time.Hour*24*30 {
			fmt.Println(color.GreenString("是"))
		} else {
			fmt.Println(color.RedString("否"))
			return fmt.Errorf("创建时间距今(%s)未超过一个月，不能直接删除!", time.Since(createTime).Round(time.Minute).String())
		}
	}

	h.prompt("集群标签是否存在? ")
	clusterID := services.Get().CLB().GetClusterID(lb)
	if clusterID == "" {
		fmt.Println(color.GreenString("否"))
	} else {
		fmt.Println(color.GreenString("是"))

		fmt.Println(color.YellowString("集群ID: %s", clusterID))
	}

	if clusterID != "" {
		if h.user == "" {
			return fmt.Errorf("检测到存在集群标签，--user 必须指定")
		}
		if h.token == "" {
			return fmt.Errorf("检测到存在集群标签，--token 必须指定")
		}

		h.prompt("集群是否存在? ")
		cluster, err := services.Get().Cluster().Get(ctx, clusterID)
		if err != nil {
			fmt.Println(color.GreenString("否: %s", err))
		} else {
			fmt.Println(color.GreenString("是"))
			fmt.Println(color.YellowString("地域: %s", cluster.Region))
			fmt.Println(color.YellowString("类型: %s", cluster.Type))
			fmt.Println(color.YellowString("集群名: %s", cluster.Name))
		}

		h.prompt("LBR是否存在? ")
		_, err = services.Get().Cluster().GetLBR(ctx, clusterID, h.lb)
		if err != nil && apierrors.IsNotFound(err) {
			fmt.Println(color.GreenString("否"))
		} else {
			fmt.Println(color.RedString("是"))
			return fmt.Errorf("存在LBR，不能直接删除!")
		}
	}

	h.prompt("是否有后端? ")
	backends, err := services.Get().CLB().ListListenerBackends(ctx, h.lb)
	if err != nil {
		return fmt.Errorf("查询后端失败 %s", err)
	}
	if len(backends) > 0 {
		canDelete := true
		for _, backend := range backends {
			if len(backend.Targets) > 0 {
				canDelete = false
			}
			for _, rule := range backend.Rules {
				if len(rule.Targets) > 0 {
					canDelete = false
				}
			}
		}

		if !canDelete {
			fmt.Println(color.RedString("是"))

			data, _ := json.MarshalIndent(backends, "", "  ")
			data, _ = yaml.JSONToYAML(data)
			fmt.Println(color.YellowString(string(data)))
			return fmt.Errorf("存在后端，不能直接删除!")
		} else {
			fmt.Println(color.GreenString("否"))
		}
	}

	return nil
}
