package yunapi

import (
	"context"

	"github.com/spf13/cobra"

	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/api/kateway"
	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/api"
	options "git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service/common/options"
	"git.woa.com/kateway/kateway-server/pkg/tmp/types/errors"
)

const (
	example = `
kops %s yunapi [namespace/]name] --cluster=<cluster>
`
)

func New(name string) *cobra.Command {
	o := options.NewLog(name)
	cmd := &cobra.Command{
		Use:     "yunapi",
		Short:   "查看云API日志",
		Aliases: []string{"yun", "api"},
		Example: fmt.Sprintf(example, name),
		RunE: func(cmd *cobra.Command, args []string) error {
			query := ""
			if len(args) == 1 {
				query = args[0]
			}
			errs := o.Validate()
			if len(errs) > 0 {
				return errors.NewAggregate(errs)
			}

			h := newHandler(o, query)

			return h.Run(cmd.Context())
		},
	}

	o.AddFlags(cmd.Flags())

	return cmd
}

type handler struct {
	*options.Log
	query string
}

func newHandler(o *options.Log, query string) *handler {
	return &handler{
		Log:   o,
		query: query,
	}
}

func (h *handler) Run(ctx context.Context) error {
	resp, err := api.Client.YunAPI(ctx, &kateway.YunAPIRequest{
		Target:     h.Target(),
		Query:      h.query,
		LogOptions: h.LogOptions(),
	})
	if err != nil {
		return err
	}
	fmt.Println(resp.GetData())

	return nil
}
