package args

import (
	"context"
	"fmt"
	"io"

	"github.com/spf13/cobra"

	"git.woa.com/kateway/kateway-server/api/kateway"
	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/api"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service/common/options"
	"git.woa.com/kateway/kateway-server/pkg/tmp/types/errors"
)

const (
	example = `
kops %s args --cluster=cls-dyg9hwtz 
`
)

const (
	OperationSet   = "set"
	OperationUnset = "unset"
)

func New(name string) *cobra.Command {
	cmd := &cobra.Command{
		Use:     "args",
		Short:   "容器启动参数管理",
		Aliases: []string{"parameters"},
		Example: fmt.Sprintf(example, name),
	}
	cmd.AddCommand(NewArgsSet(name))
	cmd.AddCommand(NewArgsUnset(name))
	return cmd
}

func NewArgsSet(name string) *cobra.Command {
	o := options.NewUpdateArgs(name)
	cmd := &cobra.Command{
		Use:     "set",
		Short:   "增加/更新容器启动参数",
		Example: fmt.Sprintf(`kops %s args set --cluster=cls-xxxx --args="--log-level=debug"`, name),
		RunE: func(cmd *cobra.Command, _ []string) error {
			errs := o.Validate()
			if len(errs) > 0 {
				return errors.NewAggregate(errs)
			}
			return newHandler(o).Run(cmd.Context(), OperationSet)
		},
	}

	o.AddFlags(cmd.Flags())

	return cmd
}

func NewArgsUnset(name string) *cobra.Command {
	o := options.NewUpdateArgs(name)
	cmd := &cobra.Command{
		Use:     "unset",
		Short:   "移除容器启动参数",
		Example: fmt.Sprintf(`kops %s args unset --cluster=cls-xxxx --args="--log-level"`, name),
		RunE: func(cmd *cobra.Command, _ []string) error {
			errs := o.Validate()
			if len(errs) > 0 {
				return errors.NewAggregate(errs)
			}
			return newHandler(o).Run(cmd.Context(), OperationUnset)
		},
	}

	o.AddFlags(cmd.Flags())

	return cmd
}

type handler struct {
	*options.UpdateArgs
}

func newHandler(o *options.UpdateArgs) *handler {
	return &handler{
		UpdateArgs: o,
	}
}

func (h *handler) Run(ctx context.Context, operation string) error {
	stream, err := api.Client.UpdateArgs(ctx, &kateway.ArgsRequest{
		Target:    h.Target(),
		Operation: operation,
		Args:      h.Args,
		Force:     h.Force,
	})
	if err != nil {
		return err
	}

	for {
		resp, err := stream.Recv()
		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}

		fmt.Println(resp.GetData())
	}

	return nil
}
