package update

import (
	"context"
	"io"

	"github.com/spf13/cobra"

	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/api/kateway"
	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/api"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service/common/options"
	"git.woa.com/kateway/kateway-server/pkg/tmp/types/errors"
)

const (
	example = `
kops %s update --cluster=cls-dyg9hwtz --imageTag=v2.5.0
`
)

func New(name string) *cobra.Command {
	o := options.NewUpdate(name)
	cmd := &cobra.Command{
		Use:     "update",
		Short:   "更新",
		Aliases: []string{"u", "update_auto"},
		Example: fmt.Sprintf(example, name),
		RunE: func(cmd *cobra.Command, args []string) error {
			errs := o.Validate()
			if len(errs) > 0 {
				return errors.NewAggregate(errs)
			}

			h := newHandler(o)

			return h.Run(cmd.Context())
		},
	}

	o.AddFlags(cmd.Flags())

	return cmd
}

type handler struct {
	*options.Update
	checkCount int
}

func newHandler(o *options.Update) *handler {
	return &handler{
		Update: o,
	}
}

func (h *handler) Run(ctx context.Context) error {
	stream, err := api.Client.Update(ctx, &kateway.UpdateRequest{
		Target: h.Target(),
		Image:  h.Image,
		Force:  h.Force,
	})
	if err != nil {
		return err
	}

	for {
		resp, err := stream.Recv()
		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}

		fmt.Println(resp.GetData())
	}

	return nil
}
