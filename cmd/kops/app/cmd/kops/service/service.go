package service

import (
	"github.com/spf13/cobra"

	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service/admin"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service/args"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service/get"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service/get/leader"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service/getpod"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service/logs"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service/mock"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service/setimage"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service/update"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service/yunapi"
)

func New() *cobra.Command {
	name := "service"
	cmd := &cobra.Command{
		Use:     name,
		Short:   "service-controller管理",
		Aliases: []string{"s", "svc"},
		RunE:    getpod.New(name).RunE,
	}
	AddCommand(name, cmd)

	return cmd
}

func AddCommand(name string, cmd *cobra.Command) {
	cmd.AddCommand(get.New(name))
	cmd.AddCommand(logs.New(name))
	cmd.AddCommand(yunapi.New(name))
	cmd.AddCommand(admin.New(name))
	cmd.AddCommand(leader.New(name))
	cmd.AddCommand(getpod.New(name))
	cmd.AddCommand(setimage.New(name))
	cmd.AddCommand(mock.New(name))
	cmd.AddCommand(update.New(name))
	cmd.AddCommand(args.New(name)) // 容器启动参数变更
}
