package get

import (
	"github.com/spf13/cobra"

	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service/get/config"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service/get/deploy"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service/get/leader"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service/get/pod"
)

func New(name string) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "get",
		Short: "查询",
	}
	cmd.AddCommand(config.New(name))
	cmd.AddCommand(pod.New(name))
	cmd.AddCommand(deploy.New(name))
	cmd.AddCommand(leader.New(name))

	return cmd
}
