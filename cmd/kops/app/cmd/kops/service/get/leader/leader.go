package leader

import (
	"context"

	"github.com/spf13/cobra"

	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/api/kateway"
	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/api"
	options "git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service/common/options"
	"git.woa.com/kateway/kateway-server/pkg/tmp/types/errors"
)

const (
	example = `
kops %s get leader --cluster=<cluster>
`
)

func New(name string) *cobra.Command {
	o := options.NewBase(name)
	cmd := &cobra.Command{
		Use:     "leader",
		Short:   "查看当前leader",
		Example: fmt.Sprintf(example, name),
		RunE: func(cmd *cobra.Command, args []string) error {
			errs := o.Validate()
			if len(errs) > 0 {
				return errors.NewAggregate(errs)
			}

			h := newHandler(o)

			return h.Run(cmd.Context())
		},
	}

	o.Add<PERSON>s(cmd.Flags())

	return cmd
}

type handler struct {
	*options.Base
}

func newHandler(o *options.Base) *handler {
	return &handler{
		Base: o,
	}
}

func (h *handler) Run(ctx context.Context) error {
	resp, err := api.Client.GetLeader(ctx, &kateway.GetLeaderRequest{
		Target: h.Target(),
	})
	if err != nil {
		return err
	}
	fmt.Println(resp.GetData())

	return nil
}
