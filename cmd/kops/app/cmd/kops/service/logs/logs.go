package logs

import (
	"context"

	"github.com/spf13/cobra"

	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/api/kateway"
	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/api"
	options "git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service/common/options"
	"git.woa.com/kateway/kateway-server/pkg/tmp/types/errors"
)

const (
	example = `
kops %s logs --cluster=<cluster>
`
)

func New(name string) *cobra.Command {
	o := options.NewLog(name)
	cmd := &cobra.Command{
		Use:     "logs",
		Short:   "查看pod日志",
		Example: fmt.Sprintf(example, name),
		RunE: func(cmd *cobra.Command, args []string) error {
			errs := o.Validate()
			if len(errs) > 0 {
				return errors.NewAggregate(errs)
			}

			h := newHandler(o)

			return h.Run(cmd.Context())
		},
	}

	o.Add<PERSON>lags(cmd.Flags())

	return cmd
}

type handler struct {
	*options.Log
}

func newHandler(o *options.Log) *handler {
	return &handler{
		Log: o,
	}
}

func (h *handler) Run(ctx context.Context) error {
	resp, err := api.Client.Logs(ctx, &kateway.LogsRequest{
		Target:     h.Target(),
		LogOptions: h.LogOptions(),
	})
	if err != nil {
		return err
	}
	fmt.Println(resp.GetData())

	return nil
}
