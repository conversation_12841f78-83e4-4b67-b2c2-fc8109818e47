package options

import (
	"errors"
	"os"
	"os/user"
	"runtime"
	"time"

	"github.com/spf13/pflag"

	"git.woa.com/kateway/kateway-server/api/kateway"
)

type Base struct {
	Name    string
	Cluster string
	User    string
	Token   string
	Query   string
}

func NewBase(name string) *Base {
	return &Base{
		Name: name,
	}
}

func (o *Base) AddFlags(fs *pflag.FlagSet) {
	fs.StringVar(&o.Cluster, "cluster", o.Cluster, "集群ID [必填] ")
	fs.StringVar(&o.Cluster, "clusterID", o.Cluster, "已废弃，请使用--cluster 替代")
	fs.StringVar(&o.User, "user", o.Cluster, "当前操作人的企微英文名, 必须和--token一起使用")
	fs.StringVar(&o.Token, "token", o.Cluster, "publish平台授权token, 必须和--user一起使用")
}

func (o *Base) Validate() (errs []error) {
	if o.Cluster == "" {
		if v := os.Getenv("kubectl_cluster"); v != "" {
			o.Cluster = v
		} else {
			errs = append(errs, errors.New("--cluster 必填"))
		}
	}

	return
}

func (o *Base) Target() *kateway.Target {
	return &kateway.Target{
		Name:    o.Name,
		Cluster: o.Cluster,
		User:    o.User,
		Token:   o.Token,
	}
}

func NewUpdate(name string) *Update {
	return &Update{
		Base: NewBase(name),
	}
}

type Update struct {
	*Base
	Image string
	Force bool
}

func (o *Update) AddFlags(fs *pflag.FlagSet) {
	o.Base.AddFlags(fs)
	fs.StringVar(&o.Image, "image", o.Cluster, "镜像 [必填] 如 2.5.0 v2.5.0 ccr.ccs.tencentyun.com/paas/service-controller:v2.5.2")
	fs.StringVar(&o.Image, "imageTag", o.Cluster, "已废弃，请使用--image")
	fs.BoolVar(&o.Force, "force", o.Force, "是否强制更新")
}

func (o *Update) Validate() (errs []error) {
	errs = o.Base.Validate()
	if o.Image == "" {
		errs = append(errs, errors.New("--Image 必填"))
	}

	// 允许智研流水线环境更新测试环境
	if u := os.Getenv("QCI_TRIGGER"); u != "" {
		o.User = u
	} else if runtime.GOOS == "linux" {
		if o.User == "" {
			errs = append(errs, errors.New("--user 必填"))
		}
		if o.Token == "" {
			errs = append(errs, errors.New("--token 必填"))
		}
	} else if runtime.GOOS == "darwin" {
		if o.User == "" {
			u, _ := user.Current()
			o.User = u.Name
		}
	}

	return
}

type Log struct {
	*Base
	SinceTime string
}

func NewLog(name string) *Log {
	return &Log{
		Base: NewBase(name),
	}
}

func (o *Log) AddFlags(fs *pflag.FlagSet) {
	o.Base.AddFlags(fs)
	fs.StringVar(&o.SinceTime, "since-time", o.SinceTime, "返回指定时间点(RFC3339)之后的日志，如 2025-02-24T16:54:00+08:00")
}

func (o *Log) Validate() (errs []error) {
	errs = o.Base.Validate()
	if o.SinceTime != "" {
		_, err := time.Parse(time.RFC3339, o.SinceTime)
		if err != nil {
			errs = append(errs, errors.New("--since-time 格式错误，类似 2025-02-24T16:54:00+08:00"))
		}
	}

	return
}

func (o *Log) LogOptions() *kateway.LogOptions {
	return &kateway.LogOptions{
		SinceTime: o.SinceTime,
	}
}

type UpdateArgs struct {
	*Base
	Force bool
	Args  []string
}

func NewUpdateArgs(name string) *UpdateArgs {
	return &UpdateArgs{
		Base: NewBase(name),
	}
}

func (o *UpdateArgs) AddFlags(fs *pflag.FlagSet) {
	o.Base.AddFlags(fs)
	fs.BoolVar(&o.Force, "force", o.Force, "是否强制更新")
	fs.StringSliceVarP(&o.Args, "args", "a", nil, "需要更新的容器启动参数列表")
}

func (o *UpdateArgs) Validate() (errs []error) {
	errs = o.Base.Validate()
	if runtime.GOOS == "linux" {
		if o.User == "" {
			errs = append(errs, errors.New("--user 必填"))
		}
		if o.Token == "" {
			errs = append(errs, errors.New("--token 必填"))
		}
	} else if runtime.GOOS == "darwin" {
		if o.User == "" {
			u, _ := user.Current()
			o.User = u.Name
		}
	}
	// 校验参数切片长度
	if len(o.Args) == 0 {
		errs = append(errs, errors.New("--args 必填"))
	}
	return
}
