package listeners

import (
	"context"

	"github.com/spf13/cobra"

	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/api/kateway"
	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/api"
)

const (
	example = `
# 查看CLB所有监听器
kops clb listeners <id>
`
)

func New() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "listeners",
		Aliases: []string{"lis", "ports"},
		Short:   "查看CLB监听器",
		Example: example,
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(args) != 1 {
				return cmd.Help()
			}

			h := newHandler(args[0])

			return h.Run(cmd.Context())
		},
	}

	return cmd
}

type handler struct {
	query string
}

func newHandler(query string) *handler {
	return &handler{
		query: query,
	}
}

func (h *handler) Run(ctx context.Context) error {
	resp, err := api.Client.GetListeners(ctx, &kateway.GetCLBRequest{
		Query: h.query,
	})
	if err != nil {
		return err
	}
	fmt.Println(resp.GetData())

	return nil
}
