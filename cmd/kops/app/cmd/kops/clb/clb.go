package clb

import (
	"context"

	"github.com/spf13/cobra"

	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/api/kateway"
	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/api"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/clb/backends"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/clb/health"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/clb/listeners"
)

const (
	example = `
# 查看CLB信息
kops clb <id>
`
)

func New() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "clb",
		Short:   "查看CLB信息",
		Aliases: []string{"lb"},
		Example: example,
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(args) != 1 {
				return cmd.Help()
			}

			h := newHandler(args[0])

			return h.Run(cmd.Context())
		},
	}

	cmd.AddCommand(listeners.New())
	cmd.AddCommand(backends.New())
	cmd.AddCommand(health.New())

	return cmd
}

type handler struct {
	query string
}

func newHandler(query string) *handler {
	return &handler{
		query: query,
	}
}

func (h *handler) Run(ctx context.Context) error {
	resp, err := api.Client.GetCLB(ctx, &kateway.GetCLBRequest{
		Query: h.query,
	})
	if err != nil {
		return err
	}
	fmt.Println(resp.GetData())

	return nil
}
