package ingress

import (
	"github.com/spf13/cobra"

	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service"
	"git.woa.com/kateway/kateway-server/cmd/kops/app/cmd/kops/service/getpod"
)

func New() *cobra.Command {
	name := "ingress"
	cmd := &cobra.Command{
		Use:     name,
		Short:   "ingress-controller管理",
		Aliases: []string{"i", "in", "ing"},
		RunE:    getpod.New(name).RunE,
	}
	service.AddCommand(name, cmd)

	return cmd
}
