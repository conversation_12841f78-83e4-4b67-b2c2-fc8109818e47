package list

import (
	"context"
	"fmt"

	"github.com/spf13/cobra"

	"git.woa.com/kateway/kateway-server/api/kateway"
	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/api"
)

const (
	example = `
kops task list [--offset=0] [--limit=20] [--filter="ID=task-xxxxx"]
`
)

var (
	offset, limit uint
	filters       []string
)

func New() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "list",
		Example: example,
		Short:   "查看任务",
		Args:    cobra.NoArgs,
		RunE: func(cmd *cobra.Command, _ []string) error {
			return listTask(cmd.Context())
		},
	}
	cmd.Flags().UintVar(&offset, "offset", 0, "偏移量")
	cmd.Flags().UintVar(&limit, "limit", 10, "限制数量")
	cmd.Flags().StringSliceVarP(&filters, "filter", "f", nil,
		"过滤器列表: key=value|key in (v1 v2 v3). 支持的key：ID, Type, State, SubType, ParentID. 示例：--filter=\"ID in (task-xxxxx),State=Running\"")
	return cmd
}

func listTask(ctx context.Context) error {
	req := &kateway.ListTaskRequest{
		Limit:   uint64(limit),
		Offset:  uint64(offset),
		Filters: filters,
	}
	resp, err := api.Client.ListTask(ctx, req)
	if err != nil {
		return err
	}
	fmt.Println(resp.Data)
	return nil
}
