package get

import (
	"context"
	"fmt"

	"github.com/spf13/cobra"

	"git.woa.com/kateway/kateway-server/api/kateway"
	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/api"
)

const (
	example = `
kops task get <task-id>
`
)

func New() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "get",
		Example: example,
		Short:   "查看任务",
		Args:    cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			id := args[0]
			return getTask(cmd.Context(), id)
		},
	}
	return cmd
}

func getTask(ctx context.Context, id string) error {
	resp, err := api.Client.GetTask(ctx, &kateway.GetTaskRequest{
		Id: id,
	})
	if err != nil {
		return err
	}
	fmt.Println(resp.Data)

	return nil
}
