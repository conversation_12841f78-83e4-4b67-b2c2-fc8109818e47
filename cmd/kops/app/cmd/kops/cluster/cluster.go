package cluster

import (
	"github.com/spf13/cobra"
	"sigs.k8s.io/yaml"

	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/api/kateway"
	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/api"
)

const (
	example = `
# 查看集群信息
kops cluster <集群ID>
`
)

func New() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "cluster",
		Short:   "查看集群信息",
		Aliases: []string{"cls"},
		Example: example,
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(args) != 1 {
				return cmd.Help()
			}

			resp, err := api.Client.GetCluster(cmd.Context(), &kateway.GetClusterRequest{
				Query: args[0],
			})
			if err != nil {
				return fmt.Errorf("查询失败: %s", err)
			}

			data, _ := yaml.Marshal(resp.GetCluster())
			fmt.Printf(string(data))

			return nil
		},
	}

	return cmd
}
