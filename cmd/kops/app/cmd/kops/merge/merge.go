package merge

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os/user"

	"github.com/samber/lo"
	"github.com/spf13/cobra"

	"git.woa.com/kateway/kateway-server/api/kateway"
	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/api"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/task"
	taskpkg "git.woa.com/kateway/kateway-server/pkg/task"
)

const (
	example = `
kops merge <cluster-id> --rollback=<true|false> --wait=<true|false>
`
)

var (
	rollback, wait, disableDryrunIngress bool
)

func New() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "merge",
		Example: example,
		Short:   "融合指定集群的controller",
		Args:    cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			return merge(cmd.Context(), args[0])
		},
	}

	cmd.Flags().BoolVar(&rollback, "rollback", false, "执行回滚操作")
	cmd.Flags().BoolVar(&wait, "wait", false, "等待融合任务执行完成")
	cmd.Flags().BoolVar(&disableDryrunIngress, "disable-dryrun-ingress", false, "跳过 ingress 预检")

	return cmd
}

func merge(ctx context.Context, clusterID string) error {
	stream, err := api.Client.CreateTask(ctx, &kateway.CreateTaskRequest{
		Type: taskpkg.GetTaskType[task.Merge](),
		Input: string(lo.Must(json.Marshal(task.Merge{
			ClusterID:            clusterID,
			Op:                   lo.Ternary(rollback, task.MergeOpRollback, task.MergeOpMigrate),
			DisableDryrunIngress: disableDryrunIngress,
		}))),
		Wait:    wait,
		Creator: lo.Must(user.Current()).Username,
	})
	if err != nil {
		return err
	}
	for {
		resp, err := stream.Recv()
		if errors.Is(err, io.EOF) {
			break
		}
		if err != nil {
			return err
		}
		fmt.Println(resp.Data)
	}
	return nil
}
