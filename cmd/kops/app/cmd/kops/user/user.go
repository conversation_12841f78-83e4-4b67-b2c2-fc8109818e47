package user

import (
	"github.com/spf13/cobra"
	"sigs.k8s.io/yaml"

	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/api/kateway"
	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/api"
)

const (
	example = `
# 查看用户信息
kops user <appid | uin>
`
)

func New() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "user",
		Short:   "查看用户信息",
		Aliases: []string{"u"},
		Example: example,
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(args) != 1 {
				return cmd.Help()
			}

			resp, err := api.Client.GetUser(cmd.Context(), &kateway.GetUserRequest{
				Query: args[0],
			})
			if err != nil {
				return fmt.Errorf("查询失败: %s", err)
			}

			data, _ := yaml.Marshal(resp.GetUser())
			fmt.Printf(string(data))

			return nil
		},
	}

	return cmd
}
