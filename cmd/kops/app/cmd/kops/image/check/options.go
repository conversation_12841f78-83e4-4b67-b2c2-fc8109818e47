package check

import (
	"errors"

	"github.com/spf13/pflag"
)

type options struct {
	name    string
	version string
	region  string
}

func newOptions() *options {
	return &options{}
}

func (o *options) AddFlags(fs *pflag.FlagSet) {
	fs.StringVar(&o.name, "name", o.name, "[必填] 组件名称: service 或 ingress")
	fs.StringVar(&o.version, "version", o.version, "[必填] 版本: v1.0.0")
	fs.StringVar(&o.region, "region", o.version, "[可选] 地域: 如 gz，不填则检测所有地域")
}

func (o *options) Validate() (errs []error) {
	if o.name == "" {
		errs = append(errs, errors.New("--name 必填"))
	}

	if o.version == "" {
		errs = append(errs, errors.New("--version 必填"))
	}

	return
}
