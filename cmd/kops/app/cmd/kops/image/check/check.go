package check

import (
	"context"
	"fmt"

	"github.com/go-logr/logr"
	"github.com/spf13/cobra"
	"k8s.io/apimachinery/pkg/util/errors"

	"git.woa.com/kateway/kateway-server/api/kateway"
	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/api"
)

const (
	example = `
# 检查镜像同步情况
kops image check --version=2.5.0 --type=service
`
)

func New() *cobra.Command {
	o := newOptions()
	cmd := &cobra.Command{
		Use:     "check",
		Short:   "检查镜像同步情况",
		Example: example,
		RunE: func(cmd *cobra.Command, args []string) error {
			errs := o.Validate()
			if len(errs) > 0 {
				return errors.NewAggregate(errs)
			}

			h := newHandler(o)

			return h.Run(cmd.Context())
		},
	}

	o.Add<PERSON>lags(cmd.Flags())

	return cmd
}

type handler struct {
	*options
	logr.Logger
	checkCount int
}

func newHandler(o *options) *handler {
	return &handler{
		options: o,
	}
}

func (h *handler) Run(ctx context.Context) error {
	_, err := api.Client.CheckImage(ctx, &kateway.CheckImageRequest{
		Name:    h.name,
		Version: h.version,
		Region:  h.region,
	})
	if err != nil {
		return fmt.Errorf("检查镜像失败: %s", err)
	}
	fmt.Println("启动镜像检查成功，请关注通知群消息。")

	return nil
}
