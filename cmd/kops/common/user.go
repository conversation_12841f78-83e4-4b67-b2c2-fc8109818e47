package common

import (
	"fmt"
	"io/ioutil"
	http2 "net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/golang/glog"
	"github.com/samber/lo"

	"git.woa.com/kateway/kateway-server/conf"
	"git.woa.com/kateway/kateway-server/pkg/component/http"
	"git.woa.com/kateway/kateway-server/pkg/context"
	"git.woa.com/kateway/kateway-server/pkg/logger/logrus"
	"git.woa.com/kateway/kateway-server/pkg/service/cauth"
)

var (
	cauthSvc     *cauth.CAuthService
	appIdMap     = make(map[uint64]string)
	appIdMapLock sync.RWMutex
)

var userBalanceMap = make(map[uint64]string)

func GetAppId(uin uint64) (string, bool) {
	appIdMapLock.RLock()
	defer appIdMapLock.RUnlock()
	s, o := appIdMap[uin]
	return s, o
}

func SetAppId(uin uint64, appid string) {
	appIdMapLock.Lock()
	defer appIdMapLock.Unlock()
	appIdMap[uin] = appid
}

func InitUserService(config *conf.Config) {
	// cauth: 用于查询用户信息, 根据appId查询uin
	httpExec := http.NewHTTP(time.Second * 10)
	cauthSvc = cauth.NewCAuthService(config.CAuth.Server["gz"], httpExec)
}

func GetUinByAppid(appid uint64) (string, error) {
	if _, exist := GetAppId(appid); !exist {
		noDataCtx := context.NewNoData(logrus.New())
		uin, err := cauthSvc.GetUINByAppID(noDataCtx, appid)
		if err != nil {
			return "", err
		}
		SetAppId(appid, uin)
	}
	uin, _ := GetAppId(appid)
	return uin, nil
}

func MustGetUINByAppID(appId uint64) string {
	return lo.Must(GetUinByAppid(appId))
}

// curl -s -XPOST --header "Content-Type: application/json" -d "{\"version\":\"1\",\"componentName\":\"cvm_mc\",\"user\":\"auto\",\"interface\":{\"interfaceName\":\"account.Qcauth.getOwnerUinByAppid\",\"para\":{\"appid\":\"$appId\"}}}" 'http://account.tencentyun.com:50001'
// curl -s -XPOST --header 'Content-Type: application/x-www-form-urlencoded' http://qcost.billing.tencentyun.com/v1/cloud_account/get_account_balance --data "uin=$uid"
func GetUserBalance(appId uint64) string {
	if value, ok := userBalanceMap[appId]; ok {
		return value
	}

	defer func() {
		if err := recover(); err != nil {
			return
		}
	}()

	// Get UIN
	response, err := http2.DefaultClient.Post("http://account.tencentyun.com:50001", "application/json", strings.NewReader(fmt.Sprintf("{\"version\":\"1\",\"componentName\":\"cvm_mc\",\"user\":\"auto\",\"interface\":{\"interfaceName\":\"account.Qcauth.getOwnerUinByAppid\",\"para\":{\"appid\":\"%d\"}}}", appId)))
	if err != nil {
		glog.Error(err)
	}
	bodyBytes, err := ioutil.ReadAll(response.Body)
	if err != nil {
		glog.Fatal(err)
	}
	responseString := string(bodyBytes)
	responseString = responseString[strings.Index(responseString, "\"uin\":")+7:]
	responseString = responseString[:strings.Index(responseString, "\"")]

	// Get Balance
	response, err = http2.DefaultClient.Post("http://qcost.billing.tencentyun.com/v1/cloud_account/get_account_balance", "application/x-www-form-urlencoded", strings.NewReader(fmt.Sprintf("uin=%s", responseString)))
	if err != nil {
		glog.Error(err)
	}
	bodyBytes, err = ioutil.ReadAll(response.Body)
	if err != nil {
		glog.Fatal(err)
	}
	responseString = string(bodyBytes)
	responseString = responseString[strings.Index(responseString, "\"balance\":")+10:]
	responseString = responseString[:strings.Index(responseString, ",")]

	balance, err := strconv.ParseInt(responseString, 10, 64)
	if err != nil {
		glog.Fatal(err)
	}
	if balance > 0 {
		userBalanceMap[appId] = "OK"
	} else {
		userBalanceMap[appId] = "Insufficient"
	}
	return userBalanceMap[appId]
}
