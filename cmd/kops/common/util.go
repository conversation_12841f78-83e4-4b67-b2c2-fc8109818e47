package common

import (
	"context"
	"fmt"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"git.woa.com/kateway/pkg/telemetry/jaeger"
)

var (
	ServiceControllerImageRepo = "/tkeimages/service-controller"
	IngressControllerImageRepo = "/tkeimages/ingress-controller"
)

var CcrRegionMap = map[string]string{
	"gz":     "ccr.ccs.tencentyun.com",
	"sh":     "ccr.ccs.tencentyun.com",
	"bj":     "ccr.ccs.tencentyun.com",
	"cd":     "ccr.ccs.tencentyun.com",
	"sg":     "ccr.ccs.tencentyun.com",
	"cq":     "ccr.ccs.tencentyun.com",
	"nj":     "ccr.ccs.tencentyun.com",
	"tsn":    "ccr.ccs.tencentyun.com",
	"szx":    "ccr.ccs.tencentyun.com",
	"tpe":    "tpeccr.ccs.tencentyun.com",
	"hk":     "hkccr.ccs.tencentyun.com",
	"szjr":   "szjrccr.ccs.tencentyun.com",
	"shjr":   "shjrccr.ccs.tencentyun.com",
	"bjjr":   "bjjrccr.ccs.tencentyun.com",
	"th":     "thccr.ccs.tencentyun.com",
	"in":     "inccr.ccs.tencentyun.com",
	"kr":     "krccr.ccs.tencentyun.com",
	"jp":     "jpccr.ccs.tencentyun.com",
	"usw":    "uswccr.ccs.tencentyun.com",
	"use":    "useccr.ccs.tencentyun.com",
	"de":     "deccr.ccs.tencentyun.com",
	"ru":     "ruccr.ccs.tencentyun.com",
	"ca":     "caccr.ccs.tencentyun.com",
	"qy":     "ccr.ccs.tencentyun.com",
	"xbec":   "ccr.ccs.tencentyun.com",
	"jkt":    "jktccr.ccs.tencentyun.com",
	"jnec":   "ccr.ccs.tencentyun.com",
	"hzec":   "ccr.ccs.tencentyun.com",
	"fzec":   "ccr.ccs.tencentyun.com",
	"whec":   "ccr.ccs.tencentyun.com",
	"csec":   "ccr.ccs.tencentyun.com",
	"sheec":  "ccr.ccs.tencentyun.com",
	"sjwec":  "ccr.ccs.tencentyun.com",
	"hfeec":  "ccr.ccs.tencentyun.com",
	"xiyec":  "ccr.ccs.tencentyun.com",
	"sao":    "saoccr.ccs.tencentyun.com",
	"shadc":  "shadcccr.ccs.tencentyun.com",
	"shwxzf": "shwxzfccr.ccs.tencentyun.com",
	"gzwxzf": "gzwxzfccr.ccs.tencentyun.com",
}

func CleanUpJob(ctx context.Context, k8sClient kubernetes.Interface, namespace string, name string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	_, err := k8sClient.BatchV1().Jobs(namespace).Get(ctx, name, v1.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return err
		}
		return nil
	}

	if err := k8sClient.BatchV1().Jobs(namespace).Delete(ctx, name, v1.DeleteOptions{}); err != nil {
		return err
	}
	time.Sleep(5 * time.Second)
	return nil
}

func CleanUpPod(ctx context.Context, k8sClient kubernetes.Interface, namespace string, name string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	pods, err := k8sClient.CoreV1().Pods(namespace).List(ctx, v1.ListOptions{
		LabelSelector: fmt.Sprintf("job-name=%s", name),
	})
	if err != nil {
		return err
	}
	for _, pod := range pods.Items {
		if err := k8sClient.CoreV1().Pods(namespace).Delete(ctx, pod.Name, v1.DeleteOptions{}); err != nil {
			return err
		}
	}
	return nil
}

func GetPodStatus(pod *corev1.Pod) (int, string) {
	restarts := 0
	reason := string(pod.Status.Phase)
	if pod.Status.Reason != "" {
		reason = pod.Status.Reason
	}

	initializing := false
	for i := range pod.Status.InitContainerStatuses {
		container := pod.Status.InitContainerStatuses[i]
		restarts += int(container.RestartCount)
		switch {
		case container.State.Terminated != nil && container.State.Terminated.ExitCode == 0:
			continue
		case container.State.Terminated != nil:
			// initialization is failed
			if len(container.State.Terminated.Reason) == 0 {
				if container.State.Terminated.Signal != 0 {
					reason = fmt.Sprintf("Init:Signal:%d", container.State.Terminated.Signal)
				} else {
					reason = fmt.Sprintf("Init:ExitCode:%d", container.State.Terminated.ExitCode)
				}
			} else {
				reason = "Init:" + container.State.Terminated.Reason
			}
			initializing = true
		case container.State.Waiting != nil && len(container.State.Waiting.Reason) > 0 && container.State.Waiting.Reason != "PodInitializing":
			reason = "Init:" + container.State.Waiting.Reason
			initializing = true
		default:
			reason = fmt.Sprintf("Init:%d/%d", i, len(pod.Spec.InitContainers))
			initializing = true
		}
		break
	}
	if !initializing {
		restarts = 0
		hasRunning := false
		for i := len(pod.Status.ContainerStatuses) - 1; i >= 0; i-- {
			container := pod.Status.ContainerStatuses[i]
			restarts += int(container.RestartCount)
			if container.State.Waiting != nil && container.State.Waiting.Reason != "" {
				reason = container.State.Waiting.Reason
			} else if container.State.Terminated != nil && container.State.Terminated.Reason != "" {
				reason = container.State.Terminated.Reason
			} else if container.State.Terminated != nil && container.State.Terminated.Reason == "" {
				if container.State.Terminated.Signal != 0 {
					reason = fmt.Sprintf("Signal:%d", container.State.Terminated.Signal)
				} else {
					reason = fmt.Sprintf("ExitCode:%d", container.State.Terminated.ExitCode)
				}
			} else if container.Ready && container.State.Running != nil {
				hasRunning = true
			}
		}

		// change pod status back to "Running" if there is at least one container still reporting as "Running" status
		if reason == "Completed" && hasRunning {
			reason = "Running"
		}
	}

	if pod.DeletionTimestamp != nil && pod.Status.Reason == "NodeLost" {
		reason = "Unknown"
	} else if pod.DeletionTimestamp != nil {
		reason = "Terminating"
	}
	return restarts, reason
}
