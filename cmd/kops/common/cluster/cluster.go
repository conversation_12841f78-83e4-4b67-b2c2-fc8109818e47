package cluster

import (
	context2 "context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/golang/glog"
	"github.com/pborman/uuid"
	"github.com/samber/lo"
	v12 "k8s.io/api/apps/v1"
	v1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	restclient "k8s.io/client-go/rest"
	platformClient "tkestack.io/tke/api/client/clientset/versioned"
	platformv1 "tkestack.io/tke/api/platform/v1"

	"git.woa.com/kateway/pkg/telemetry/jaeger"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	"git.woa.com/kateway/kateway-server/cmd/kops/common"
	"git.woa.com/kateway/kateway-server/conf"
	"git.woa.com/kateway/kateway-server/pkg/component/eks"
	"git.woa.com/kateway/kateway-server/pkg/component/http"
	tke2 "git.woa.com/kateway/kateway-server/pkg/component/tke"
	"git.woa.com/kateway/kateway-server/pkg/context"
	"git.woa.com/kateway/kateway-server/pkg/logger/logrus"
	"git.woa.com/kateway/kateway-server/pkg/models"
	"git.woa.com/kateway/kateway-server/pkg/service/cluster"
	"git.woa.com/kateway/kateway-server/pkg/store/dao"
	"git.woa.com/kateway/kateway-server/pkg/util/k8sutil"
	region2 "git.woa.com/kateway/kateway-server/pkg/util/region"
)

var (
	Config       *conf.Config
	MisakaConfig *common.MisakaConfig
	KopsConfig   *config.Config
)

var (
	RegionStore     = make(map[string]*dao.Store)
	RegionStoreLock sync.RWMutex
)

func GetRegionStore(region string) (*dao.Store, bool) {
	RegionStoreLock.RLock()
	defer RegionStoreLock.RUnlock()
	st, exist := RegionStore[region]
	return st, exist
}

func SetRegionStore(region string, st *dao.Store) {
	RegionStoreLock.Lock()
	defer RegionStoreLock.Unlock()
	RegionStore[region] = st
}

var (
	RegionClusterFactory     = make(map[string]*cluster.Clusters)
	RegionClusterFactoryLock sync.RWMutex
)

func GetRegionClusterFactory(region string) (*cluster.Clusters, bool) {
	RegionClusterFactoryLock.RLock()
	defer RegionClusterFactoryLock.RUnlock()
	clusters, exist := RegionClusterFactory[region]
	return clusters, exist
}

func SetRegionClusterFactory(region string, cluster *cluster.Clusters) {
	RegionClusterFactoryLock.Lock()
	defer RegionClusterFactoryLock.Unlock()
	RegionClusterFactory[region] = cluster
}

var (
	TKEClusterMap     = make(map[string]*models.Cluster)
	TKEClusterMapLock sync.RWMutex
)

func GetTKEClusterMap(region string) (*models.Cluster, bool) {
	TKEClusterMapLock.RLock()
	defer TKEClusterMapLock.RUnlock()
	clusters, exist := TKEClusterMap[region]
	return clusters, exist
}

func SetTKEClusterMap(region string, cluster *models.Cluster) {
	TKEClusterMapLock.Lock()
	defer TKEClusterMapLock.Unlock()
	TKEClusterMap[region] = cluster
}

var (
	lock                       sync.Mutex
	TKEClusterNotFoundMap      = make(map[string]bool)
	TKEClusterComponentUpgrade = make(map[string]bool)
	TKEClusterService          = make(map[string][]*v1.Service)
)

func CheckTKEClusterComponent(region string, clusterId string) (bool, error) {
	if _, exist := TKEClusterComponentUpgrade[clusterId]; !exist {
		cls := MustGetTKECluster(region, clusterId)
		var deployment *v12.Deployment
		if cls.ClusterType == models.CLUSTER_TYPE_CLUSTER_DEPLOY_ASYNC_PROCESS ||
			cls.ClusterType == models.CLUSTER_TYPE_CLUSTER_DEPLOY_MIGRATE_TO_OPERATOR ||
			cls.ClusterType == models.CLUSTER_TYPE_CLUSTER_DEPLOY_ON_OPERATOR {

			if cls.LifeState == models.CLUSTER_LIFESTATE_IDLE {
				return false, fmt.Errorf(models.CLUSTER_LIFESTATE_IDLE)
			}

			metaClusterId := cls.MetaClusterID
			if metaClusterId == "" {
				rg := region2.Get(region)
				metaClusterId = GetDefaultMetaCluster(rg.Alias)
			}
			metacls := MustGetTKECluster(region, metaClusterId)
			clientSet, err := GetTKEClusterClientSet(region, metacls)
			if err != nil {
				glog.Errorf("ClientSet Get. error:%s", err.Error())
				return false, err
			}
			deployment, err = clientSet.K8sCli.AppsV1().Deployments(cls.ClusterInstanceId).Get(context2.Background(), fmt.Sprintf("%s-service-controller", cls.ClusterInstanceId), metav1.GetOptions{})
			if err != nil {
				glog.Errorf("Deployments Get. error:%s", err.Error())
				return false, err
			}
		} else {
			clientSet, err := GetTKEClusterClientSet(region, cls)
			if err != nil {
				glog.Errorf("ClientSet Get. error:%s", err.Error())
				return false, err
			}
			deployment, err = clientSet.K8sCli.AppsV1().Deployments("kube-system").Get(context2.Background(), "service-controller", metav1.GetOptions{})
			if err != nil {
				glog.Errorf("Deployments Get. error:%s", err.Error())
				return false, err
			}
		}
		split := strings.Split(deployment.Spec.Template.Spec.Containers[0].Image, ":")
		if strings.HasPrefix(split[1], "v1") {
			TKEClusterComponentUpgrade[clusterId] = false
		} else {
			TKEClusterComponentUpgrade[clusterId] = true
		}
	}

	return TKEClusterComponentUpgrade[clusterId], nil
}

func GetTKEClusterRestConfig(region string, appid uint64, clusterID string) (*restclient.Config, error) {
	if _, exist := GetRegionClusterFactory(region); !exist {
		rg := region2.Get(region)
		if rg == nil {
			panic("region not found")
		}

		httpExec := http.NewHTTP(time.Second * 10)
		tkeClient := tke2.NewClient(region, strings.ReplaceAll(Config.TKE.Dashboard.Url, "REGION", rg.Alias), httpExec)
		tkeSvc := cluster.NewTKEService(tkeClient)

		eksClient := eks.NewClientManager(rg.Name, strings.ReplaceAll(Config.EKS.EKSPlatform.Url, "REGION", rg.Alias), http.MustHTTPS(time.Second*10, Config.EKS.EKSPlatform.Cert, Config.EKS.EKSPlatform.Key, Config.EKS.EKSPlatform.Token))
		eksCluster := eks.NewClusterManager(rg.Name, strings.ReplaceAll(Config.EKS.CloudGwURL, "REGION", rg.Alias), httpExec)
		eksInternal := eks.NewInternalServer(rg.Name, strings.ReplaceAll(Config.EKS.EKSServerURL, "REGION", rg.Alias), httpExec)
		eksSvc := cluster.NewEKSService(eksClient, eksCluster, eksInternal)

		cf := cluster.NewClusters(map[string]cluster.ClusterFactory{
			cluster.ClusterTypeTKE: func(r *region2.Region) (cluster.K8sCluster, error) {
				return tkeSvc, nil
			},
			cluster.ClusterTypeEKS: func(r *region2.Region) (cluster.K8sCluster, error) {
				return eksSvc, nil
			},
		})
		SetRegionClusterFactory(region, cf)
	}

	clusterFactory, _ := GetRegionClusterFactory(region)
	noDataCtx := context.NewNoData(logrus.New())
	ctx := context.New(appid, common.MustGetUINByAppID(appid), common.MustGetUINByAppID(appid), uuid.New(), noDataCtx.Logger)
	restConfig, err := clusterFactory.RestConfig(ctx, region, cluster.ClusterTypeTKE, appid, clusterID)
	if err != nil {
		glog.Errorf("restConfig. error:%s", err.Error())
		return restConfig, err
	}
	return restConfig, nil
}

func GetTKEClusterClientSet(region string, cls *models.Cluster, timeout ...time.Duration) (cluster.ClientsSet, error) {
	if _, exist := GetRegionClusterFactory(region); !exist {
		rg := region2.Get(region)
		if rg == nil {
			panic("region not found")
		}

		httpExec := http.NewHTTP(time.Second * 10)
		tkeClient := tke2.NewClient(region, strings.ReplaceAll(Config.TKE.Dashboard.Url, "REGION", rg.Alias), httpExec)
		tkeSvc := cluster.NewTKEService(tkeClient, timeout...)

		eksClient := eks.NewClientManager(rg.Name, strings.ReplaceAll(Config.EKS.EKSPlatform.Url, "REGION", rg.Alias), http.MustHTTPS(time.Second*10, Config.EKS.EKSPlatform.Cert, Config.EKS.EKSPlatform.Key, Config.EKS.EKSPlatform.Token))
		eksCluster := eks.NewClusterManager(rg.Name, strings.ReplaceAll(Config.EKS.CloudGwURL, "REGION", rg.Alias), httpExec)
		eksInternal := eks.NewInternalServer(rg.Name, strings.ReplaceAll(Config.EKS.EKSServerURL, "REGION", rg.Alias), httpExec)
		eksSvc := cluster.NewEKSService(eksClient, eksCluster, eksInternal, timeout...)

		cf := cluster.NewClusters(map[string]cluster.ClusterFactory{
			cluster.ClusterTypeTKE: func(r *region2.Region) (cluster.K8sCluster, error) {
				return tkeSvc, nil
			},
			cluster.ClusterTypeEKS: func(r *region2.Region) (cluster.K8sCluster, error) {
				return eksSvc, nil
			},
		})
		SetRegionClusterFactory(region, cf)
	}

	clusterFactory, _ := GetRegionClusterFactory(region)
	noDataCtx := context.NewNoData(logrus.New())
	ctx := context.New(cls.AppId, common.MustGetUINByAppID(cls.AppId), common.MustGetUINByAppID(cls.AppId), uuid.New(), noDataCtx.Logger)
	metaclient, err := clusterFactory.ClientsSet(ctx, region, cluster.ClusterTypeTKE, cls.AppId, cls.ClusterInstanceId)
	if err != nil {
		glog.Errorf("ClientsSet. error:%s", err.Error())
		return metaclient, err
	}
	return metaclient, nil
}

func GetEKSClusterRestConfig(region string, appid uint64, clusterID string) (*restclient.Config, error) {
	if _, exist := GetRegionClusterFactory(region); !exist {
		rg := region2.Get(region)
		if rg == nil {
			panic("region not found")
		}

		httpExec := http.NewHTTP(time.Second * 10)
		tkeClient := tke2.NewClient(region, strings.ReplaceAll(Config.TKE.Dashboard.Url, "REGION", rg.Alias), httpExec)
		tkeSvc := cluster.NewTKEService(tkeClient)

		eksClient := eks.NewClientManager(rg.Name, strings.ReplaceAll(Config.EKS.EKSPlatform.Url, "REGION", rg.Alias), http.MustHTTPS(time.Second*10, Config.EKS.EKSPlatform.Cert, Config.EKS.EKSPlatform.Key, Config.EKS.EKSPlatform.Token))
		eksCluster := eks.NewClusterManager(rg.Name, strings.ReplaceAll(Config.EKS.CloudGwURL, "REGION", rg.Alias), httpExec)
		eksInternal := eks.NewInternalServer(rg.Name, strings.ReplaceAll(Config.EKS.EKSServerURL, "REGION", rg.Alias), httpExec)
		eksSvc := cluster.NewEKSService(eksClient, eksCluster, eksInternal)

		cf := cluster.NewClusters(map[string]cluster.ClusterFactory{
			cluster.ClusterTypeTKE: func(r *region2.Region) (cluster.K8sCluster, error) {
				return tkeSvc, nil
			},
			cluster.ClusterTypeEKS: func(r *region2.Region) (cluster.K8sCluster, error) {
				return eksSvc, nil
			},
		})
		SetRegionClusterFactory(region, cf)
	}

	clusterFactory, _ := GetRegionClusterFactory(region)
	noDataCtx := context.NewNoData(logrus.New())
	ctx := context.New(appid, common.MustGetUINByAppID(appid), common.MustGetUINByAppID(appid), uuid.New(), noDataCtx.Logger)
	restConfig, err := clusterFactory.RestConfig(ctx, region, cluster.ClusterTypeEKS, appid, clusterID)
	if err != nil {
		glog.Errorf("restConfig. error:%s", err.Error())
		return restConfig, err
	}
	return restConfig, nil
}

func GetEKSClusterClientSet(region string, cls *platformv1.Cluster, timeout ...time.Duration) (cluster.ClientsSet, error) {
	if _, exist := GetRegionClusterFactory(region); !exist {
		rg := region2.Get(region)
		if rg == nil {
			panic("region not found")
		}

		httpExec := http.NewHTTP(time.Second * 10)
		tkeClient := tke2.NewClient(region, strings.ReplaceAll(Config.TKE.Dashboard.Url, "REGION", rg.Alias), httpExec)
		tkeSvc := cluster.NewTKEService(tkeClient, timeout...)

		eksClient := eks.NewClientManager(rg.Name, strings.ReplaceAll(Config.EKS.EKSPlatform.Url, "REGION", rg.Alias), http.MustHTTPS(time.Second*10, Config.EKS.EKSPlatform.Cert, Config.EKS.EKSPlatform.Key, Config.EKS.EKSPlatform.Token))
		eksCluster := eks.NewClusterManager(rg.Name, strings.ReplaceAll(Config.EKS.CloudGwURL, "REGION", rg.Alias), httpExec)
		eksInternal := eks.NewInternalServer(rg.Name, strings.ReplaceAll(Config.EKS.EKSServerURL, "REGION", rg.Alias), httpExec)
		eksSvc := cluster.NewEKSService(eksClient, eksCluster, eksInternal, timeout...)

		cf := cluster.NewClusters(map[string]cluster.ClusterFactory{
			cluster.ClusterTypeTKE: func(r *region2.Region) (cluster.K8sCluster, error) {
				return tkeSvc, nil
			},
			cluster.ClusterTypeEKS: func(r *region2.Region) (cluster.K8sCluster, error) {
				return eksSvc, nil
			},
		})
		SetRegionClusterFactory(region, cf)
	}

	clusterFactory, _ := GetRegionClusterFactory(region)
	noDataCtx := context.NewNoData(logrus.New())
	eksAppId, _ := strconv.ParseUint(cls.Spec.TenantID, 10, 64)
	ctx := context.New(eksAppId, common.MustGetUINByAppID(eksAppId), common.MustGetUINByAppID(eksAppId), uuid.New(), noDataCtx.Logger)
	metaclient, err := clusterFactory.ClientsSet(ctx, region, cluster.ClusterTypeEKS, eksAppId, cls.Name)
	if err != nil {
		glog.Errorf("ClientsSet. error:%s", err.Error())
		return metaclient, err
	}
	return metaclient, nil
}

func GetServiceList(region string, clusterId string) ([]*v1.Service, error) {
	if result, exist := TKEClusterService[clusterId]; exist {
		return result, nil
	}

	result := make([]*v1.Service, 0)

	tkeCluster := MustGetTKECluster(region, clusterId)
	clientSet, err := GetTKEClusterClientSet(region, tkeCluster)
	if err != nil {
		glog.Errorf("ClientsSet. error:%s", err.Error())
		return result, err
	}

	serviceList, err := clientSet.K8sCli.CoreV1().Services(metav1.NamespaceAll).List(context2.Background(), metav1.ListOptions{})
	if err != nil {
		glog.Errorf("ClientsSet. error:%s", err.Error())
		return result, err
	}
	for index := range serviceList.Items {
		result = append(result, &serviceList.Items[index])
	}
	TKEClusterService[clusterId] = result
	return result, nil
}

func ListTKECluster(region string, sql string) []models.Cluster {
	// init db
	st := mustGetStore(region)
	clusters, err := st.GetClustersBySqlSelector(sql)
	if err != nil {
		panic(fmt.Sprintf("dashboard db query error, %s", err))
	}

	for idx := range clusters {
		clusters[idx].Region = region
	}

	return clusters
}

func GetTKECluster(region, clusterID string) (*models.Cluster, error) {
	lock.Lock()
	defer lock.Unlock()

	if _, exist := TKEClusterNotFoundMap[clusterID]; exist {
		return nil, errors.New("cluster not found")
	}

	if cluster, exist := GetTKEClusterMap(clusterID); exist {
		return cluster, nil
	}

	// init db
	st, err := getStore(region)
	if err != nil {
		return nil, err
	}
	cluster, err := st.GetCluster(clusterID)
	if err != nil {
		TKEClusterNotFoundMap[clusterID] = true
		return nil, err
	}

	cluster.Region = region
	SetTKEClusterMap(clusterID, cluster)
	return cluster, nil
}

func MustGetTKECluster(region string, clusterID string) *models.Cluster {
	return lo.Must(GetTKECluster(region, clusterID))
}

func GetDefaultMetaCluster(region string) string {
	return Config.TKE.DefaultMetaCluster[region]
}

func mustGetStore(region string) *dao.Store {
	return lo.Must(getStore(region))
}

func getStore(region string) (*dao.Store, error) {
	if _, exist := GetRegionStore(region); !exist {
		rg := region2.Get(region)
		if rg == nil {
			return nil, errors.New("region not found")
		}
		dbConfig, ok := Config.DashboardDB[rg.Alias]
		if !ok {
			return nil, fmt.Errorf("dashboard db for %q config not found in config file", rg.Alias)
		}
		st, err := dao.NewStore(&dao.StoreConfig{
			DBType: dbConfig.DBType(),
			DBArg:  dbConfig.GormURL(),
		})
		if err != nil {
			return nil, err
		}
		SetRegionStore(region, st)
	}

	store, _ := GetRegionStore(region)
	return store, nil
}

func GetEKSCluster(region string, clusterId string) (*platformv1.Cluster, error) {
	rg := region2.Get(region)
	if rg == nil {
		panic(fmt.Sprintf("region not found: %s", region))
	}
	// eks platform
	eksPlatformUrl := strings.ReplaceAll(Config.EKS.EKSPlatform.Url, "REGION", rg.Alias)
	eksPlatformConfig, err := k8sutil.GetRestConfigByCertFile(eksPlatformUrl, Config.EKS.EKSPlatform.Cert, Config.EKS.EKSPlatform.Key)
	if err != nil {
		glog.Errorf("EKS ClientsSet. error:%s", err.Error())
		return nil, err
	}
	// 用于更新eks-platform的cluster资源
	eksPlatformCli, err := platformClient.NewForConfig(eksPlatformConfig)
	if err != nil {
		glog.Errorf("EKS ClientsSet. error:%s", err.Error())
		return nil, err
	}

	noDataCtx := context.NewNoData(logrus.New())
	eksCls, err := eksPlatformCli.PlatformV1().Clusters().Get(noDataCtx, clusterId, metav1.GetOptions{})
	if err != nil {
		if apierrors.IsNotFound(err) {
			return nil, nil
		}
		glog.Errorf("EKS ClientsSet. error:%s", err.Error())
		return nil, err
	}
	return eksCls, nil
}

func GetAllEKSCluster(ctx context2.Context, region string) ([]*platformv1.Cluster, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	span.LogKV("region", region)

	rg := region2.Get(region)
	// eks platform
	eksPlatformUrl := strings.ReplaceAll(Config.EKS.EKSPlatform.Url, "REGION", rg.Alias)
	eksPlatformConfig, err := k8sutil.GetRestConfigByCertFile(eksPlatformUrl, Config.EKS.EKSPlatform.Cert, Config.EKS.EKSPlatform.Key)
	if err != nil {
		glog.Errorf("EKS ClientsSet. error:%s", err.Error())
		return nil, err
	}
	// 用于更新eks-platform的cluster资源
	eksPlatformCli, err := platformClient.NewForConfig(eksPlatformConfig)
	if err != nil {
		glog.Errorf("EKS ClientsSet. error:%s", err.Error())
		return nil, err
	}

	var totalClusters []*platformv1.Cluster
	times := 0
	Continue := ""
	for {
		times = times + 1
		clusters, err := eksPlatformCli.PlatformV1().Clusters().List(ctx, metav1.ListOptions{
			Limit:    1000,
			Continue: Continue,
		})
		if err != nil {
			return nil, err
		}
		for index := range clusters.Items {
			totalClusters = append(totalClusters, &clusters.Items[index])
		}
		Continue = clusters.Continue
		if Continue == "" {
			break
		}
	}
	return totalClusters, nil
}
