package cluster

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	rbac "k8s.io/api/rbac/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"git.woa.com/kateway/pkg/telemetry/jaeger"

	"git.woa.com/kateway/kateway-server/pkg/merge"
	"git.woa.com/kateway/kateway-server/pkg/models"
)

const (
	ServiceClusterRole = "lb-service-clusterrole"
	IngressClusterRole = "lb-ingress-clusterrole"
)

func EnsureServiceClusterRole(ctx context.Context, cluster *models.Cluster) error {
	return ensureClusterRole(ctx, cluster, ServiceClusterRole)
}

func EnsureIngressClusterRole(ctx context.Context, cluster *models.Cluster) error {
	return ensureClusterRole(ctx, cluster, IngressClusterRole)
}

// ensureClusterRole 针对tke托管集群的 ingress、tke 独立集群的 ingress 和 service
// 1. 判断 是否有 nodes 的update 权限，如果没有加上，背景：node 优雅删除要求对 nodes 有写权限
// 2. 判断是否有 leases 权限，如果没有加上，背景：未来leader 选举会使用 leases
func ensureClusterRole(ctx context.Context, cluster *models.Cluster, clusterRoleName string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	// create the clientset
	clientSet, err := GetTKEClusterClientSet(cluster.Region, cluster)
	if err != nil {
		return err
	}

	clusterRole, err := clientSet.K8sCli.RbacV1().ClusterRoles().Get(ctx, clusterRoleName, v1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return nil // 老的独立集群可能没有使用这个 cluster role
		}
		return err
	}

	var needUpdate bool
	for i, rule := range clusterRole.Rules {
		nodesIndex := lo.IndexOf(rule.Resources, "nodes")
		if nodesIndex > -1 &&
			lo.IndexOf(rule.Verbs, "update") == -1 &&
			lo.IndexOf(rule.Verbs, "*") == -1 { // nodes 没有 update 权限

			needUpdate = true
			clusterRole.Rules[i].Resources = lo.DropByIndex(rule.Resources, nodesIndex)
			clusterRole.Rules = lo.Splice(clusterRole.Rules, i+1, rbac.PolicyRule{
				APIGroups: []string{""},
				Resources: []string{
					"nodes",
				},
				Verbs: []string{
					"get",
					"list",
					"watch",
					"update",
				},
			})

			break
		}
	}

	var needLeases = true
	for _, rule := range clusterRole.Rules {
		if lo.IndexOf(rule.Resources, "leases") > -1 {
			needLeases = false
			break
		}
	}

	if _, exists := clusterRole.Annotations[merge.AnnotationIngressCtrlMigrated]; exists {
		needLeases = false
	}

	if needLeases {
		needUpdate = true
		clusterRole.Rules = append(clusterRole.Rules, rbac.PolicyRule{
			APIGroups: []string{"coordination.k8s.io"},
			Resources: []string{
				"leases",
			},
			Verbs: []string{
				"*",
			},
		})
	}

	if needUpdate {
		fmt.Printf("update cluster role %s for cluster %s\n", clusterRoleName, cluster.ClusterInstanceId)
		_, err = clientSet.K8sCli.RbacV1().ClusterRoles().Update(ctx, clusterRole, v1.UpdateOptions{})
	}
	return err
}
