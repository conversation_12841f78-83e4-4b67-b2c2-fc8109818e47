package cluster

import (
	"context"

	v12 "k8s.io/api/apps/v1"
	"k8s.io/api/extensions/v1beta1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"git.woa.com/kateway/pkg/telemetry/jaeger"
)

type MixedDeployment struct {
	IsAPPv1                     bool
	AppV1Deployment             *v12.Deployment
	ExtensionsV1beta1Deployment *v1beta1.Deployment
}

func (m MixedDeployment) GetReplicas() int32 {
	if m.IsAPPv1 {
		return *m.AppV1Deployment.Spec.Replicas
	} else {
		return *m.ExtensionsV1beta1Deployment.Spec.Replicas
	}
}

func (m MixedDeployment) GetContainerImage(containerName string) string {
	if m.IsAPPv1 {
		for _, container := range m.AppV1Deployment.Spec.Template.Spec.Containers {
			if container.Name == containerName {
				return container.Image
			}
		}
	} else {
		for _, container := range m.ExtensionsV1beta1Deployment.Spec.Template.Spec.Containers {
			if container.Name == containerName {
				return container.Image
			}
		}
	}
	return ""
}

type Deployment interface {
	GetContainerImage(containerName string) string
	GetReplicas() int32
}

func BuildAppV1Deployment(deployment *v12.Deployment) Deployment {
	return MixedDeployment{
		IsAPPv1:         true,
		AppV1Deployment: deployment,
	}
}

func BuildExtensionsV1beta1Deployment(deployment *v1beta1.Deployment) Deployment {
	return MixedDeployment{
		IsAPPv1:                     false,
		ExtensionsV1beta1Deployment: deployment,
	}
}

func GetMixedDeployment(ctx context.Context, k8sClient kubernetes.Interface, namespace string, name string) Deployment {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	ingressControllerDeployment, err := k8sClient.AppsV1().Deployments(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		ingressOldControllerDeployment, err := k8sClient.ExtensionsV1beta1().Deployments(namespace).Get(ctx, name, metav1.GetOptions{})
		if err != nil {
			return nil
		}
		return BuildExtensionsV1beta1Deployment(ingressOldControllerDeployment)
	}
	return BuildAppV1Deployment(ingressControllerDeployment)
}
