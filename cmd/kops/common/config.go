package common

import (
	"io/ioutil"

	"github.com/pkg/errors"
	"gopkg.in/yaml.v3"
)

type MisakaConfig struct {
	ServiceControllerImageRepo string `yaml:"serviceControllerImageRepo"`
	IngressControllerImageRepo string `yaml:"ingressControllerImageRepo"`

	SkipAppIds     []string `yaml:"skipAppIds"`
	SkipClusterIds []string `yaml:"skipClusterIds"`
}

func LoadConfigFromFile(file string) (*MisakaConfig, error) {
	data, err := ioutil.ReadFile(file)
	if err != nil {
		return nil, errors.Wrapf(err, "read config file %s failed", file)
	}

	ret := &MisakaConfig{}
	if err := yaml.Unmarshal(data, ret); err != nil {
		return nil, errors.Wrapf(err, "parse config file %s failed", file)
	}
	return ret, nil
}
