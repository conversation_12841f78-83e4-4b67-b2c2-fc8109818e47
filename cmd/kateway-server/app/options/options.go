package options

import (
	"os"

	"github.com/spf13/pflag"
)

type Options struct {
	Dev        bool
	EnableTask bool
	Conf       string
}

func New() *Options {
	return &Options{
		Conf: "conf/kops.yaml",
	}
}

func (o *Options) AddFlags(fs *pflag.FlagSet) {
	fs.BoolVar(&o.Dev, "dev", false, "dev mode")
	fs.Bool<PERSON>ar(&o.EnableTask, "enable-task", false, "是否启动任务引擎")
}

func (o *Options) Validate() (errs []error) {
	return
}

func (o *Options) Complete() error {
	// 在开发环境中，会通过指定MYSQL_DATABASE来指定个人开发数据库，这时默认启动任务引擎
	env := os.Getenv("MYSQL_DATABASE")
	if env != "" {
		o.EnableTask = true
	}

	// 在生产环境中，会部署到每个地域的caas集群，只有处于gz地域的实例会启动任务引擎，因为当前数据库全局共享且位于gz，所以就近启动
	env = os.Getenv("NAMESPACE")
	if env == "tke-gz" {
		o.EnableTask = true
	}

	return nil
}
