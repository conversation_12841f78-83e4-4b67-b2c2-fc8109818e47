package app

import (
	"os"
	osuser "os/user"
	"strings"

	"github.com/opentracing/opentracing-go"
	"github.com/samber/lo"
	"github.com/segmentio/ksuid"
	jaegerconfig "github.com/uber/jaeger-client-go/config"

	"git.woa.com/kateway/pkg/app/version"
	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/options"
	"git.woa.com/kateway/kateway-server/cmd/kops/common"
	cluster2 "git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
	"git.woa.com/kateway/kateway-server/conf"
	"git.woa.com/kateway/kateway-server/pkg/alarm"
	"git.woa.com/kateway/kateway-server/pkg/tmp/tencentcloud/sts"
)

func Init(opts *options.Options) error {
	id := ksuid.New().String()
	fmt.Init(&fmt.Config{
		ID:         id,
		ProjectID:  "14315",
		Env:        "prod",
		EnvID:      "120539",
		ViewID:     "6422",
		Topic:      "sdk-abcg2b23e837b259",
		ServerAddr: "publiclog.zhiyan.tencent-cloud.net:11001",
		Proto:      "tcp",
	})
	defer func() {
		fmt.Close()
	}()

	u, _ := osuser.Current()
	fmt.Printf("          id: %v\n", id)
	fmt.Printf("        user: %v\n", u.Username)
	fmt.Printf("        args: %v\n", strings.Join(os.Args, " "))
	fmt.Printf(" working dir: %v\n", lo.Must(os.Getwd()))
	fmt.Printf("%s\n\n", version.Get())

	err := config.Init(opts)
	if err != nil {
		return err
	}

	services.Init()

	alarm.Init(alarm.Config{
		SecurityCode: "f58e734827ca11efbe5c525400a423ee",
		// Disable:      true,
		Zhiyan: &alarm.ZhiyanConfig{StringMark: "kateway_server"},
	})

	jaeger.Init(jaeger.Config{
		Web: "https://zhiyan.woa.com/apm_monitor",
		ZhiYan: &jaeger.ZhiYan{
			ID:        id,
			Env:       "prod",
			ProjectID: "14315",
		},
		Configuration: jaegerconfig.Configuration{
			ServiceName: "kateway-server",
			Reporter: &jaegerconfig.ReporterConfig{
				QueueSize:           1000,
				BufferFlushInterval: 1,
				LogSpans:            false,
				LocalAgentHostPort:  "trace.zhiyan.tencent-cloud.net:6831",
			},
			Tags: []opentracing.Tag{
				{
					Key:   "tps.tenant.id",
					Value: "4138#apm-log-bfd69ec5fdc2gab2#14315_109284___apm",
				},
			},
		},
	})

	sts.Init(config.Get().STS)

	readonlyConfig, err := conf.LoadConfigFromFile("conf/tops-readonly.yaml")
	if err != nil {
		return err
	}
	cluster2.Config = readonlyConfig
	common.InitUserService(readonlyConfig)

	misakaConfig, err := common.LoadConfigFromFile("conf/eks-tool-config.yaml")
	if err != nil {
		return err
	}
	cluster2.MisakaConfig = misakaConfig
	if misakaConfig.ServiceControllerImageRepo != "" {
		common.ServiceControllerImageRepo = misakaConfig.ServiceControllerImageRepo
	}
	if misakaConfig.IngressControllerImageRepo != "" {
		common.IngressControllerImageRepo = misakaConfig.IngressControllerImageRepo
	}

	return nil
}
