package api

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"runtime"
	"sync"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	"git.woa.com/kateway/pkg/app/version"

	"git.woa.com/kateway/kateway-server/api/kateway"
)

var (
	Client kateway.KatewayClient
	once   sync.Once
)

func init() {
	once.Do(func() {
		server := "***********:443" // cls-7m2mnrz0 default/kateway-server grpc
		v := os.Getenv("KATEWAY_GRPC_SERVER")
		if v != "" {
			server = v
		}

		conn, err := grpc.NewClient(server,
			grpc.WithTransportCredentials(insecure.NewCredentials()),
			grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(1024*1024*1024)))
		if err != nil {
			panic(fmt.Errorf("连接服务端失败: %s", err))
		}
		Client = kateway.NewKatewayClient(conn)
	})
}

func CheckUpgrade() error {
	if os.Getenv("KOPS_SKIP_UPGRADE") != "" {
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	checkUpgradeResponse, err := Client.CheckUpgrade(ctx, &kateway.CheckUpgradeRequest{
		Version: version.Get().GitVersion,
		Os:      runtime.GOOS,
	})
	if err != nil {
		return err
	}
	if checkUpgradeResponse.GetUrl() == "" {
		return nil
	}

	fmt.Printf("发现更新: %s => %s\n", version.Get().GitVersion, checkUpgradeResponse.GetVersion())
	fmt.Printf("下载地址: %s\n", checkUpgradeResponse.GetUrl())

	output := os.Getenv("HOME") + "/.local/bin/kops"
	tmp := os.Getenv("HOME") + "/.local/bin/kops.tmp"
	fmt.Printf("安装地址: %s\n", output)

	fmt.Printf("正在下载新版本 %s，请稍等...\n", checkUpgradeResponse.GetVersion())
	resp, err := http.Get(checkUpgradeResponse.GetUrl())
	if err != nil {
		return fmt.Errorf("下载失败: %w", err)
	}
	defer resp.Body.Close()

	fmt.Printf("写入到临时文件: %s\n", tmp)
	file, err := os.Create(tmp)
	if err != nil {
		return fmt.Errorf("创建文件失败: %w", err)
	}
	defer file.Close()

	// 将下载的内容写入文件
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return fmt.Errorf("写入文件失败: %w", err)
	}

	fmt.Println("添加可执行权限")
	err = os.Chmod(tmp, 0755)
	if err != nil {
		return fmt.Errorf("添加可执行权限失败: %w", err)
	}

	fmt.Println("检测新版本是否可以正常运行")
	cmd := exec.Command(tmp, "version")
	data, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("新版本执行失败: %w", err)
	}
	fmt.Printf("替换原有文件: %s => %s\n", tmp, output)
	os.Rename(tmp, output)

	fmt.Printf("新版本:\n%s", string(data))
	fmt.Println("升级成功，为保证最新逻辑生效，请再次运行本命令!")
	os.Exit(0)

	return nil
}
