package api

import (
	"testing"
)

func Test_cleanLog(t *testing.T) {
	type args struct {
		line string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			args: args{
				line: `I0121 15:44:20.638437       1 pkg domain/tencentapi/tencentapi.go:2024] APIRecord Kind=Service Name=anyserver Namespace=default Region=ap-shanghai API=CLB Action=BatchRegisterTargets Request={"LoadBalancerId":"lb-51sh3v5v","Targets":[{"ListenerId":"lbl-ayz2gy6f","Port":7000,"EniIp":"**********","Weight":10},{"ListenerId":"lbl-h3nzru1v","Port":4000,"EniIp":"**********","Weight":10}]} Response={"Response":null}`,
			},
			want: `I0121 15:44:20.638437 Region=ap-shanghai API=CLB Action=BatchRegisterTargets Request={"LoadBalancerId":"lb-51sh3v5v","Targets":[{"ListenerId":"lbl-ayz2gy6f","Port":7000,"EniIp":"**********","Weight":10},{"ListenerId":"lbl-h3nzru1v","Port":4000,"EniIp":"**********","Weight":10}]} Response={"Response":null}`,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := cleanLog(tt.args.line); got != tt.want {
				t.Errorf("cleanLog() = %v, want %v", got, tt.want)
			}
		})
	}
}
