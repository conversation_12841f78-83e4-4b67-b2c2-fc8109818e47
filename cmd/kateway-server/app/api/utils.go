package api

import (
	"errors"
	"fmt"
	"regexp"
	"strings"
)

type filter struct {
	name   string
	values []string
}

var (
	filterRegexEqual = regexp.MustCompile(`^([[:alpha:]]+)=(\S+)$`)
	filterRegexIn    = regexp.MustCompile(`^([[:alpha:]]+) +in +\((\S+( \S+)*)\)$`)
)

func parseFilter(s string) (*filter, error) {
	f := &filter{}
	equalMatches := filterRegexEqual.FindStringSubmatch(s)
	if len(equalMatches) == 0 {
		inMatches := filterRegexIn.FindStringSubmatch(s)
		if len(inMatches) == 0 {
			return nil, fmt.Errorf("invalid filter format %q", s)
		}
		f.name = inMatches[1]
		valuesRaw := inMatches[2]
		f.values = strings.Split(valuesRaw, " ")
		return f, nil
	}
	f.name = equalMatches[1]
	f.values = append(f.values, equalMatches[2])
	return f, nil
}

func parseFilters(ss []string) ([]filter, error) {
	filters := make([]filter, 0, len(ss))
	errs := []error{}
	for _, s := range ss {
		f, err := parseFilter(s)
		if err != nil {
			errs = append(errs, err)
			continue
		}
		filters = append(filters, *f)
	}
	return filters, errors.Join(errs...)
}
