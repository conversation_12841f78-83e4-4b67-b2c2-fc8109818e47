package api

import (
	"context"
	"fmt"
	"strings"

	"github.com/samber/lo"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/kateway-server/api/pb"
	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/task"
	pkgtask "git.woa.com/kateway/kateway-server/pkg/task"
)

type StarShipServer struct {
	pb.UnimplementedTPTaskEngineServer
}

func NewStarShipServer() *StarShipServer {
	return &StarShipServer{}
}

func (s *StarShipServer) CreateTask(ctx context.Context, req *pb.TPCreateTaskRequest) (*pb.TPCreateTaskReply, error) {
	klog.Infof("CreateTask: %+v", req)

	cluster := req.GetClusterId()

	allow, err := allowUpdate(ctx, cluster, req.GetToken().GetToken())
	if err != nil {
		return nil, err
	}
	if !allow {
		return nil, fmt.Errorf("不允许操作该集群")
	}

	var newTask any

	switch req.GetAction() {
	case "precheck":
		newTask = task.PreCheck{
			ClusterID: req.GetClusterId(),
			Name:      strings.TrimSuffix(req.GetWorkload(), "-controller"),
			Version:   req.GetImageTag(),
			User:      req.GetToken().GetUsername(),
			Token:     req.GetToken().GetToken(),
		}
	case "upgrade":
		newTask = task.Upgrade{
			ClusterID: req.GetClusterId(),
			Name:      strings.TrimSuffix(req.GetWorkload(), "-controller"),
			Version:   req.GetImageTag(),
			User:      req.GetToken().GetUsername(),
			Token:     req.GetToken().GetToken(),
		}
	case "postcheck":
		newTask = task.PostCheck{
			ClusterID: req.GetClusterId(),
			Name:      strings.TrimSuffix(req.GetWorkload(), "-controller"),
			Version:   req.GetImageTag(),
			User:      req.GetToken().GetUsername(),
			Token:     req.GetToken().GetToken(),
		}
	default:
		return nil, fmt.Errorf("invalid action %q", req.GetAction())
	}

	id, err := services.Get().Task().Create(ctx, newTask, pkgtask.WithCreator(req.GetToken().GetUsername()))
	if err != nil {
		return nil, err
	}

	rsp := &pb.TPCreateTaskReply{
		TaskId: id,
		Code:   0,
		Reason: "",
	}

	klog.Infof("CreateTask: %+v", rsp)
	return rsp, nil
}

func (s *StarShipServer) DescribeTask(ctx context.Context, req *pb.TPDescribeTaskRequest) (*pb.TPDescribeTaskReply, error) {
	klog.Infof("DescribeTask: %+v", req)

	t, err := services.Get().Task().GetByID(ctx, req.GetTaskId())
	if err != nil {
		return nil, err
	}

	rsp := &pb.TPDescribeTaskReply{
		TaskId: t.ID,
		Status: "processing",
		Code:   0,
		Reason: t.LastError,
	}
	if t.IsFinished() {
		rsp.Status = "done"
		if t.Type == "PreCheck" || t.Type == "Upgrade" {
			dryRunStatus := task.GetPreCheckStatus(t)
			for _, record := range dryRunStatus.Records {
				rsp.Risks = append(rsp.Risks, &pb.TPRisk{
					AppName:  lo.Ternary(strings.Contains(t.Input, "service"), "service-controller", "ingress-controller"),
					Name:     record.Action,
					Resrouce: fmt.Sprintf("%s/%s/%s", record.Resource.Type, record.Resource.Namespace, record.Resource.Name),
					Code:     "FAILED",
					Detail:   record.Request,
					Level:    "fatal",
				})
			}
			if len(rsp.Risks) == 0 {
				rsp.Risks = append(rsp.Risks, &pb.TPRisk{
					AppName: lo.Ternary(strings.Contains(t.Input, "service"), "service-controller", "ingress-controller"),
					Name:    "资源级检查",
					Code:    "PASS",
					Detail:  "资源级检查通过",
					Level:   "info",
				})
			}
		}
	}
	if t.LastError != "" {
		rsp.Code = 1
		rsp.Reason = t.LastError
	}

	klog.Infof("DescribeTask: %+v", rsp)

	return rsp, nil
}

func (s *StarShipServer) CancelTask(ctx context.Context, req *pb.TPCancelTaskRequest) (*pb.TPCancelTaskReply, error) {
	klog.Infof("CancelTask: %+v", req)

	rsp := &pb.TPCancelTaskReply{
		TaskId: req.GetTaskId(),
		Code:   0,
		Reason: "",
	}

	err := services.Get().Task().Abort(ctx, req.GetTaskId())
	if err != nil {
		rsp.Code = 1
		rsp.Reason = err.Error()
	}

	klog.Infof("CancelTask: %+v", rsp)
	return rsp, nil
}
