<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TKE 接入层运营平台 - 集群检索</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/tech-theme.css">
</head>
<body>
    <div class="page-wrapper">
        <!-- 通用头部 -->
        <div id="header-container"></div>

        <!-- 主内容区域 -->
        <main class="main-content">
            <div class="search-container">
                <h1 class="text-center">集群检索</h1>
                <h3 class="text-center mb-4">一键获取集群相关排障信息</h3>

                <form id="clusterForm" class="mb-4">
                    <div class="form-group">
                        <input type="text" id="clusterID" name="clusterID" class="form-control" placeholder="请输入集群ID" required>
                    </div>

                    <button type="button" class="btn btn-block" onclick="searchCluster()">
                        <i class="bi bi-search"></i> 查询集群信息
                    </button>
                </form>
            </div>
        </main>

        <!-- 通用页脚 -->
        <div id="footer-container"></div>
    </div>

    <script src="/scripts.js"></script>
    <script src="/k8s-effects.js"></script>
    <script src="/page-loader.js"></script>
    <script>
        function searchCluster() {
            const clusterIDInput = document.getElementById('clusterID');
            const clusterID = clusterIDInput.value.trim();

            if (!clusterID) {
                showNotification('请输入集群ID', 'warning');
                clusterIDInput.focus();
                return;
            }

            const btn = document.querySelector('.btn');
            showLoading(btn, '查询中...');

            const url = `/inspection/cluster/report/get?clusterID=${encodeURIComponent(clusterID)}&filter=clb`;

            // 使用AI加载动画
            const hideLoading = showAILoadingEffect('正在查询集群信息...');

            // 导航到查询结果页面，延迟模拟AI处理
            setTimeout(() => {
                window.location.href = url;
            }, 800);
        }
    </script>
</body>
</html>
