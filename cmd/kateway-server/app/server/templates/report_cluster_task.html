<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TKE 接入层运营平台 - 集群任务信息</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/tech-theme.css">
    <link rel="stylesheet" href="/report-styles.css">
</head>
<body>
    <div class="page-wrapper">

        <main class="main-content">
            <div class="container">
                <h1>巡检任务列表</h1>
                <h2>任务统计</h2>
                <table>
                    <tr>
                        <th>信息类型</th>
                        <th>详情</th>
                    </tr>
                    <tr>
                        <td>任务总数</td>
                        <td>{{ .TotalTaskCount }}</td>
                    </tr>
                    <tr>
                        <td>运行中任务数</td>
                        <td>{{ .TotalRunningTaskCount }}</td>
                    </tr>
                    <tr>
                        <td>已完成任务数</td>
                        <td>{{ .TotalDoneTaskCount }}</td>
                    </tr>
                    <tr>
                        <td>异常退出任务数</td>
                        <td>{{ .TotalTerminatedTaskCount }}</td>
                    </tr>
                </table>

                <h2>任务列表</h2>
                <h3>当前组件内部正在运行的健康检查任务列表。</h3>
                <table>
                    <tr>
                        <th>任务ID</th>
                        <th>集群ID</th>
                        <th>任务状态</th>
                        <th>当前同步次数</th>
                        <th>最大同步次数</th>
                        <th>同步间隙</th>
                        <th>创建时间</th>
                        <th>更新时间</th>
                        <th>结束时间</th>
                        <th>状态信息</th>
                    </tr>
                    {{ range $code, $clb := .TaskList }}
                        <tr>
                            <td>{{ $clb.TaskID }}</td>
                            <td><a href='/inspection/cluster/report/get?clusterID={{ $clb.InstanceID }}&refreshCache=true'>{{ $clb.InstanceID }}</a></td>
                            <td>{{ $clb.State }}</td>
                            <td>{{ $clb.CurSyncTimes }}</td>
                            <td>{{ $clb.MaxSyncTimes }}</td>
                            <td>{{ $clb.SyncInterval }}</td>
                            <td>{{ $clb.CreatedAt }}</td>
                            <td>{{ $clb.UpdatedAt }}</td>
                            <td>{{ $clb.FinishedAt }}</td>
                            <td>{{ $clb.Reason }}</td>
                        </tr>
                    {{ end }}
                </table>

            </div>
        </main>

        <!-- 通用页脚 -->
        <div id="footer-container"></div>
    </div>

    <script src="/k8s-effects.js"></script>
    <script src="/page-loader.js"></script>
    <script src="/report-scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 如果任务还在运行中，自动刷新页面
            {{ if eq .State "RUNNING" }}
            setTimeout(function() {
                window.location.reload();
            }, 5000);
            {{ end }}
        });
    </script>
</body>
</html>
