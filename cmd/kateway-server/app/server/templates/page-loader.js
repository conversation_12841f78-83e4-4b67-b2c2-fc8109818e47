/**
 * TKE 接入层运营平台 - 页面加载脚本
 * 用于动态加载头部和页脚组件
 */

// 检查是否在iframe中或者是否已经存在头部
function isInIframeOrHasHeader() {
    try {
        // 检查是否在iframe中
        if (window.self !== window.top) {
            return true;
        }

        // 检查是否已经存在头部
        const existingHeader = document.querySelector('.tech-header');
        return existingHeader !== null;
    } catch (e) {
        // 如果无法访问parent，则可能是跨域iframe
        return true;
    }
}

// 检查是否已经存在页脚
function hasFooter() {
    return document.querySelector('footer') !== null;
}

// 动态加载头部和页脚
function loadPageComponents(activePage) {
    const skipHeader = isInIframeOrHasHeader();

    // 如果不需要跳过头部，则加载头部
    if (!skipHeader) {
        // 加载头部
        fetch('/header.html')
            .then(response => response.text())
            .then(html => {
                const headerContainer = document.getElementById('header-container');
                if (headerContainer) {
                    headerContainer.innerHTML = html;
                    // 激活当前页面的导航链接
                    const navLink = document.getElementById('nav-' + activePage);
                    if (navLink) navLink.classList.add('active');
                }
            })
            .catch(error => {
                console.error('加载头部失败:', error);
            });
    } else {
        // 如果需要跳过头部，则隐藏头部容器
        const headerContainer = document.getElementById('header-container');
        if (headerContainer) {
            headerContainer.style.display = 'none';
        }
    }

    // 检查是否已经存在页脚
    const skipFooter = hasFooter();

    // 如果不需要跳过页脚，则加载页脚
    if (!skipFooter) {
        fetch('/footer.html')
            .then(response => response.text())
            .then(html => {
                const footerContainer = document.getElementById('footer-container');
                if (footerContainer) {
                    footerContainer.innerHTML = html;
                }
            })
            .catch(error => {
                console.error('加载页脚失败:', error);
            });
    } else {
        // 如果需要跳过页脚，则隐藏页脚容器
        const footerContainer = document.getElementById('footer-container');
        if (footerContainer) {
            footerContainer.style.display = 'none';
        }
    }
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 根据当前页面URL确定激活的导航项
    const path = window.location.pathname;
    let activePage = 'home';

    if (path.includes('cluster-console') || path.includes('cluster/report')) {
        activePage = 'cluster';
    } else if (path.includes('clb-console') || path.includes('clb/report')) {
        activePage = 'clb';
    } else if (path.includes('resource-console') || path.includes('resource/report')) {
        activePage = 'resource';
    } else if (path.includes('scan-console')) {
        activePage = 'scan';
    } else if (path.includes('risk-console') || path.includes('risk/report')) {
        activePage = 'risk';
    } else if (path.includes('task-console') || path.includes('task/report')) {
        activePage = 'task';
    }

    // 加载页面组件
    loadPageComponents(activePage);
});
