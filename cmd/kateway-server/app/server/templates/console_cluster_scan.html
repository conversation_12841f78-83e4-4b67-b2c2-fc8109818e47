<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TKE 接入层运营平台 - 风险扫描</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/tech-theme.css">
    <style>

        /* 添加 AI 波纹按钮效果 */
        .btn {
            position: relative;
            overflow: hidden;
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* AI 节点连接点样式 */
        .connection-node {
            position: absolute;
            width: 8px;
            height: 8px;
            background-color: var(--primary-color);
            border-radius: 50%;
            opacity: 0.6;
            z-index: 11;
            box-shadow: 0 0 8px var(--primary-color);
            animation: node-pulse 3s infinite;
        }

        @keyframes node-pulse {
            0%, 100% { transform: scale(1); opacity: 0.6; }
            50% { transform: scale(1.5); opacity: 0.9; }
        }

        /* AI加载动画 */
        .ai-loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .ai-loading-container.active {
            opacity: 1;
        }

        .ai-loading-effect {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .ai-loading-brain {
            position: relative;
            width: 120px;
            height: 120px;
        }

        .ai-node {
            position: absolute;
            width: 12px;
            height: 12px;
            background: var(--primary-color);
            border-radius: 50%;
            box-shadow: 0 0 10px var(--primary-color);
        }

        .ai-node.n1 { top: 20%; left: 20%; animation: pulse 1.5s infinite alternate; }
        .ai-node.n2 { top: 20%; right: 20%; animation: pulse 1.7s infinite alternate; }
        .ai-node.n3 { top: 50%; left: 10%; animation: pulse 1.9s infinite alternate; }
        .ai-node.n4 { top: 50%; right: 10%; animation: pulse 2.1s infinite alternate; }
        .ai-node.n5 { bottom: 20%; left: 30%; animation: pulse 1.3s infinite alternate; }
        .ai-node.n6 { bottom: 20%; right: 30%; animation: pulse 1.6s infinite alternate; }
        .ai-node.n7 { top: 50%; left: 50%; transform: translate(-50%, -50%); animation: pulse 1.8s infinite alternate; }

        .ai-connection {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), transparent);
            animation: connection-glow 2s infinite alternate;
        }

        .ai-connection.c1 { width: 30px; top: 22%; left: 26%; transform: rotate(5deg); }
        .ai-connection.c2 { width: 40px; top: 30%; right: 26%; transform: rotate(-45deg); }
        .ai-connection.c3 { width: 35px; top: 50%; left: 18%; }
        .ai-connection.c4 { width: 35px; top: 50%; right: 18%; }
        .ai-connection.c5 { width: 30px; bottom: 30%; left: 35%; transform: rotate(45deg); }
        .ai-connection.c6 { width: 30px; bottom: 30%; right: 35%; transform: rotate(-45deg); }

        @keyframes connection-glow {
            0% { opacity: 0.3; }
            100% { opacity: 0.8; }
        }

        .ai-loading-text {
            margin-top: 20px;
            color: white;
            font-size: 16px;
            animation: pulse 1.5s infinite alternate;
        }
    </style>
</head>
<body>
    <div class="page-wrapper">
        <!-- 通用头部 -->
        <div id="header-container"></div>

        <!-- 主内容区域 -->
        <main class="main-content">
            <div class="search-container">
                <h1 class="text-center">风险扫描</h1>
                <h3 class="text-center mb-4">一键扫描集群当前 CLB 风险</h3>

                <form id="scanForm" class="mb-4">
                    <div class="form-group">
                        <input type="text" id="clusterID" name="clusterID" class="form-control" placeholder="请输入集群ID" required>
                    </div>

                    <button type="button" class="btn btn-block" onclick="scanCluster()">
                        <i class="bi bi-scan"></i> 扫描集群风险
                    </button>
                </form>
            </div>
        </main>

        <!-- 通用页脚 -->
        <div id="footer-container"></div>
    </div>

    <script src="/scripts.js"></script>
    <script src="/k8s-effects.js"></script>
    <script src="/page-loader.js"></script>
    <script>
        function scanCluster() {
            const clusterIDInput = document.getElementById('clusterID');
            const clusterID = clusterIDInput.value.trim();

            if (!clusterID) {
                showNotification('请输入集群ID', 'warning');
                clusterIDInput.focus();
                return;
            }

            const btn = document.querySelector('.btn');
            showLoading(btn, '扫描中...');

            const url = `/inspection/cluster/report/get?clusterID=${encodeURIComponent(clusterID)}&filter=risk`;

            // 使用AI加载动画
            const hideLoading = showAILoadingEffect('正在扫描集群风险...');

            // 导航到查询结果页面
            setTimeout(() => {
                window.location.href = url;
            }, 800);
        }

        // 支持回车提交
        document.getElementById('clusterID').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                scanCluster();
            }
        });
    </script>
</body>
</html>
