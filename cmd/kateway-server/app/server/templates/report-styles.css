/* TKE 接入层运营平台 - 报告页面样式 */
:root {
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --primary-light: rgba(59, 130, 246, 0.1);
  --primary-gradient: linear-gradient(135deg, #3b82f6, #2563eb);

  --secondary-color: #1e293b;
  --secondary-light: #334155;
  --secondary-dark: #0f172a;
  --secondary-gradient: linear-gradient(135deg, #1e293b, #0f172a);

  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #0ea5e9;

  --text-color: #334155;
  --text-light: #64748b;
  --text-lighter: #94a3b8;

  --background-light: #f8fafc;
  --background-white: #ffffff;
  --border-color: #e2e8f0;
  --shadow-color: rgba(0, 0, 0, 0.08);

  --card-border-radius: 16px;
  --input-border-radius: 10px;
  --button-border-radius: 10px;
  --transition-speed: 0.3s;

  --font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;

  --header-height: 68px;
  --content-max-width: 1440px;
}

[data-theme="dark"] {
  --primary-color: #3b82f6;
  --primary-hover: #60a5fa;
  --primary-light: rgba(59, 130, 246, 0.15);

  --secondary-color: #1e1e2d;
  --secondary-light: #2a2a3c;
  --secondary-dark: #15151f;
  --secondary-gradient: linear-gradient(135deg, #1e1e2d, #15151f);

  --text-color: #e2e8f0;
  --text-light: #cbd5e1;
  --text-lighter: #94a3b8;

  --background-light: #0f172a;
  --background-white: #1e293b;
  --border-color: #334155;
  --shadow-color: rgba(0, 0, 0, 0.3);
}

body {
  font-family: var(--font-family);
  margin: 0;
  padding: 0;
  background-color: var(--background-light);
  color: var(--text-color);
  line-height: 1.6;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  transition: background-color var(--transition-speed), color var(--transition-speed);
}

/* 背景网格装饰 */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(to right, var(--border-color) 1px, transparent 1px),
    linear-gradient(to bottom, var(--border-color) 1px, transparent 1px);
  background-size: 50px 50px;
  opacity: 0.07;
  z-index: -15;
  pointer-events: none;
}

/* 页面包装器 */
.page-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
  z-index: 1;
}

/* 导航栏 */
.navbar {
  height: var(--header-height);
  padding: 0 2rem;
  display: flex;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 1000;
  background: var(--secondary-gradient);
  box-shadow: 0 4px 20px var(--shadow-color);
  transition: all var(--transition-speed);
}

.navbar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: var(--content-max-width);
  margin: 0 auto;
}

.navbar-brand {
  font-size: 1.3rem;
  font-weight: 500;
  color: white;
  text-decoration: none;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
  margin-right: 2rem;
}

.navbar-brand i {
  margin-right: 0.75rem;
  font-size: 1.5rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.5));
}

.nav-links {
  display: flex;
  gap: 0.75rem;
}

.nav-link {
  color: rgba(255, 255, 255, 0.85);
  text-decoration: none;
  padding: 0.65rem 1rem;
  border-radius: 8px;
  transition: all var(--transition-speed);
  font-weight: 400;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity var(--transition-speed);
  z-index: -1;
  border-radius: 8px;
  pointer-events: none;
}

.nav-link.active::before {
  opacity: 1;
}

.nav-link:hover::before {
  opacity: 0.7;
}

.nav-link:hover, .nav-link.active {
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.nav-link i {
  margin-right: 0.5rem;
  font-size: 1.05rem;
}

.nav-spacer {
  margin-left: auto;
}

.theme-toggle {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  margin-left: 0.75rem;
  transition: all var(--transition-speed);
  position: relative;
  overflow: hidden;
}

.theme-toggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity var(--transition-speed);
  z-index: -1;
  border-radius: 50%;
  pointer-events: none;
}

.theme-toggle:hover::before {
  opacity: 1;
}

.theme-toggle:hover {
  transform: rotate(12deg);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
}

/* 头部导航 */
.top-bar {
  width: 100%;
  height: 64px;
  padding: 0 20px;
  background: var(--secondary-gradient);
  display: flex;
  align-items: center;
  box-shadow: 0 4px 20px var(--shadow-color);
  z-index: 100;
  position: sticky;
  top: 0;
}

.top-bar a {
  text-decoration: none;
}

.top-bar-brand {
  display: flex;
  align-items: center;
  font-size: 1.3rem;
  font-weight: 500;
  color: white;
  margin-right: 2rem;
}

.top-bar-brand i {
  margin-right: 0.75rem;
  font-size: 1.5rem;
  color: var(--primary-color);
}

.spacer {
  margin-left: auto;
}

.top-bar-link {
  padding: 8px 16px;
  margin-left: 10px;
  color: white;
  background-color: var(--primary-color);
  border: none;
  border-radius: var(--button-border-radius);
  text-decoration: none;
  transition: all var(--transition-speed);
  display: inline-flex;
  align-items: center;
  font-weight: 500;
}

.top-bar-link:hover {
  background-color: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(59, 130, 246, 0.3);
}

.top-bar-link i {
  margin-right: 0.5rem;
}

.export-button {
  margin-left: 10px;
  font-size: 0.9em;
}

/* 主内容样式 */
.container {
  max-width: 1200px;
  width: 90%;
  margin: 40px auto;
  background: var(--background-white);
  padding: 2rem;
  border-radius: var(--card-border-radius);
  box-shadow: 0 10px 30px var(--shadow-color), 0 1px 3px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  border: 1px solid var(--border-color);
  z-index: 10;
  flex: 1;
}

/* Kubernetes 装饰元素 */
.container::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 150px;
  height: 150px;
  background: var(--primary-gradient);
  opacity: 0.05;
  border-radius: 0 0 0 100%;
  z-index: -1;
  pointer-events: none;
}

/* 标题样式 */
h1, h2, h3 {
  color: var(--text-color);
  line-height: 1.3;
  font-weight: 500;
  position: relative;
}

h1 {
  font-size: 2rem;
  margin-bottom: 1.5rem;
  display: inline-block;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  border-bottom: none;
  padding-bottom: 0;
}

h2 {
  font-size: 1.5rem;
  margin: 2rem 0 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-light);
}

h3 {
  font-size: 1.2rem;
  font-weight: 500;
  margin: 1.5rem 0 1rem;
  color: var(--text-color);
}

/* 表格样式 */
table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin: 1.5rem 0;
  background-color: var(--background-white);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px var(--shadow-color);
}

th, td {
  border: none;
  text-align: left;
  padding: 1rem 1.2rem;
}

th {
  background: var(--primary-gradient);
  color: white;
  font-weight: 500;
  position: relative;
}

td {
  border-bottom: 1px solid var(--border-color);
}

tr:last-child td {
  border-bottom: none;
}

tr:nth-child(even) {
  background-color: rgba(0, 0, 0, 0.02);
}

tbody tr:hover {
  background-color: var(--primary-light);
}

/* 代码样式 */
pre {
  background-color: var(--secondary-color);
  color: white;
  padding: 1.25rem;
  border-radius: var(--card-border-radius);
  overflow-x: auto;
  margin-bottom: 2rem;
  max-height: 500px;
  box-shadow: 0 4px 10px var(--shadow-color);
  position: relative;
}

code {
  font-family: 'Courier New', Courier, monospace;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: #e2e8f0;
  line-height: 1.6;
  font-size: 0.9rem;
}

/* YAML 语法高亮 */
.yaml-keyword {
  color: #ff79c6;
  font-weight: bold;
}

.yaml-string {
  color: #f1fa8c;
}

.yaml-number {
  color: #bd93f9;
}

.yaml-boolean {
  color: #ff5555;
}

.yaml-null {
  color: #ff5555;
  font-style: italic;
}

.yaml-comment {
  color: #6272a4;
  font-style: italic;
}

.yaml-indent {
  color: #888;
}

/* 代码块工具栏 */
.code-toolbar {
  position: absolute;
  top: 5px;
  right: 10px;
  display: flex;
  gap: 8px;
}

.code-toolbar button {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: #fff;
  border-radius: 4px;
  padding: 3px 8px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s;
}

.code-toolbar button:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 资源描述区域样式修复 */
.resource-description {
  background-color: var(--background-white);
  color: var(--text-color);
  padding: 1.25rem;
  border-radius: var(--card-border-radius);
  margin-bottom: 2rem;
  border: 1px solid var(--border-color);
  font-size: 0.95rem;
  line-height: 1.6;
}

.resource-metadata {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: var(--primary-light);
  border-radius: var(--card-border-radius);
}

.metadata-item {
  display: flex;
  flex-direction: column;
}

.metadata-label {
  font-weight: 500;
  font-size: 0.85rem;
  color: var(--text-light);
  margin-bottom: 0.25rem;
}

.metadata-value {
  font-family: 'Courier New', Courier, monospace;
  font-size: 0.9rem;
  word-break: break-all;
}

/* 暗色模式调整 */
[data-theme="dark"] pre {
  background-color: #1a1b26;
}

[data-theme="dark"] .resource-description {
  background-color: var(--background-white);
  color: var(--text-color);
}

/* 链接样式 */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-speed);
}

a:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

/* 页脚 */
footer {
  text-align: center;
  padding: 1.5rem;
  height: 60px;
  color: var(--text-light);
  background: var(--background-white);
  margin-top: auto;
  border-top: 1px solid var(--border-color);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0.3;
}

footer a {
  color: var(--primary-color);
  font-weight: 500;
}

/* 徽章样式 */
.badge {
  display: inline-block;
  padding: 0.35em 0.65em;
  font-size: 0.75em;
  font-weight: 500;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 50rem;
  color: #fff;
  background-color: var(--primary-color);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.badge-success {
  background: linear-gradient(135deg, #10b981, #059669);
}

.badge-warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: #ffffff;
}

.badge-danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.badge-info {
  background: linear-gradient(135deg, #0ea5e9, #0284c7);
}

/* 图表容器 */
.chart-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 20px;
  margin: 2rem 0;
}

.chart-half {
  background: var(--background-white);
  border-radius: var(--card-border-radius);
  box-shadow: 0 8px 20px var(--shadow-color);
  border: 1px solid var(--border-color);
  padding: 1.5rem;
  flex: 1 1 45%;
  min-width: 300px;
  height: 300px;
  position: relative;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .container {
    width: 95%;
    padding: 1.5rem;
  }

  h1 {
    font-size: 1.8rem;
  }

  h2 {
    font-size: 1.4rem;
  }

  .chart-half {
    flex: 1 1 100%;
  }

  .navbar {
    padding: 0 1.5rem;
  }
}

@media (max-width: 768px) {
  .container {
    width: 100%;
    margin: 20px auto;
    padding: 1.2rem;
    border-radius: 0;
  }

  h1 {
    font-size: 1.6rem;
  }

  h2 {
    font-size: 1.3rem;
  }

  th, td {
    padding: 0.75rem 1rem;
  }

  .top-bar {
    padding: 0 15px;
  }

  .top-bar-brand span {
    display: none;
  }

  .navbar {
    padding: 0 1rem;
    height: auto;
    min-height: var(--header-height);
  }

  .navbar-container {
    flex-wrap: wrap;
  }

  .nav-links {
    order: 3;
    width: 100%;
    margin-top: 1rem;
    margin-bottom: 1rem;
    justify-content: space-between;
    overflow-x: auto;
    padding-bottom: 0.5rem;
  }

  .nav-link {
    padding: 0.5rem 0.75rem;
  }

  .nav-link span {
    display: none;
  }

  .nav-link i {
    margin-right: 0;
    font-size: 1.2rem;
  }
}

/* 报告页面基础样式 */
/* 不再引入 styles.css，所有样式已合并到 tech-theme.css */

body {
  background-color: var(--background-light);
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 2rem auto;
  padding: 2.5rem;
  background-color: var(--background-white);
  border-radius: var(--card-border-radius);
  box-shadow: 0 10px 30px var(--shadow-color);
  transition: all var(--transition-speed);
}

/* 不再引入 styles.css，所有样式已合并到 tech-theme.css */

/* 报告页面标题区域 */
.header {
  margin-bottom: 2rem;
  margin-top: 1rem;
  text-align: center;
  padding: 1.5rem;
  background: var(--background-white);
  border-radius: var(--card-border-radius);
  box-shadow: 0 4px 15px var(--shadow-color);
  position: relative;
  overflow: hidden;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--primary-gradient);
}

h2 {
  margin-top: 2.5rem;
  margin-bottom: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

h3 {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  color: var(--text-light);
}

table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 2rem;
  overflow-x: auto;
  display: block;
  white-space: nowrap;
}

table {
  width: 100%;
  display: table;
}

@media (max-width: 992px) {
  table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
  }

  .container {
    margin: 1rem auto;
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .container {
    margin: 0.5rem auto;
    padding: 1rem;
    border-radius: calc(var(--card-border-radius) / 1.5);
  }

  h1 {
    font-size: 1.5rem;
  }

  h2 {
    font-size: 1.3rem;
    margin-top: 2rem;
  }

  h3 {
    font-size: 1rem;
  }

  table, th, td {
    font-size: 0.85rem;
  }

  th, td {
    padding: 0.5rem 0.75rem;
  }

  /* 改进图表在移动设备上的显示 */
  .chart-container {
    flex-direction: column;
    height: auto;
  }

  .chart-half {
    width: 100%;
    height: 300px;
    margin-bottom: 1.5rem;
  }
}

/* 表格通用样式 */
thead {
  background: var(--primary-gradient);
  color: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

th, td {
  padding: 1rem 1.25rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

tbody tr:nth-child(even) {
  background-color: rgba(0, 0, 0, 0.02);
}

tbody tr:hover {
  background-color: var(--primary-light);
}

/* 图表容器样式 */
.chart-container {
  display: flex;
  margin: 2rem 0;
  height: 400px;
  gap: 2rem;
}

.chart-half {
  width: 50%;
  height: 100%;
  background-color: var(--background-white);
  border-radius: var(--card-border-radius);
  padding: 1rem;
  box-shadow: 0 5px 15px var(--shadow-color);
  position: relative;
}

/* 导出按钮样式 */
.export-button {
  display: inline-flex;
  align-items: center;
  background-color: var(--primary-color);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: var(--button-border-radius);
  text-decoration: none;
  font-size: 0.9rem;
  transition: all var(--transition-speed);
}

.export-button:hover {
  background-color: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(59, 130, 246, 0.3);
}

.export-button i {
  margin-right: 0.5rem;
}

/* 预格式化代码区域 */
pre {
  background-color: var(--secondary-light);
  color: white;
  padding: 1.25rem;
  border-radius: var(--card-border-radius);
  overflow-x: auto;
  margin-bottom: 2rem;
}

code {
  font-family: 'Courier New', Courier, monospace;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 标签和链接样式 */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-speed);
}

a:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}
