<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TKE 接入层运营平台 - 集群扫描报告</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/tech-theme.css">
    <link rel="stylesheet" href="/report-styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="page-wrapper">

        <main class="main-content">
            <div class="container">
                <div class="header">
                    <h1>集群风险报告</h1>
                </div>

                <!-- 添加缺失的图表容器 -->
                <div class="chart-container">
                    <div id="riskLevelChartContainer" class="chart-half">
                        <canvas id="riskLevelChart"></canvas>
                    </div>
                    <div id="riskTypeChartContainer" class="chart-half">
                        <canvas id="riskTypeChart"></canvas>
                    </div>
                </div>

                <h2>基本信息</h2>
                <table>
                    <tr>
                        <th>信息类型</th>
                        <th>详情</th>
                    </tr>
                    <tr>
                        <td>APPID</td>
                        <td>{{ .AppID }}</td>
                    </tr>
                    <tr>
                        <td>集群 ID</td>
                        <td>{{ .ClusterID }}</td>
                    </tr>
                    <tr>
                        <td>集群名称</td>
                        <td>{{ .ClusterName }}</td>
                    </tr>
                    <tr>
                        <td>集群地域</td>
                        <td>{{ .ClusterRegion }}</td>
                    </tr>
                    <tr>
                        <td>集群类型</td>
                        <td>{{ .ClusterType }}</td>
                    </tr>
                    <tr>
                        <td>MetaClusterID</td>
                        <td>{{ .MetaClusterID }}</td>
                    </tr>
                    <tr>
                        <td>集群描述</td>
                        <td>{{ .Description }}</td>
                    </tr>
                    <tr>
                        <td>集群状态</td>
                        <td>{{ .State }}</td>
                    </tr>
                    <tr>
                        <td>报告过期时间</td>
                        <td>{{ .ExpireAt }}</td>
                    </tr>
                </table>

                <h2>网络信息</h2>
                <table>
                    <tr>
                        <th>信息类型</th>
                        <th>详情</th>
                    </tr>
                    <tr>
                        <td>K8S版本</td>
                        <td>{{ .K8SVersion }}</td>
                    </tr>
                    <tr>
                        <td>VPCID</td>
                        <td>{{ .VpcID }}</td>
                    </tr>
                    <tr>
                        <td>子网ID</td>
                        <td>{{ .SubnetID }}</td>
                    </tr>
                    <tr>
                        <td>ServiceCIDR</td>
                        <td>{{ .ServiceCIDR }}</td>
                    </tr>
                    <tr>
                        <td>网络模式</td>
                        <td>{{ .NetworkType }}</td>
                    </tr>
                    <tr>
                        <td>kube-proxy转发模式</td>
                        <td>{{ .KubeProxyMode }}</td>
                    </tr>
                </table>

                <h2>Service Controller 组件信息</h2>
                <table>
                    <tr>
                        <th>信息类型</th>
                        <th>详情</th>
                    </tr>
                    <tr>
                        <td>镜像信息</td>
                        <td>{{ .ServiceControllerImage }}</td>
                    </tr>
                    <tr>
                        <td>镜像版本</td>
                        <td>{{ .ServiceControllerVersion }}</td>
                    </tr>
                    <tr>
                        <td>副本数</td>
                        <td>{{ .ServiceExpectReplicas }}</td>
                    </tr>
                    <tr>
                        <td>可用副本数</td>
                        <td>{{ .ServiceAvailableReplicas }}</td>
                    </tr>
                    <tr>
                        <td>指定参数</td>
                        <td>{{ .ServiceArgs }}</td>
                    </tr>
                    <tr>
                        <td>镜像拉取策略</td>
                        <td>{{ .ServiceImagePullPolicy }}</td>
                    </tr>
                    <tr>
                        <td>ConfigMap参数</td>
                        <td>{{ .ServiceConfigMap }}</td>
                    </tr>
                </table>

                <h2>Ingress Controller 组件信息</h2>
                <table>
                    <tr>
                        <th>信息类型</th>
                        <th>详情</th>
                    </tr>
                    <tr>
                        <td>镜像信息</td>
                        <td>{{ .IngressControllerImage }}</td>
                    </tr>
                    <tr>
                        <td>镜像版本</td>
                        <td>{{ .IngressControllerVersion }}</td>
                    </tr>
                    <tr>
                        <td>副本数</td>
                        <td>{{ .IngressExpectReplicas }}</td>
                    </tr>
                    <tr>
                        <td>可用副本数</td>
                        <td>{{ .IngressAvailableReplicas }}</td>
                    </tr>
                    <tr>
                        <td>指定参数</td>
                        <td>{{ .IngressArgs }}</td>
                    </tr>
                    <tr>
                        <td>镜像拉取策略</td>
                        <td>{{ .IngressImagePullPolicy }}</td>
                    </tr>
                    <tr>
                        <td>ConfigMap参数</td>
                        <td>{{ .IngressConfigMap }}</td>
                    </tr>
                </table>

                <h2>周边系统</h2>
                <table>
                <tr>
                    <td>集群OSS信息</td>
                    <td><a href="{{ .References.OSSCluster }}">查看详情</a></td>
                </tr>
                <tr>
                    <td>用户OSS信息</td>
                    <td><a href="{{ .References.OSSUser }}">查看详情</a></td>
                </tr>
                <tr>
                    <td>用户CLB配额</td>
                    <td><a href="{{ .References.Quota }}">查看详情</a></td>
                </tr>
                <tr>
                    <td>service-controller 监控</td>
                    <td><a href="{{ .References.MonitorServiceController }}">查看详情</a></td>
                </tr>
                <tr>
                    <td>托管组件监控</td>
                    <td><a href="{{ .References.MonitorMetaComponent }}">查看详情</a></td>
                </tr>
                </table>

                <h2>接入层资源统计</h2>
                <h3>CLB 资源统计</h2>
                    <table>
                        <tr>
                            <th>统计维度</th>
                            <th>资源个数</th>
                        </tr>
                        <tr>
                            <td>CLB 总数</td>
                            <td>{{ .Statistics.CLB.TotalCount }}</td>
                        </tr>
                        <tr>
                            <td>流量不通 CLB（健康检查全失败）</td>
                            <td>{{ .Statistics.CLB.TotalUnhealthCount }}</td>
                        </tr>
                        <tr>
                            <td>流量不通 CLB（权重全为0）</td>
                            <td>{{ .Statistics.CLB.TotalZeroWeightCount }}</td>
                        </tr>
                        <tr>
                            <td>闲置 CLB（CLB 存在但没有注册 RS）</td>
                            <td>{{ .Statistics.CLB.TotalNoRealServerCount }}</td>
                        </tr>
                        <tr>
                            <td>待回收 CLB（CLB 不存在但 LBR 存在）</td>
                            <td>{{ .Statistics.CLB.TotalNotExistedCount }}</td>
                        </tr>
                    </table>

                <h3>监听器、Rule、RS 资源统计</h3>
                <table>
                    <tr>
                        <th>资源维度</th>
                        <th>资源总数</th>
                        <th>健康检查全失败个数</th>
                        <th>权重全为0个数</th>
                    </tr>
                    <tr>
                        <td>CLB Listener</td>
                        <td>{{ .Statistics.Listener.TotalCount }}</td>
                        <td>{{ .Statistics.Listener.TotalUnhealthCount }}</td>
                        <td>{{ .Statistics.Listener.TotalZeroWeightCount }}</td>

                    </tr>
                    <tr>
                        <td>CLB Rule</td>
                        <td>{{ .Statistics.Rule.TotalCount }}</td>
                        <td>{{ .Statistics.Rule.TotalUnhealthCount }}</td>
                        <td>{{ .Statistics.Rule.TotalZeroWeightCount }}</td>
                    </tr>
                    <tr>
                        <td>CLB Real Server</td>
                        <td>{{ .Statistics.RealServer.TotalCount }}</td>
                        <td>{{ .Statistics.RealServer.TotalUnhealthCount }}</td>
                        <td>{{ .Statistics.RealServer.TotalZeroWeightCount }}</td>
                    </tr>
                </table>

                <h2>CLB 风险统计</h2>
                <h3>根据最高风险维度，统计集群内存在风险的 CLB</h3>
                <table>
                    <tr>
                        <th>风险维度</th>
                        <th>CLB个数</th>
                    </tr>
                    <tr>
                        <td>【CLB级别】CLB 健康检查全失败或权重全为 0</td>
                        <td>{{ .Analysises.TotalCLBLevelRiskCount }}</td>
                    </tr>
                    <tr>
                        <td>【Listener级别】CLB 存在健康检查全失败或权重全为 0 的监听器</td>
                        <td>{{ .Analysises.TotalListenerLevelRiskCount }}</td>
                    </tr>
                    <tr>
                        <td>【Rule级别】CLB 存在健康检查失败或权重为 0 的 Rule</td>
                        <td>{{ .Analysises.TotalRuleLevelRiskCount }}</td>
                    </tr>
                    <tr>
                        <td>【RS级别】CLB 存在健康检查失败或权重为 0 的 RS</td>
                        <td>{{ .Analysises.TotalRSLevelRiskCount }}</td>
                    </tr>
                </table>

                <h2>CLB 风险列表 <a href="/inspection/cluster/report/download?clusterID={{ .ClusterID }}" class="top-bar-link export-button">一键导出</a></h2>
                {{ $clusterID := .ClusterID }}
                <table>
                    <tr>
                        <th>CLB 名称</th>
                        <th>CLB 使用协议</th>
                        <th>关联资源</th>
                        <th>风险维度</th>
                        <th>风险总数</th>
                        <th>风险列表</th>
                    </tr>
                    {{ range $code, $score := .Analysises.Details }}
                        {{ range $index, $risk := $score.RiskInfos }}
                            <tr>
                                {{ if eq $index 0 }}
                                    <td><a href='/inspection/clb/report/get?clbID={{ $score.CLBID }}&clusterID={{ $clusterID }}&refreshCache=true'>{{ $score.CLBID }}</a></td>
                                    <td>{{ $score.Protocols }}</td>
                                    <td>{{ $score.UsingResources }}</td>
                                    <td>{{ $score.RiskLevel }}</td>
                                    <td>{{ $score.RiskCount }}</td>
                                {{ else }}
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                {{ end }}
                                <td>{{ $risk }}</td>
                            </tr>
                        {{ end }}
                    {{ end }}
                </table>
            </div>
        </main>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 设置图表样式
            const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
            const textColor = isDark ? '#e2e8f0' : '#334155';
            const gridColor = isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)';

            // 风险等级分布图表
            const riskLevelCtx = document.getElementById('riskLevelChart').getContext('2d');
            const riskLevelChart = new Chart(riskLevelCtx, {
                type: 'pie',
                data: {
                    labels: ['健康检查不通 CLB', '权重不通 CLB', '闲置 CLB', '待回收 CLB'],
                    datasets: [{
                        data: [{{ .Statistics.CLB.TotalUnhealthCount }}, {{ .Statistics.CLB.TotalZeroWeightCount }}, {{ .Statistics.CLB.TotalNoRealServerCount }}, {{ .Statistics.CLB.TotalNotExistedCount }}],
                        backgroundColor: [
                            'rgba(239, 68, 68, 0.8)',    // 红色
                            'rgba(245, 158, 11, 0.8)',    // 橙色
                            'rgba(59, 130, 246, 0.8)',   // 蓝色
                            'rgba(16, 185, 129, 0.8)'   // 绿色
                        ],
                        borderColor: [
                            'rgba(239, 68, 68, 1)',        // 红色
                            'rgba(245, 158, 11, 1)',      // 橙色
                            'rgba(59, 130, 246, 1)',     // 蓝色
                            'rgba(16, 185, 129, 1)'     // 绿色
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                color: textColor
                            }
                        },
                        title: {
                            display: true,
                            text: 'CLB 异常分布',
                            color: textColor,
                            font: {
                                size: 16
                            }
                        }
                    }
                }
            });

            // 风险类型分布图表
            const riskTypeCtx = document.getElementById('riskTypeChart').getContext('2d');
            const riskTypeChart = new Chart(riskTypeCtx, {
                type: 'bar',
                data: {
                    labels: ['CLB维度异常', 'Listener维度异常', 'Rule维度异常', 'RS 维度异常'],
                    datasets: [{
                        label: 'CLB数量',
                        data: [{{ .Analysises.TotalCLBLevelRiskCount }}, {{ .Analysises.TotalListenerLevelRiskCount }}, {{ .Analysises.TotalRuleLevelRiskCount }}, {{ .Analysises.TotalRSLevelRiskCount }}],
                        backgroundColor: 'rgba(59, 130, 246, 0.5)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 1,
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                color: textColor
                            }
                        },
                        title: {
                            display: true,
                            text: '风险类型分布',
                            color: textColor,
                            font: {
                                size: 16
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: gridColor
                            },
                            ticks: {
                                color: textColor
                            }
                        },
                        x: {
                            grid: {
                                color: gridColor
                            },
                            ticks: {
                                color: textColor
                            }
                        }
                    }
                }
            });

            // 将图表实例暴露给全局主题切换功能
            window.updateChartsTheme = function(isDark) {
                const textColor = isDark ? '#e2e8f0' : '#334155';
                const gridColor = isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)';

                // 更新风险等级图表
                riskLevelChart.options.plugins.legend.labels.color = textColor;
                riskLevelChart.options.plugins.title.color = textColor;

                // 更新风险类型图表
                riskTypeChart.options.plugins.legend.labels.color = textColor;
                riskTypeChart.options.plugins.title.color = textColor;
                riskTypeChart.options.scales.y.ticks.color = textColor;
                riskTypeChart.options.scales.x.ticks.color = textColor;
                riskTypeChart.options.scales.y.grid.color = gridColor;
                riskTypeChart.options.scales.x.grid.color = gridColor;

                riskLevelChart.update();
                riskTypeChart.update();
            };
        });
    </script>

    <script src="/k8s-effects.js"></script>
    <script src="/page-loader.js"></script>
    <script src="/report-scripts.js"></script>
</body>
</html>
