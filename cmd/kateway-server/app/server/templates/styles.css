:root {
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --primary-light: rgba(59, 130, 246, 0.1);
  --primary-gradient: linear-gradient(135deg, #3b82f6, #2563eb);
  
  --secondary-color: #1e293b;
  --secondary-light: #334155;
  --secondary-dark: #0f172a;
  --secondary-gradient: linear-gradient(135deg, #1e293b, #0f172a);
  
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #0ea5e9;
  
  --text-color: #334155;
  --text-light: #64748b;
  --text-lighter: #94a3b8;
  
  --background-light: #f8fafc;
  --background-white: #ffffff;
  --border-color: #e2e8f0;
  --shadow-color: rgba(0, 0, 0, 0.08);
  
  --card-border-radius: 16px;
  --input-border-radius: 10px;
  --button-border-radius: 10px;
  --transition-speed: 0.3s;
  
  --header-height: 68px;
  --content-max-width: 1440px;
  
  --font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
}

[data-theme="dark"] {
  --primary-color: #3b82f6;
  --primary-hover: #60a5fa;
  --primary-light: rgba(59, 130, 246, 0.15);
  
  --secondary-color: #1e1e2d;
  --secondary-light: #2a2a3c;
  --secondary-dark: #15151f;
  --secondary-gradient: linear-gradient(135deg, #1e1e2d, #15151f);
  
  --text-color: #e2e8f0;
  --text-light: #cbd5e1;
  --text-lighter: #94a3b8;
  
  --background-light: #0f172a;
  --background-white: #1e293b;
  --border-color: #334155;
  --shadow-color: rgba(0, 0, 0, 0.3);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 全局页面样式修复 */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  background-color: var(--background-light);
  color: var(--text-color);
  line-height: 1.6;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  transition: background-color var(--transition-speed), color var(--transition-speed);
  position: relative;
  overflow-x: hidden;
}

/* 背景网格装饰 - 修复 z-index 确保不阻挡交互 */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25px 25px, var(--border-color) 1px, transparent 0),
    radial-gradient(circle at 75px 75px, var(--border-color) 1px, transparent 0);
  background-size: 100px 100px;
  opacity: 0.3;
  z-index: -10; /* 降低 z-index，避免阻挡交互 */
  pointer-events: none; /* 确保点击事件可以穿透 */
}

/* 页面包装器 - 修复高度和布局 */
.page-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh; /* 使用视窗高度 */
  width: 100%;
  position: relative;
  z-index: 1;
  flex: 1 0 auto;
  overflow-x: hidden;
}

/* 顶部导航栏 */
.navbar {
  height: var(--header-height);
  padding: 0 2rem;
  display: flex;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 1000;
  background: var(--secondary-gradient);
  box-shadow: 0 4px 20px var(--shadow-color);
  transition: all var(--transition-speed);
}

.navbar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: var(--content-max-width);
  margin: 0 auto;
}

.navbar-brand {
  font-size: 1.3rem;
  font-weight: 500;
  color: white;
  text-decoration: none;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
  margin-right: 2rem; /* 增加品牌与导航链接之间的间距 */
}

.navbar-brand i {
  margin-right: 0.75rem;
  font-size: 1.5rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.5));
}

.nav-links {
  display: flex;
  gap: 0.75rem; /* 增加导航链接之间的间距 */
}

.nav-link {
  color: rgba(255, 255, 255, 0.85);
  text-decoration: none;
  padding: 0.65rem 1rem;
  border-radius: 8px;
  transition: all var(--transition-speed);
  font-weight: 400;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity var(--transition-speed);
  z-index: -1;
  border-radius: 8px;
  pointer-events: none; /* 确保点击事件可以穿透 */
}

.nav-link.active::before {
  opacity: 1;
}

.nav-link:hover::before {
  opacity: 0.7;
}

.nav-link:hover, .nav-link.active {
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.nav-link i {
  margin-right: 0.5rem;
  font-size: 1.05rem;
}

.nav-spacer {
  margin-left: auto;
}

.theme-toggle {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  margin-left: 0.75rem;
  transition: all var(--transition-speed);
  position: relative;
  overflow: hidden;
}

.theme-toggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity var(--transition-speed);
  z-index: -1;
  border-radius: 50%;
  pointer-events: none; /* 确保点击事件可以穿透 */
}

.theme-toggle:hover::before {
  opacity: 1;
}

.theme-toggle:hover {
  transform: rotate(12deg);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
}

/* 主内容区域 - 确保填充剩余空间 */
.main-content {
  flex: 1 0 auto; /* 填充空间，不收缩，可以增长 */
  padding: 0 2rem; /* 移除顶部和底部的内边距，让容器可以真正垂直居中 */
  display: flex;
  flex-direction: column;
  justify-content: center; /* 垂直居中 */
  align-items: center;
  margin: 0 auto;
  width: 100%;
  max-width: var(--content-max-width);
  position: relative;
  z-index: 5; /* 确保主内容区域在可交互层级 */
  min-height: calc(100vh - var(--header-height) - 60px); /* 减去头部和底部的高度 */
}

.container {
  width: 100%;
  max-width: 1200px;
  background: var(--background-white);
  padding: 2.5rem;
  border-radius: var(--card-border-radius);
  box-shadow: 
    0 10px 30px var(--shadow-color),
    0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all var(--transition-speed);
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
  z-index: 10; /* 确保容器在可交互层级 */
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 150px;
  height: 150px;
  background: var(--primary-gradient);
  opacity: 0.05;
  border-radius: 0 0 0 100%;
  z-index: -1; /* 确保装饰在内容下层 */
  pointer-events: none; /* 确保点击事件可以穿透 */
}

/* 搜索容器 - 统一大小 */
.search-container {
  width: 650px; /* 固定宽度 */
  max-width: 100%; /* 在小屏幕上允许响应式 */
  min-height: 400px; /* 最小高度，确保视觉一致性 */
  text-align: center;
  margin: 0 auto;
  position: relative;
  background: var(--background-white);
  padding: 2.5rem;
  border-radius: var(--card-border-radius);
  box-shadow: 
    0 20px 40px var(--shadow-color),
    0 1px 5px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
  z-index: 10; /* 确保搜索容器在可交互层级 */
  display: flex;
  flex-direction: column;
  justify-content: center; /* 内容垂直居中 */
}

.search-container::before,
.search-container::after {
  content: '';
  position: absolute;
  background: var(--primary-gradient);
  opacity: 0.05;
  z-index: -1; /* 确保装饰在内容下层 */
  pointer-events: none; /* 确保点击事件可以穿透 */
}

.search-container::before {
  top: 0;
  right: 0;
  width: 150px;
  height: 100px;
  border-radius: 0 var(--card-border-radius) 0 100%;
}

.search-container::after {
  bottom: 0;
  left: 0;
  width: 100px;
  height: 75px;
  border-radius: 0 100% 0 var(--card-border-radius);
}

/* 资源检索页面的搜索容器特别调整 - 因为表单更长 */
.resource-search-container {
  min-height: 500px;
}

/* 响应式调整 */
@media (max-width: 700px) {
  .search-container {
    width: 100%; /* 在小屏幕上占满宽度 */
    min-height: 350px; /* 减小最小高度 */
  }
}

/* 表单元素 */
.form-group {
  margin-bottom: 1.75rem;
  position: relative;
  z-index: 5; /* 确保表单元素在可交互层级 */
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.95rem;
}

.form-control {
  width: 100%;
  padding: 0.85rem 1.2rem;
  border: 2px solid var(--border-color);
  border-radius: var(--input-border-radius);
  font-size: 1rem;
  color: var(--text-color);
  transition: all var(--transition-speed);
  background-color: var(--background-white);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
  position: relative;
  z-index: 5; /* 确保输入框在可交互层级 */
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 
    0 0 0 4px var(--primary-light),
    0 0 10px rgba(59, 130, 246, 0.2);
  z-index: 6; /* 聚焦时提高层级 */
  animation: form-glow 1.5s infinite alternate;
}

select.form-control {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1em;
  padding-right: 3rem;
}

/* 按钮 */
.btn {
  display: inline-block;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  padding: 0.9rem 1.5rem;
  font-size: 1rem;
  border-radius: var(--button-border-radius);
  transition: all var(--transition-speed);
  color: #fff;
  background: var(--primary-gradient);
  border: none;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.35);
  position: relative;
  overflow: hidden;
  z-index: 5; /* 确保按钮在可交互层级 */
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s;
  z-index: -1; /* 确保装饰在按钮内容下层 */
  pointer-events: none; /* 确保点击事件可以穿透 */
}

.btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(120deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: translateX(-100%);
  z-index: 1;
  pointer-events: none;
}

.btn:hover::before {
  left: 100%;
}

.btn:hover::after {
  transform: translateX(100%);
  transition: transform 0.7s ease;
}

.btn:hover {
  box-shadow: 0 6px 15px rgba(59, 130, 246, 0.45);
  transform: translateY(-2px);
}

.btn:active {
  transform: translateY(0);
  box-shadow: 0 4px 10px rgba(59, 130, 246, 0.35);
}

.btn-secondary {
  background: var(--secondary-gradient);
  box-shadow: 0 4px 12px rgba(30, 41, 59, 0.3);
}

.btn-secondary:hover {
  box-shadow: 0 6px 15px rgba(30, 41, 59, 0.4);
}

.btn-block {
  display: block;
  width: 100%;
}

.btn i {
  margin-right: 0.5rem;
}

.btn-loading {
  pointer-events: none;
  opacity: 0.9;
}

.btn-loading::after {
  content: "";
  display: inline-block;
  width: 1rem;
  height: 1rem;
  margin-left: 0.5rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 标题文本 */
h1, h2, h3 {
  color: var(--text-color);
  line-height: 1.3;
  font-weight: 500;
}

h1 {
  font-size: 2.2rem;
  margin-bottom: 1rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
}

h2 {
  font-size: 1.8rem;
  margin-bottom: 1.25rem;
}

h3 {
  font-size: 1.4rem;
  font-weight: 400;
  margin-bottom: 1.5rem;
  color: var(--text-light);
}

/* 表格样式 */
table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin: 1.5rem 0;
  background-color: var(--background-white);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px var(--shadow-color);
}

thead {
  background: var(--primary-gradient);
  color: white;
}

th, td {
  padding: 1.2rem 1.5rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

th {
  font-weight: 500;
  position: relative;
}

th .sort-icon {
  margin-left: 0.5rem;
  font-size: 0.85rem;
  opacity: 0.7;
  transition: transform var(--transition-speed);
}

th[data-sort="asc"] .sort-icon {
  transform: rotate(180deg);
}

tr:last-child td {
  border-bottom: none;
}

tbody tr {
  transition: background-color var(--transition-speed);
}

tbody tr:nth-child(even) {
  background-color: rgba(0, 0, 0, 0.02);
}

tbody tr:hover {
  background-color: var(--primary-light);
}

/* 修复页脚 - 确保它紧贴底部 */
footer {
  flex-shrink: 0; /* 防止页脚收缩 */
  text-align: center;
  padding: 1.5rem 0;
  height: 60px;
  color: var(--text-light);
  background: var(--background-white);
  border-top: 1px solid var(--border-color);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  box-sizing: border-box;
  margin-top: auto;
  margin-bottom: 0;
  z-index: 10;
}

footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0.3;
}

footer a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--transition-speed);
}

footer a:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

/* 图表容器 */
.chart-container {
  margin: 2.5rem 0;
  border-radius: var(--card-border-radius);
  overflow: hidden;
  box-shadow: 0 10px 25px var(--shadow-color);
  background-color: var(--background-white);
  border: 1px solid var(--border-color);
}

.chart-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.chart-card {
  flex: 1;
  min-width: 300px;
  background-color: var(--background-white);
  border-radius: var(--card-border-radius);
  padding: 1.5rem;
  box-shadow: 0 8px 20px var(--shadow-color);
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
}

.chart-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: var(--primary-gradient);
  opacity: 0.05;
  border-radius: 0 0 0 100%;
}

/* 徽章 */
.badge {
  display: inline-block;
  padding: 0.35em 0.65em;
  font-size: 0.75em;
  font-weight: 500;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 50rem;
  color: #fff;
  background-color: var(--primary-color);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.badge-success { 
  background: linear-gradient(135deg, #10b981, #059669); 
}

.badge-warning { 
  background: linear-gradient(135deg, #f59e0b, #d97706); 
  color: #ffffff;
}

.badge-danger { 
  background: linear-gradient(135deg, #ef4444, #dc2626); 
}

.badge-info { 
  background: linear-gradient(135deg, #0ea5e9, #0284c7); 
}

/* 通知样式 */
.notification-container {
  position: fixed;
  top: 1.5rem;
  right: 1.5rem;
  z-index: 2000; /* 提高 z-index，确保通知显示在最顶层 */
}

.notification {
  display: flex;
  align-items: center;
  padding: 1rem 1.25rem;
  margin-bottom: 0.75rem;
  background-color: var(--background-white);
  border-radius: 12px;
  box-shadow: 0 10px 25px var(--shadow-color);
  transform: translateX(100%);
  opacity: 0;
  transition: transform 0.3s, opacity 0.3s;
  max-width: 350px;
  border-left: 4px solid var(--primary-color);
}

.notification-show {
  transform: translateX(0);
  opacity: 1;
}

.notification i {
  margin-right: 0.75rem;
  font-size: 1.25rem;
}

.notification-close {
  margin-left: auto;
  background: none;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  font-size: 1rem;
  transition: color var(--transition-speed);
}

.notification-close:hover {
  color: var(--text-color);
}

.notification-success { border-left-color: var(--success-color); }
.notification-warning { border-left-color: var(--warning-color); }
.notification-error { border-left-color: var(--danger-color); }
.notification-info { border-left-color: var(--info-color); }

.notification-success i { color: var(--success-color); }
.notification-warning i { color: var(--warning-color); }
.notification-error i { color: var(--danger-color); }
.notification-info i { color: var(--info-color); }

/* 卡片组 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.card {
  background-color: var(--background-white);
  border-radius: var(--card-border-radius);
  box-shadow: 0 8px 20px var(--shadow-color);
  padding: 1.5rem;
  transition: all var(--transition-speed);
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px var(--shadow-color);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: var(--primary-gradient);
  opacity: 0.05;
  border-radius: 0 0 0 100%;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.card-icon {
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-light);
  color: var(--primary-color);
  font-size: 1.5rem;
  border-radius: 12px;
  margin-right: 1rem;
}

.card-title {
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0;
}

.card-body {
  color: var(--text-light);
}

/* 响应式调整 */
@media (max-width: 992px) {
  .navbar {
    padding: 0 1rem;
  }
  
  .main-content {
    padding: 2rem 1rem;
  }
  
  .container, .search-container {
    padding: 1.75rem;
  }
}

@media (max-width: 768px) {
  .navbar {
    padding: 0.5rem;
    height: auto;
    flex-wrap: wrap;
  }
  
  .navbar-brand {
    font-size: 1.1rem;
    margin-right: 1rem;
    white-space: nowrap;
  }
  
  .nav-links {
    flex-wrap: nowrap;
    overflow-x: auto;
    padding-bottom: 0.5rem;
    width: 100%;
    order: 3;
    margin-top: 0.5rem;
    justify-content: flex-start;
    -webkit-overflow-scrolling: touch; /* 提高滚动体验 */
    scrollbar-width: none; /* Firefox */
  }
  
  .nav-links::-webkit-scrollbar {
    display: none; /* Chrome, Safari */
  }
  
  .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
    white-space: nowrap;
  }
  
  .nav-link span {
    display: none; /* 只显示图标 */
  }
  
  .nav-link i {
    margin-right: 0;
    font-size: 1.2rem;
  }
  
  .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background-color: var(--primary-color);
  }
  
  .nav-spacer {
    display: none;
  }
  
  .search-container {
    width: 95%;
    padding: 1.5rem 1rem;
    min-height: auto;
  }
  
  /* 调整页脚在移动端的样式 */
  footer {
    padding: 1rem 0.5rem;
    height: auto;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  h1 {
    font-size: 1.5rem;
  }
  
  h3 {
    font-size: 1rem;
  }
  
  /* 调整表格在移动设备上的显示 */
  table, th, td {
    font-size: 0.9rem;
  }
  
  th, td {
    padding: 0.6rem 0.8rem;
  }
}

/* 新增: 为小屏幕手机优化 */
@media (max-width: 480px) {
  .navbar {
    padding: 0.5rem 0.25rem;
  }
  
  .navbar-brand {
    font-size: 1rem;
    margin-right: 0.5rem;
  }
  
  .nav-links {
    margin-top: 0.25rem;
    gap: 0.25rem;
  }
  
  .nav-link {
    padding: 0.4rem 0.6rem;
  }
  
  .theme-toggle {
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }
  
  .search-container {
    width: 100%;
    padding: 1.25rem 0.75rem;
    border-radius: calc(var(--card-border-radius) / 1.5);
  }
  
  .form-control {
    padding: 0.75rem 1rem;
  }
  
  .btn {
    padding: 0.75rem 1.25rem;
  }
  
  footer {
    padding: 0.75rem 0.5rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 992px) {
  .navbar {
    padding: 0 1.5rem;
  }
  
  .main-content {
    padding: 2.5rem 1.5rem;
  }
  
  .container, .search-container {
    padding: 2rem;
  }
  
  h1 {
    font-size: 1.8rem;
  }
  
  h3 {
    font-size: 1.25rem;
  }

  .k8s-nodes,
  .ai-network,
  .network-icons {
    opacity: 0.04; /* 降低移动设备上的不透明度 */
  }
}

@media (max-width: 768px) {
  .navbar {
    padding: 0 1rem;
    height: auto;
    min-height: var(--header-height);
  }
  
  .navbar-container {
    flex-wrap: wrap;
  }
  
  .nav-links {
    order: 3;
    width: 100%;
    margin-top: 1rem;
    margin-bottom: 1rem;
    justify-content: space-between;
    overflow-x: auto;
    padding-bottom: 0.5rem;
  }
  
  .nav-link {
    padding: 0.5rem 0.75rem;
  }
  
  .nav-link span {
    display: none;
  }
  
  .nav-link i {
    margin-right: 0;
    font-size: 1.2rem;
  }
  
  .main-content {
    padding: 2rem 1rem;
  }
  
  .container, .search-container {
    padding: 1.5rem;
  }
  
  h1 {
    font-size: 1.6rem;
  }
  
  h3 {
    font-size: 1.1rem;
  }
  
  th, td {
    padding: 0.75rem 1rem;
  }

  .k8s-nodes,
  .network-icons {
    display: none; /* 在小屏幕上隐藏一些装饰 */
  }
}

/* 加载样式 */
.loading-spinner {
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 0.8s linear infinite;
}

/* 工具类 */
.text-center { text-align: center; }
.text-right { text-align: right; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.mb-5 { margin-bottom: 2.5rem; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
.mt-5 { margin-top: 2.5rem; }

/* 细节动画 */
@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
  100% { transform: translateY(0px); }
}

/* 科技感装饰 - 确保不阻挡交互 */
.tech-circle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none; /* 确保点击事件可以穿透 */
  z-index: -5; /* 降低 z-index，避免阻挡交互 */
}

.tech-circle-1 {
  width: 300px;
  height: 300px;
  background: var(--primary-color);
  opacity: 0.03;
  top: -100px;
  right: -100px;
}

.tech-circle-2 {
  width: 200px;
  height: 200px;
  background: var(--primary-color);
  opacity: 0.02;
  bottom: -50px;
  left: -50px;
}

.tech-line {
  position: absolute;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0.1;
  width: 50%;
  pointer-events: none; /* 确保点击事件可以穿透 */
  z-index: -5; /* 降低 z-index，避免阻挡交互 */
}

/* 表单验证样式 */
.is-invalid {
  border-color: var(--danger-color) !important;
}

.is-invalid:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2) !important;
}

.invalid-feedback {
  color: var(--danger-color);
  font-size: 0.85rem;
  margin-top: 0.5rem;
  display: block;
}

/* 添加抖动动画 */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* 添加 Kubernetes 和网络主题元素 */
.k8s-grid-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(to right, var(--border-color) 1px, transparent 1px),
    linear-gradient(to bottom, var(--border-color) 1px, transparent 1px);
  background-size: 50px 50px;
  opacity: 0.07;
  z-index: -15;
  pointer-events: none;
}

.k8s-nodes {
  position: fixed;
  bottom: 0;
  right: 0;
  width: 450px;
  height: 250px;
  opacity: 0.05;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 400'%3E%3Cpath fill='%233b82f6' d='M203.8,107.8l-10.4,2.6l-0.3,9.8c-0.2,7.3,0,9.9,0.6,10.3c0.5,0.3,5.3,1.6,10.7,2.9l9.8,2.4l10.9-2.8c6-1.5,10.9-3,10.9-3.3c0-0.3-0.4-4.9-0.9-10.3l-0.8-9.7l-9.8-2.5C213.5,105.5,203.8,105.5,203.8,107.8z M172.6,129.2c-7.3,7.6-10.2,11.4-9.2,12.6c0.7,0.9,4.8,5.3,9,9.8l7.7,8.1l9.5-0.2c5.2-0.1,9.6-0.5,9.7-0.8c0.2-0.3,1.4-4.9,2.8-10.4l2.5-9.8l-6.8-7.1c-5.1-5.3-7.2-7.1-8.3-7.1C188,124.3,180.5,126.5,172.6,129.2z M233.4,124.6c-1.1,0.3-5.6,4.8-9.9,9.8l-7.9,9.2l2.4,9.7c1.3,5.3,2.7,9.7,3,9.8c0.3,0.1,4.7,0,9.8-0.3l9.3-0.5l7.9-8.3c4.3-4.6,8.1-9,8.5-9.8c0.5-1.2-1.1-3.8-8.4-13.4C241.6,123.3,236.5,123.9,233.4,124.6z M170.4,162.5c-3.7,1-8.5,2.2-10.6,2.8l-3.9,1l-2.7,10.5c-1.5,5.8-2.5,10.6-2.3,10.8c0.2,0.1,3.3,0.9,6.8,1.6c3.5,0.7,8.3,1.7,10.6,2.2l4.2,0.9l3.8-7c2.1-3.8,4.2-7.9,4.5-9.1c0.6-1.9,0.1-3.4-2.9-10C175.7,160.2,176.1,161,170.4,162.5z M235.2,160.5c-3.1,6.1-5.5,11.5-5.3,11.9c0.2,0.4,2.3,4.5,4.7,9.1l4.3,8.3l9.4-1.9c5.2-1.1,10-2.1,10.7-2.3c1-0.3,2.5-3.5,5.3-11.1l3.9-10.7l-8-2.1l-8-2.1l-3.8-1c-2.1-0.5-6.8-1.8-10.4-2.7C233.5,154.9,238.1,155.6,235.2,160.5z M192.7,173.1c-3.5,1-7.7,2.1-9.5,2.4l-3.2,0.6l-0.9,3.9c-0.5,2.1-1.6,6.8-2.5,10.4c-0.9,3.6-1.5,6.7-1.3,6.9c0.2,0.2,4.5,1.3,9.6,2.5l9.3,2.2l4.9-4.6c2.7-2.5,5.2-5,5.6-5.5c0.4-0.5,0.6-1.5,0.3-2.2c-0.3-0.8-1.6-4.9-2.9-9.3l-2.4-8l-2.5,0.3C196.9,172.7,194.7,172.5,192.7,173.1z M217.7,172.4c-0.7,0.2-2.2,0.5-3.5,0.8c-1.2,0.3-2.2,0.8-2.2,1.2c0,0.3-1.3,4.7-2.8,9.8l-2.8,9.2l5.1,4.9c2.8,2.7,5.4,4.9,5.7,4.9c0.3,0,4.7-0.9,9.8-2.1c5.1-1.2,9.5-2.4,9.7-2.7c0.2-0.2-0.4-3.3-1.2-6.9c-0.9-3.6-2-8.3-2.5-10.4l-0.9-3.9l-6.5-1.2C222.4,173.3,218.4,172.3,217.7,172.4z M153.9,184.3c-9.1,2.4-9.8,2.9-9.1,7.2c0.5,3.1,3.9,16.8,4.1,17c0.1,0.1,4.4-0.7,9.5-1.8l9.3-2l0.5-8.8c0.3-4.8,0.3-9.2,0-9.8C167.8,185.3,163.6,184.3,153.9,184.3z M259.1,185.2c-3.3,0.8-7.3,1.9-9,2.2l-3,0.7l0.3,9.7c0.2,5.3,0.7,9.7,1,9.8c0.3,0.1,4.7,1,9.8,2.1c8,1.6,9.4,1.7,9.7,0.9c0.2-0.5,1.2-4.5,2.3-8.9c1-4.4,2-8.9,2.1-9.9C272.3,188.4,265.6,186.5,259.1,185.2z M139.4,216.9c-2.3,0.5-4.3,1.2-4.5,1.4c-0.2,0.2,0.8,3.4,2.2,7.1c1.5,3.7,3.2,8.2,3.9,10l1.2,3.3l10.8,0.1c5.9,0,10.9-0.2,11-0.5c0.1-0.3-0.7-4.7-1.7-9.8l-1.9-9.4l-7.7-1.6C148.2,216.2,143.1,216.2,139.4,216.9z M254.5,216.7c-4.9,1-9.2,2-9.5,2.3c-0.3,0.3-1.3,4.7-2.1,9.8l-1.5,9.3l11,0.5l11,0.5l3.9-10c2.1-5.5,3.8-10.1,3.6-10.3C269.3,216.4,260.1,215.5,254.5,216.7z M172.9,222.5c-4.9,1-9.2,2-9.5,2.3c-0.3,0.3-1.6,5.8-2.9,12.2l-2.3,11.7l5.9,2.3c3.2,1.3,7.7,3.1,9.9,4.1l4,1.8l5.8-5.7c3.2-3.1,6.1-6.1,6.3-6.6c0.3-0.5-0.1-5.1-0.9-10.3l-1.4-9.3l-4.5-1.1C178.4,222.1,175.7,221.9,172.9,222.5z M232.1,221.9c-1.6,0.3-4.1,0.8-5.5,1.1l-2.5,0.6l-1.7,9.3c-0.9,5.1-1.4,9.7-1.1,10.2c0.3,0.5,3.1,3.5,6.3,6.6l5.8,5.7l6.3-2.5c3.5-1.4,8-3.3,10.1-4.2l3.7-1.7l-2.3-11.4c-1.2-6.3-2.5-11.7-2.9-12C248.2,223.3,235.5,221.2,232.1,221.9z M194.8,253.6c-0.6,0.7-1.4,2.1-1.6,3c-0.3,0.9-2.7,5.4-5.4,9.9l-4.9,8.3l2.3,7.1c1.2,3.9,2.5,7.4,2.7,7.8c0.3,0.4,5.1,1.4,10.8,2.3c5.7,0.9,10.5,1.5,10.7,1.4c0.3-0.3,5.2-14.4,5.2-15.2c0-0.3-2.4-4.8-5.4-9.9l-5.4-9.3l-4.4-2.4C197.3,255.6,195.7,252.6,194.8,253.6z M254.6,264.8c-2.4,6.3-4.1,11.7-3.8,12c0.3,0.3,9.6,8.2,14.3,12.1c0.5,0.4,5.4-1.3,10.9-3.8l10-4.5l-0.3-7.3c-0.2-4-0.7-8-1.1-8.7c-0.4-0.8-4.9-3.4-10-5.8l-9.3-4.3l-2.8 1.3C260.5,256.8,257.4,257.3,254.6,264.8z M169.8,264.6c-8.3,3.8-12.1,5.9-12.1,6.7c0,0.7-0.5,4.6-1.1,8.7l-1.1,7.5l9.5,4.3c5.2,2.4,10.1,4.5,10.8,4.7c1,0.4,3.2-1.4,8.7-6.6c4-3.8,7.5-7.1,7.7-7.4c0.2-0.3-1.5-5.4-3.7-11.4l-4.1-10.9l-3.6 0.8C179.8,261.3,173.8,263.3,169.8,264.6z M139.3,275c-6.1,1.6-11.4,3.2-11.8,3.6c-0.5,0.5,1.1,5.8,4.4,15.1l2.5,7.1l9.7-0.2c5.3-0.1,9.9-0.5,10.2-0.8c0.2-0.3,1.4-4.9,2.6-10.3l2.2-9.7l-5.1-2.3C150,276.3,145.2,274.7,139.3,275z M121.6,302.5c-4.6,1.2-8.7,2.5-9.1,2.8c-0.4,0.3,2.1,8.2,5.5,17.5l6.2,16.9l5.7-0.4c3.1-0.2,7.8-0.5,10.5-0.6l4.8-0.2l-2.5-9c-1.4-4.9-3.1-11.1-3.8-13.7c-0.7-2.6-1.5-5.6-1.9-6.6l-0.6-1.9l-4.3-1.1C128.3,304.5,126.7,301.9,121.6,302.5z M192.1,302.8c-1.9,0.3-8.8,1.6-13.1,2.4c-1.6,0.3-1.7,0.7-2.5,5.5c-0.6,3.4-2,9.6-3.2,13.8c-1.2,4.2-2,7.7-1.9,7.9c0.2,0.2,4.7,1.3,10,2.6l9.7,2.3l7.7-8.1c4.2-4.4,7.8-8.4,7.9-8.8c0.1-0.4-1.6-4.9-3.8-10l-4-9.2l-2.7 0.5C195.7,302.1,193.1,302.7,192.1,302.8z M214.5,302.2c-0.5,0.1-1.9,0.5-3.2,0.8c-1.2,0.3-2.2,0.9-2.2,1.3c0,0.4-1.7,4.8-3.8,9.8l-3.8,9.1l7.5,8c4.1,4.4,7.9,8,8.4,8c0.5,0,5.3-1.1,10.7-2.5c7.8-2,9.8-2.7,9.8-3.7c0-0.7-1.5-7.4-3.4-14.8c-1.9-7.4-3.6-13.9-3.7-14.3c-0.1-0.5-2.3-1.2-4.8-1.6c-2.5-0.4-5.2-0.8-6-1C218.6,302.9,215.2,303,214.5,302.2z M275.3,304.2c-1.7,0.4-3.2,1-3.5,1.2c-0.3,0.3-2.5,6.9-4.9,14.7c-2.4,7.8-4.1,14.3-3.8,14.5c0.3,0.3,4.9,0.8,10.2,1.2l9.7,0.7l4.3-8.8c2.4-4.8,4.5-9.6,4.8-10.6c0.2-1-0.4-3.3-1.4-5.1c-1-1.8-3.4-6.3-5.2-9.8l-3.4-6.4l-2.2 4.1C279.2,301.5,277.5,303.7,275.3,304.2z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: bottom right;
  z-index: -12;
  pointer-events: none;
}

/* AI 灵感元素 - 右侧网络浮动连接线 */
.ai-network {
  position: fixed;
  top: 10%;
  right: 0;
  width: 200px;
  height: 400px;
  opacity: 0.07;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='200' height='400'%3E%3Cpath stroke='%233b82f6' stroke-width='1' fill='none' d='M30,20 L170,50 L60,100 L180,150 L40,210 L160,300 L70,380'/%3E%3Ccircle cx='30' cy='20' r='5' fill='%233b82f6'/%3E%3Ccircle cx='170' cy='50' r='5' fill='%233b82f6'/%3E%3Ccircle cx='60' cy='100' r='5' fill='%233b82f6'/%3E%3Ccircle cx='180' cy='150' r='5' fill='%233b82f6'/%3E%3Ccircle cx='40' cy='210' r='5' fill='%233b82f6'/%3E%3Ccircle cx='160' cy='300' r='5' fill='%233b82f6'/%3E%3Ccircle cx='70' cy='380' r='5' fill='%233b82f6'/%3E%3C/svg%3E");
  z-index: -12;
  pointer-events: none;
}

/* 左侧网络图标装饰 */
.network-icons {
  position: fixed;
  bottom: 5%;
  left: 2%;
  width: 150px;
  height: 300px;
  opacity: 0.08;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='150' height='300'%3E%3Crect x='20' y='20' width='30' height='30' stroke='%233b82f6' stroke-width='2' fill='none' /%3E%3Crect x='80' y='60' width='30' height='30' stroke='%233b82f6' stroke-width='2' fill='none' /%3E%3Crect x='30' y='120' width='30' height='30' stroke='%233b82f6' stroke-width='2' fill='none' /%3E%3Crect x='90' y='180' width='30' height='30' stroke='%233b82f6' stroke-width='2' fill='none' /%3E%3Crect x='40' y='240' width='30' height='30' stroke='%233b82f6' stroke-width='2' fill='none' /%3E%3Cpath stroke='%233b82f6' stroke-width='1' fill='none' d='M35,50 L80,60 M95,90 L45,120 M45,150 L90,180 M105,210 L55,240'/%3E%3C/svg%3E");
  z-index: -12;
  pointer-events: none;
}

/* Kubernetes 标志图标 */
.k8s-logo {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(3.5);
  width: 40px;
  height: 40px;
  opacity: 0.035;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath fill='%233b82f6' d='M15.9.2c-.3 0-.6.1-.9.1-.2.1-.5.1-.7.3L2.5 7.7c-.5.3-.9.6-1.2 1-.4.3-.6.8-.6 1.3v12.1c0 .5.2 1 .6 1.4.4.4.8.7 1.2.9l11.8 7c.2.1.5.2.8.3.3.1.5.1.8.1s.6 0 .9-.1.5-.2.8-.3l11.9-7c.5-.3.9-.6 1.2-.9.4-.4.6-.9.6-1.4V10c0-.5-.2-1-.6-1.4-.4-.4-.8-.7-1.2-.9L17.5.6c-.2-.1-.5-.2-.7-.3-.3 0-.6-.1-.9-.1zm.1 1.6c.2 0 .4 0 .6.1.2 0 .3.1.5.2l11.8 7c.2.1.3.2.5.4.2.1.3.3.3.7v12.1c0 .3-.1.5-.3.7-.2.2-.3.3-.5.4l-11.8 7c-.2.1-.3.2-.5.2-.2.1-.4.1-.6.1s-.4 0-.6-.1c-.2 0-.3-.1-.5-.2l-11.8-7c-.2-.1-.3-.2-.5-.4-.2-.2-.3-.4-.3-.7V10c0-.3.1-.5.3-.7.2-.2.3-.3.5-.4l11.8-7c.2-.1.3-.2.5-.2.2-.1.4-.1.6-.1z'/%3E%3Cpath fill='%233b82f6' d='M22.4 17.5c-.5 0-1.2.3-1.2.3l-4.7-4.8.1-7.5s.7.1 1-.1c.3-.2.8-1.2.1-2.8-.7-1.6-2.3-1.9-2.8-1.9s-2.1.3-2.8 1.9c-.7 1.6-.2 2.6.1 2.8.3.2 1 .1 1 .1l.1 7.5-4.7 4.8S7.8 17.5 7.3 17.5c-.5 0-2 .3-2.3 2.3-.3 2.1.8 2.6 1.2 2.7.4.1 1.5.1 1.5.1s3.3 3.5 3.8 3.9c.5.4 1.1.5 1.5.5.4 0 1 0 1.5-.5.5-.4 3.8-3.9 3.8-3.9s1.1 0 1.5-.1c.4-.1 1.5-.6 1.2-2.7-.3-2-1.8-2.3-2.3-2.3h.7z'/%3E%3C/svg%3E");
  z-index: -13;
  pointer-events: none;
}

/* 添加网络连接线装饰 */
.network-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -11;
}

.network-line {
  position: absolute;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color) 50%, transparent);
  animation: network-pulse 3s infinite;
  opacity: 0.05;
}

@keyframes network-pulse {
  0% { opacity: 0.03; }
  50% { opacity: 0.08; }
  100% { opacity: 0.03; }
}

/* 改进卡片悬停效果 */
.search-container:hover {
  box-shadow: 
    0 25px 50px var(--shadow-color),
    0 10px 20px rgba(59, 130, 246, 0.1),
    0 0 15px rgba(59, 130, 246, 0.05);
  transform: translateY(-5px);
  transition: all 0.4s ease;
}

/* 添加连接点动画效果到搜索容器 */
.search-container::after {
  content: '';
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--primary-color);
  top: 15px;
  right: 15px;
  opacity: 0.5;
  box-shadow: 0 0 10px var(--primary-color);
  animation: connection-pulse 2s infinite;
}

@keyframes connection-pulse {
  0% { transform: scale(0.8); opacity: 0.4; }
  50% { transform: scale(1.1); opacity: 0.7; }
  100% { transform: scale(0.8); opacity: 0.4; }
}

/* 添加更好的表单聚焦效果 */
@keyframes form-glow {
  from { box-shadow: 0 0 0 4px var(--primary-light); }
  to { box-shadow: 0 0 0 4px var(--primary-light), 0 0 15px rgba(59, 130, 246, 0.4); }
}