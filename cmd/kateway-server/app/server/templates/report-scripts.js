/**
 * TKE 接入层运营平台 - 报告页面脚本
 */

// 初始化主题
function initTheme() {
  const savedTheme = localStorage.getItem('tke-theme') || 'light';
  document.documentElement.setAttribute('data-theme', savedTheme);
}

// 切换主题
function toggleTheme() {
  const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
  const newTheme = currentTheme === 'light' ? 'dark' : 'light';

  // 添加过渡动画
  document.body.style.transition = 'opacity 0.3s ease';
  document.body.style.opacity = '0.8';

  setTimeout(() => {
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('tke-theme', newTheme);

    setTimeout(() => {
      document.body.style.opacity = '1';

      // 恢复默认过渡
      setTimeout(() => {
        document.body.style.transition = '';
      }, 300);
    }, 50);

    // 更新图表主题（如果页面有图表）
    if (typeof updateChartsTheme === 'function') {
      updateChartsTheme(newTheme === 'dark');
    }
  }, 100);
}

// 科技感背景函数已移除，只保留粒子系统效果

// 增强表格
function enhanceTables() {
  // 添加表格样式和交互
  const tables = document.querySelectorAll('table');

  tables.forEach(table => {
    // 添加表格响应式容器
    const wrapper = document.createElement('div');
    wrapper.style.overflowX = 'auto';
    wrapper.style.width = '100%';
    wrapper.style.marginBottom = '1.5rem';

    // 将表格包裹在容器中
    table.parentNode.insertBefore(wrapper, table);
    wrapper.appendChild(table);

    // 添加表格悬停效果
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
      row.addEventListener('mouseover', () => {
        row.style.backgroundColor = 'var(--primary-light)';
      });

      row.addEventListener('mouseout', () => {
        row.style.backgroundColor = '';
      });
    });

    // 检查状态列并添加徽章
    const cells = table.querySelectorAll('td');
    cells.forEach(cell => {
      const text = cell.textContent.trim();

      // 根据内容添加徽章
      if (text === '高' || text === 'High') {
        cell.innerHTML = '<span class="badge badge-danger">高</span>';
      } else if (text === '中' || text === 'Medium') {
        cell.innerHTML = '<span class="badge badge-warning">中</span>';
      } else if (text === '低' || text === 'Low') {
        cell.innerHTML = '<span class="badge badge-info">低</span>';
      } else if (text === 'Running') {
        cell.innerHTML = '<span class="badge badge-warning">Running</span>';
      } else if (text === 'Done') {
        cell.innerHTML = '<span class="badge badge-success">Done</span>';
      } else if (text === 'Terminated') {
        cell.innerHTML = '<span class="badge badge-danger">Terminated</span>';
      }
    });
  });
}

// 增强链接
function enhanceLinks() {
  // 给链接添加图标和转场效果
  const links = document.querySelectorAll('a');

  links.forEach(link => {
    // 排除顶部导航栏的链接
    if (link.closest('.navbar')) return;

    // 如果链接没有图标，添加一个
    if (!link.querySelector('i') && !link.closest('h1') && !link.closest('h2')) {
      // 根据链接类型选择图标
      let icon = 'link';
      const href = link.getAttribute('href');

      if (href && href.includes('/inspection/cluster/')) {
        icon = 'hdd-stack';
      } else if (href && href.includes('/inspection/clb/')) {
        icon = 'hdd-network';
      } else if (href && href.includes('/inspection/resource/')) {
        icon = 'diagram-3';
      } else if (href && href.includes('download')) {
        icon = 'download';
      }

      // 添加图标
      const iconEl = document.createElement('i');
      iconEl.className = `bi bi-${icon}`;
      iconEl.style.marginRight = '0.25rem';
      iconEl.style.fontSize = '0.9em';
      link.insertBefore(iconEl, link.firstChild);
    }

    // 添加链接悬停效果
    link.addEventListener('mouseover', () => {
      link.style.textDecoration = 'none';
      link.style.color = 'var(--primary-hover)';
      link.style.transition = 'all 0.3s ease';
    });

    link.addEventListener('mouseout', () => {
      link.style.textDecoration = '';
      link.style.color = '';
    });
  });
}

// 更新图表主题
function updateChartsTheme(isDark) {
  if (!window.Chart) return;

  // 设置图表全局默认值
  Chart.defaults.color = isDark ? '#e2e8f0' : '#334155';
  Chart.defaults.borderColor = isDark ? '#334155' : '#e2e8f0';

  // 更新所有图表
  Chart.instances.forEach(chart => {
    // 更新标签颜色
    if (chart.options.plugins && chart.options.plugins.legend) {
      chart.options.plugins.legend.labels.color = isDark ? '#e2e8f0' : '#334155';
    }

    // 更新网格线颜色
    if (chart.options.scales) {
      Object.values(chart.options.scales).forEach(scale => {
        if (scale.grid) {
          scale.grid.color = isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
        }
        if (scale.ticks) {
          scale.ticks.color = isDark ? '#cbd5e1' : '#64748b';
        }
      });
    }

    chart.update();
  });
}

// 添加导航栏
function addTopNavigation() {
    const topBar = document.createElement('nav');
    topBar.className = 'navbar';

    // 添加品牌链接
    const brand = document.createElement('a');
    brand.href = '/';
    brand.className = 'navbar-brand';
    brand.innerHTML = '<i class="bi bi-cloud-check"></i> TKE 接入层运营平台';

    // 添加导航链接
    const navLinks = document.createElement('div');
    navLinks.className = 'nav-links';

    // 导航项目
    const links = [
        { href: '/', icon: 'house', text: '首页' },
        { href: '/cluster-console', icon: 'search', text: '集群检索' },
        { href: '/clb-console', icon: 'hdd-network', text: 'CLB检索' },
        { href: '/resource-console', icon: 'diagram-3', text: '资源检索' },
        { href: '/scan-console', icon: 'scan', text: '风险扫描' },
        { href: '/risk-console', icon: 'shield-exclamation', text: '风险大盘' },
        { href: '/task-console', icon: 'list-task', text: '任务检索' }
    ];

    // 创建链接元素
    links.forEach(link => {
        const a = document.createElement('a');
        a.href = link.href;
        a.className = 'nav-link';
        a.innerHTML = `<i class="bi bi-${link.icon}"></i><span>${link.text}</span>`;
        navLinks.appendChild(a);
    });

    // 添加间隔元素
    const spacer = document.createElement('div');
    spacer.className = 'nav-spacer';

    // 添加额外链接
    const apiLink = document.createElement('a');
    apiLink.href = 'https://panel.woa.com/d/u7mCbX7Nk/jian-kong-dao-hang?orgId=5';
    apiLink.className = 'nav-link';
    apiLink.title = '监控面板';
    apiLink.innerHTML = '<i class="bi bi-file-earmark-code"></i><span>监控面板</span>';

    const manualLink = document.createElement('a');
    manualLink.href = 'https://iwiki.woa.com/p/4012233007';
    manualLink.className = 'nav-link';
    manualLink.title = '操作手册';
    manualLink.innerHTML = '<i class="bi bi-book"></i><span>操作手册</span>';

    // 创建主题切换按钮
    const themeBtn = document.createElement('button');
    themeBtn.id = 'themeToggle';
    themeBtn.className = 'theme-toggle';
    themeBtn.onclick = function() { toggleTheme(); };
    themeBtn.title = '切换主题';
    themeBtn.innerHTML = '<i class="bi bi-moon-fill"></i>';

    // 组装导航栏
    topBar.appendChild(brand);
    topBar.appendChild(navLinks);
    topBar.appendChild(spacer);
    topBar.appendChild(apiLink);
    topBar.appendChild(manualLink);
    topBar.appendChild(themeBtn);

    // 添加到页面
    document.querySelector('.page-wrapper').prepend(topBar);
}

// YAML 语法高亮函数
function highlightYaml() {
  const codeBlocks = document.querySelectorAll('pre code');

  codeBlocks.forEach(codeBlock => {
    // 检查是否包含 YAML 内容
    if (codeBlock.textContent.includes(':') && !codeBlock.classList.contains('highlighted')) {
      const yaml = codeBlock.textContent;
      let highlightedYaml = '';
      const lines = yaml.split('\n');

      lines.forEach(line => {
        // 处理缩进
        const indentMatch = line.match(/^(\s+)/);
        const indent = indentMatch ? indentMatch[0] : '';
        const indentHtml = `<span class="yaml-indent">${indent}</span>`;

        let processedLine = line;

        // 跳过空行
        if (line.trim() === '') {
          highlightedYaml += line + '\n';
          return;
        }

        // 处理注释
        if (line.trim().startsWith('#')) {
          highlightedYaml += indentHtml + `<span class="yaml-comment">${line.trim()}</span>\n`;
          return;
        }

        // 处理键值对
        const keyValueMatch = line.trim().match(/^([^:]+):(.*)/);
        if (keyValueMatch) {
          const key = keyValueMatch[1];
          let value = keyValueMatch[2];

          // 高亮键
          processedLine = indentHtml + `<span class="yaml-keyword">${key}</span>:`;

          // 高亮值
          if (value) {
            // 数字
            if (value.trim().match(/^-?\d+(\.\d+)?$/)) {
              processedLine += `<span class="yaml-number">${value}</span>`;
            }
            // 布尔值
            else if (value.trim().match(/^(true|false)$/i)) {
              processedLine += `<span class="yaml-boolean">${value}</span>`;
            }
            // null 值
            else if (value.trim().match(/^(null|~)$/i)) {
              processedLine += `<span class="yaml-null">${value}</span>`;
            }
            // 字符串
            else {
              processedLine += `<span class="yaml-string">${value}</span>`;
            }
          }
        } else {
          // 处理列表项
          if (line.trim().startsWith('-')) {
            const parts = line.trim().split(' ');
            processedLine = indentHtml + `<span class="yaml-keyword">-</span> `;
            if (parts.length > 1) {
              processedLine += `<span class="yaml-string">${parts.slice(1).join(' ')}</span>`;
            }
          } else {
            processedLine = indentHtml + line.trim();
          }
        }

        highlightedYaml += processedLine + '\n';
      });

      // 添加复制按钮
      const parent = codeBlock.parentNode;
      const toolbar = document.createElement('div');
      toolbar.className = 'code-toolbar';

      const copyBtn = document.createElement('button');
      copyBtn.textContent = '复制';
      copyBtn.onclick = function() {
        navigator.clipboard.writeText(yaml)
          .then(() => {
            const originalText = this.textContent;
            this.textContent = '已复制!';
            setTimeout(() => {
              this.textContent = originalText;
            }, 2000);
          });
      };

      toolbar.appendChild(copyBtn);

      // 更新代码块内容和标记为已高亮
      codeBlock.innerHTML = highlightedYaml;
      codeBlock.classList.add('highlighted');

      // 添加工具栏
      if (!parent.querySelector('.code-toolbar')) {
        parent.style.position = 'relative';
        parent.appendChild(toolbar);
      }
    }
  });
}

// 初始化页面
function initPage() {
  // 检查是否已有导航栏，如果没有，添加一个基本的导航栏
  if (!document.querySelector('.navbar')) {
    addTopNavigation();
  }

  // 检查是否已有页脚容器，如果有，不添加页脚
  if (!document.querySelector('footer') && !document.getElementById('footer-container')) {
    // 创建页脚
    const footer = document.createElement('footer');
    footer.innerHTML = '<p>Tencent Kubernetes Engine <a href="/">接入层运营平台</a></p>';

    // 添加到页面底部
    document.body.appendChild(footer);
  }
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
  // 初始化主题
  initTheme();

  // 检查页面元素并根据需要初始化页面结构
  initPage();

  // 科技感背景已移除，只保留粒子系统效果

  // 增强表格
  enhanceTables();

  // 增强链接
  enhanceLinks();

  // 高亮 YAML 代码
  highlightYaml();

  // 如果存在图表，更新图表主题
  if (window.Chart) {
    updateChartsTheme(document.documentElement.getAttribute('data-theme') === 'dark');
  }
});
