/**
 * TKE 接入层运营平台 - 粒子系统特效脚本
 * 只保留粒子系统效果和加载动画
 */

// 在页面加载完成后初始化粒子系统
document.addEventListener('DOMContentLoaded', function() {
  // 如果在大屏幕上，添加粒子系统效果
  if (window.innerWidth > 992) {
    setTimeout(() => {
      initParticleSystem();
    }, 500);
  }
});

// 粒子系统 - 模拟数据流动
function initParticleSystem() {
  if (document.querySelector('.particle-system')) return;

  const particleContainer = document.createElement('div');
  particleContainer.className = 'particle-system';
  particleContainer.style.position = 'fixed';
  particleContainer.style.top = '0';
  particleContainer.style.left = '0';
  particleContainer.style.width = '100%';
  particleContainer.style.height = '100%';
  particleContainer.style.pointerEvents = 'none';
  particleContainer.style.zIndex = '-5';
  document.body.appendChild(particleContainer);

  // 创建粒子
  for (let i = 0; i < 15; i++) {
    createParticle(particleContainer);
  }
}

// 创建单个粒子
function createParticle(container) {
  const particle = document.createElement('div');

  // 随机位置
  const startX = Math.random() * 100;
  const startY = Math.random() * 100;

  // 随机大小
  const size = Math.random() * 4 + 2;

  // 随机速度
  const duration = Math.random() * 15 + 10;

  // 设置样式
  particle.style.position = 'absolute';
  particle.style.width = `${size}px`;
  particle.style.height = `${size}px`;
  particle.style.backgroundColor = 'var(--primary-color)';
  particle.style.borderRadius = '50%';
  particle.style.opacity = '0.2';
  particle.style.top = `${startY}%`;
  particle.style.left = `${startX}%`;
  particle.style.boxShadow = '0 0 10px var(--primary-color)';

  // 添加到容器
  container.appendChild(particle);

  // 动画
  animateParticle(particle, startX, startY, duration);
}

// 粒子动画
function animateParticle(particle, startX, startY, duration) {
  // 随机目标位置
  const targetX = Math.random() * 100;
  const targetY = Math.random() * 100;

  // 设置动画
  particle.style.transition = `all ${duration}s linear`;

  // 延迟一下，确保过渡效果生效
  setTimeout(() => {
    particle.style.top = `${targetY}%`;
    particle.style.left = `${targetX}%`;

    // 动画结束后重新创建
    setTimeout(() => {
      if (particle.parentNode) {
        particle.parentNode.removeChild(particle);
        createParticle(particle.parentNode);
      }
    }, duration * 1000);
  }, 10);
}

// 添加AI加载动画 - 使用风险扫描页面的查询动画
function showAILoadingEffect(message = '正在处理...') {
  // 创建样式
  if (!document.getElementById('ai-loading-styles')) {
    const style = document.createElement('style');
    style.id = 'ai-loading-styles';
    style.textContent = `
      .ai-loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.85);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .ai-loading-container.active {
        opacity: 1;
      }

      .ai-loading-effect {
        text-align: center;
      }

      .ai-loading-brain {
        position: relative;
        width: 120px;
        height: 120px;
        margin: 0 auto;
      }

      .ai-node {
        position: absolute;
        width: 12px;
        height: 12px;
        background-color: var(--primary-color);
        border-radius: 50%;
        box-shadow: 0 0 15px var(--primary-color);
      }

      .ai-node.n1 { top: 10%; left: 30%; animation: pulse 1.5s infinite alternate; }
      .ai-node.n2 { top: 10%; right: 30%; animation: pulse 1.7s infinite alternate; }
      .ai-node.n3 { top: 40%; left: 10%; animation: pulse 1.9s infinite alternate; }
      .ai-node.n4 { top: 40%; right: 10%; animation: pulse 2.1s infinite alternate; }
      .ai-node.n5 { bottom: 20%; left: 20%; animation: pulse 1.6s infinite alternate; }
      .ai-node.n6 { bottom: 20%; right: 20%; animation: pulse 1.8s infinite alternate; }
      .ai-node.n7 { top: 50%; left: 50%; transform: translate(-50%, -50%); animation: pulse 1.8s infinite alternate; }

      .ai-connection {
        position: absolute;
        height: 2px;
        background: linear-gradient(90deg, var(--primary-color), transparent);
        animation: connection-glow 2s infinite alternate;
      }

      .ai-connection.c1 { width: 30px; top: 22%; left: 26%; transform: rotate(5deg); }
      .ai-connection.c2 { width: 40px; top: 30%; right: 26%; transform: rotate(-45deg); }
      .ai-connection.c3 { width: 35px; top: 50%; left: 18%; }
      .ai-connection.c4 { width: 35px; top: 50%; right: 18%; }
      .ai-connection.c5 { width: 30px; bottom: 30%; left: 35%; transform: rotate(45deg); }
      .ai-connection.c6 { width: 30px; bottom: 30%; right: 35%; transform: rotate(-45deg); }

      @keyframes connection-glow {
        0% { opacity: 0.3; }
        100% { opacity: 0.8; }
      }

      @keyframes pulse {
        0% { transform: scale(1); opacity: 0.7; }
        100% { transform: scale(1.3); opacity: 1; }
      }

      .ai-loading-text {
        margin-top: 20px;
        color: white;
        font-size: 16px;
        animation: pulse 1.5s infinite alternate;
      }
    `;
    document.head.appendChild(style);
  }

  // 创建加载容器
  const container = document.createElement('div');
  container.className = 'ai-loading-container';

  // 创建加载效果
  const effect = document.createElement('div');
  effect.className = 'ai-loading-effect';

  // 创建大脑动画
  const brain = document.createElement('div');
  brain.className = 'ai-loading-brain';

  // 节点和连接线
  for (let i = 1; i <= 7; i++) {
    const node = document.createElement('div');
    node.className = `ai-node n${i}`;
    brain.appendChild(node);
  }

  for (let i = 1; i <= 6; i++) {
    const connection = document.createElement('div');
    connection.className = `ai-connection c${i}`;
    brain.appendChild(connection);
  }

  // 加载文本
  const text = document.createElement('div');
  text.className = 'ai-loading-text';
  text.textContent = message;

  // 组装元素
  effect.appendChild(brain);
  effect.appendChild(text);
  container.appendChild(effect);
  document.body.appendChild(container);

  // 显示加载动画
  setTimeout(() => {
    container.classList.add('active');
  }, 10);

  // 返回关闭函数
  return function hideAILoading() {
    container.classList.remove('active');
    setTimeout(() => {
      container.remove();
    }, 300);
  };
}

// 导出全局函数
window.showAILoadingEffect = showAILoadingEffect;
