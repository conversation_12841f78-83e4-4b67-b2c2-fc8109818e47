<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TKE 接入层运营平台 - 首页</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/tech-theme.css">
    <style>

        /* 主页特定样式 */
        .hero-section {
            min-height: 500px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 3rem 2rem;
            margin-top: 2rem;       /* 增加与顶栏的间距 */
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
            border-radius: var(--card-border-radius);
            background: var(--background-white);
            box-shadow: 0 10px 30px var(--shadow-color);
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, var(--primary-light) 0%, transparent 70%);
            opacity: 0.6;
            z-index: -1;
        }

        .hero-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 1.5rem;
            color: var(--text-light);
            margin-bottom: 2rem;
            max-width: 800px;
        }

        .hero-cta {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .hero-cta .btn {
            padding: 1rem 2rem;
            font-size: 1.1rem;
        }

        .features-section {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }

        /* 更新: 使用三列布局，在较大屏幕上保持每行3个 */
        @media (min-width: 992px) {
            .features-section {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        .feature-card {
            background: var(--background-white);
            border-radius: var(--card-border-radius);
            padding: 2rem;
            box-shadow: 0 5px 20px var(--shadow-color);
            transition: all var(--transition-speed);
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px var(--shadow-color);
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: var(--primary-gradient);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1.5rem;
            color: var(--primary-color);
            background: var(--primary-light);
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 500;
            margin-bottom: 1rem;
            color: var(--text-color);
        }

        .feature-description {
            color: var(--text-light);
            margin-bottom: 1.5rem;
        }

        .feature-link {
            margin-top: auto;
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all var(--transition-speed);
        }

        .feature-link:hover {
            color: var(--primary-hover);
            gap: 0.75rem;
        }

        .k8s-diagram {
            margin: 4rem auto;
            max-width: 900px;
            position: relative;
        }

        .diagram-container {
            background: var(--background-white);
            border-radius: var(--card-border-radius);
            padding: 2rem;
            box-shadow: 0 10px 30px var(--shadow-color);
            position: relative;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .diagram-title {
            text-align: center;
            margin-bottom: 2rem;
            font-weight: 500;
            color: var(--text-color);
        }

        .diagram-img {
            width: 100%;
            height: auto;
            max-height: 400px;
            object-fit: contain;
        }

        .diagram-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, transparent 50%, var(--background-white) 95%);
            pointer-events: none;
        }

        .platform-stats {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 2rem;
            margin: 3rem 0;
        }

        .stat-item {
            text-align: center;
            padding: 1.5rem;
            background: var(--background-white);
            border-radius: var(--card-border-radius);
            box-shadow: 0 5px 15px var(--shadow-color);
            min-width: 200px;
            border: 1px solid var(--border-color);
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            color: var(--text-light);
            font-size: 1.1rem;
        }

        .tech-border {
            position: relative;
        }

        .tech-border::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border: 2px solid var(--primary-color);
            border-radius: var(--card-border-radius);
            opacity: 0.1;
            z-index: -1;
            clip-path: polygon(0% 0%, 100% 0%, 100% calc(100% - 15px), calc(100% - 15px) 100%, 0% 100%);
        }

        .tech-badge {
            display: inline-block;
            background: var(--primary-light);
            color: var(--primary-color);
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }

            .hero-subtitle {
                font-size: 1.2rem;
            }

            .hero-cta {
                flex-direction: column;
            }

            .feature-card {
                padding: 1.5rem;
            }

            .feature-icon {
                width: 60px;
                height: 60px;
                font-size: 2rem;
            }
        }

        /* 修复页脚问题 */
        .page-wrapper {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            position: relative;
            z-index: 1;
        }

        /* 确保主内容区域会推动页脚到底部 */
        .main-content {
            flex: 1 0 auto;
            width: 100%;
            max-width: var(--content-max-width);
            margin: 0 auto;
            position: relative;
            z-index: 5;
            padding: 0 2rem;
            display: flex;
            flex-direction: column;
        }

        /* 修复页脚样式，确保它固定在底部且没有额外空间 */
        footer {
            flex-shrink: 0;
            text-align: center;
            padding: 1.5rem 0;
            height: 60px;
            color: var(--text-light);
            background: var(--background-white);
            margin-top: auto;
            border-top: 1px solid var(--border-color);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            box-sizing: border-box;
        }

        /* 架构图样式优化 */
        .k8s-diagram {
            margin: 4rem auto;
            max-width: 900px;
            position: relative;
        }

        .diagram-container {
            background: var(--background-white);
            border-radius: var(--card-border-radius);
            padding: 2rem;
            box-shadow: 0 10px 30px var(--shadow-color);
            position: relative;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .diagram-title {
            text-align: center;
            margin-bottom: 2rem;
            font-weight: 500;
            color: var(--text-color);
            font-size: 1.5rem;
        }

        /* 彻底修复页脚问题 */
        body {
            min-height: 100vh;
            position: relative;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            overflow-x: hidden;
        }

        html {
            height: 100%;
            overflow-x: hidden;
        }

        .page-wrapper {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            width: 100%;
            position: relative;
            z-index: 1;
            flex: 1 0 auto;
        }

        .main-content {
            flex: 1 0 auto;
            display: flex;
            flex-direction: column;
            width: 100%;
            max-width: var(--content-max-width);
            margin: 0 auto;
            padding: 0 2rem;
            position: relative;
            z-index: 5;
        }

        footer {
            flex-shrink: 0;
            text-align: center;
            padding: 1.5rem 0;
            margin-top: auto;
            margin-bottom: 0;
            height: 60px;
            color: var(--text-light);
            background: var(--background-white);
            border-top: 1px solid var(--border-color);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            box-sizing: border-box;
            z-index: 10;
        }
    </style>
</head>
<body>
    <div class="page-wrapper">
        <!-- 通用头部 -->
        <div id="header-container"></div>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 英雄区域 -->
            <section class="hero-section tech-border">
                <h1 class="hero-title">TKE 接入层运营平台</h1>
                <p class="hero-subtitle">一站式管理 TKE 接入层资源，提供易用、智能的运营排障能力</p>

                <div class="hero-cta">
                    <a href="/cluster-console" class="btn">
                        <i class="bi bi-search"></i> 开始检索
                    </a>
                    <a href="/scan-console" class="btn btn-secondary">
                        <i class="bi bi-search"></i> 扫描风险
                    </a>
                </div>
            </section>

            <!-- 功能特性 -->
            <h2 class="text-center">平台核心功能</h2>
            <div class="features-section">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="bi bi-search"></i>
                    </div>
                    <h3 class="feature-title">集群检索</h3>
                    <p class="feature-description">快速查询集群相关排障信息，一键获取集群配置、接入层资源与状态</p>
                    <a href="/cluster-console" class="feature-link">
                        前往检索 <i class="bi bi-arrow-right"></i>
                    </a>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="bi bi-hdd-network"></i>
                    </div>
                    <h3 class="feature-title">CLB 检索</h3>
                    <p class="feature-description">详细查看 CLB 配置、关联资源和健康状态，便捷诊断负载均衡问题</p>
                    <a href="/clb-console" class="feature-link">
                        前往检索 <i class="bi bi-arrow-right"></i>
                    </a>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="bi bi-diagram-3"></i>
                    </div>
                    <h3 class="feature-title">资源检索</h3>
                    <p class="feature-description">查询 Kubernetes 资源的详细配置和状态，追踪资源关联和依赖关系</p>
                    <a href="/resource-console" class="feature-link">
                        前往检索 <i class="bi bi-arrow-right"></i>
                    </a>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="bi bi-search"></i>
                    </div>
                    <h3 class="feature-title">风险扫描</h3>
                    <p class="feature-description">一键扫描集群中的 CLB 风险，主动发现并预防潜在问题</p>
                    <a href="/scan-console" class="feature-link">
                        前往扫描 <i class="bi bi-arrow-right"></i>
                    </a>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="bi bi-shield-exclamation"></i>
                    </div>
                    <h3 class="feature-title">风险大盘</h3>
                    <p class="feature-description">集中展示集群风险统计与分析，全面了解系统健康状况</p>
                    <a href="/risk-console" class="feature-link">
                        查看大盘 <i class="bi bi-arrow-right"></i>
                    </a>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="bi bi-list-task"></i>
                    </div>
                    <h3 class="feature-title">任务检索</h3>
                    <p class="feature-description">查询和管理系统中的健康检查任务，监控任务执行状态</p>
                    <a href="/task-console" class="feature-link">
                        前往检索 <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>
            <!-- 平台架构图 - 更新架构层级关系 -->
            <div class="k8s-diagram">
                <div class="diagram-container">
                    <h3 class="diagram-title">TKE 接入层架构</h3>
                    <div style="position: relative;">
                        <svg width="100%" height="550" viewBox="0 0 800 550" xmlns="http://www.w3.org/2000/svg">
                            <!-- 顶部运营平台 -->
                            <g transform="translate(400, 50)">
                                <rect x="-200" y="-30" width="400" height="60" rx="10" fill="#3b82f6" opacity="0.9"/>
                                <text x="0" y="10" font-size="20" fill="white" text-anchor="middle">TKE 接入层运营平台</text>
                            </g>

                            <!-- TKE 集群层 -->
                            <g transform="translate(400, 140)">
                                <rect x="-250" y="-30" width="500" height="60" rx="10" fill="#3b82f6" opacity="0.85"/>
                                <text x="0" y="10" font-size="18" fill="white" text-anchor="middle">TKE / EKS 集群</text>
                            </g>

                            <!-- CLB 层 -->
                            <g transform="translate(400, 230)">
                                <rect x="-200" y="-30" width="400" height="60" rx="10" fill="#3b82f6" opacity="0.8"/>
                                <text x="0" y="10" font-size="18" fill="white" text-anchor="middle">CLB (腾讯云负载均衡)</text>
                            </g>

                            <!-- Service/Ingress 层 -->
                            <g transform="translate(250, 320)">
                                <rect x="-150" y="-30" width="300" height="60" rx="10" fill="#3b82f6" opacity="0.75"/>
                                <text x="0" y="10" font-size="16" fill="white" text-anchor="middle">Service Controller</text>
                            </g>

                            <g transform="translate(550, 320)">
                                <rect x="-150" y="-30" width="300" height="60" rx="10" fill="#3b82f6" opacity="0.75"/>
                                <text x="0" y="10" font-size="16" fill="white" text-anchor="middle">Ingress Controller</text>
                            </g>

                            <!-- Pod 层 -->
                            <g transform="translate(250, 410)">
                                <rect x="-150" y="-30" width="300" height="60" rx="10" fill="#3b82f6" opacity="0.7"/>
                                <text x="0" y="10" font-size="16" fill="white" text-anchor="middle">Service 资源</text>
                            </g>

                            <!-- Node 层 -->
                            <g transform="translate(550, 410)">
                                <rect x="-150" y="-30" width="300" height="60" rx="10" fill="#3b82f6" opacity="0.7"/>
                                <text x="0" y="10" font-size="16" fill="white" text-anchor="middle">Ingress 资源</text>
                            </g>

                            <!-- Container 层 -->
                            <g transform="translate(400, 500)">
                                <rect x="-200" y="-30" width="400" height="60" rx="10" fill="#3b82f6" opacity="0.65"/>
                                <text x="0" y="10" font-size="16" fill="white" text-anchor="middle">Container</text>
                            </g>

                            <!-- 连接线 - 从上往下 -->
                            <line x1="400" y1="80" x2="400" y2="110" stroke="#3b82f6" stroke-width="3" stroke-dasharray="5,5"/>
                            <line x1="400" y1="170" x2="400" y2="200" stroke="#3b82f6" stroke-width="3" stroke-dasharray="5,5"/>
                            <line x1="400" y1="260" x2="250" y2="290" stroke="#3b82f6" stroke-width="3" stroke-dasharray="5,5"/>
                            <line x1="400" y1="260" x2="550" y2="290" stroke="#3b82f6" stroke-width="3" stroke-dasharray="5,5"/>
                            <line x1="250" y1="350" x2="250" y2="380" stroke="#3b82f6" stroke-width="3" stroke-dasharray="5,5"/>
                            <line x1="550" y1="350" x2="550" y2="380" stroke="#3b82f6" stroke-width="3" stroke-dasharray="5,5"/>
                            <line x1="250" y1="440" x2="400" y2="470" stroke="#3b82f6" stroke-width="3" stroke-dasharray="5,5"/>
                            <line x1="550" y1="440" x2="400" y2="470" stroke="#3b82f6" stroke-width="3" stroke-dasharray="5,5"/>

                            <!-- 动画点 -->
                            <circle cx="400" cy="95" r="6" fill="#3b82f6">
                                <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
                            </circle>
                            <circle cx="400" cy="185" r="6" fill="#3b82f6">
                                <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite" begin="0.2s"/>
                            </circle>
                            <circle cx="400" cy="275" r="6" fill="#3b82f6">
                                <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite" begin="0.4s"/>
                            </circle>
                            <circle cx="250" cy="365" r="6" fill="#3b82f6">
                                <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite" begin="0.6s"/>
                            </circle>
                            <circle cx="550" cy="365" r="6" fill="#3b82f6">
                                <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite" begin="0.6s"/>
                            </circle>
                            <circle cx="250" cy="455" r="6" fill="#3b82f6">
                                <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite" begin="0.8s"/>
                            </circle>
                            <circle cx="550" cy="455" r="6" fill="#3b82f6">
                                <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite" begin="0.8s"/>
                            </circle>
                            <circle cx="400" cy="525" r="6" fill="#3b82f6">
                                <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite" begin="1s"/>
                            </circle>
                        </svg>
                        <div class="diagram-overlay"></div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 通用页脚 -->
        <div id="footer-container"></div>
    </div>

    <script src="/scripts.js"></script>
    <script src="/k8s-effects.js"></script>
    <script src="/page-loader.js"></script>
</body>
</html>
