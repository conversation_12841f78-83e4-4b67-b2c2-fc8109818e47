/**
 * TKE 接入层运营平台 - 通用 JavaScript 函数
 */

// 初始化主题
function initTheme() {
  const savedTheme = localStorage.getItem('tke-theme') || 'light';
  document.documentElement.setAttribute('data-theme', savedTheme);
  updateThemeToggleIcon(savedTheme);
}

// 切换主题
function toggleTheme() {
  const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
  const newTheme = currentTheme === 'light' ? 'dark' : 'light';

  // 添加过渡动画
  document.body.style.transition = 'opacity 0.3s ease';
  document.body.style.opacity = '0.8';

  setTimeout(() => {
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('tke-theme', newTheme);
    updateThemeToggleIcon(newTheme);

    setTimeout(() => {
      document.body.style.opacity = '1';

      // 恢复默认过渡
      setTimeout(() => {
        document.body.style.transition = '';
      }, 300);
    }, 50);

    // 更新图表主题（如果页面有图表）
    if (typeof updateChartsTheme === 'function') {
      updateChartsTheme(newTheme === 'dark');
    }
  }, 100);
}

// 更新主题切换按钮图标
function updateThemeToggleIcon(theme) {
  const themeToggle = document.getElementById('themeToggle');
  if (themeToggle) {
    themeToggle.innerHTML = theme === 'dark'
      ? '<i class="bi bi-sun-fill"></i>'
      : '<i class="bi bi-moon-fill"></i>';
  }
}

// 显示加载状态
function showLoading(button, text = '处理中...') {
  if (!button) return;

  // 保存原始内容
  button.dataset.originalText = button.innerHTML;

  // 禁用按钮
  button.disabled = true;

  // 添加加载样式和文本
  button.classList.add('btn-loading');
  button.innerHTML = text;

  // 添加脉冲效果
  button.style.animation = 'pulse 1.5s infinite';
}

// 隐藏加载状态
function hideLoading(button) {
  if (!button || !button.dataset.originalText) return;

  // 恢复原始内容
  button.innerHTML = button.dataset.originalText;

  // 启用按钮
  button.disabled = false;

  // 移除加载样式
  button.classList.remove('btn-loading');
  button.style.animation = '';
}

// 科技感装饰已移除，只保留粒子系统效果

// 添加浮动粒子效果 - 更新版本确保不阻挡交互
function addParticles() {
  // 仅在较大屏幕添加粒子效果
  if (window.innerWidth < 992) return;

  const container = document.createElement('div');
  container.id = 'particles-js';
  container.style.position = 'fixed';
  container.style.top = '0';
  container.style.left = '0';
  container.style.width = '100%';
  container.style.height = '100%';
  container.style.zIndex = '-10'; // 确保在最底层
  container.style.pointerEvents = 'none'; // 禁用鼠标事件

  // 确保在所有元素之下
  document.body.insertBefore(container, document.body.firstChild);

  // 减少粒子数量以提高性能
  const particleCount = 25;
  const particleColors = ['#3b82f6', '#60a5fa', '#93c5fd'];

  for (let i = 0; i < particleCount; i++) {
    const particle = document.createElement('div');
    const size = Math.random() * 4 + 1; // 1-5px

    particle.style.position = 'absolute';
    particle.style.width = `${size}px`;
    particle.style.height = `${size}px`;
    particle.style.backgroundColor = particleColors[Math.floor(Math.random() * particleColors.length)];
    particle.style.borderRadius = '50%';
    particle.style.opacity = Math.random() * 0.3 + 0.1; // 0.1-0.4
    particle.style.left = `${Math.random() * 100}%`;
    particle.style.top = `${Math.random() * 100}%`;
    particle.style.pointerEvents = 'none'; // 确保粒子不会阻挡点击

    // 使用更简单的动画以避免性能问题
    const duration = Math.random() * 15 + 10; // 10-25s

    // 添加浮动和渐隐动画
    particle.style.animation = `float ${duration}s infinite ease-in-out`;
    particle.style.animationDelay = `${Math.random() * 5}s`;

    container.appendChild(particle);
  }

  // 添加浮动动画样式
  if (!document.getElementById('particle-styles')) {
    const style = document.createElement('style');
    style.id = 'particle-styles';
    style.textContent = `
      @keyframes float {
        0% { transform: translate(0, 0); }
        25% { transform: translate(${Math.random() * 30}px, ${Math.random() * 30}px); }
        50% { transform: translate(${Math.random() * -30}px, ${Math.random() * 30}px); }
        75% { transform: translate(${Math.random() * -30}px, ${Math.random() * -30}px); }
        100% { transform: translate(0, 0); }
      }
    `;
    document.head.appendChild(style);
  }
}

// 添加科技感键盘交互
function addKeyboardInteractions() {
  document.addEventListener('keydown', (e) => {
    // 按下 / 键，聚焦到搜索框
    if (e.key === '/' && document.activeElement.tagName !== 'INPUT' && document.activeElement.tagName !== 'TEXTAREA') {
      e.preventDefault();
      const input = document.querySelector('input[type="text"]');
      if (input) {
        input.focus();

        // 显示小提示
        showNotification('按 / 键快速搜索', 'info', 2000);
      }
    }

    // 按 Escape 键收起通知
    if (e.key === 'Escape') {
      const notifications = document.querySelectorAll('.notification-show');
      notifications.forEach(notification => {
        notification.classList.remove('notification-show');
        setTimeout(() => {
          if (notification.parentNode) {
            notification.remove();
          }
        }, 300);
      });
    }
  });
}

// 通知提示
function showNotification(message, type = 'info', duration = 3000) {
  let container = document.querySelector('.notification-container');

  if (!container) {
    container = document.createElement('div');
    container.className = 'notification-container';
    document.body.appendChild(container);
  }

  // 设置图标
  let icon = 'info-circle-fill';
  if (type === 'success') icon = 'check-circle-fill';
  if (type === 'warning') icon = 'exclamation-triangle-fill';
  if (type === 'error') icon = 'x-circle-fill';

  // 创建通知元素
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.innerHTML = `
    <i class="bi bi-${icon}"></i>
    <span>${message}</span>
    <button class="notification-close">
      <i class="bi bi-x"></i>
    </button>
  `;

  // 添加到容器
  container.appendChild(notification);

  // 添加显示样式
  setTimeout(() => {
    notification.classList.add('notification-show');
  }, 10);

  // 关闭按钮点击事件
  const closeBtn = notification.querySelector('.notification-close');
  closeBtn.addEventListener('click', () => {
    notification.classList.remove('notification-show');

    setTimeout(() => {
      notification.remove();
    }, 300);
  });

  // 自动关闭
  if (duration > 0) {
    setTimeout(() => {
      if (notification.parentNode) {
        notification.classList.remove('notification-show');

        setTimeout(() => {
          if (notification.parentNode) {
            notification.remove();
          }
        }, 300);
      }
    }, duration);
  }

  return notification;
}

// 表单验证
function validateForm(form) {
  if (!form) return true;

  let isValid = true;
  const inputs = form.querySelectorAll('input, select, textarea');

  // 移除所有现有错误
  form.querySelectorAll('.invalid-feedback').forEach(el => el.remove());
  inputs.forEach(input => {
    input.classList.remove('is-invalid');
  });

  inputs.forEach(input => {
    if (input.hasAttribute('required') && !input.value.trim()) {
      isValid = false;

      // 添加错误样式
      input.classList.add('is-invalid');

      // 如果没有错误提示，添加一个
      const feedback = document.createElement('div');
      feedback.className = 'invalid-feedback';
      feedback.textContent = '此字段必填';
      input.parentNode.appendChild(feedback);

      // 添加抖动效果
      input.style.animation = 'shake 0.5s';
      setTimeout(() => {
        input.style.animation = '';
      }, 500);
    } else {
      input.classList.remove('is-invalid');
    }
  });

  return isValid;
}

// 表格排序功能
function enableTableSort(tableId) {
  const table = document.getElementById(tableId);
  if (!table) return;

  const headers = table.querySelectorAll('th:not(.no-sort)');

  headers.forEach((header, index) => {
    // 只有当单元格不包含.no-sort类时添加排序功能
    header.style.position = 'relative';
    header.style.cursor = 'pointer';
    header.innerHTML += '<i class="bi bi-arrow-down-up sort-icon"></i>';

    header.addEventListener('click', () => {
      const isAscending = header.getAttribute('data-sort') !== 'asc';

      // 重置其他表头
      headers.forEach(h => {
        h.setAttribute('data-sort', '');
        const icon = h.querySelector('.sort-icon');
        if (icon) {
          icon.className = 'bi bi-arrow-down-up sort-icon';
        }
      });

      // 设置当前表头
      header.setAttribute('data-sort', isAscending ? 'asc' : 'desc');
      const icon = header.querySelector('.sort-icon');
      if (icon) {
        icon.className = `bi bi-arrow-${isAscending ? 'up' : 'down'} sort-icon`;
      }

      // 排序行
      const rows = Array.from(table.querySelectorAll('tbody tr'));

      rows.sort((a, b) => {
        let aValue = a.cells[index].textContent.trim();
        let bValue = b.cells[index].textContent.trim();

        // 检查是否为数字
        const aNum = parseFloat(aValue);
        const bNum = parseFloat(bValue);

        if (!isNaN(aNum) && !isNaN(bNum)) {
          return isAscending ? aNum - bNum : bNum - aNum;
        }

        // 字符串比较
        return isAscending
          ? aValue.localeCompare(bValue, 'zh-CN')
          : bValue.localeCompare(aValue, 'zh-CN');
      });

      // 更新表格
      const tbody = table.querySelector('tbody');
      rows.forEach(row => tbody.appendChild(row));
    });
  });
}

// 表格搜索功能
function enableTableSearch(inputId, tableId) {
  const input = document.getElementById(inputId);
  const table = document.getElementById(tableId);

  if (!input || !table) return;

  input.addEventListener('input', () => {
    const term = input.value.toLowerCase();
    const rows = table.querySelectorAll('tbody tr');

    rows.forEach(row => {
      const text = row.textContent.toLowerCase();
      row.style.display = text.includes(term) ? '' : 'none';
    });
  });
}

// 复制到剪贴板
function copyToClipboard(text) {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text)
      .then(() => {
        showNotification('复制成功', 'success');
      })
      .catch(err => {
        showNotification('复制失败: ' + err, 'error');
        fallbackCopyToClipboard(text);
      });
  } else {
    fallbackCopyToClipboard(text);
  }
}

// 降级复制方法
function fallbackCopyToClipboard(text) {
  const textarea = document.createElement('textarea');
  textarea.value = text;
  textarea.style.position = 'fixed';
  textarea.style.left = '-9999px';
  document.body.appendChild(textarea);
  textarea.select();

  try {
    const successful = document.execCommand('copy');
    document.body.removeChild(textarea);

    if (successful) {
      showNotification('复制成功', 'success');
    } else {
      showNotification('复制失败，请手动复制', 'warning');
    }
  } catch (err) {
    document.body.removeChild(textarea);
    showNotification('复制失败: ' + err, 'error');
  }
}

// 初始化可复制元素
function initCopyElements() {
  document.querySelectorAll('[data-copy]').forEach(el => {
    el.style.cursor = 'pointer';
    el.title = '点击复制';

    // 添加复制图标
    if (!el.querySelector('.copy-icon')) {
      const icon = document.createElement('i');
      icon.className = 'bi bi-clipboard copy-icon';
      icon.style.marginLeft = '5px';
      icon.style.fontSize = '0.8em';
      el.appendChild(icon);
    }

    el.addEventListener('click', () => {
      const textToCopy = el.getAttribute('data-copy') || el.textContent.trim();
      copyToClipboard(textToCopy);
    });
  });
}

// 添加科技感加载动画
function addFancyLoader() {
  const loaderContainer = document.createElement('div');
  loaderContainer.className = 'loader-container';
  loaderContainer.style.display = 'none';
  loaderContainer.style.position = 'fixed';
  loaderContainer.style.top = '0';
  loaderContainer.style.left = '0';
  loaderContainer.style.width = '100%';
  loaderContainer.style.height = '100%';
  loaderContainer.style.background = 'rgba(0, 0, 0, 0.7)';
  loaderContainer.style.zIndex = '9999';
  loaderContainer.style.display = 'flex';
  loaderContainer.style.justifyContent = 'center';
  loaderContainer.style.alignItems = 'center';
  loaderContainer.style.opacity = '0';
  loaderContainer.style.transition = 'opacity 0.3s';
  loaderContainer.style.backdropFilter = 'blur(5px)';

  const loader = document.createElement('div');
  loader.className = 'fancy-loader';
  loader.innerHTML = `
    <div class="cube-loader">
      <div class="cube-top"></div>
      <div class="cube-wrapper">
        <span style="--i:0"></span>
        <span style="--i:1"></span>
        <span style="--i:2"></span>
        <span style="--i:3"></span>
      </div>
    </div>
    <div class="loading-text">正在加载中...</div>
  `;

  loaderContainer.appendChild(loader);
  document.body.appendChild(loaderContainer);

  // 添加样式
  const style = document.createElement('style');
  style.textContent = `
    .cube-loader {
      position: relative;
      width: 80px;
      height: 80px;
      margin-bottom: 20px;
    }

    .cube-top {
      position: absolute;
      width: 40px;
      height: 40px;
      background: var(--primary-color);
      transform: rotateX(90deg) translateZ(20px);
      transform-origin: 50% 50%;
      animation: cube-top 2s infinite;
      animation-delay: 0.5s;
    }

    @keyframes cube-top {
      0% {
        opacity: 1;
        transform: rotateX(90deg) translateZ(20px);
      }
      25% {
        opacity: 1;
        transform: rotateX(90deg) translateZ(40px);
      }
      75% {
        opacity: 1;
        transform: rotateX(90deg) translateZ(40px);
      }
      100% {
        opacity: 1;
        transform: rotateX(90deg) translateZ(20px);
      }
    }

    .cube-wrapper {
      position: absolute;
      width: 40px;
      height: 40px;
      transform-style: preserve-3d;
      animation: cube-wrapper 2s infinite;
    }

    @keyframes cube-wrapper {
      0% {
        transform: rotateX(0) rotateY(0);
      }
      25% {
        transform: rotateX(0) rotateY(90deg);
      }
      50% {
        transform: rotateX(0) rotateY(180deg);
      }
      75% {
        transform: rotateX(0) rotateY(270deg);
      }
      100% {
        transform: rotateX(0) rotateY(360deg);
      }
    }

    .cube-wrapper span {
      position: absolute;
      width: 40px;
      height: 40px;
      transform-style: preserve-3d;
      transform-origin: 50% 50%;
    }

    .cube-wrapper span::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      background: var(--primary-color);
      opacity: 0.8;
      transition: 0.3s;
    }

    .cube-wrapper span:nth-child(1)::before {
      transform: rotateY(0) translateZ(20px);
    }

    .cube-wrapper span:nth-child(2)::before {
      transform: rotateY(90deg) translateZ(20px);
    }

    .cube-wrapper span:nth-child(3)::before {
      transform: rotateY(180deg) translateZ(20px);
    }

    .cube-wrapper span:nth-child(4)::before {
      transform: rotateY(270deg) translateZ(20px);
    }

    .loading-text {
      color: white;
      font-size: 18px;
      margin-top: 20px;
      animation: pulse 1.5s infinite;
    }
  `;

  document.head.appendChild(style);

  // 公开显示和隐藏方法
  window.showFancyLoader = function() {
    loaderContainer.style.display = 'flex';
    setTimeout(() => {
      loaderContainer.style.opacity = '1';
    }, 10);
  };

  window.hideFancyLoader = function() {
    loaderContainer.style.opacity = '0';
    setTimeout(() => {
      loaderContainer.style.display = 'none';
    }, 300);
  };
}

// 页面初始化 - 更新确保正确加载顺序
document.addEventListener('DOMContentLoaded', function() {
  // 初始化主题
  initTheme();

  // 尝试增强状态元素，但不影响模板变量
  try {
    enhanceStatusElements();
  } catch (e) {
    console.log('Status enhancement skipped:', e);
  }

  // 表格排序初始化
  document.querySelectorAll('table[data-sortable]').forEach(table => {
    if (table.id) {
      enableTableSort(table.id);
    }
  });

  // 表格搜索初始化
  document.querySelectorAll('input[data-search-table]').forEach(input => {
    if (input.id && input.getAttribute('data-search-table')) {
      enableTableSearch(input.id, input.getAttribute('data-search-table'));
    }
  });

  // 初始化可复制元素
  initCopyElements();

  // 表单提交按钮加载状态
  document.querySelectorAll('form').forEach(form => {
    form.addEventListener('submit', (e) => {
      const submitBtn = form.querySelector('button[type="submit"]');
      if (submitBtn) {
        showLoading(submitBtn);
      }
    });
  });

  // 支持回车提交
  document.querySelectorAll('input[required]').forEach(input => {
    input.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        e.preventDefault();
        const form = this.closest('form');
        const submitBtn = form?.querySelector('button[type="submit"], button[type="button"]');
        if (submitBtn && typeof submitBtn.onclick === 'function') {
          submitBtn.onclick();
        }
      }
    });
  });

  // 添加键盘交互
  addKeyboardInteractions();

  // 只保留粒子系统效果
  setTimeout(() => {
    // 尝试添加粒子效果（仅在大屏幕）
    if (window.innerWidth > 992) {
      addParticles();
    }
  }, 1000);

  // 页面加载完成提示已移除
});

// 添加徽章样式
function enhanceStatusElements() {
  // 风险等级徽章
  document.querySelectorAll('td').forEach(td => {
    const text = td.textContent.trim();
    if (text === '高') {
      td.innerHTML = '<span class="badge badge-danger">高</span>';
    } else if (text === '中') {
      td.innerHTML = '<span class="badge badge-warning">中</span>';
    } else if (text === '低') {
      td.innerHTML = '<span class="badge badge-info">低</span>';
    }
  });

  // 任务状态徽章
  document.querySelectorAll('td').forEach(td => {
    const text = td.textContent.trim();
    if (text === 'Running') {
      td.innerHTML = '<span class="badge badge-warning">Running</span>';
    } else if (text === 'Done') {
      td.innerHTML = '<span class="badge badge-success">Done</span>';
    } else if (text === 'Terminated') {
      td.innerHTML = '<span class="badge badge-danger">Terminated</span>';
    }
  });
}

// 表单提交函数
function submitForm(formId, url, method = 'get', showLoadingBtn = true) {
  const form = document.getElementById(formId);
  if (!form) return;

  // 验证表单
  if (!validateForm(form)) return;

  // 收集表单数据
  const formData = new FormData(form);
  const params = new URLSearchParams();

  for (const [key, value] of formData.entries()) {
    if (value) {
      params.append(key, value);
    }
  }

  // 显示加载状态
  const submitBtn = form.querySelector('button[type="submit"], button[type="button"]');
  if (showLoadingBtn && submitBtn) {
    showLoading(submitBtn);
  }

  // 构建URL
  const queryString = params.toString();
  const fullUrl = method.toLowerCase() === 'get' && queryString
    ? `${url}?${queryString}`
    : url;

  // 对于GET请求，直接重定向
  if (method.toLowerCase() === 'get') {
    window.location.href = fullUrl;
    return;
  }

  // 对于其他请求，使用fetch API
  fetch(url, {
    method: method.toUpperCase(),
    body: method.toLowerCase() === 'get' ? null : params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
  .then(response => {
    if (!response.ok) {
      throw new Error('请求失败');
    }
    return response.json();
  })
  .then(data => {
    // 处理成功响应
    showNotification('操作成功', 'success');

    // 如果有回调函数，调用它
    if (typeof window.formSubmitSuccess === 'function') {
      window.formSubmitSuccess(data);
    }
  })
  .catch(error => {
    // 处理错误
    showNotification('操作失败: ' + error.message, 'error');
  })
  .finally(() => {
    // 隐藏加载状态
    if (showLoadingBtn && submitBtn) {
      hideLoading(submitBtn);
    }
  });
}

// 科技感动画已移除，只保留粒子系统效果
