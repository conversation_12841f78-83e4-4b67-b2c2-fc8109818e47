<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TKE 接入层运营平台 - 集群排障信息</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/tech-theme.css">
    <link rel="stylesheet" href="/report-styles.css">
</head>
<body>
    <div class="page-wrapper">

        <main class="main-content">
            <div class="container">
                <div class="header">
                    <h1>集群排障信息</h1>
                </div>

                <h2>基本信息</h2>
                <table>
                    <tr>
                        <th>信息类型</th>
                        <th>详情</th>
                    </tr>
                    <tr>
                        <td>APPID</td>
                        <td>{{ .AppID }}</td>
                    </tr>
                    <tr>
                        <td>集群 ID</td>
                        <td>{{ .ClusterID }}</td>
                    </tr>
                    <tr>
                        <td>集群名称</td>
                        <td>{{ .ClusterName }}</td>
                    </tr>
                    <tr>
                        <td>集群地域</td>
                        <td>{{ .ClusterRegion }}</td>
                    </tr>
                    <tr>
                        <td>集群类型</td>
                        <td>{{ .ClusterType }}</td>
                    </tr>
                    <tr>
                        <td>MetaClusterID</td>
                        <td>{{ .MetaClusterID }}</td>
                    </tr>
                    <tr>
                        <td>集群描述</td>
                        <td>{{ .Description }}</td>
                    </tr>
                    <tr>
                        <td>集群状态</td>
                        <td>{{ .State }}</td>
                    </tr>
                    <tr>
                        <td>报告过期时间</td>
                        <td>{{ .ExpireAt }}</td>
                    </tr>
                </table>

                <h2>网络信息</h2>
                <table>
                    <tr>
                        <th>信息类型</th>
                        <th>详情</th>
                    </tr>
                    <tr>
                        <td>K8S版本</td>
                        <td>{{ .K8SVersion }}</td>
                    </tr>
                    <tr>
                        <td>VPCID</td>
                        <td>{{ .VpcID }}</td>
                    </tr>
                    <tr>
                        <td>子网ID</td>
                        <td>{{ .SubnetID }}</td>
                    </tr>
                    <tr>
                        <td>ServiceCIDR</td>
                        <td>{{ .ServiceCIDR }}</td>
                    </tr>
                    <tr>
                        <td>网络模式</td>
                        <td>{{ .NetworkType }}</td>
                    </tr>
                    <tr>
                        <td>kube-proxy转发模式</td>
                        <td>{{ .KubeProxyMode }}</td>
                    </tr>
                </table>

                <h2>Service Controller 组件信息</h2>
                <table>
                    <tr>
                        <th>信息类型</th>
                        <th>详情</th>
                    </tr>
                    <tr>
                        <td>镜像信息</td>
                        <td>{{ .ServiceControllerImage }}</td>
                    </tr>
                    <tr>
                        <td>镜像版本</td>
                        <td>{{ .ServiceControllerVersion }}</td>
                    </tr>
                    <tr>
                        <td>副本数</td>
                        <td>{{ .ServiceExpectReplicas }}</td>
                    </tr>
                    <tr>
                        <td>可用副本数</td>
                        <td>{{ .ServiceAvailableReplicas }}</td>
                    </tr>
                    <tr>
                        <td>指定参数</td>
                        <td>{{ .ServiceArgs }}</td>
                    </tr>
                    <tr>
                        <td>镜像拉取策略</td>
                        <td>{{ .ServiceImagePullPolicy }}</td>
                    </tr>
                    <tr>
                        <td>ConfigMap参数</td>
                        <td>{{ .ServiceConfigMap }}</td>
                    </tr>
                </table>

                <h2>Ingress Controller 组件信息</h2>
                <table>
                    <tr>
                        <th>信息类型</th>
                        <th>详情</th>
                    </tr>
                    <tr>
                        <td>镜像信息</td>
                        <td>{{ .IngressControllerImage }}</td>
                    </tr>
                    <tr>
                        <td>镜像版本</td>
                        <td>{{ .IngressControllerVersion }}</td>
                    </tr>
                    <tr>
                        <td>副本数</td>
                        <td>{{ .IngressExpectReplicas }}</td>
                    </tr>
                    <tr>
                        <td>可用副本数</td>
                        <td>{{ .IngressAvailableReplicas }}</td>
                    </tr>
                    <tr>
                        <td>指定参数</td>
                        <td>{{ .IngressArgs }}</td>
                    </tr>
                    <tr>
                        <td>镜像拉取策略</td>
                        <td>{{ .IngressImagePullPolicy }}</td>
                    </tr>
                    <tr>
                        <td>ConfigMap参数</td>
                        <td>{{ .IngressConfigMap }}</td>
                    </tr>
                </table>

                <h2>周边系统</h2>
                <table>
                <tr>
                    <td>集群OSS信息</td>
                    <td><a href="{{ .References.OSSCluster }}">查看详情</a></td>
                </tr>
                <tr>
                    <td>用户OSS信息</td>
                    <td><a href="{{ .References.OSSUser }}">查看详情</a></td>
                </tr>
                <tr>
                    <td>用户CLB配额</td>
                    <td><a href="{{ .References.Quota }}">查看详情</a></td>
                </tr>
                <tr>
                    <td>service-controller 监控</td>
                    <td><a href="{{ .References.MonitorServiceController }}">查看详情</a></td>
                </tr>
                <tr>
                    <td>托管组件监控</td>
                    <td><a href="{{ .References.MonitorMetaComponent }}">查看详情</a></td>
                </tr>
                </table>

                <h2>接入层资源统计</h2>
                <h3>CLB 资源统计</h2>
                    <table>
                        <tr>
                            <th>统计维度</th>
                            <th>资源个数</th>
                        </tr>
                        <tr>
                            <td>CLB 总数</td>
                            <td>{{ .Statistics.CLB.TotalCount }}</td>
                        </tr>
                    </table>

                <h2>CLB 列表</h2>
                {{ $clusterID := .ClusterID }}
                <table>
                    <tr>
                        <th>CLB 名称</th>
                        <th>CLB 使用协议</th>
                        <th>关联资源</th>
                    </tr>
                    {{ range $code, $score := .Analysises.Details }}
                    <tr>
                        <td><a href='/inspection/clb/report/get?clbID={{ $score.CLBID }}&clusterID={{ $clusterID }}&refreshCache=true'>{{ $score.CLBID }}</a></td>
                        <td>{{ $score.Protocols }}</td>
                        <td>{{ $score.UsingResources }}</td>
                    </tr>
                    {{ end }}
                </table>
            </div>
        </main>

        <!-- 通用页脚 -->
        <div id="footer-container"></div>
    </div>

    <script src="/k8s-effects.js"></script>
    <script src="/page-loader.js"></script>
    <script src="/report-scripts.js"></script>
</body>
</html>
