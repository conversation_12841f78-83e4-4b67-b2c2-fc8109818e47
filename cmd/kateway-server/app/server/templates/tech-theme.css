/* TKE 接入层运营平台 - 现代科技主题 */
:root {
  /* 主色调 - 蓝色系 */
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --primary-light: rgba(59, 130, 246, 0.1);
  --primary-gradient: linear-gradient(135deg, #3b82f6, #2563eb);
  
  /* 次要色调 - 深色系 */
  --secondary-color: #1e293b;
  --secondary-light: #334155;
  --secondary-dark: #0f172a;
  --secondary-gradient: linear-gradient(135deg, #1e293b, #0f172a);
  
  /* 状态颜色 */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #0ea5e9;
  
  /* 文本颜色 */
  --text-color: #334155;
  --text-light: #64748b;
  --text-lighter: #94a3b8;
  
  /* 背景颜色 */
  --background-light: #f8fafc;
  --background-white: #ffffff;
  --border-color: #e2e8f0;
  --shadow-color: rgba(0, 0, 0, 0.08);
  
  /* 圆角和过渡 */
  --card-border-radius: 16px;
  --input-border-radius: 10px;
  --button-border-radius: 10px;
  --transition-speed: 0.3s;
  
  /* 布局变量 */
  --header-height: 68px;
  --content-max-width: 1440px;
  --footer-height: 60px;
  
  /* 字体 */
  --font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
}

/* 暗色主题 */
[data-theme="dark"] {
  --primary-color: #3b82f6;
  --primary-hover: #60a5fa;
  --primary-light: rgba(59, 130, 246, 0.15);
  
  --secondary-color: #1e1e2d;
  --secondary-light: #2a2a3c;
  --secondary-dark: #15151f;
  --secondary-gradient: linear-gradient(135deg, #1e1e2d, #15151f);
  
  --text-color: #e2e8f0;
  --text-light: #cbd5e1;
  --text-lighter: #94a3b8;
  
  --background-light: #0f172a;
  --background-white: #1e293b;
  --border-color: #334155;
  --shadow-color: rgba(0, 0, 0, 0.3);
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  background-color: var(--background-light);
  color: var(--text-color);
  line-height: 1.6;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  transition: background-color var(--transition-speed), color var(--transition-speed);
  position: relative;
  overflow-x: hidden;
}

/* 科技感背景网格 */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25px 25px, var(--border-color) 1px, transparent 0),
    radial-gradient(circle at 75px 75px, var(--border-color) 1px, transparent 0);
  background-size: 100px 100px;
  opacity: 0.3;
  z-index: -10;
  pointer-events: none;
}

/* 页面包装器 */
.page-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
  position: relative;
  z-index: 1;
  flex: 1 0 auto;
  overflow-x: hidden;
}

/* 现代化科技感头部 */
.tech-header {
  position: sticky;
  top: 0;
  width: 100%;
  z-index: 1000;
}

/* 导航栏 */
.navbar {
  height: var(--header-height);
  padding: 0 2rem;
  display: flex;
  align-items: center;
  position: relative;
  background: var(--secondary-gradient);
  box-shadow: 0 4px 20px var(--shadow-color);
  transition: all var(--transition-speed);
  overflow: hidden;
}

/* 品牌标志容器 */
.logo-container {
  position: relative;
  display: flex;
  align-items: center;
}

.brand-text {
  font-size: 1.3rem;
  font-weight: 500;
  color: white;
  position: relative;
  z-index: 2;
}

/* 品牌标志发光效果 */
.logo-glow {
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-color);
  opacity: 0.2;
  filter: blur(10px);
  animation: logo-pulse 3s infinite alternate;
  z-index: 1;
  left: 0;
}

@keyframes logo-pulse {
  0% { transform: scale(0.8); opacity: 0.2; }
  100% { transform: scale(1.2); opacity: 0.4; }
}

.navbar-brand {
  font-size: 1.3rem;
  font-weight: 500;
  color: white;
  text-decoration: none;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
  margin-right: 2rem;
}

.navbar-brand i {
  margin-right: 0.75rem;
  font-size: 1.5rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.5));
}

.nav-links {
  display: flex;
  gap: 0.75rem;
}

.nav-link {
  color: rgba(255, 255, 255, 0.85);
  text-decoration: none;
  padding: 0.65rem 1rem;
  border-radius: 8px;
  transition: all var(--transition-speed);
  font-weight: 400;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity var(--transition-speed);
  z-index: -1;
  border-radius: 8px;
  pointer-events: none;
}

.nav-link.active::before {
  opacity: 1;
}

.nav-link:hover::before {
  opacity: 0.7;
}

.nav-link:hover, .nav-link.active {
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.nav-link i {
  margin-right: 0.5rem;
  font-size: 1.05rem;
}

.nav-spacer {
  margin-left: auto;
}

/* K8s 头部装饰元素 */
.k8s-header-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.k8s-wheel {
  position: absolute;
  top: -30px;
  right: -30px;
  width: 120px;
  height: 120px;
  border: 2px solid rgba(59, 130, 246, 0.1);
  border-radius: 50%;
  animation: rotate 20s linear infinite;
}

.k8s-wheel::before,
.k8s-wheel::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
}

.k8s-wheel::before {
  width: 80%;
  height: 80%;
  border: 2px solid rgba(59, 130, 246, 0.15);
  animation: rotate 15s linear infinite reverse;
}

.k8s-wheel::after {
  width: 60%;
  height: 60%;
  border: 2px solid rgba(59, 130, 246, 0.2);
  animation: rotate 10s linear infinite;
}

.k8s-nodes-small {
  position: absolute;
  top: 10px;
  left: 30%;
  width: 40px;
  height: 40px;
  opacity: 0.15;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath fill='%233b82f6' d='M15.9.2c-.3 0-.6.1-.9.1-.2.1-.5.1-.7.3L2.5 7.7c-.5.3-.9.6-1.2 1-.4.3-.6.8-.6 1.3v12.1c0 .5.2 1 .6 1.4.4.4.8.7 1.2.9l11.8 7c.2.1.5.2.8.3.3.1.5.1.8.1s.6 0 .9-.1.5-.2.8-.3l11.9-7c.5-.3.9-.6 1.2-.9.4-.4.6-.9.6-1.4V10c0-.5-.2-1-.6-1.4-.4-.4-.8-.7-1.2-.9L17.5.6c-.2-.1-.5-.2-.7-.3-.3 0-.6-.1-.9-.1zm.1 1.6c.2 0 .4 0 .6.1.2 0 .3.1.5.2l11.8 7c.2.1.3.2.5.4.2.1.3.3.3.7v12.1c0 .3-.1.5-.3.7-.2.2-.3.3-.5.4l-11.8 7c-.2.1-.3.2-.5.2-.2.1-.4.1-.6.1s-.4 0-.6-.1c-.2 0-.3-.1-.5-.2l-11.8-7c-.2-.1-.3-.2-.5-.4-.2-.2-.3-.4-.3-.7V10c0-.3.1-.5.3-.7.2-.2.3-.3.5-.4l11.8-7c.2-.1.3-.2.5-.2.2-.1.4-.1.6-.1z'/%3E%3Cpath fill='%233b82f6' d='M22.4 17.5c-.5 0-1.2.3-1.2.3l-4.7-4.8.1-7.5s.7.1 1-.1c.3-.2.8-1.2.1-2.8-.7-1.6-2.3-1.9-2.8-1.9s-2.1.3-2.8 1.9c-.7 1.6-.2 2.6.1 2.8.3.2 1 .1 1 .1l.1 7.5-4.7 4.8S7.8 17.5 7.3 17.5c-.5 0-2 .3-2.3 2.3-.3 2.1.8 2.6 1.2 2.7.4.1 1.5.1 1.5.1s3.3 3.5 3.8 3.9c.5.4 1.1.5 1.5.5.4 0 1 0 1.5-.5.5-.4 3.8-3.9 3.8-3.9s1.1 0 1.5-.1c.4-.1 1.5-.6 1.2-2.7-.3-2-1.8-2.3-2.3-2.3h.7z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  animation: float 6s ease-in-out infinite;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

/* 主题切换按钮 */
.theme-toggle {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  margin-left: 0.75rem;
  transition: all var(--transition-speed);
  position: relative;
  overflow: hidden;
}

.theme-toggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity var(--transition-speed);
  z-index: -1;
  border-radius: 50%;
  pointer-events: none;
}

.theme-toggle:hover::before {
  opacity: 1;
}

.theme-toggle:hover {
  transform: rotate(12deg);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
}

/* 主内容区域 */
.main-content {
  flex: 1 0 auto;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  width: 100%;
  max-width: var(--content-max-width);
  position: relative;
  z-index: 5;
  min-height: calc(100vh - var(--header-height) - var(--footer-height));
}

/* 科技感页脚 */
.tech-footer {
  flex-shrink: 0;
  padding: 1.5rem 0;
  height: var(--footer-height);
  color: var(--text-light);
  background: var(--background-white);
  border-top: 1px solid var(--border-color);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  box-sizing: border-box;
  margin-top: auto;
  z-index: 10;
  overflow: hidden;
}

.footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  z-index: 2;
}

.footer-brand {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.footer-brand i {
  color: var(--primary-color);
}

.footer-links {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.footer-links a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-speed);
}

.footer-links a:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

.footer-divider {
  color: var(--text-lighter);
}

/* 页脚装饰元素 */
.footer-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.tech-circuit {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0.3;
}

.tech-nodes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.tech-node {
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--primary-color);
  opacity: 0.3;
}

.tech-node.n1 {
  top: 10px;
  left: 20%;
  animation: pulse 3s infinite alternate;
}

.tech-node.n2 {
  top: 10px;
  left: 50%;
  animation: pulse 4s infinite alternate;
}

.tech-node.n3 {
  top: 10px;
  left: 80%;
  animation: pulse 5s infinite alternate;
}

@keyframes pulse {
  0% { opacity: 0.2; transform: scale(0.8); }
  100% { opacity: 0.5; transform: scale(1.2); }
}

/* 容器样式 */
.container {
  width: 100%;
  max-width: 1200px;
  background: var(--background-white);
  padding: 2.5rem;
  border-radius: var(--card-border-radius);
  box-shadow: 
    0 10px 30px var(--shadow-color),
    0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all var(--transition-speed);
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
  z-index: 10;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 150px;
  height: 150px;
  background: var(--primary-gradient);
  opacity: 0.05;
  border-radius: 0 0 0 100%;
  z-index: -1;
  pointer-events: none;
}

/* 搜索容器 */
.search-container {
  width: 650px;
  max-width: 100%;
  min-height: 400px;
  text-align: center;
  margin: 0 auto;
  position: relative;
  background: var(--background-white);
  padding: 2.5rem;
  border-radius: var(--card-border-radius);
  box-shadow: 
    0 20px 40px var(--shadow-color),
    0 1px 5px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
  z-index: 10;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.search-container::before,
.search-container::after {
  content: '';
  position: absolute;
  background: var(--primary-gradient);
  opacity: 0.05;
  z-index: -1;
  pointer-events: none;
}

.search-container::before {
  top: 0;
  right: 0;
  width: 150px;
  height: 100px;
  border-radius: 0 var(--card-border-radius) 0 100%;
}

.search-container::after {
  bottom: 0;
  left: 0;
  width: 100px;
  height: 75px;
  border-radius: 0 100% 0 var(--card-border-radius);
}

/* 表单元素 */
.form-group {
  margin-bottom: 1.75rem;
  position: relative;
  z-index: 5;
}

.form-control {
  width: 100%;
  padding: 0.85rem 1.2rem;
  border: 2px solid var(--border-color);
  border-radius: var(--input-border-radius);
  font-size: 1rem;
  color: var(--text-color);
  transition: all var(--transition-speed);
  background-color: var(--background-white);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
  position: relative;
  z-index: 5;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 
    0 0 0 4px var(--primary-light),
    0 0 10px rgba(59, 130, 246, 0.2);
  z-index: 6;
  animation: form-glow 1.5s infinite alternate;
}

@keyframes form-glow {
  from { box-shadow: 0 0 0 4px var(--primary-light); }
  to { box-shadow: 0 0 0 4px var(--primary-light), 0 0 15px rgba(59, 130, 246, 0.4); }
}

/* 按钮 */
.btn {
  display: inline-block;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  padding: 0.9rem 1.5rem;
  font-size: 1rem;
  border-radius: var(--button-border-radius);
  transition: all var(--transition-speed);
  color: #fff;
  background: var(--primary-gradient);
  border: none;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.35);
  position: relative;
  overflow: hidden;
  z-index: 5;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s;
  z-index: -1;
  pointer-events: none;
}

.btn:hover::before {
  left: 100%;
}

.btn:hover {
  box-shadow: 0 6px 15px rgba(59, 130, 246, 0.45);
  transform: translateY(-2px);
}

.btn:active {
  transform: translateY(0);
  box-shadow: 0 4px 10px rgba(59, 130, 246, 0.35);
}

.btn-block {
  display: block;
  width: 100%;
}

.btn i {
  margin-right: 0.5rem;
}

/* 标题文本 */
h1, h2, h3 {
  color: var(--text-color);
  line-height: 1.3;
  font-weight: 500;
}

h1 {
  font-size: 2.2rem;
  margin-bottom: 1rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
}

h2 {
  font-size: 1.8rem;
  margin-bottom: 1.25rem;
}

h3 {
  font-size: 1.4rem;
  font-weight: 400;
  margin-bottom: 1.5rem;
  color: var(--text-light);
}

/* 表格样式 */
table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin: 1.5rem 0;
  background-color: var(--background-white);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px var(--shadow-color);
}

thead {
  background: var(--primary-gradient);
  color: white;
}

th, td {
  padding: 1.2rem 1.5rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

th {
  font-weight: 500;
  position: relative;
}

tbody tr {
  transition: background-color var(--transition-speed);
}

tbody tr:nth-child(even) {
  background-color: rgba(0, 0, 0, 0.02);
}

tbody tr:hover {
  background-color: var(--primary-light);
}

/* 响应式调整 */
@media (max-width: 992px) {
  .navbar {
    padding: 0 1.5rem;
  }
  
  .main-content {
    padding: 2rem 1.5rem;
  }
  
  .container, .search-container {
    padding: 2rem;
  }
  
  h1 {
    font-size: 1.8rem;
  }
  
  h3 {
    font-size: 1.25rem;
  }
}

@media (max-width: 768px) {
  .navbar {
    padding: 0 1rem;
    height: auto;
    min-height: var(--header-height);
  }
  
  .nav-links {
    flex-wrap: nowrap;
    overflow-x: auto;
    padding-bottom: 0.5rem;
    width: 100%;
    order: 3;
    margin-top: 0.5rem;
    justify-content: flex-start;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
  }
  
  .nav-links::-webkit-scrollbar {
    display: none;
  }
  
  .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
    white-space: nowrap;
  }
  
  .nav-link span {
    display: none;
  }
  
  .nav-link i {
    margin-right: 0;
    font-size: 1.2rem;
  }
  
  .search-container {
    width: 95%;
    padding: 1.5rem 1rem;
    min-height: auto;
  }
  
  .tech-footer {
    padding: 1rem 0.5rem;
    height: auto;
    flex-direction: column;
    gap: 0.5rem;
  }
}

/* 工具类 */
.text-center { text-align: center; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.mb-5 { margin-bottom: 2.5rem; }
