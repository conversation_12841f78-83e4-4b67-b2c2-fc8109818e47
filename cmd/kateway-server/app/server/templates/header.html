<!-- Modern Tech-Focused Header Component -->
<header class="tech-header">
    <nav class="navbar">
        <a href="/" class="navbar-brand">
            <div class="logo-container">
                <i class="bi bi-cloud-check"></i>
                <span class="brand-text">TKE 接入层运营平台</span>
                <div class="logo-glow"></div>
            </div>
        </a>
        
        <div class="nav-links">
            <a href="/" class="nav-link" id="nav-home">
                <i class="bi bi-house"></i>
                <span>首页</span>
            </a>
            <a href="/cluster-console" class="nav-link" id="nav-cluster">
                <i class="bi bi-search"></i>
                <span>集群检索</span>
            </a>
            <a href="/clb-console" class="nav-link" id="nav-clb">
                <i class="bi bi-hdd-network"></i>
                <span>CLB检索</span>
            </a>
            <a href="/resource-console" class="nav-link" id="nav-resource">
                <i class="bi bi-diagram-3"></i>
                <span>资源检索</span>
            </a>
            <a href="/scan-console" class="nav-link" id="nav-scan">
                <i class="bi bi-scan"></i>
                <span>风险扫描</span>
            </a>
            <a href="/risk-console" class="nav-link" id="nav-risk">
                <i class="bi bi-shield-exclamation"></i>
                <span>风险大盘</span>
            </a>
            <a href="/task-console" class="nav-link" id="nav-task">
                <i class="bi bi-list-task"></i>
                <span>任务检索</span>
            </a>
        </div>
        
        <div class="nav-spacer"></div>
        
        <a href="https://panel.woa.com/d/u7mCbX7Nk/jian-kong-dao-hang?orgId=5" class="nav-link" title="监控看板">
            <i class="bi bi-file-earmark-code"></i>
            <span>监控看板</span>
        </a>
        <a href="https://iwiki.woa.com/p/4012233007" class="nav-link" title="操作手册">
            <i class="bi bi-book"></i>
            <span>操作手册</span>
        </a>
        
        <button id="themeToggle" class="theme-toggle" onclick="toggleTheme()" title="切换主题">
            <i class="bi bi-moon-fill"></i>
        </button>
    </nav>
    
    <!-- K8s Decorative Elements -->
    <div class="k8s-header-decoration">
        <div class="k8s-wheel"></div>
        <div class="k8s-nodes-small"></div>
    </div>
</header>

<script>
    // Set active navigation link based on current page
    document.addEventListener('DOMContentLoaded', function() {
        const path = window.location.pathname;
        let activeNavId = 'nav-home';
        
        if (path.includes('cluster-console')) activeNavId = 'nav-cluster';
        else if (path.includes('clb-console')) activeNavId = 'nav-clb';
        else if (path.includes('resource-console')) activeNavId = 'nav-resource';
        else if (path.includes('scan-console')) activeNavId = 'nav-scan';
        else if (path.includes('risk-console')) activeNavId = 'nav-risk';
        else if (path.includes('task-console')) activeNavId = 'nav-task';
        
        const activeNav = document.getElementById(activeNavId);
        if (activeNav) activeNav.classList.add('active');
    });
</script>
