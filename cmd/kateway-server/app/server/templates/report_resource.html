<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TKE 接入层运营平台 - 资源排障信息</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/tech-theme.css">
    <link rel="stylesheet" href="/report-styles.css">
</head>
<body>
    <div class="page-wrapper">

        <main class="main-content">
            <div class="container">
                <h1>资源排障信息</h1>

                <h2>资源信息</h2>
                <table>
                    <tr>
                        <th>信息</th>
                        <th>详情</th>
                    </tr>
                    <tr>
                        <td>资源类型</td>
                        <td>{{ .Type }}</td>
                    </tr>
                    <tr>
                        <td>命名空间</td>
                        <td>{{ .Namespace }}</td>
                    </tr>
                    <tr>
                        <td>资源名称</td>
                        <td>{{ .Name }}</td>
                    </tr>
                    <tr>
                        <td>资源状态</td>
                        <td>{{ .Status }}</td>
                    </tr>
                    <tr>
                        <td>创建时间</td>
                        <td>{{ .CreateTime }}</td>
                    </tr>
                    <tr>
                        <td>CLBID</td>
                        <td><a href='/inspection/clb/report/get?clbID={{ .CLBID }}&clusterID={{ .ClusterID }}&refreshCache=true'>{{ .CLBID }}</a></td>
                    </tr>
                    <tr>
                        <td>使用已有</td>
                        <td>{{ .Reused }}</td>
                    </tr>
                    <tr>
                        <td>报告过期时间</td>
                        <td>{{ .ExpireAt }}</td>
                    </tr>
                </table>

                {{if .AppFabric}}
                <h2>TKE AppFabric 关联信息</h2>
                <table>
                    <tr>
                        <th>信息</th>
                        <th>详情</th>
                    </tr>
                    <tr>
                        <td>业务ID</td>
                        <td>{{ .AppFabric.ProjectID }}</td>
                    </tr>
                    <tr>
                        <td>跳转链接</td>
                        <td><a href='{{ .AppFabric.Link }}'>点击跳转</a></td>
                    </tr>
                    <tr>
                        <td>环境信息</td>
                        <td>{{ .AppFabric.EnvironmentName }}</td>
                    </tr>
                    <tr>
                        <td>应用信息</td>
                        <td>{{ .AppFabric.AppID }}</td>
                    </tr>
                    <tr>
                        <td>实例信息</td>
                        <td>{{ .AppFabric.InstanceID }}</td>
                    </tr>
                    <tr>
                        <td>组件信息</td>
                        <td>{{ .AppFabric.ComponentName }}</td>
                    </tr>
                </table>
            {{end}}

            {{if .TKEx}}
                <h2>TKEx 关联信息</h2>
                <table>
                    <tr>
                        <th>信息</th>
                        <th>详情</th>
                    </tr>
                    <tr>
                        <td>业务ID</td>
                        <td>{{ .TKEx.ProjectID }}</td>
                    </tr>
                    <tr>
                        <td>跳转链接</td>
                        <td><a href='{{ .TKEx.Link }}'>点击跳转</a></td>
                    </tr>
                </table>
            {{end}}

                <h2>资源描述</h2>
                <div class="resource-description">
                    <pre><code>{{ .Raw }}</code></pre>
                </div>
            </div>
        </main>

        <!-- 通用页脚 -->
        <div id="footer-container"></div>
    </div>

    <script src="/k8s-effects.js"></script>
    <script src="/page-loader.js"></script>
    <script src="/report-scripts.js"></script>
</body>
</html>
