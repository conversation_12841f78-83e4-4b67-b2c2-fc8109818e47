/**
 * TKE 接入层运营平台 - 科技感特效脚本
 * 提供 AI、Kubernetes 和网络元素动画效果
 */

// 在页面加载完成后初始化所有特效
document.addEventListener('DOMContentLoaded', function() {
  // 初始化科技感背景元素
  initTechBackground();
  
  // 如果在大屏幕上，添加更多高级特效
  if (window.innerWidth > 992) {
    setTimeout(() => {
      initNetworkLines();
      initParticleSystem();
      initNodeConnections();
    }, 500);
  }
});

// 添加 Kubernetes 和网格图形背景
function initTechBackground() {
  // 1. 添加背景网格
  const k8sGrid = document.createElement('div');
  k8sGrid.className = 'k8s-grid-bg';
  document.body.appendChild(k8sGrid);
  
  // 2. 添加 Kubernetes 标志
  const k8sLogo = document.createElement('div');
  k8sLogo.className = 'k8s-logo';
  document.body.appendChild(k8sLogo);
  
  // 3. 添加网络节点图
  const nodesImg = document.createElement('div');
  nodesImg.className = 'k8s-nodes';
  document.body.appendChild(nodesImg);
  
  // 4. 添加AI网络连接线
  const aiNetwork = document.createElement('div');
  aiNetwork.className = 'ai-network';
  document.body.appendChild(aiNetwork);
  
  // 5. 添加网络图标装饰
  const networkIcons = document.createElement('div');
  networkIcons.className = 'network-icons';
  document.body.appendChild(networkIcons);
}

// 动态网络连接线
function initNetworkLines() {
  const linesContainer = document.createElement('div');
  linesContainer.className = 'network-lines';
  document.body.appendChild(linesContainer);
  
  // 创建多条网络线
  for (let i = 0; i < 6; i++) {
    const line = document.createElement('div');
    line.className = 'network-line';
    
    // 随机位置和大小
    const top = Math.floor(Math.random() * 85) + 5; // 5-90%
    const width = Math.floor(Math.random() * 30) + 40; // 40-70%
    const left = Math.floor(Math.random() * (100 - width)); // 确保不超出屏幕
    
    line.style.top = `${top}%`;
    line.style.left = `${left}%`;
    line.style.width = `${width}%`;
    line.style.animationDelay = `${Math.random() * 3}s`;
    
    linesContainer.appendChild(line);
  }
}

// 粒子系统
function initParticleSystem() {
  const container = document.createElement('div');
  container.className = 'particles-container';
  document.body.appendChild(container);
  
  // 创建粒子
  for (let i = 0; i < 30; i++) {
    const particle = document.createElement('div');
    particle.className = 'tech-particle';
    
    // 随机设置粒子属性
    const size = Math.random() * 5 + 2; // 2-7px
    const posX = Math.random() * 100;
    const posY = Math.random() * 100;
    const opacity = Math.random() * 0.3 + 0.05;
    const delay = Math.random() * 4;
    const duration = Math.random() * 20 + 15;
    
    particle.style.width = `${size}px`;
    particle.style.height = `${size}px`;
    particle.style.left = `${posX}%`;
    particle.style.top = `${posY}%`;
    particle.style.opacity = opacity;
    particle.style.animationDelay = `${delay}s`;
    particle.style.animationDuration = `${duration}s`;
    
    container.appendChild(particle);
  }
}

// 创建节点连接动画
function initNodeConnections() {
  // 在搜索容器上添加连接点特效
  const searchContainers = document.querySelectorAll('.search-container');
  
  searchContainers.forEach(container => {
    // 为每个容器添加连接点
    for (let i = 0; i < 3; i++) {
      const connectionDot = document.createElement('div');
      connectionDot.className = 'connection-node';
      
      // 随机位置（在容器边缘）
      const side = Math.floor(Math.random() * 4); // 0=上, 1=右, 2=下, 3=左
      const pos = Math.random() * 100;
      
      switch(side) {
        case 0: // 上边
          connectionDot.style.top = '0';
          connectionDot.style.left = `${pos}%`;
          break;
        case 1: // 右边
          connectionDot.style.top = `${pos}%`;
          connectionDot.style.right = '0';
          break;
        case 2: // 下边
          connectionDot.style.bottom = '0';
          connectionDot.style.left = `${pos}%`;
          break;
        case 3: // 左边
          connectionDot.style.top = `${pos}%`;
          connectionDot.style.left = '0';
          break;
      }
      
      // 随机延迟脉冲动画
      connectionDot.style.animationDelay = `${Math.random() * 2}s`;
      
      container.appendChild(connectionDot);
    }
  });
}

// 为表单提交添加波纹效果
function addRippleEffect(button) {
  button.addEventListener('click', function(e) {
    // 创建波纹元素
    const ripple = document.createElement('span');
    ripple.className = 'ripple';
    this.appendChild(ripple);
    
    // 设置波纹位置
    const rect = this.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = e.clientX - rect.left - size / 2;
    const y = e.clientY - rect.top - size / 2;
    
    ripple.style.width = `${size}px`;
    ripple.style.height = `${size}px`;
    ripple.style.left = `${x}px`;
    ripple.style.top = `${y}px`;
    
    // 动画结束后移除波纹
    setTimeout(() => {
      ripple.remove();
    }, 600);
  });
}

// 初始化波纹效果
document.addEventListener('DOMContentLoaded', function() {
  const buttons = document.querySelectorAll('.btn');
  buttons.forEach(addRippleEffect);
});

// 添加独特的AI加载动画
function showAILoadingEffect(message = '正在处理...') {
  // 创建加载容器
  const loadingContainer = document.createElement('div');
  loadingContainer.className = 'ai-loading-container';
  
  // 创建加载内容
  loadingContainer.innerHTML = `
    <div class="ai-loading-effect">
      <div class="ai-loading-brain">
        <div class="ai-node n1"></div>
        <div class="ai-node n2"></div>
        <div class="ai-node n3"></div>
        <div class="ai-node n4"></div>
        <div class="ai-node n5"></div>
        <div class="ai-node n6"></div>
        <div class="ai-node n7"></div>
        
        <div class="ai-connection c1"></div>
        <div class="ai-connection c2"></div>
        <div class="ai-connection c3"></div>
        <div class="ai-connection c4"></div>
        <div class="ai-connection c5"></div>
        <div class="ai-connection c6"></div>
      </div>
      <div class="ai-loading-text">${message}</div>
    </div>
  `;
  
  // 添加到页面
  document.body.appendChild(loadingContainer);
  
  // 显示动画
  setTimeout(() => {
    loadingContainer.classList.add('active');
  }, 10);
  
  // 返回关闭函数
  return function hideLoading() {
    loadingContainer.classList.remove('active');
    setTimeout(() => {
      loadingContainer.remove();
    }, 300);
  };
}

// 导出全局函数
window.showAILoadingEffect = showAILoadingEffect;
window.initNodeConnections = initNodeConnections;

/**
 * TKE 接入层运营平台 - 技术特效脚本
 */

// 为按钮添加波纹效果
function addRippleEffect() {
    document.addEventListener('click', function(e) {
        const target = e.target;
        if (target.classList.contains('btn') || target.classList.contains('nav-link')) {
            const rect = target.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const ripple = document.createElement('span');
            ripple.className = 'ripple';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            
            target.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        }
    });
}

// 添加AI加载动画
function showAILoadingEffect(message = '正在处理...') {
    // 创建加载容器
    const container = document.createElement('div');
    container.className = 'ai-loading-container';
    
    // 创建加载效果
    const effect = document.createElement('div');
    effect.className = 'ai-loading-effect';
    
    // 创建大脑动画
    const brain = document.createElement('div');
    brain.className = 'ai-loading-brain';
    
    // 节点和连接线
    for (let i = 1; i <= 7; i++) {
        const node = document.createElement('div');
        node.className = `ai-node n${i}`;
        brain.appendChild(node);
    }
    
    for (let i = 1; i <= 6; i++) {
        const connection = document.createElement('div');
        connection.className = `ai-connection c${i}`;
        brain.appendChild(connection);
    }
    
    // 加载文本
    const text = document.createElement('div');
    text.className = 'ai-loading-text';
    text.textContent = message;
    
    // 组装元素
    effect.appendChild(brain);
    effect.appendChild(text);
    container.appendChild(effect);
    document.body.appendChild(container);
    
    // 显示加载动画
    setTimeout(() => {
        container.classList.add('active');
    }, 10);
    
    // 返回关闭函数
    return function hideAILoading() {
        container.classList.remove('active');
        setTimeout(() => {
            container.remove();
        }, 300);
    };
}

// 检测设备类型
function isMobileDevice() {
    return (window.innerWidth <= 768) || 
           /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

// 针对移动设备优化界面
function optimizeForMobile() {
    if (isMobileDevice()) {
        // 如果是移动设备，减少视觉效果提高性能
        document.documentElement.classList.add('mobile-device');
        
        // 为导航栏添加滑动指示
        const navLinks = document.querySelector('.nav-links');
        if (navLinks && navLinks.scrollWidth > navLinks.clientWidth) {
            const indicator = document.createElement('div');
            indicator.className = 'nav-scroll-indicator';
            indicator.innerHTML = '<i class="bi bi-chevron-right"></i>';
            
            // 仅在移动端且显示溢出时显示指示器
            indicator.style.position = 'absolute';
            indicator.style.right = '10px';
            indicator.style.top = '50%';
            indicator.style.transform = 'translateY(-50%)';
            indicator.style.color = 'rgba(255,255,255,0.7)';
            indicator.style.animation = 'pulse 2s infinite';
            indicator.style.pointerEvents = 'none';
            
            // 添加到导航栏
            document.querySelector('.navbar').appendChild(indicator);
            
            // 滚动后隐藏指示器
            navLinks.addEventListener('scroll', function() {
                if (this.scrollLeft > 10) {
                    indicator.style.opacity = '0';
                    setTimeout(() => {
                        indicator.remove();
                    }, 300);
                }
            });
        }
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 添加按钮波纹效果
    addRippleEffect();
    
    // 优化移动设备体验
    optimizeForMobile();
    
    // 修复iOS上的100vh问题
    if (/iPhone|iPad|iPod/i.test(navigator.userAgent)) {
        const fixHeight = () => {
            document.documentElement.style.setProperty('--real-height', `${window.innerHeight}px`);
        };
        
        window.addEventListener('resize', fixHeight);
        fixHeight();
    }
});
