<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TKE 接入层运营平台 - 风险大盘</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/tech-theme.css">
    <link rel="stylesheet" href="/report-styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="page-wrapper">

        <main class="main-content">
            <div class="container">
                <div class="header">
                    <h1>全局巡检风险大盘</h1>
                </div>
                <h3>统计在后检/巡检扫描过程中，新增和存量 CLB 风险</h3>

                <!-- 保持原有模板变量不变，只改变页面结构 -->
                <h2>存量/新增风险统计 <a href="/inspection/cluster/risk/download" class="top-bar-link export-button"><i class="bi bi-download"></i> 一键导出</a></h2>
                <table>
                    <tr>
                        <th>维度</th>
                        <th>详情</th>
                    </tr>
                    <tr>
                        <td>风险总数</td>
                        <td>{{ .TotalRiskCount }}</td>
                    </tr>
                    <tr>
                        <td>存量风险数</td>
                        <td>{{ .TotalStockTaskCount }}</td>
                    </tr>
                    <tr>
                        <td>新增风险数</td>
                        <td>{{ .TotalIncrementalTaskCount }}</td>
                    </tr>
                    <tr>
                        <td>风险集群数</td>
                        <td>{{ .TotalClusterCount }}</td>
                    </tr>
                    <tr>
                        <td>当前任务</td>
                        <td><a href="/inspection/task/report/get?state=Running">查看详情</a></td>
                    </tr>
                </table>

                <div class="chart-container">
                    <div id="dimensionChartContainer" class="chart-half">
                        <canvas id="dimensionChart"></canvas>
                    </div>
                    <div id="riskChartContainer" class="chart-half">
                        <canvas id="riskChart"></canvas>
                    </div>
                </div>

                <h2>风险维度统计</h2>
                <table>
                    <tr>
                        <th>维度</th>
                        <th>详情</th>
                    </tr>
                    <tr>
                        <td>CLB 维度风险总数</td>
                        <td>{{ .TotalCLBLevelRiskCount }}</td>
                    </tr>
                    <tr>
                        <td>Listener 维度风险总数</td>
                        <td>{{ .TotalListenerLevelRiskCount }}</td>
                    </tr>
                    <tr>
                        <td>Rule 维度风险总数</td>
                        <td>{{ .TotalRuleLevelRiskCount }}</td>
                    </tr>
                    <tr>
                        <td>RS 维度风险总数</td>
                        <td>{{ .TotalRSLevelRiskCount }}</td>
                    </tr>
                </table>

                <h2>CLB 新增风险列表 <a href="/inspection/cluster/risk/download?state=increment" class="top-bar-link export-button"><i class="bi bi-download"></i> 一键导出</a></h2>
                <h3>新增风险含义：在巡检扫描过程中 CLB 发生异常且当前未恢复，风险较高。</h3>
                <table>
                    <tr>
                        <th>AppID</th>
                        <th>集群ID</th>
                        <th>风险CLB</th>
                        <th>风险个数</th>
                        <th>风险等级</th>
                        <th>风险详情</th>
                        <th>关联资源</th>
                        <th>风险上升次数</th>
                        <th>风险发生时间</th>
                        <th>风险持续时间</th>
                    </tr>
                    {{ range $code, $risk := .Increment }}
                        <tr>
                            <td>{{ $risk.AppID }}</td>
                            <td><a href='/inspection/cluster/report/get?clusterID={{ $risk.ClusterName }}&filter=clb&refreshCache=true'>{{ $risk.ClusterName }}</a></td>
                            <td><a href='/inspection/clb/report/get?clbID={{ $risk.CLBID }}&clusterID={{ $risk.ClusterName }}&refreshCache=true'>{{ $risk.CLBID }}</a></td>
                            <td>{{ $risk.RiskCount }}</td>
                            <td>{{ $risk.RiskLevel }}</td>
                            <td>{{ $risk.RiskSummary }}</td>
                            <td>{{ $risk.UsingResources }}</td>
                            <td>{{ $risk.RiseCount }}</td>
                            <td>{{ $risk.CreatedAt }}</td>
                            <td>{{ $risk.LastDuration }}</td>
                        </tr>
                    {{ end }}
                </table>

                <h2>CLB 存量风险列表 <a href="/inspection/cluster/risk/download?state=stock" class="top-bar-link export-button"><i class="bi bi-download"></i> 一键导出</a></h2>
                <h3>存量风险含义：在巡检扫描前 CLB 已经异常且未变化，风险较低。</h3>
                <table>
                    <tr>
                        <th>AppID</th>
                        <th>集群ID</th>
                        <th>风险CLB</th>
                        <th>风险个数</th>
                        <th>风险等级</th>
                        <th>风险详情</th>
                        <th>关联资源</th>
                        <th>风险上升次数</th>
                        <th>风险发生时间</th>
                        <th>风险持续时间</th>
                    </tr>
                    {{ range $code, $risk := .Stock }}
                        <tr>
                            <td>{{ $risk.AppID }}</td>
                            <td><a href='/inspection/cluster/report/get?clusterID={{ $risk.ClusterName }}&filter=clb&refreshCache=true'>{{ $risk.ClusterName }}</a></td>
                            <td><a href='/inspection/clb/report/get?clbID={{ $risk.CLBID }}&clusterID={{ $risk.ClusterName }}&refreshCache=true'>{{ $risk.CLBID }}</a></td>
                            <td>{{ $risk.RiskCount }}</td>
                            <td>{{ $risk.RiskLevel }}</td>
                            <td>{{ $risk.RiskSummary }}</td>
                            <td>{{ $risk.UsingResources }}</td>
                            <td>{{ $risk.RiseCount }}</td>
                            <td>{{ $risk.CreatedAt }}</td>
                            <td>{{ $risk.LastDuration }}</td>
                        </tr>
                    {{ end }}
                </table>
            </div>
        </main>

        <!-- 通用页脚 -->
        <div id="footer-container"></div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 设置图表样式
            const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
            const textColor = isDark ? '#e2e8f0' : '#334155';
            const gridColor = isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)';

            // 风险等级图表
            const ctx1 = document.getElementById('riskChart').getContext('2d');
            const riskChart = new Chart(ctx1, {
                type: 'pie',
                data: {
                    labels: ['存量风险', '新增风险'],
                    datasets: [{
                        data: [{{ .TotalStockTaskCount }}, {{ .TotalIncrementalTaskCount }}],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',  // 蓝色
                            'rgba(239, 68, 68, 0.8)'    // 红色
                        ],
                        borderColor: [
                            'rgba(59, 130, 246, 1)',    // 蓝色
                            'rgba(239, 68, 68, 1)'      // 红色
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                color: textColor
                            }
                        },
                        title: {
                            display: true,
                            text: '风险类型分布',
                            color: textColor,
                            font: {
                                size: 16
                            }
                        }
                    }
                }
            });

            // 风险维度图表
            const ctx2 = document.getElementById('dimensionChart').getContext('2d');
            const dimensionChart = new Chart(ctx2, {
                type: 'bar',
                data: {
                    labels: ['CLB维度异常', 'Listener维度异常', 'Rule维度异常', 'RS 维度异常'],
                    datasets: [{
                        label: 'CLB数量',
                        data: [{{ .TotalCLBLevelRiskCount }}, {{ .TotalListenerLevelRiskCount }}, {{ .TotalRuleLevelRiskCount }}, {{ .TotalRSLevelRiskCount }}],
                        backgroundColor: 'rgba(59, 130, 246, 0.5)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 1,
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                color: textColor
                            }
                        },
                        title: {
                            display: true,
                            text: '风险维度分布',
                            color: textColor,
                            font: {
                                size: 16
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: gridColor
                            },
                            ticks: {
                                color: textColor
                            }
                        },
                        x: {
                            grid: {
                                color: gridColor
                            },
                            ticks: {
                                color: textColor
                            }
                        }
                    }
                }
            });

            // 将图表实例暴露给全局主题切换功能
            window.updateChartsTheme = function(isDark) {
                const textColor = isDark ? '#e2e8f0' : '#334155';
                const gridColor = isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)';

                // 更新风险等级图表
                riskChart.options.plugins.legend.labels.color = textColor;
                riskChart.options.plugins.title.color = textColor;

                // 更新风险维度图表
                dimensionChart.options.plugins.legend.labels.color = textColor;
                dimensionChart.options.plugins.title.color = textColor;
                dimensionChart.options.scales.y.ticks.color = textColor;
                dimensionChart.options.scales.x.ticks.color = textColor;
                dimensionChart.options.scales.y.grid.color = gridColor;
                dimensionChart.options.scales.x.grid.color = gridColor;

                riskChart.update();
                dimensionChart.update();
            };
        });
    </script>

    <script src="/k8s-effects.js"></script>
    <script src="/page-loader.js"></script>
    <script src="/report-scripts.js"></script>
</body>
</html>
