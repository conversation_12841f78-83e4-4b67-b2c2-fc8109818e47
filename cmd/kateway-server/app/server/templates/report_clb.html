<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TKE 接入层运营平台 - CLB 排障信息</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/tech-theme.css">
    <link rel="stylesheet" href="/report-styles.css">
</head>
<body>
    <div class="page-wrapper">

        <main class="main-content">
            <div class="container">
                <h1>CLB 排障信息</h1>

                <h2>CLB 信息</h2>
                <table>
                    <tr>
                        <th>信息类型</th>
                        <th>详情</th>
                    </tr>
                    <tr>
                        <td>CLBID</td>
                        <td data-copy>{{ .CLBInfo.LoadBalancerId }}</td>
                    </tr>
                    <tr>
                        <td>CLB名称</td>
                        <td>{{ .CLBInfo.LoadBalancerName }}</td>
                    </tr>
                    <tr>
                        <td>CLB类型</td>
                        <td>{{ .CLBInfo.LoadBalancerType }}</td>
                    </tr>
                    <tr>
                        <td>CLB地域</td>
                        <td>{{ .LBRInfo.CLBRegion }}</td>
                    </tr>
                    <tr>
                        <td>所属VPC</td>
                        <td>{{ .CLBInfo.VpcId }}</td>
                    </tr>
                    <tr>
                        <td>IP类型</td>
                        <td>{{ .CLBInfo.AddressIPVersion }}</td>
                    </tr>
                    <tr>
                        <td>是否开启跨域2.0</td>
                        <td>{{ .CLBInfo.SnatPro }}</td>
                    </tr>
                    <tr>
                        <td>创建时间</td>
                        <td>{{ .CLBInfo.CreateTime }}</td>
                    </tr>
                    <tr>
                        <td>CLB状态</td>
                        <td>{{ .CLBInfo.Status }}</td>
                    </tr>
                    <tr>
                        <td>报告过期时间</td>
                        <td>{{ .ExpireAt }}</td>
                    </tr>
                </table>

                <!-- 以下内容保持原模板变量不变，只改变页面结构 -->
                <h2>周边系统</h2>
                <table>
                    <tr>
                        <td>DFS实例</td>
                        <td><a href="{{ .CLBInfo.DFSCLBDetail }}">查看详情</a></td>
                    </tr>
                    <tr>
                        <td>CLB对账诊断</td>
                        <td><a href="{{ .CLBInfo.DFSCLBHealth }}">查看详情</a></td>
                    </tr>
                    <tr>
                        <td>CLB七层访问日志</td>
                        <td><a href="{{ .CLBInfo.DFSCLBAccessLog }}">查看详情</a></td>
                    </tr>
                    <tr>
                        <td>CLB任务日志</td>
                        <td><a href="{{ .CLBInfo.DFSCLBTask }}">查看详情</a></td>
                    </tr>
                    <tr>
                        <td>CLB业务日志</td>
                        <td><a href="{{ .CLBInfo.DFSCLSLog }}">查看详情</a></td>
                    </tr>
                    <tr>
                        <td>巴拉多监控</td>
                        <td><a href="{{ .CLBInfo.DFSCLBMonitor }}">查看详情</a></td>
                    </tr>
                </table>

                <h2>TKE 使用信息</h2>
                <table>
                    <tr>
                        <th>信息类型</th>
                        <th>详情</th>
                    </tr>
                    <tr>
                        <td>归属权</td>
                        <td>{{ .CLBInfo.CreatedByTKE }}</td>
                    </tr>
                    <tr>
                        <td>集群ID</td>
                        <td><a href='/inspection/cluster/report/get?clusterID={{ .CLBInfo.ClusterID }}&refreshCache=true'>{{ .CLBInfo.ClusterID }}</a></td>
                    </tr>
                    <tr>
                        <td>使用协议</td>
                        <td>{{ .CLBStats.UsingProtocols }}</td>
                    </tr>
                    <tr>
                        <td>关联 Service 数量</td>
                        <td>{{ .LBRInfo.UsingServiceCount }}</td>
                    </tr>
                    <tr>
                        <td>关联 Ingress 数量</td>
                        <td>{{ .LBRInfo.UsingIngressCount }}</td>
                    </tr>
                </table>

                <h2>TKE 资源关联列表</h2>
                <table>
                    <tr>
                        <th>资源类型</th>
                        <th>资源命名空间</th>
                        <th>资源名称</th>
                        <th>使用监听器</th>
                        <th>后端Pod个数</th>
                        <th>直连/非直连</th>
                        <th>资源详情</th>
                    </tr>
                    {{ $clusterID := .LBRInfo.ClusterID }}
                    {{ range $index, $resource := .LBRInfo.Resources }}
                        <tr>
                            <td>{{ $resource.Kind }}</td>
                            <td>{{ $resource.Namespace }}</td>
                            <td>{{ $resource.Name }}</td>
                            <td>{{ $resource.PortProtocols }}</td>
                            <td>{{ $resource.PodCount }}</td>
                            <td>{{ $resource.DirectAccess }}</td>
                            <td><a href='/inspection/resource/report/get?resourceType={{ $resource.Kind }}&resourceName={{ $resource.Name }}&resourceNamespace={{ $resource.Namespace }}&clusterID={{ $clusterID }}&refreshCache=true'><i class="bi bi-search"></i> 资源详情</a></td>
                        </tr>
                    {{ end }}
                </table>

                {{if .LBRInfo.Migrate }}
                <h2>CLB 迁移信息</h2>
                <table>
                    <tr>
                        <th>是否为迁移 CLB</th>
                        <th>迁移原集群</th>
                    </tr>
                    <tr>
                        <td>{{ .LBRInfo.Migrate.IsMigratedCLB }}</td>
                        <td>{{ .LBRInfo.Migrate.MigrateFromCluster }}</td>
                    </tr>
                </table>
                {{end}}

                <h2>CLB 锁状态</h2>
                <table>
                    <tr>
                        <th>信息类型</th>
                        <th>详情</th>
                    </tr>
                    <tr>
                        <td>是否被占用</td>
                        <td>{{ .LBRInfo.LoadBalancerResourceLock.Status }}</td>
                    </tr>
                    <tr>
                        <td>资源类型</td>
                        <td>{{ .LBRInfo.LoadBalancerResourceLock.Resource.Kind }}</td>
                    </tr>
                    <tr>
                        <td>资源命名空间</td>
                        <td>{{ .LBRInfo.LoadBalancerResourceLock.Resource.Namespace }}</td>
                    </tr>
                    <tr>
                        <td>资源名称</td>
                        <td>{{ .LBRInfo.LoadBalancerResourceLock.Resource.Name }}</td>
                    </tr>
                </table>

                <h2>CLB 资源统计</h2>
                <table>
                    <tr>
                        <th>统计维度</th>
                        <th>资源总数</th>
                        <th>健康检查失败个数</th>
                        <th>权重为0个数</th>
                    </tr>
                    <tr>
                        <td>Listener</td>
                        <td>{{ .CLBStats.TotalListenerCount }}</td>
                        <td>{{ .CLBStats.TotalAllDownlistenerCountByHealth }}</td>
                        <td>{{ .CLBStats.TotalAllDownlistenerCountByWeight }}</td>
                    </tr>
                    <tr>
                        <td>Rule</td>
                        <td>{{ .CLBStats.TotalRuleCount }}</td>
                        <td>{{ .CLBStats.TotalAllDownRuleCountByHealth }}</td>
                        <td>{{ .CLBStats.TotalAllDownRuleCountByWeight }}</td>
                    </tr>
                    <tr>
                        <td>RealServer</td>
                        <td>{{ .CLBStats.TotalRSCount }}</td>
                        <td>{{ .CLBStats.TotalUnhealthForbiddenRSCount }}</td>
                        <td>{{ .CLBStats.TotalZeroWeightForbiddenRSCount }}</td>
                    </tr>
                </table>

                <h2>CLB 不健康端口/规则列表</h2>
                <table>
                    <tr>
                        <th>类型</th>
                        <th>协议</th>
                        <th>端口</th>
                        <th>域名</th>
                        <th>路径</th>
                        <th>原因</th>
                    </tr>
                    {{ range $index, $target := .CLBStats.Stats }}
                        <tr>
                            <td>{{ $target.Type }}</td>
                            <td>{{ $target.Protocol }}</td>
                            <td>{{ $target.Port }}</td>
                            <td>{{ $target.Host }}</td>
                            <td>{{ $target.Path }}</td>
                            <td>{{ $target.Reason }}</td>
                        </tr>
                    {{ end }}
                </table>
            </div>
        </main>

        <!-- 通用页脚 -->
        <div id="footer-container"></div>
    </div>

    <script src="/k8s-effects.js"></script>
    <script src="/page-loader.js"></script>
    <script src="/report-scripts.js"></script>
</body>
</html>
