package server

import (
	"embed"
	"html/template"
	"net/http"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	_ "git.woa.com/kateway/kateway-server/docs"
)

type Server struct {
	engine *gin.Engine
}

//go:embed templates/*
var f embed.FS

func New() *Server {
	// 设置 Gin 模式为 Release
	gin.SetMode(gin.ReleaseMode)
	s := &Server{
		engine: gin.Default(),
	}
	tmpl, err := template.New("tmpl").ParseFS(f,
		"templates/*")
	if err != nil {
		panic(err)
	}

	s.engine.GET("/healthz", s.HealthZ)

	s.engine.SetHTMLTemplate(tmpl)

	s.engine.GET("/CheckImage", s.CheckImage)

	s.engine.GET("/service/mock", s.ServiceMock)
	s.engine.Any("/service/admin/*path", s.ServiceAdmin)

	// 重新组织路由，添加主页
	s.engine.GET("/", s.HomePage)
	s.engine.GET("/cluster-console", s.ClusterConsole) // 更新路由名称保持一致性
	s.engine.GET("/scan-console", s.ClusterScanConsole)
	s.engine.GET("/clb-console", s.CLBConsole)
	s.engine.GET("/resource-console", s.ResourceConsole)
	s.engine.GET("/task-console", s.TaskConsole)
	s.engine.GET("/risk-console", s.RiskConsole)

	s.engine.GET("/inspection/cluster/task/run", s.RunClusterTask)
	s.engine.GET("/inspection/cluster/task/get", s.GetClusterTask)
	s.engine.GET("/inspection/cluster/task/update", s.UpdateClusterTask)
	s.engine.GET("/inspection/cluster/task/stop", s.StopClusterTask)

	s.engine.GET("/inspection/cluster/info/list", s.ListClusterInfo)
	s.engine.GET("/inspection/cluster/info/get", s.GetClusterInfo)
	s.engine.GET("/inspection/cluster/report/get", s.GetClusterReport)
	s.engine.GET("/inspection/clb/report/get", s.GetCLBReport)
	s.engine.GET("/inspection/resource/report/get", s.GetResourceReport)
	s.engine.GET("/inspection/risk/report/get", s.GetRiskReport)
	s.engine.GET("/inspection/task/report/get", s.GetTaskReport)
	s.engine.GET("/inspection/cluster/report/download", s.DownloadClusterReport)
	s.engine.GET("/inspection/cluster/risk/download", s.DownloadRiskReport)

	s.engine.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	s.engine.GET("/styles.css", func(c *gin.Context) {
		data, err := f.ReadFile("templates/styles.css")
		if err != nil {
			c.Status(http.StatusNotFound)
			return
		}
		c.Data(http.StatusOK, "text/css", data)
	})
	s.engine.GET("/scripts.js", func(c *gin.Context) {
		data, err := f.ReadFile("templates/scripts.js")
		if err != nil {
			c.Status(http.StatusNotFound)
			return
		}
		c.Data(http.StatusOK, "application/javascript", data)
	})

	s.engine.GET("/k8s-effects.js", func(c *gin.Context) {
		data, err := f.ReadFile("templates/k8s-effects.js")
		if err != nil {
			c.Status(http.StatusNotFound)
			return
		}
		c.Data(http.StatusOK, "application/javascript", data)
	})
	s.engine.GET("/tech-theme.css", func(c *gin.Context) {
		data, err := f.ReadFile("templates/tech-theme.css")
		if err != nil {
			c.Status(http.StatusNotFound)
			return
		}
		c.Data(http.StatusOK, "text/css", data)
	})
	s.engine.GET("/report-styles.css", func(c *gin.Context) {
		data, err := f.ReadFile("templates/report-styles.css")
		if err != nil {
			c.Status(http.StatusNotFound)
			return
		}
		c.Data(http.StatusOK, "text/css", data)
	})
	s.engine.GET("/report-scripts.js", func(c *gin.Context) {
		data, err := f.ReadFile("templates/report-scripts.js")
		if err != nil {
			c.Status(http.StatusNotFound)
			return
		}
		c.Data(http.StatusOK, "application/javascript", data)
	})

	// 提供头部和页脚文件
	s.engine.GET("/header.html", func(c *gin.Context) {
		data, err := f.ReadFile("templates/header.html")
		if err != nil {
			c.Status(http.StatusNotFound)
			return
		}
		c.Data(http.StatusOK, "text/html", data)
	})

	s.engine.GET("/footer.html", func(c *gin.Context) {
		data, err := f.ReadFile("templates/footer.html")
		if err != nil {
			c.Status(http.StatusNotFound)
			return
		}
		c.Data(http.StatusOK, "text/html", data)
	})

	// 提供页面加载脚本
	s.engine.GET("/page-loader.js", func(c *gin.Context) {
		data, err := f.ReadFile("templates/page-loader.js")
		if err != nil {
			c.Status(http.StatusNotFound)
			return
		}
		c.Data(http.StatusOK, "application/javascript", data)
	})

	return s
}

func (s *Server) Run() {
	s.engine.Run(":80")
}

func (s *Server) HealthZ(c *gin.Context) {
	c.Status(http.StatusOK)
}
