package server

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/tidwall/gjson"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"git.woa.com/kateway/pkg/telemetry/log"

	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
)

// @Summary 访问Admin服务
// @Description 转发访问指定集群 service controller admin 接口
// @Tags service-controller
// @Param clusterID query string true "集群ID"
// @Accept application/json
// @Produce application/json
// @Router /service/admin [get]
func (s *Server) ServiceAdmin(c *gin.Context) {
	var request struct {
		ClusterID string `form:"clusterID"`
	}
	lo.Must0(c.BindQuery(&request))

	cluster, err := services.Get().Cluster().Get(c, request.ClusterID)
	if err != nil {
		c.String(http.StatusInternalServerError, err.Error())
		return
	}
	clientset, err := services.Get().Cluster().Clientset(c, cluster)
	if err != nil {
		c.String(http.StatusInternalServerError, err.Error())
		return
	}
	cm, err := clientset.K8sCli.CoreV1().ConfigMaps(v1.NamespaceSystem).Get(c.Request.Context(), "qcloud-service-controller", v1.GetOptions{})
	if err != nil {
		c.String(http.StatusInternalServerError, err.Error())
		return
	}
	leader, ok := cm.Annotations["control-plane.alpha.kubernetes.io/leader"]
	if !ok {
		c.String(http.StatusInternalServerError, "leader not found")
		return
	}
	pod := gjson.Get(leader, "holderIdentity").String()

	proxyRequest := clientset.K8sCli.CoreV1().RESTClient().Verb(c.Request.Method).
		Namespace(v1.NamespaceSystem).
		Resource("pods").
		Name(pod).
		SubResource("proxy").
		Suffix(c.Param("path"))

	for key, values := range c.Request.URL.Query() {
		if key == "clusterID" {
			continue
		}
		for _, value := range values {
			proxyRequest.Param(key, value)
		}
	}

	log.Info("proxy", "method", c.Request.Method, "url", proxyRequest.URL().String())
	body, err := proxyRequest.DoRaw(c.Request.Context())
	if err != nil {
		c.String(http.StatusInternalServerError, err.Error())
		return
	}

	c.Writer.Write(body)
}
