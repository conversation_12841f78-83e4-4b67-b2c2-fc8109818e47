package server

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"

	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/task"
)

// @Summary 查询指定健康检查任务
// @Description 查询指定健康检查任务
// @Tags 健康检查
// @Param taskID query string true "任务ID"
// @Accept application/json
// @Produce application/json
// @Router /inspection/cluster/task/get [get]
func (s *Server) GetClusterTask(c *gin.Context) {
	var request struct {
		TaskID string `form:"taskID"`
	}
	lo.Must0(c.BindQuery(&request))
	// 查找任务
	task, err := services.Get().LegacyTask().GetTaskByTaskID(c, request.TaskID)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, err)
		return
	}

	// 如果没有找到任务，返回 404
	c.IndentedJSON(http.StatusOK, task)
}

// @Summary 执行集群健康检查任务
// @Description 执行集群健康检查任务
// @Tags 健康检查
// @Param clusterID query string true "集群ID"
// @Param maxSyncTimes query int true "任务最大执行次数"
// @Param syncInterval query string true "任务执行间隔"
// @Param ignoreRiskLevel query string false "屏蔽风险维度"
// @Accept application/json
// @Produce application/json
// @Router /inspection/cluster/task/run [get]
func (s *Server) RunClusterTask(c *gin.Context) {
	var request struct {
		ClusterID       string `form:"clusterID"`
		MaxSyncTimes    int    `form:"maxSyncTimes"`
		SyncInterval    string `form:"syncInterval"`
		IgnoreRiskLevel string `form:"ignoreRiskLevel"`
	}
	lo.Must0(c.BindQuery(&request))

	cluster, err := services.Get().Cluster().Get(c, request.ClusterID)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, err)
		return
	}
	duration, err := time.ParseDuration(request.SyncInterval)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, err)
		return
	}
	task := task.NewInspection().AddTask(c,
		cluster,
		duration,
		request.MaxSyncTimes,
		request.IgnoreRiskLevel)

	c.IndentedJSON(http.StatusOK, task)
}

// @Summary 更新集群健康检查任务
// @Description 更新集群健康检查任务
// @Tags 健康检查
// @Param taskID query string true "任务ID"
// @Param maxSyncTimes query int true "任务最大执行次数"
// @Param syncInterval query string true "任务执行间隔"
// @Param ignoreRiskLevel query string false "屏蔽风险维度"
// @Accept application/json
// @Produce application/json
// @Router /inspection/cluster/task/update [get]
func (s *Server) UpdateClusterTask(c *gin.Context) {
	var request struct {
		TaskID          string `form:"taskID"`
		MaxSyncTimes    int    `form:"maxSyncTimes"`
		SyncInterval    string `form:"syncInterval"`
		IgnoreRiskLevel string `form:"ignoreRiskLevel"`
	}
	lo.Must0(c.BindQuery(&request))

	duration, err := time.ParseDuration(request.SyncInterval)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, err)
		return
	}
	task := task.NewInspection().UpdateTask(c, request.TaskID, duration, request.MaxSyncTimes, request.IgnoreRiskLevel)

	c.IndentedJSON(http.StatusOK, task)
}

// @Summary 结束集群健康检查任务
// @Description 结束集群健康检查任务
// @Tags 健康检查
// @Param taskID query string true "任务ID"
// @Param clusterID query string true "集群ID"
// @Accept application/json
// @Produce application/json
// @Router /inspection/cluster/task/stop [get]
func (s *Server) StopClusterTask(c *gin.Context) {
	var request struct {
		TaskID    string `form:"taskID"`
		ClusterID string `form:"clusterID"`
	}
	lo.Must0(c.BindQuery(&request))

	task := task.NewInspection().StopTask(c, request.TaskID, request.ClusterID)
	c.IndentedJSON(http.StatusOK, task)
}
