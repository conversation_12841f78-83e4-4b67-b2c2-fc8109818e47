package server

import (
	"context"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"

	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
)

// @Summary 执行预检
// @Description 指定镜像版本，执行 service controller 预检
// @Tags service-controller
// @Param clusterID query string true "集群ID"
// @Param version query string true "镜像版本"
// @Accept application/json
// @Produce application/json
// @Router /service/mock [get]
func (s *Server) ServiceMock(c *gin.Context) {
	var request struct {
		ClusterID string `form:"clusterID"`
		Version   string `form:"version"`
	}
	lo.Must0(c.BindQuery(&request))

	cluster, err := services.Get().Cluster().Get(c, request.ClusterID)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, err)
		return
	}

	result, err := services.Get().Service().Mock(context.TODO(), cluster, request.Version)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, err)
	}

	c.IndentedJSON(http.StatusOK, result)
}
