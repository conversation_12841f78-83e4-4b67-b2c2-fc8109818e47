package server

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
	"git.woa.com/kateway/kateway-server/pkg/alarm"
	"git.woa.com/kateway/kateway-server/pkg/image"
)

// @Summary 镜像一致性校验
// @Description 指定镜像版本，执行镜像一致性校验
// @Tags kateway-server
// @Param type query string false "镜像类型"
// @Param version query string false "镜像版本"
// @Param region query string false "镜像地域"
// @Accept application/json
// @Produce application/json
// @Router /CheckImage [get]
func (s *Server) CheckImage(c *gin.Context) {
	var request struct {
		Type    string `form:"type"`
		Version string `form:"version"`
		Region  string `form:"region"`
	}
	lo.Must0(c.<PERSON>d<PERSON>(&request))

	var (
		regions []string
		err     error
	)
	if request.Region == "" {
		regions, err = services.Get().Kateway().ListCCRRegion()
		if err != nil {
			c.String(http.StatusInternalServerError, err.Error())
		}
	} else {
		regions = []string{request.Region}
	}

	src := config.Get().GetImageName(request.Type)
	go func() {
		err = image.Check(src, []string{request.Version}, regions)
		content := fmt.Sprintf("检查镜像 通过：\n源镜像：%s\n版本：%s\n", src, request.Version)
		if err != nil {
			content = fmt.Sprintf("检查镜像 失败：\n源镜像：%s\n版本：%s\n错误：%s\n", src, request.Version, err)
		}

		req := &alarm.Request{
			ObjType: "kateway",
			ObjName: src,
			ObjID:   request.Version,
			Content: content,
		}
		alarm.Send(req)
	}()

	c.IndentedJSON(http.StatusOK, "ok")
}
