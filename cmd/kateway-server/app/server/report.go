package server

import (
	"context"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"

	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
)

func (s *Server) ClusterConsole(c *gin.Context) {
	RespondWithTemplate(c, "console_cluster", struct{}{})
}

func (s *Server) ClusterScanConsole(c *gin.Context) {
	RespondWithTemplate(c, "console_cluster_scan", struct{}{})
}

func (s *Server) CLBConsole(c *gin.Context) {
	RespondWithTemplate(c, "console_clb", struct{}{})
}

func (s *Server) ResourceConsole(c *gin.Context) {
	RespondWithTemplate(c, "console_resource", struct{}{})
}

func (s *Server) TaskConsole(c *gin.Context) {
	RespondWithTemplate(c, "console_task", struct{}{})
}

func (s *Server) RiskConsole(c *gin.Context) {
	RespondWithTemplate(c, "console_risk", struct{}{})
}

// @Summary List 集群信息
// @Description 指定AppID，扫描全量集群
// @Tags 健康检查
// @Param appID query string true "AppID"
// @Accept application/json
// @Produce application/json
// @Router /inspection/cluster/info/list [get]
func (s *Server) ListClusterInfo(c *gin.Context) {
	var request struct {
		AppID string `form:"appID"`
	}
	lo.Must0(c.BindQuery(&request))

	clusterlist, err := services.Get().Inspection().ListCluster(context.Background(), request.AppID)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, err)
		return
	}

	c.IndentedJSON(http.StatusOK, struct {
		TotalCluster int
		State        string
	}{TotalCluster: len(clusterlist), State: "Running"})
}

// @Summary 查询集群信息
// @Description 指定集群ID，获取当前集群信息
// @Tags 健康检查
// @Param clusterID query string true "集群ID"
// @Param refreshCache query boolean false "强制刷新缓存"
// @Accept application/json
// @Produce application/json
// @Router /inspection/cluster/info/get [get]
func (s *Server) GetClusterInfo(c *gin.Context) {
	var request struct {
		ClusterID string `form:"clusterID"`

		RefreshCache bool `form:"refreshCache"`
	}
	lo.Must0(c.BindQuery(&request))

	ctx, cancel := context.WithCancel(c)
	defer cancel()

	go func() {
		<-c.Writer.CloseNotify()
		cancel()
	}()

	cluster, err := services.Get().Cluster().Get(ctx, strings.TrimSpace(request.ClusterID))
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, err)
		return
	}
	var report *model.ClusterInfo

	report, err = services.Get().Inspection().GetCluster(ctx, cluster, request.RefreshCache)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, err)
		return
	}

	c.IndentedJSON(http.StatusOK, report)
}

// @Summary 查询集群健康检查数据
// @Description 指定集群ID，获取当前集群健康检查数据
// @Tags 健康检查
// @Param clusterID query string true "集群ID"
// @Param refreshCache query boolean false "强制刷新缓存"
// @Accept application/json
// @Produce application/json
// @Router /inspection/cluster/report/get [get]
func (s *Server) GetClusterReport(c *gin.Context) {
	var request struct {
		ClusterID string `form:"clusterID"`
		Filter    string `form:"filter"`

		RefreshCache bool `form:"refreshCache"`
	}
	lo.Must0(c.BindQuery(&request))

	ctx, cancel := context.WithCancel(c)
	defer cancel()

	go func() {
		<-c.Writer.CloseNotify()
		cancel()
	}()

	cluster, err := services.Get().Cluster().Get(ctx, strings.TrimSpace(request.ClusterID))
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, err)
		return
	}
	var report *model.ClusterReport

	if request.Filter == "risk" {
		report, err = services.Get().Inspection().GetClusterRiskReport(ctx, cluster, request.RefreshCache)
		if err != nil {
			c.IndentedJSON(http.StatusBadRequest, err)
			return
		}
	} else {
		report, err = services.Get().Inspection().GetClusterReport(ctx, cluster, request.RefreshCache)
		if err != nil {
			c.IndentedJSON(http.StatusBadRequest, err)
			return
		}
	}

	RespondWithTemplate(c, report.Template, report)
}

// @Summary 查询CLB健康检查数据
// @Description 指定集群ID，CLBID，获取当前CLB健康检查数据
// @Tags 健康检查
// @Param clusterID query string true "集群ID"
// @Param clbID query string true "CLBID"
// @Param refreshCache query boolean false "强制刷新缓存"
// @Accept application/json
// @Produce application/json
// @Router /inspection/clb/report/get [get]
func (s *Server) GetCLBReport(c *gin.Context) {
	var request struct {
		ClusterID string `form:"clusterID"`
		CLBID     string `form:"clbID"`

		RefreshCache   bool `form:"refreshCache"`
		IncludeBackend bool `form:"includeBackend"`
	}
	lo.Must0(c.BindQuery(&request))
	ctx, cancel := context.WithCancel(c)
	defer cancel()

	go func() {
		<-c.Writer.CloseNotify()
		cancel()
	}()

	cluster, err := services.Get().Cluster().Get(ctx, strings.TrimSpace(request.ClusterID))
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, err)
		return
	}
	report, err := services.Get().Inspection().GetCLBReport(ctx, cluster, strings.TrimSpace(request.CLBID), request.RefreshCache, request.IncludeBackend)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, err)
		return

	}

	RespondWithTemplate(c, report.Template, report)
}

// @Summary 查询资源健康检查数据
// @Description 指定集群ID，资源类型，资源NS，资源名称，获取资源信息
// @Tags 健康检查
// @Param clusterID query string true "集群ID"
// @Param resourceType query string true "资源类型"
// @Param resourceName query string true "资源名称"
// @Param resourceNamespace query string true "资源命名空间"
// @Param refreshCache query boolean false "强制刷新缓存"
// @Accept application/json
// @Produce application/json
// @Router /inspection/resource/report/get [get]
func (s *Server) GetResourceReport(c *gin.Context) {
	var request struct {
		ClusterID         string `form:"clusterID"`
		ResourceName      string `form:"resourceName"`
		ResourceType      string `form:"resourceType"`
		ResourceNamespace string `form:"resourceNamespace"`

		RefreshCache bool `form:"refreshCache"`
	}
	lo.Must0(c.BindQuery(&request))
	ctx, cancel := context.WithCancel(c)
	defer cancel()

	go func() {
		<-c.Writer.CloseNotify()
		cancel()
	}()

	cluster, err := services.Get().Cluster().Get(ctx, strings.TrimSpace(request.ClusterID))
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, err)
		return
	}
	report, err := services.Get().Inspection().GetResourceReport(ctx, cluster,
		strings.TrimSpace(request.ResourceType),
		strings.TrimSpace(request.ResourceNamespace),
		strings.TrimSpace(request.ResourceName),
		request.RefreshCache)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, err)
		return
	}

	RespondWithTemplate(c, report.Template, report)
}

// @Summary 查询健康检查任务列表
// @Description 查询健康检查任务列表
// @Tags 健康检查
// @Param clusterID query string false "指定集群ID"
// @Param state query string false "过滤指定状态的任务"
// @Param refreshCache query boolean false "强制刷新缓存"
// @Accept application/json
// @Produce application/json
// @Router /inspection/task/report/get [get]
func (s *Server) GetTaskReport(c *gin.Context) {
	var request struct {
		State     string `form:"state"`
		ClusterID string `form:"clusterID"`
	}
	lo.Must0(c.BindQuery(&request))
	ctx, cancel := context.WithCancel(c)
	defer cancel()

	go func() {
		<-c.Writer.CloseNotify()
		cancel()
	}()

	report, err := services.Get().LegacyTask().GetTaskReport(ctx, strings.TrimSpace(request.ClusterID), strings.TrimSpace(request.State))
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, err)
		return
	}

	RespondWithTemplate(c, report.Template, report)
}

// @Summary 查询巡检风险列表
// @Description 查询巡检风险列表
// @Tags 健康检查
// @Param state query string false "保留指定状态的风险"
// @Accept application/json
// @Produce application/json
// @Router /inspection/risk/report/get [get]
func (s *Server) GetRiskReport(c *gin.Context) {
	var request struct {
		State string `form:"state"`
	}
	lo.Must0(c.BindQuery(&request))
	ctx, cancel := context.WithCancel(c)
	defer cancel()

	go func() {
		<-c.Writer.CloseNotify()
		cancel()
	}()

	report, err := services.Get().LegacyTask().GetRiskReport(ctx)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, err)
		return
	}
	if request.State == "stock" {
		report.Increment = nil
	} else if request.State == "increment" {
		report.Stock = nil
	}

	RespondWithTemplate(c, report.Template, report)
}

// @Summary 导出集群扫描数据
// @Description 导出集群扫描数据
// @Tags 健康检查
// @Param clusterID query string true "集群ID"
// @Param refreshCache query boolean false "强制刷新缓存"
// @Accept application/json
// @Produce application/json
// @Router /inspection/cluster/report/download [get]
func (s *Server) DownloadClusterReport(c *gin.Context) {
	var request struct {
		ClusterID    string `form:"clusterID"`
		RefreshCache bool   `form:"refreshCache"`
	}
	lo.Must0(c.BindQuery(&request))
	ctx, cancel := context.WithCancel(c)
	defer cancel()

	go func() {
		<-c.Writer.CloseNotify()
		cancel()
	}()

	cluster, err := services.Get().Cluster().Get(ctx, strings.TrimSpace(request.ClusterID))
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, err)
		return
	}

	result, err := services.Get().Inspection().GetClusterRiskReport(ctx, cluster, request.RefreshCache)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, err)
		return
	}

	// 设置响应的内容类型和其他头部信息
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", "attachment; filename="+cluster.ClusterID+"-"+time.Now().Format("20060102150405")+".csv")
	c.Data(http.StatusOK, "text/csv", result.CSV())
}

// @Summary 导出风险数据
// @Description 导出风险数据
// @Tags 健康检查
// @Param state query string false "保留指定状态的风险"
// @Accept application/json
// @Produce application/json
// @Router /inspection/risk/report/download [get]
func (s *Server) DownloadRiskReport(c *gin.Context) {
	var request struct {
		State string `form:"state"`
	}
	lo.Must0(c.BindQuery(&request))
	ctx, cancel := context.WithCancel(c)
	defer cancel()

	go func() {
		<-c.Writer.CloseNotify()
		cancel()
	}()

	report, err := services.Get().LegacyTask().GetRiskReport(ctx)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, err)
		return
	}

	if request.State == "stock" {
		report.Increment = nil
	} else if request.State == "increment" {
		report.Stock = nil
	}

	// 设置响应的内容类型和其他头部信息
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", "attachment; filename="+"clbrisks"+"-"+time.Now().Format("20060102150405")+".csv")
	c.Data(http.StatusOK, "text/csv", report.CSV())
}

func (s *Server) HomePage(c *gin.Context) {
	RespondWithTemplate(c, "home", struct{}{})
}

func RespondWithTemplate(c *gin.Context, template string, report any) {
	if strings.Contains(c.Request.UserAgent(), "Mozilla") {
		c.HTML(http.StatusOK, template+".html", report)
	} else {
		c.IndentedJSON(http.StatusOK, report)
	}
}
