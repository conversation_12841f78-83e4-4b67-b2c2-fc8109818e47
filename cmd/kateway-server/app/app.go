package app

import (
	"context"

	"github.com/segmentio/ksuid"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/app"
	"git.woa.com/kateway/pkg/telemetry/jaeger"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/api"
	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/options"
	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/server"
	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/tracing"
	taskpkg "git.woa.com/kateway/kateway-server/pkg/task"
	"git.woa.com/kateway/kateway-server/pkg/tmp/zhiyan"
)

func New(_ string) *app.App {
	opts := options.New()
	application := app.New(
		app.WithOptions(opts),
		app.WithRun(run(opts)),
	)
	return application
}

func run(opts *options.Options) app.RunFunc {
	return func(ctx context.Context) (err error) {
		id := ksuid.New().String()
		if err = Init(opts); err != nil {
			return
		}

		zhiyan.Init(&zhiyan.Config{
			ProjectID:  "14315",
			Env:        "prod",
			EnvID:      "120539",
			ViewID:     "6422",
			Topic:      "sdk-abcg2b23e837b259",
			ServerAddr: "publiclog.zhiyan.tencent-cloud.net:11001",
			Proto:      "tcp",
		})

		span, ctx := jaeger.StartSpanFromContext(context.Background(), jaeger.WithOperationName("main"))
		tracing.System(ctx, span, id)
		defer func() {
			if err != nil {
				jaeger.LogError(span, err)
			}

			span.Finish()
			jaeger.Close()
		}()

		go api.NewServer().Run()
		go server.New().Run()

		if opts.EnableTask {
			klog.Info("start task engine")
			defer klog.Info("stop task engine")

			eCfg := config.Get().Task.Engine
			engine := taskpkg.NewEngine(services.Get().Task(), eCfg.Concurrency, eCfg.TimeoutDuration, eCfg.ResyncInterval)
			errCh := engine.Run(ctx)
			defer engine.Shutdown(ctx)

			//go task.RunCronTasks(ctx)
			//inspection := task.NewInspection()
			//go inspection.Start(ctx)

			select {
			case <-ctx.Done():
				return nil
			case err = <-errCh:
				return err
			}
		}
		select {
		case <-ctx.Done():
			return nil
		}
	}
}
