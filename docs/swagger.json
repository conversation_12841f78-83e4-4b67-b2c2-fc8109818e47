{"swagger": "2.0", "info": {"description": "Tencent Kubernetes Engine 接入层 Kateway Server API 文档", "title": "Kateway Server API 文档", "contact": {}, "version": "1.0"}, "paths": {"/CheckImage": {"get": {"description": "指定镜像版本，执行镜像一致性校验", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["kate<PERSON>-server"], "summary": "镜像一致性校验", "parameters": [{"type": "string", "description": "镜像类型", "name": "type", "in": "query"}, {"type": "string", "description": "镜像版本", "name": "version", "in": "query"}, {"type": "string", "description": "镜像地域", "name": "region", "in": "query"}], "responses": {}}}, "/inspection/clb/report/get": {"get": {"description": "指定集群ID，CLBID，获取当前CLB健康检查数据", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["健康检查"], "summary": "查询CLB健康检查数据", "parameters": [{"type": "string", "description": "集群ID", "name": "clusterID", "in": "query", "required": true}, {"type": "string", "description": "CLBID", "name": "clbID", "in": "query", "required": true}, {"type": "boolean", "description": "强制刷新缓存", "name": "refreshCache", "in": "query"}], "responses": {}}}, "/inspection/cluster/report/download": {"get": {"description": "导出风险扫描数据", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["健康检查"], "summary": "导出风险扫描数据", "parameters": [{"type": "string", "description": "集群ID", "name": "clusterID", "in": "query", "required": true}, {"type": "boolean", "description": "强制刷新缓存", "name": "refreshCache", "in": "query"}], "responses": {}}}, "/inspection/cluster/report/get": {"get": {"description": "指定集群ID，获取当前集群健康检查数据", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["健康检查"], "summary": "查询集群健康检查数据", "parameters": [{"type": "string", "description": "集群ID", "name": "clusterID", "in": "query", "required": true}, {"type": "boolean", "description": "强制刷新缓存", "name": "refreshCache", "in": "query"}], "responses": {}}}, "/inspection/cluster/task/get": {"get": {"description": "查询指定健康检查任务", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["健康检查"], "summary": "查询指定健康检查任务", "parameters": [{"type": "string", "description": "任务ID", "name": "taskID", "in": "query", "required": true}], "responses": {}}}, "/inspection/cluster/task/run": {"get": {"description": "执行集群健康检查任务", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["健康检查"], "summary": "执行集群健康检查任务", "parameters": [{"type": "string", "description": "集群ID", "name": "clusterID", "in": "query", "required": true}, {"type": "integer", "description": "任务最大执行次数", "name": "maxSyncTimes", "in": "query", "required": true}, {"type": "string", "description": "任务执行间隔", "name": "syncInterval", "in": "query", "required": true}, {"type": "string", "description": "屏蔽风险维度", "name": "ignoreRiskLevel", "in": "query"}], "responses": {}}}, "/inspection/cluster/task/stop": {"get": {"description": "结束集群健康检查任务", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["健康检查"], "summary": "结束集群健康检查任务", "parameters": [{"type": "string", "description": "任务ID", "name": "taskID", "in": "query", "required": true}, {"type": "string", "description": "集群ID", "name": "clusterID", "in": "query", "required": true}], "responses": {}}}, "/inspection/cluster/task/update": {"get": {"description": "更新集群健康检查任务", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["健康检查"], "summary": "更新集群健康检查任务", "parameters": [{"type": "string", "description": "任务ID", "name": "taskID", "in": "query", "required": true}, {"type": "integer", "description": "任务最大执行次数", "name": "maxSyncTimes", "in": "query", "required": true}, {"type": "string", "description": "任务执行间隔", "name": "syncInterval", "in": "query", "required": true}, {"type": "string", "description": "屏蔽风险维度", "name": "ignoreRiskLevel", "in": "query"}], "responses": {}}}, "/inspection/resource/report/get": {"get": {"description": "指定集群ID，资源类型，资源NS，资源名称，获取资源信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["健康检查"], "summary": "查询资源健康检查数据", "parameters": [{"type": "string", "description": "集群ID", "name": "clusterID", "in": "query", "required": true}, {"type": "string", "description": "资源类型", "name": "resourceType", "in": "query", "required": true}, {"type": "string", "description": "资源名称", "name": "resourceName", "in": "query", "required": true}, {"type": "string", "description": "资源命名空间", "name": "resourceNamespace", "in": "query", "required": true}, {"type": "boolean", "description": "强制刷新缓存", "name": "refreshCache", "in": "query"}], "responses": {}}}, "/inspection/risk/report/download": {"get": {"description": "导出风险数据", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["健康检查"], "summary": "导出风险数据", "parameters": [{"type": "string", "description": "保留指定状态的风险", "name": "state", "in": "query"}], "responses": {}}}, "/inspection/risk/report/get": {"get": {"description": "查询巡检风险列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["健康检查"], "summary": "查询巡检风险列表", "parameters": [{"type": "string", "description": "保留指定状态的风险", "name": "state", "in": "query"}], "responses": {}}}, "/inspection/task/report/get": {"get": {"description": "查询健康检查任务列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["健康检查"], "summary": "查询健康检查任务列表", "parameters": [{"type": "string", "description": "指定集群ID", "name": "clusterID", "in": "query"}, {"type": "string", "description": "过滤指定状态的任务", "name": "state", "in": "query"}, {"type": "boolean", "description": "强制刷新缓存", "name": "refreshCache", "in": "query"}], "responses": {}}}, "/service/admin": {"get": {"description": "转发访问指定集群 service controller admin 接口", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["service-controller"], "summary": "访问Admin服务", "parameters": [{"type": "string", "description": "集群ID", "name": "clusterID", "in": "query", "required": true}], "responses": {}}}, "/service/mock": {"get": {"description": "指定镜像版本，执行 service controller 预检", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["service-controller"], "summary": "执行预检", "parameters": [{"type": "string", "description": "集群ID", "name": "clusterID", "in": "query", "required": true}, {"type": "string", "description": "镜像版本", "name": "version", "in": "query", "required": true}], "responses": {}}}}}