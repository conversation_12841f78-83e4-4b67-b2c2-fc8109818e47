info:
  contact: {}
  description: Tencent Kubernetes Engine 接入层 Kateway Server API 文档
  title: Kateway Server API 文档
  version: "1.0"
paths:
  /CheckImage:
    get:
      consumes:
      - application/json
      description: 指定镜像版本，执行镜像一致性校验
      parameters:
      - description: 镜像类型
        in: query
        name: type
        type: string
      - description: 镜像版本
        in: query
        name: version
        type: string
      - description: 镜像地域
        in: query
        name: region
        type: string
      produces:
      - application/json
      responses: {}
      summary: 镜像一致性校验
      tags:
      - kateway-server
  /inspection/clb/report/get:
    get:
      consumes:
      - application/json
      description: 指定集群ID，CLBID，获取当前CLB健康检查数据
      parameters:
      - description: 集群ID
        in: query
        name: clusterID
        required: true
        type: string
      - description: CLBID
        in: query
        name: clbID
        required: true
        type: string
      - description: 强制刷新缓存
        in: query
        name: refreshCache
        type: boolean
      produces:
      - application/json
      responses: {}
      summary: 查询CLB健康检查数据
      tags:
      - 健康检查
  /inspection/cluster/report/download:
    get:
      consumes:
      - application/json
      description: 导出风险扫描数据
      parameters:
      - description: 集群ID
        in: query
        name: clusterID
        required: true
        type: string
      - description: 强制刷新缓存
        in: query
        name: refreshCache
        type: boolean
      produces:
      - application/json
      responses: {}
      summary: 导出风险扫描数据
      tags:
      - 健康检查
  /inspection/cluster/report/get:
    get:
      consumes:
      - application/json
      description: 指定集群ID，获取当前集群健康检查数据
      parameters:
      - description: 集群ID
        in: query
        name: clusterID
        required: true
        type: string
      - description: 强制刷新缓存
        in: query
        name: refreshCache
        type: boolean
      produces:
      - application/json
      responses: {}
      summary: 查询集群健康检查数据
      tags:
      - 健康检查
  /inspection/cluster/task/get:
    get:
      consumes:
      - application/json
      description: 查询指定健康检查任务
      parameters:
      - description: 任务ID
        in: query
        name: taskID
        required: true
        type: string
      produces:
      - application/json
      responses: {}
      summary: 查询指定健康检查任务
      tags:
      - 健康检查
  /inspection/cluster/task/run:
    get:
      consumes:
      - application/json
      description: 执行集群健康检查任务
      parameters:
      - description: 集群ID
        in: query
        name: clusterID
        required: true
        type: string
      - description: 任务最大执行次数
        in: query
        name: maxSyncTimes
        required: true
        type: integer
      - description: 任务执行间隔
        in: query
        name: syncInterval
        required: true
        type: string
      - description: 屏蔽风险维度
        in: query
        name: ignoreRiskLevel
        type: string
      produces:
      - application/json
      responses: {}
      summary: 执行集群健康检查任务
      tags:
      - 健康检查
  /inspection/cluster/task/stop:
    get:
      consumes:
      - application/json
      description: 结束集群健康检查任务
      parameters:
      - description: 任务ID
        in: query
        name: taskID
        required: true
        type: string
      - description: 集群ID
        in: query
        name: clusterID
        required: true
        type: string
      produces:
      - application/json
      responses: {}
      summary: 结束集群健康检查任务
      tags:
      - 健康检查
  /inspection/cluster/task/update:
    get:
      consumes:
      - application/json
      description: 更新集群健康检查任务
      parameters:
      - description: 任务ID
        in: query
        name: taskID
        required: true
        type: string
      - description: 任务最大执行次数
        in: query
        name: maxSyncTimes
        required: true
        type: integer
      - description: 任务执行间隔
        in: query
        name: syncInterval
        required: true
        type: string
      - description: 屏蔽风险维度
        in: query
        name: ignoreRiskLevel
        type: string
      produces:
      - application/json
      responses: {}
      summary: 更新集群健康检查任务
      tags:
      - 健康检查
  /inspection/resource/report/get:
    get:
      consumes:
      - application/json
      description: 指定集群ID，资源类型，资源NS，资源名称，获取资源信息
      parameters:
      - description: 集群ID
        in: query
        name: clusterID
        required: true
        type: string
      - description: 资源类型
        in: query
        name: resourceType
        required: true
        type: string
      - description: 资源名称
        in: query
        name: resourceName
        required: true
        type: string
      - description: 资源命名空间
        in: query
        name: resourceNamespace
        required: true
        type: string
      - description: 强制刷新缓存
        in: query
        name: refreshCache
        type: boolean
      produces:
      - application/json
      responses: {}
      summary: 查询资源健康检查数据
      tags:
      - 健康检查
  /inspection/risk/report/download:
    get:
      consumes:
      - application/json
      description: 导出风险数据
      parameters:
      - description: 保留指定状态的风险
        in: query
        name: state
        type: string
      produces:
      - application/json
      responses: {}
      summary: 导出风险数据
      tags:
      - 健康检查
  /inspection/risk/report/get:
    get:
      consumes:
      - application/json
      description: 查询巡检风险列表
      parameters:
      - description: 保留指定状态的风险
        in: query
        name: state
        type: string
      produces:
      - application/json
      responses: {}
      summary: 查询巡检风险列表
      tags:
      - 健康检查
  /inspection/task/report/get:
    get:
      consumes:
      - application/json
      description: 查询健康检查任务列表
      parameters:
      - description: 指定集群ID
        in: query
        name: clusterID
        type: string
      - description: 过滤指定状态的任务
        in: query
        name: state
        type: string
      - description: 强制刷新缓存
        in: query
        name: refreshCache
        type: boolean
      produces:
      - application/json
      responses: {}
      summary: 查询健康检查任务列表
      tags:
      - 健康检查
  /service/admin:
    get:
      consumes:
      - application/json
      description: 转发访问指定集群 service controller admin 接口
      parameters:
      - description: 集群ID
        in: query
        name: clusterID
        required: true
        type: string
      produces:
      - application/json
      responses: {}
      summary: 访问Admin服务
      tags:
      - service-controller
  /service/mock:
    get:
      consumes:
      - application/json
      description: 指定镜像版本，执行 service controller 预检
      parameters:
      - description: 集群ID
        in: query
        name: clusterID
        required: true
        type: string
      - description: 镜像版本
        in: query
        name: version
        required: true
        type: string
      produces:
      - application/json
      responses: {}
      summary: 执行预检
      tags:
      - service-controller
swagger: "2.0"
