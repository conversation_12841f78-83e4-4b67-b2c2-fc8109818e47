TASK := TASK_X_REMOTE_TASKFILES=1 task -y -t Taskfile-kops.yml
VERSION := $(shell git describe --dirty --always --tag)
OUTPUT_DIR := ./_output

OS?=linux
ARCH?=amd64
SRC=$(OUTPUT_DIR)/$(OS)/$(ARCH)/kops
TARGET=kops_$(VERSION)
DST_HOST=************
DST_DIR=/data/tke-net/ingress-service
DST_TARGET=$(DST_DIR)/release/$(TARGET)
HOME_DIR=~/kops
HOME_BIN_DIR=~/kops/release
HOME_TARGET=$(HOME_BIN_DIR)/$(TARGET)

kops.build:
	$(TASK) build

kops.tag:
	tag=$$(git --no-pager show \
				 --quiet \
				 --abbrev=8 \
				 --date='format-local:%Y%m%d' \
				 --format="%cd-%h") && git tag -f $$tag && git push origin $$tag

kops.deploy:
	@echo "===========> Releasing binary kops $(VERSION) for $(OS) $(ARCH)"
	make kops.build

	@echo "复制 $(SRC) 到 跳板机 csig.mnet2.com:$(HOME_TARGET)"
	ssh csig.mnet2.com 'mkdir -p $(HOME_BIN_DIR)'
	scp $(OUTPUT_DIR)/linux/amd64/kops 'csig.mnet2.com:$(HOME_TARGET)'

	@echo "复制 csig.mnet2.com:$(HOME_TARGET) 到 $(DST_HOST):$(DST_TARGET)"
	ssh -t csig.mnet2.com ssh $(DST_HOST) 'mkdir -p $(HOME_BIN_DIR)'
	ssh -t csig.mnet2.com ssh $(DST_HOST) 'test -d || cp -R $(DST_DIR)/conf $(HOME_DIR)'
	ssh -t csig.mnet2.com scp '$(HOME_TARGET)' $(DST_HOST):'$(HOME_TARGET)'
	ssh -t csig.mnet2.com ssh $(DST_HOST) 'ln -sf $(HOME_TARGET) $(HOME_DIR)/kops'

kops.release: kops.deploy
	ssh -t csig.mnet2.com ssh $(DST_HOST) 'sudo cp $(HOME_TARGET) $(DST_TARGET)'
	ssh -t csig.mnet2.com ssh $(DST_HOST) 'sudo ln -sf $(DST_TARGET) $(DST_DIR)/kops'


kops.image:
	$(TASK) image

kops.run: kops.image
	k delete po kops
	k run kops --image=mirrors.tencent.com/kateway/kops:$(VERSION) --image-pull-policy=Always -it --command -- /bin/bash

gen:
	cd api/kateway && protoc --go_out=. --go_opt=paths=source_relative --go-grpc_out=. --go-grpc_opt=paths=source_relative *.proto

kateway.deploy:
	TASK_X_REMOTE_TASKFILES=1 task -y image
	t cd cls-7m2mnrz0
	kubectl set image deployment kateway-server kateway-server=mirrors.tencent.com/kateway/kateway-server:$(VERSION)-linux-amd64 -n $(USER)-prj-qg54hjsd
