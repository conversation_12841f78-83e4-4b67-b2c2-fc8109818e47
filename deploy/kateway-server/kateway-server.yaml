apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: kateway-server
rules:
  - apiGroups: ["coordination.k8s.io"]
    resources: ["*"]
    verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: kateway-server
subjects:
  - kind: ServiceAccount
    name: kateway-server
roleRef:
  kind: Role
  name: kateway-server
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: _NAMESPACE_-kateway-server
rules:
  - apiGroups: [""]
    resources: ["namespaces"]
    verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: _NAMESPACE_-kateway-server
subjects:
  - kind: ServiceAccount
    name: kateway-server
    namespace: _NAMESPACE_
roleRef:
  kind: ClusterRole
  name: _NAMESPACE_-kateway-server
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kateway-server
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: kateway-server
  name: kateway-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kateway-server
  template:
    metadata:
      labels:
        app: kateway-server
    spec:
      serviceAccountName: kateway-server
      hostAliases:
        - hostnames:
            - qy.eks.caas.tencentyun.com
          ip: ***********
      containers:
        - args:
            - -v=4
          image: mirrors.tencent.com/kateway/kateway-server:_VERSION_
          imagePullPolicy: Always
          name: kateway-server
          env:
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
---
apiVersion: v1
kind: Service
metadata:
  name: kateway-server
  annotations:
    service.kubernetes.io/qcloud-loadbalancer-internal-subnetid: _SUBNET_
  labels:
    app: kateway-server
spec:
  ports:
    - port: 80
      name: web
  selector:
    app: kateway-server
  type: LoadBalancer
