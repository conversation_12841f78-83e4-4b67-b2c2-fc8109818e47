version: 3

includes:
  tools:
    taskfile: "{{.INCLUDE_REPO}}/tools/Taskfile.yml"
    internal: true
  go:
    taskfile: "{{.INCLUDE_REPO}}/go/Taskfile.yml"
    vars:
      VERSION_PACKAGE: "git.woa.com/kateway/pkg/app/version"
  tag:
    taskfile: "{{.INCLUDE_REPO}}/git/tag.yml"
    internal: true
  docker:
    taskfile: "{{.INCLUDE_REPO}}/docker/Taskfile.yml"
    internal: true
    vars:
      REGISTRY_PREFIX: mirrors.tencent.com/kateway
  kubectl:
    taskfile: "{{.INCLUDE_REPO}}/kubectl/Taskfile.yml"
    internal: true
    vars:
      CONTEXT: '{{default "kateway.dev" .CONTEXT }}'
      NAMESPACE: '{{default "kube-system" .NAMESPACE }}'
      WORKLOAD: "deploy/{{.NAME}}"
      CONTAINER: "{{.NAME}}"

set:
  - nounset
  - errexit
  - pipefail

env:
  ENV:
    sh: echo ${ENV:-dev}

dotenv: [".env", "{{.ENV}}.env.", "{{.HOME}}/.env"]

vars:
  INCLUDE_REPO: https://mirrors.tencent.com/repository/generic/kateway/taskfile/v1.24.2-1
  NAME:
    sh: basename $(git remote get-url origin | sed 's/\.git$//')
  PACKAGE: "./cmd/{{.NAME}}"

  BASE: mirrors.tencent.com/kateway/base:v1.0.0

  ARCHS: "amd64 arm64"
tasks:
  default:
    desc: "默认"
    cmds:
      - task: check
      - task: build

  init:
    desc: "项目初始化，安装依赖工具，初始化相关配置"
    cmds:
      - task: tools:init

  check:
    desc: "检查"
    cmds:
      - task: go:check

  build:
    desc: "编译"
    cmds:
      - task: go:build
        vars:
          OS: "{{default OS .OS}}"
          ARCH: "{{default ARCH .ARCH}}"
          PACKAGE: "{{.PACKAGE}}"

  test:
    desc: "go:test"
    cmds:
      - task: go:test

  image:
    desc: "构建并推送镜像"
    cmds:
      - task: build
        vars:
          OS: '{{default "linux" .OS}}'
          ARCH: '{{default "amd64" .ARCH}}'
      - task: docker:build
        vars:
          NAME: "{{.NAME}}"
          TAG: "{{.VERSION}}"
          BASE: "{{.BASE}}"
          OS: "{{.OS}}"
          ARCH: "{{.ARCH}}"

  release:
    desc: "制作发布包"
    cmds:
      - for: { var: ARCHS, as: ARCH }
        task: image
        vars:
          OS: "{{.OS}}"
          ARCH: "{{.ARCH}}"

      - task: docker:manifest
        vars:
          NAME: "{{.NAME}}"
          TAG: "{{.VERSION}}"

  clean:
    desc: "go:clean"
    cmds:
      - task: go:clean
      - task: docker:clean
        vars:
          PATTERN: "{{.NAME}}"

  update:
    desc: "更新工作负载镜像"
    cmds:
      - task: kubectl:set-image
        vars:
          VERSION: "{{.VERSION}}"

  undo:
    desc: "回滚工作负载"
    cmds:
      - task: kubectl:undo

  tag:
    desc: "打tag"
    cmds:
      - task: tag:date

  untag:
    desc: "撤销最近版本"
    cmds:
      - task: tag:untag

  changelog:
    desc: "生成changelog"
    cmds:
      - task: tools:changelog

  upload:
    desc: "上传构建物"
    deps:
      - kops
    cmds:
      - task: tools:upload
        vars:
          NAMESPACE: kops/{{.VERSION}}/darwin
          WORKING_DIR: _output/darwin/arm64
          FILES: kops
      - task: tools:upload
        vars:
          NAMESPACE: kops/{{.VERSION}}/linux
          WORKING_DIR: _output/linux/amd64
          FILES: kops
      - sed 's;_VERSION_;{{.VERSION}};g' tools/install > _output/darwin/arm64/install
      - task: tools:upload
        vars:
          NAMESPACE: kops
          WORKING_DIR: _output/darwin/arm64
          FILES: install

  kops:
    desc: "构建kops"
    cmds:
      - task: go:build
        vars:
          OS: "darwin"
          ARCH: "arm64"
          PACKAGE: "./cmd/kops"
      - cp -fv _output/{{OS}}/{{ARCH}}/kops ~/.local/bin/

      - task: go:build
        vars:
          OS: "linux"
          ARCH: "amd64"
          PACKAGE: "./cmd/kops"

  release.kops:
    desc: "发布kops"
    deps:
      - upload
    cmds:
      - >
        sed -i '' "2s;: .*;: {{.VERSION}};g" build/docker/kateway-server/conf/kops.yaml
      - git add build/docker/kateway-server/conf/kops.yaml
      - >
        git commit -m "feat: 发布 kops {{.VERSION}}"
      - task: image
      - task: update
