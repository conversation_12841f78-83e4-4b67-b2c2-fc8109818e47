BEGIN;

CREATE TABLE IF NOT EXISTS `release_audit` (
    `ID` BIGINT(20) UNSIGNED AUTO_INCREMENT,             -- 主键，自增
    `Date` DATE NOT NULL,                                -- 变更日期
    `Region` VARCHAR(255) NOT NULL DEFAULT '',           -- 集群所在地域
    `ClusterID` VARCHAR(255) NOT NULL,                   -- 集群ID
    `Publisher` VARCHAR(255) NOT NULL DEFAULT '',        -- 发布人
    `Component` VARCHAR(255) NOT NULL,                   -- 组件类型
    `ImageTag` VARCHAR(255) NOT NULL,                    -- 镜像版本
    `SourceImageTag` VARCHAR(255) NOT NULL DEFAULT '',   -- 镜像版本
    `token` VARCHAR(255) DEFAULT '',                     -- 可选字段
    `Status` BOOLEAN NOT NULL,                           -- 变更成功或者失败
    `Reason` LONGTEXT DEFAULT '',                        -- 可选字段，失败原因
    `TaskID` VARCHAR(255) DEFAULT '',                    -- 可选字段，后检任务ID
    `CreatedAt` DATETIME NOT NULL,                       -- 发布任务创建时间
    `UpdatedAt` DATETIME NOT NULL,                       -- 变更时间
    `FinishedAt` DATETIME NOT NULL,                      -- 完成时间
    PRIMARY KEY (`ID`),                                  -- 主键
    INDEX (`ClusterID`)                                  -- 在 ClusterID 列上创建索引
)ENGINE=InnoDB AUTO_INCREMENT=1773680 DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `clbriskrecord` (
    AppID INT UNSIGNED NOT NULL,
    ClusterName VARCHAR(255) NOT NULL,
    CLBID VARCHAR(255) NOT NULL,
    RiskScore INT UNSIGNED NOT NULL,
    RiskCount INT UNSIGNED NOT NULL,
    RiseCount INT UNSIGNED NOT NULL,
    AllDownCLBByWeight INT UNSIGNED,
    AllDownCLBByHealth INT UNSIGNED,
    HasAllDownListenerByWeight INT UNSIGNED,
    HasAllDownListenerByHealth INT UNSIGNED,
    HasAllDownRuleByWeight INT UNSIGNED,
    HasAllDownRuleByHealth INT UNSIGNED,
    HasDownRSByHealth INT UNSIGNED,
    HasDownRSByWeight INT UNSIGNED,
    RiskLevel VARCHAR(255) NOT NULL,
    UsingResources LONGTEXT,
    `State` VARCHAR(20) NOT NULL,
    CreatedAt DATETIME NOT NULL,
    UpdatedAt DATETIME NOT NULL,
    PRIMARY KEY (ClusterName, CLBID) -- 假设 ClusterName 和 Name 组合为主键
) ENGINE=InnoDB CHARSET=utf8;


CREATE TABLE IF NOT EXISTS `inspectiontask` (
    TaskID VARCHAR(255) PRIMARY KEY,
    InstanceID VARCHAR(255) NOT NULL,
    InstanceType VARCHAR(50) NOT NULL,
    Reason TEXT,
    Creator VARCHAR(255),
    CurSyncTimes INT NOT NULL DEFAULT 0,
    MaxSyncTimes INT NOT NULL DEFAULT 0,
    SyncInterval VARCHAR(20) NOT NULL,
    IgnoreRiskLevel VARCHAR(20) DEFAULT '',
    `State` VARCHAR(20) NOT NULL,
    CreatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FinishedAt TIMESTAMP NULL,
    INDEX idxInstanceType (InstanceID, InstanceType) -- 联合索引
) ENGINE=InnoDB CHARSET=utf8;


CREATE TABLE IF NOT EXISTS `clusterstats` (
    `ID` BIGINT(20) UNSIGNED AUTO_INCREMENT,             -- 主键，自增

    ClusterName VARCHAR(255) NOT NULL,
    CLBTotalCount INT UNSIGNED NOT NULL,
    CLBTotalZeroWeightCount INT UNSIGNED NOT NULL,
    CLBTotalUnhealthCount INT UNSIGNED NOT NULL,
    CLBTotalNoRealServerCount INT UNSIGNED NOT NULL,
    CLBTotalNotExistedCount INT UNSIGNED NOT NULL,

    ListenerTotalCount INT UNSIGNED NOT NULL,
    ListenerTotalZeroWeightCount INT UNSIGNED NOT NULL,
    ListenerTotalUnhealthCount INT UNSIGNED NOT NULL,

    RuleTotalCount INT UNSIGNED NOT NULL,
    RuleTotalZeroWeightCount INT UNSIGNED NOT NULL,
    RuleTotalUnhealthCount INT UNSIGNED NOT NULL,

    RealServerTotalCount INT UNSIGNED NOT NULL,
    RealServerTotalZeroWeightCount INT UNSIGNED NOT NULL,
    RealServerTotalUnhealthCount INT UNSIGNED NOT NULL,

    CreatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FinishedAt TIMESTAMP NULL,
    PRIMARY KEY (`ID`)
) ENGINE=InnoDB CHARSET=utf8;

COMMIT:
