BEGIN;

CREATE TABLE IF NOT EXISTS `backendgroup` (
  `ID` bigint(20) unsigned NOT NULL,
  `Date` date NOT NULL,
  `Appid` varchar(255) DEFAULT '',
  `Region` varchar(255) DEFAULT '',
  `ClusterID` varchar(255) DEFAULT NULL,
  `ClusterName` varchar(255) DEFAULT NULL,
  `ClusterType` varchar(255) DEFAULT NULL,
  `ProjectID` varchar(255) DEFAULT NULL,
  `Name` varchar(255) NOT NULL,
  `Namespace` varchar(255) NOT NULL,
  `Driver` varchar(255) NOT NULL,
  `LoadBalancers` text,
  `LoadBalancerIDs` text,
  `State` varchar(255) NOT NULL,
  `Error` text NOT NULL,
  `Raw` text NOT NULL,
  `CreatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY <PERSON> (`ID`),
  UNIQUE KEY `UNIQUE_Date_Namespace_Name` (`Date`,`Namespace`,`Name`)
);
CREATE TABLE IF NOT EXISTS `clb` (
  `ID` bigint(20) unsigned NOT NULL,
  `Date` date NOT NULL,
  `Uin` varchar(255) DEFAULT NULL,
  `Appid` varchar(255) DEFAULT '',
  `Region` varchar(255) DEFAULT '',
  `ClusterID` varchar(255) DEFAULT NULL,
  `Service` varchar(255) DEFAULT NULL,
  `LB` varchar(255) NOT NULL,
  `Listener` varchar(255) DEFAULT '',
  `TargetType` varchar(255) DEFAULT '',
  `Count` int(11) DEFAULT '0',
  `CreatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Date` (`Date`,`LB`,`Listener`,`TargetType`)
);
CREATE TABLE IF NOT EXISTS `cluster` (
  `TaskID` longtext,
  `ClusterID` varchar(255) NOT NULL,
  `Type` longtext,
  `SubType` longtext,
  `Appid` bigint(20) unsigned DEFAULT NULL,
  `Region` longtext,
  `Name` longtext,
  `Error` longtext,
  `KubeConfig` longtext,
  `MetaClusterID` longtext,
  `MetaKubeConfig` longtext,
  `CreatedAt` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`ClusterID`)
);
CREATE TABLE IF NOT EXISTS `clustertotal` (
  `Date` date NOT NULL,
  `EKS` bigint(20) NOT NULL,
  `TKE` bigint(20) DEFAULT NULL,
  `TKEHosting` bigint(20) NOT NULL,
  `TKEIndependent` bigint(20) NOT NULL,
  `EKSPencent` decimal(10,2) NOT NULL,
  `TKEHostingPencent` decimal(10,2) NOT NULL,
  `TKEIndependentPencent` decimal(10,2) NOT NULL,
  `TotalUser` bigint(20) DEFAULT NULL,
  `TotalCluster` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`Date`)
);
CREATE TABLE IF NOT EXISTS `ingress_controller` (
  `ID` bigint(20) unsigned NOT NULL,
  `Date` date NOT NULL,
  `ClusterID` varchar(255) NOT NULL,
  `ClusterType` varchar(255) NOT NULL,
  `State` varchar(255) NOT NULL,
  `Cause` varchar(255) DEFAULT NULL,
  `CauseDetail` text,
  `CreatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `UNIQUE_Date_ClusterID` (`Date`,`ClusterID`)
);
CREATE TABLE IF NOT EXISTS `kateway` (
  `Date` date NOT NULL,
  `Type` varchar(255) NOT NULL,
  `ClusterID` varchar(255) NOT NULL,
  `ClusterType` varchar(255) DEFAULT 'tke',
  `Appid` varchar(255) DEFAULT '',
  `Region` varchar(255) DEFAULT '',
  `Image` varchar(255) DEFAULT '',
  `Version` varchar(255) DEFAULT '',
  `State` varchar(255) DEFAULT '',
  `Restart` int(11) DEFAULT '0',
  `Replicas` int(10) unsigned DEFAULT NULL,
  `CheckAt` datetime DEFAULT NULL,
  `CheckCost` varchar(255) DEFAULT NULL,
  `CheckError` longtext,
  `MockAt` datetime DEFAULT NULL,
  `MockCost` varchar(255) DEFAULT NULL,
  `MockError` varchar(255) DEFAULT 'Unknown',
  `MockErrorDetail` longtext,
  `CreatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`Date`,`Type`,`ClusterID`)
);
CREATE TABLE IF NOT EXISTS `katewaytotal` (
  `Date` date NOT NULL,
  `Type` varchar(255) NOT NULL,
  `Total` bigint(20) DEFAULT NULL,
  `ValidClusterPencent` decimal(10,2) DEFAULT NULL,
  `MockCount` bigint(20) DEFAULT NULL,
  `MockPercent` decimal(10,2) DEFAULT NULL,
  `CheckCount` bigint(20) DEFAULT NULL,
  `CheckPercent` decimal(10,2) DEFAULT NULL,
  `RunningCount` bigint(20) DEFAULT NULL,
  `RunningPercent` decimal(10,2) DEFAULT NULL,
  `NotRunningCount` bigint(20) DEFAULT NULL,
  `ExpectedVersionCount` bigint(20) DEFAULT NULL,
  `ExpectedVersionPercent` decimal(10,2) DEFAULT NULL,
  `UnusedTagsTotal` bigint(20) DEFAULT NULL,
  `TagsTotal` bigint(20) DEFAULT NULL,
  `UnusedTagsPercent` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`Date`,`Type`)
);
CREATE TABLE IF NOT EXISTS `lbr` (
  `ID` bigint(20) unsigned NOT NULL,
  `Date` date NOT NULL,
  `Uin` varchar(255) DEFAULT NULL,
  `Appid` varchar(255) DEFAULT '',
  `Region` varchar(255) DEFAULT '',
  `ClusterID` varchar(255) DEFAULT NULL,
  `ClusterType` varchar(255) DEFAULT NULL,
  `LB` varchar(255) NOT NULL,
  `ServiceCount` int(11) DEFAULT '0',
  `IngressCount` int(11) DEFAULT '0',
  `CreatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Date` (`Date`,`LB`)
);
CREATE TABLE IF NOT EXISTS `policy` (
  `ID` bigint(20) unsigned NOT NULL,
  `PolicyID` varchar(255) NOT NULL DEFAULT '',
  `TenantID` varchar(255) NOT NULL,
  `ClusterID` varchar(255) NOT NULL,
  `Type` varchar(255) NOT NULL DEFAULT '',
  `Region` varchar(255) NOT NULL,
  `State` varchar(255) NOT NULL DEFAULT '',
  `LastError` varchar(255) NOT NULL DEFAULT '',
  `KubeConfig` varchar(2048) NOT NULL DEFAULT '{}',
  `CreatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `DeletedAt` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `UNIQUE_ClusterID` (`ClusterID`)
);
CREATE TABLE IF NOT EXISTS `system_lb` (
  `ID` bigint(20) unsigned NOT NULL,
  `Date` date NOT NULL,
  `Appid` varchar(255) DEFAULT '',
  `Region` varchar(255) DEFAULT '',
  `ClusterID` varchar(255) DEFAULT NULL,
  `ClusterName` varchar(255) DEFAULT NULL,
  `ClusterType` varchar(255) DEFAULT NULL,
  `ProjectID` varchar(255) DEFAULT NULL,
  `Name` varchar(255) NOT NULL,
  `Namespace` varchar(255) NOT NULL,
  `Driver` varchar(255) NOT NULL,
  `LoadBalancerID` varchar(255) NOT NULL,
  `LoadBalancerName` varchar(255) NOT NULL,
  `LoadBalancerType` varchar(255) NOT NULL,
  `VIP` varchar(255) NOT NULL,
  `State` varchar(255) NOT NULL,
  `Error` text NOT NULL,
  `Raw` text NOT NULL,
  `CreatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `UNIQUE_Date_Namespace_Name` (`Date`,`Namespace`,`Name`)
);
CREATE TABLE IF NOT EXISTS `tag` (
  `ID` bigint(20) unsigned NOT NULL,
  `Appid` varchar(255) NOT NULL,
  `Key` varchar(255) NOT NULL,
  `Value` varchar(255) NOT NULL,
  `Uin` varchar(255) NOT NULL,
  `State` varchar(255) NOT NULL,
  `Resources` text NOT NULL,
  `StateDetectedTotal` int(11) NOT NULL,
  `LastTransitionTime` datetime NOT NULL,
  `CreatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `DeletedAt` int(11) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `UNIQUE_AppID_Key_Value_DeletedAt` (`Appid`,`Key`,`Value`,`DeletedAt`)
);
CREATE TABLE IF NOT EXISTS `task` (
  `TaskID` varchar(255) NOT NULL,
  `Type` varchar(255) NOT NULL DEFAULT '',
  `Version` varchar(255) NOT NULL DEFAULT '',
  `Description` text,
  `State` varchar(255) NOT NULL DEFAULT 'Pending',
  `Cost` decimal(10,2) NOT NULL,
  `Total` int(11) NOT NULL,
  `Progress` text NOT NULL,
  `Error` text NOT NULL,
  `Creator` varchar(255) NOT NULL DEFAULT 'wallaceqian',
  `CreatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `FinishedAt` datetime DEFAULT NULL,
  `UpdatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`TaskID`,`Type`)
);
CREATE TABLE IF NOT EXISTS `user_lb` (
  `ID` bigint(20) unsigned NOT NULL,
  `Date` date NOT NULL,
  `Appid` varchar(255) DEFAULT '',
  `Region` varchar(255) DEFAULT '',
  `ClusterID` varchar(255) DEFAULT NULL,
  `ClusterName` varchar(255) DEFAULT NULL,
  `ClusterType` varchar(255) DEFAULT NULL,
  `ProjectID` varchar(255) DEFAULT NULL,
  `Name` varchar(255) NOT NULL,
  `Namespace` varchar(255) NOT NULL,
  `Driver` varchar(255) NOT NULL,
  `LoadBalancerID` varchar(255) NOT NULL,
  `ListenerID` varchar(255) NOT NULL,
  `State` varchar(255) NOT NULL,
  `Error` text NOT NULL,
  `Raw` text NOT NULL,
  `CreatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `UNIQUE_Date_Namespace_Name` (`Date`,`Namespace`,`Name`)
);

COMMIT:
