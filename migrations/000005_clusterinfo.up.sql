BEGIN;

CREATE TABLE IF NOT EXISTS `clusterinfo` (
    `ID` BIGINT(20) UNSIGNED AUTO_INCREMENT,             -- 主键，自增

    -- 集群基本信息
    `ClusterName` VARCHAR(255) NOT NULL,                 -- 集群名称
    `ClusterID` VARCHAR(255) NOT NULL,                   -- 集群ID
    `ClusterRegion` VARCHAR(255) NOT NULL,               -- 集群所在地域
    `ClusterType` VARCHAR(255) NOT NULL,                 -- 集群类型
    `AppID` BIGINT(20) NOT NULL,                         -- 应用ID
    `Description` TEXT,                                  -- 集群描述
    `MetaClusterID` VARCHAR(255),                        -- 元集群ID
    `State` VARCHAR(255) NOT NULL,                       -- 集群状态

    -- 网络信息
    `ServiceCIDR` VARCHAR(255),                          -- 服务CIDR
    `NetworkType` VARCHAR(255),                          -- 网络类型
    `KubeProxyMode` VARCHAR(255),                        -- KubeProxy模式
    `K8SVersion` VARCHAR(255),                           -- <PERSON>bernetes版本
    `VpcID` VARCHAR(255),                                -- VPC ID
    `SubnetID` VARCHAR(255),                             -- 子网ID

    -- 集群基本统计信息
    `TotalCLBCount` INT UNSIGNED NOT NULL DEFAULT 0,      -- CLB总数
    `TotalServiceCount` INT UNSIGNED NOT NULL DEFAULT 0,  -- Service总数
    `TotalIngressCount` INT UNSIGNED NOT NULL DEFAULT 0,  -- Ingress总数
    `TotalTKEServiceConfigCount` INT UNSIGNED NOT NULL DEFAULT 0, -- TKE Service Config总数

    -- 组件信息
    `ServiceControllerImage` VARCHAR(255),               -- Service Controller镜像
    `ServiceControllerVersion` VARCHAR(255),             -- Service Controller版本
    `ServiceAvailableReplicas` INT,                      -- Service可用副本数
    `ServiceExpectReplicas` INT,                         -- Service期望副本数
    `ServiceArgs` TEXT,                                  -- Service参数
    `ServiceImagePullPolicy` VARCHAR(255),               -- Service镜像拉取策略
    `ServiceResource` TEXT,                              -- Service资源
    `ServiceConfigMap` TEXT,                             -- Service配置映射

    `IngressControllerImage` VARCHAR(255),               -- Ingress Controller镜像
    `IngressControllerVersion` VARCHAR(255),             -- Ingress Controller版本
    `IngressAvailableReplicas` INT,                      -- Ingress可用副本数
    `IngressExpectReplicas` INT,                         -- Ingress期望副本数
    `IngressArgs` TEXT,                                  -- Ingress参数
    `IngressImagePullPolicy` VARCHAR(255),               -- Ingress镜像拉取策略
    `IngressResource` TEXT,                              -- Ingress资源
    `IngressConfigMap` TEXT,                             -- Ingress配置映射

    -- 时间戳
    `CreatedAt` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 创建时间
    `UpdatedAt` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 更新时间
    `FinishedAt` TIMESTAMP NULL,                              -- 完成时间

    PRIMARY KEY (`ID`),
    INDEX (`ClusterID`),                                 -- 在 ClusterID 列上创建索引
    UNIQUE KEY `UNIQUE_ClusterID` (`ClusterID`)          -- 确保ClusterID唯一
) ENGINE=InnoDB CHARSET=utf8;

COMMIT;
