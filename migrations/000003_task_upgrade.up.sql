BEGIN;

DROP TABLE `task`;

CREATE TABLE `task` (
	`ID` varchar(255) NOT NULL,
	`<PERSON>rentID` varchar(255) NOT NULL,
	`Type` varchar(255) NOT NULL,
	`SubType` varchar(255) NOT NULL,
	`State` varchar(255) NOT NULL,
	`Input` text NOT NULL,
	`Progress` text NOT NULL,
	`LastError` text NOT NULL,
	`Creator` varchar(255) NOT NULL,
	`Status` text NOT NULL,
	`FinishedAt` datetime,
	`CreatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`UpdatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	PRIMARY KEY (`ID`),
	INDEX idx_ParentID (`ParentID`),
	INDEX idx_Type_State_SubType (`Type`, `State`, `SubType`)
);

COMMIT;