package merge

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	masterv1alpha1 "cloud.tencent.com/tke/master-operator/pkg/apis/master.cloud.tencent.com/v1alpha1"
	masterclient "cloud.tencent.com/tke/master-operator/pkg/client/clientset/versioned"
	"github.com/Masterminds/semver"
	"github.com/samber/lo"
	"github.com/spf13/pflag"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	rbacv1 "k8s.io/api/rbac/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/util/retry"

	"git.woa.com/kateway/pkg/telemetry/jaeger"

	"git.woa.com/kateway/kateway-server/pkg/service/cluster"
)

const (
	annotationIngCtrlSpecBeforeMerge        = "cloud.tencent.com/ingress-controller-spec-before-merge"
	annotationMasterEnableIngressController = "cloud.tencent.com/tke-cluster-ingress-controller-enabled"
	AnnotationIngressCtrlMigrated           = "cloud.tencent.com/ingress-controller-migrated"
	keyEnableIngressController              = "EnableIngressController"
	serviceControllerConfigMapName          = "tke-service-controller-config"
	ingressControllerConfigMapName          = "tke-ingress-controller-config"
	// 该注解的使用场景见：https://iwiki.woa.com/p/4012085107#eks%E9%9B%86%E7%BE%A4%E7%9A%84%E8%BF%81%E7%A7%BB
	annotationEnableIngressControllerAwareness = "cloud.tencent.com/enable-ingress-controller-awareness"
)

const (
	ingressContainerNameTKE = "l7-lb-controller"
	ingressContainerNameEKS = "ingress-controller"
)

var (
	RequiredVersion = lo.Must(semver.NewVersion("2.5.0"))
)

type ClientSets struct {
	Cluster     cluster.ClientsSet
	MetaCluster *cluster.ClientsSet
}

type Processor struct {
	ClientSets
	clsName                  string
	svcCtrlInfo, ingCtrlInfo types.NamespacedName
	inEKS                    bool
}

func NewProcessor(clsName string, clientSets ClientSets, inEKS bool) *Processor {
	p := &Processor{
		ClientSets: clientSets,
		clsName:    clsName,
		inEKS:      inEKS,
	}
	p.svcCtrlInfo, p.ingCtrlInfo = buildCtrlDeployNamespacedNames(clsName, p.MetaCluster != nil, inEKS)
	return p
}

func buildCtrlDeployNamespacedNames(name string, hasMeta, inEKS bool) (svc, ing types.NamespacedName) {
	svc = types.NamespacedName{
		Namespace: lo.Ternary(hasMeta, name, metav1.NamespaceSystem),
		Name:      lo.Ternary(hasMeta, fmt.Sprintf("%s-%s", name, "service-controller"), "service-controller"),
	}
	ing = types.NamespacedName{
		Namespace: lo.Ternary(inEKS, name, metav1.NamespaceSystem),
		Name:      lo.Ternary(inEKS, fmt.Sprintf("%s-%s", name, "ingress-controller"), "l7-lb-controller"),
	}
	return
}

func (p *Processor) getMasterClient() masterclient.Interface {
	if p.inEKS {
		return nil
	}
	if p.MetaCluster != nil {
		return p.MetaCluster.MasterCli
	}
	return nil
}

func (p *Processor) getSvcCtrlImageFromDeploy(d *appsv1.Deployment) (string, error) {
	c, exists := lo.Find(d.Spec.Template.Spec.Containers, func(c corev1.Container) bool {
		return c.Name == "service-controller"
	})
	if !exists {
		return "", errors.New("container with name service-controller does not exist")
	}
	return c.Image, nil
}

func (p *Processor) getCliForSvcCtrl() cluster.ClientsSet {
	if p.MetaCluster != nil {
		return *p.MetaCluster
	}
	return p.Cluster
}

func (p *Processor) getCliForIngCtrl() cluster.ClientsSet {
	if p.MetaCluster == nil {
		return p.Cluster
	}
	return lo.Ternary(p.inEKS, *p.MetaCluster, p.Cluster)
}

func (p *Processor) getIngCtrlDeploy(ctx context.Context) (*appsv1.Deployment, error) {
	d, err := p.getCliForIngCtrl().K8sCli.AppsV1().Deployments(p.ingCtrlInfo.Namespace).Get(ctx, p.ingCtrlInfo.Name, metav1.GetOptions{})
	if err != nil {
		if !apierrors.IsNotFound(err) {
			return nil, err
		}
		return nil, nil
	}
	return d, nil
}

func (p *Processor) getSvcCtrlDeploy(ctx context.Context) (*appsv1.Deployment, error) {
	return p.getCliForSvcCtrl().K8sCli.AppsV1().Deployments(p.svcCtrlInfo.Namespace).Get(ctx, p.svcCtrlInfo.Name, metav1.GetOptions{})
}

func (p *Processor) checkCtrlVersion(d *appsv1.Deployment) error {
	if len(d.Spec.Template.Spec.Containers) == 0 {
		return nil
	}
	container := d.Spec.Template.Spec.Containers[0]
	img := container.Image
	tag := strings.Split(img, ":")[1]
	ready, err := IsVersionReady(tag)
	if err != nil {
		return err
	}
	if ready {
		return nil
	}
	return fmt.Errorf("current version(%s) of the controller %s does not satisfy the requirement(>= %s)", tag, d.Name, RequiredVersion)
}

func IsIngressControllerEnabled(ctx context.Context, cli kubernetes.Interface) (bool, error) {
	cm, err := cli.CoreV1().ConfigMaps(metav1.NamespaceSystem).Get(ctx, serviceControllerConfigMapName, metav1.GetOptions{})
	if err != nil {
		return false, err
	}
	return cm.Data[keyEnableIngressController] == "true", nil
}

func IsVersionReady(version string) (bool, error) {
	index := strings.Index(version, "-")
	if index != -1 {
		version = version[:index]
	}
	v, err := semver.NewVersion(version)
	if err != nil {
		return false, err
	}
	return v.Compare(RequiredVersion) >= 0, nil
}

// 检查当前的ingress-controller状态是否符合迁移条件
func (p *Processor) preflightCheck(ctx context.Context, svcDeploy, ingDeploy *appsv1.Deployment) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	if ingDeploy != nil {
		if err := p.checkIngCtrlContainer(ingDeploy); err != nil {
			return err
		}

		if err := p.checkIngCtrlArgs(ingDeploy); err != nil {
			return err
		}

		if err := p.checkSvcCtrlArgs(svcDeploy); err != nil {
			return err
		}

		if err := p.checkIngCtrlConfigMap(ctx); err != nil {
			return err
		}

		if !p.inEKS {
			if err := p.checkRoleBindings(ctx, ingDeploy.Spec.Template.Spec.ServiceAccountName); err != nil {
				return err
			}
		}
	}

	return p.checkCtrlVersion(svcDeploy)
}

func (p *Processor) disableStandaloneIngressController(ctx context.Context) error {
	// eks环境中只能将ingress-controller缩容，platform组件检测到ingress-controller的deployment不存在会主动创建
	if p.inEKS {
		if err := p.updateIngCtrl(ctx, func(d *appsv1.Deployment) {
			d.Spec.Replicas = lo.ToPtr(int32(0))
		}); err != nil {
			return fmt.Errorf("failed to scale the ingress controller to 0: %w", err)
		}
		return nil
	}
	// tke托管环境中首先要在master crd上添加关闭ingress-controller的annotation
	if cli := p.getMasterClient(); cli != nil {
		if err := p.updateMaster(ctx, func(m *masterv1alpha1.Master) {
			m.Annotations[annotationMasterEnableIngressController] = "false"
		}); err != nil {
			return fmt.Errorf("failed to patch the master resource: %w", err)
		}
	}
	// tke托管或独立环境，删除ingress-controller
	err := p.getCliForIngCtrl().K8sCli.AppsV1().Deployments(p.ingCtrlInfo.Namespace).Delete(ctx, p.ingCtrlInfo.Name, metav1.DeleteOptions{})
	if err != nil {
		if !apierrors.IsNotFound(err) {
			return fmt.Errorf("failed to delete the deployment of ingress controller: %w", err)
		}
	}
	return nil
}

func (p *Processor) backupStandaloneIngressController(ctx context.Context, svcDeploy, ingDeploy *appsv1.Deployment) error {
	if _, exists := getDeploymentAnnotation(svcDeploy, annotationIngCtrlSpecBeforeMerge); exists {
		return nil
	}
	// 将ingress-controller的deployment信息去除status后更新到service-controller的annotation上
	copy := ingDeploy.DeepCopy()
	copy.Status = appsv1.DeploymentStatus{}
	copy.ManagedFields = nil
	copy.ObjectMeta.Generation = 0
	copy.ObjectMeta.ResourceVersion = ""
	return p.updateSvcCtrl(ctx, func(d *appsv1.Deployment) {
		if d.Annotations == nil {
			d.Annotations = map[string]string{}
		}
		d.Annotations[annotationIngCtrlSpecBeforeMerge] = string(lo.Must(json.Marshal(copy)))
	})
}

func (p *Processor) restoreStandaloneIngressController(ctx context.Context, deploy *appsv1.Deployment) error {
	var restored bool
	if p.inEKS {
		if err := p.updateIngCtrl(ctx, func(d *appsv1.Deployment) {
			if lo.FromPtrOr(d.Spec.Replicas, 1) == 0 {
				restored = true
				d.Spec.Replicas = deploy.Spec.Replicas
			}
		}); err != nil {
			return err
		}
	} else {
		if _, err := p.getCliForIngCtrl().K8sCli.AppsV1().Deployments(p.ingCtrlInfo.Namespace).Create(ctx, deploy, metav1.CreateOptions{}); err != nil {
			if !apierrors.IsAlreadyExists(err) {
				return fmt.Errorf("failed to create deployment: %w", err)
			}
		} else {
			restored = true
		}
	}

	if restored && lo.FromPtrOr(deploy.Spec.Replicas, 1) > 0 {
		cctx, cancel := context.WithTimeout(ctx, 2*time.Minute)
		defer cancel()

		if err := wait.PollImmediateUntilWithContext(cctx, 1*time.Second, func(ctx context.Context) (done bool, err error) {
			d, err := p.getIngCtrlDeploy(ctx)
			if err != nil {
				return false, err
			}
			return d.Status.AvailableReplicas > 0, nil
		}); err != nil {
			return fmt.Errorf("failed to wait for the ingress controller to be ready: %w", err)
		}
	}

	if p.getMasterClient() != nil {
		return p.updateMaster(ctx, func(m *masterv1alpha1.Master) {
			delete(m.Annotations, annotationMasterEnableIngressController)
		})
	}
	return nil
}

func (p *Processor) Migrate(ctx context.Context, dryrunIngressFunc func(ctx context.Context, image string) error) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	ingDeploy, err := p.getIngCtrlDeploy(ctx)
	if err != nil {
		return err
	}
	svcDeploy, err := p.getSvcCtrlDeploy(ctx)
	if err != nil {
		return err
	}

	if v, exists := getDeploymentAnnotation(svcDeploy, AnnotationIngressCtrlMigrated); exists && v == "true" {
		return nil
	}

	if err := p.preflightCheck(ctx, svcDeploy, ingDeploy); err != nil {
		return err
	}

	if dryrunIngressFunc != nil {
		dryrunIngress, err := shouldDryrunIngress(svcDeploy, ingDeploy)
		if err != nil {
			return err
		}
		if dryrunIngress {
			image, err := p.getSvcCtrlImageFromDeploy(svcDeploy)
			if err != nil {
				return err
			}
			if err := dryrunIngressFunc(ctx, image); err != nil {
				return err
			}
		}
	}

	if ingDeploy != nil {
		if err := p.backupStandaloneIngressController(ctx, svcDeploy, ingDeploy); err != nil {
			return fmt.Errorf("failed to backup ingress controller: %w", err)
		}

		replicas := lo.FromPtrOr(ingDeploy.Spec.Replicas, 1)
		enableMergedIngressController := replicas > 0
		if enableMergedIngressController {
			if err := p.startAndWaitMergedIngCtrl(ctx, svcDeploy); err != nil {
				return err
			}
		}

		if err := p.disableStandaloneIngressController(ctx); err != nil {
			return fmt.Errorf("failed to disable standalone ingress controller: %w", err)
		}

		if enableMergedIngressController {
			// 检查融合后的ingress-controller是否选主成功
			if err := p.matchSvcCtrlLogs(ctx, svcDeploy, func(logs []string) bool {
				for _, log := range lo.Reverse(logs) {
					if isIngressControllerExitLog(log) {
						return false
					}
					if isIngressControllerStartedLog(log) {
						return true
					}
				}
				return false
			}, 1*time.Minute); err != nil {
				return fmt.Errorf("the merged ingress controller failed to take the leader: %w", err)
			}
		}
	}

	if !p.inEKS {
		// 非EKS环境，禁止ingress-controller更新configmap或者lease从而获取leader权限
		if err := p.updateIngCtrlRole(ctx, func(role *rbacv1.ClusterRole) {
			_, index, exists := lo.FindIndexOf(role.Rules, func(r rbacv1.PolicyRule) bool {
				return lo.Contains(r.APIGroups, "") && lo.Contains(r.Resources, "configmaps") && lo.Contains(r.Verbs, "*")
			})
			if exists {
				role.Rules[index].Resources = lo.Filter(role.Rules[index].Resources, func(s string, _ int) bool {
					return s != "configmaps"
				})
			}
			role.Rules = lo.Filter(role.Rules, func(r rbacv1.PolicyRule, _ int) bool {
				return !(r.APIGroups[0] == "coordination.k8s.io" && r.Resources[0] == "leases")
			})
			if role.Annotations == nil {
				role.Annotations = map[string]string{}
			}
			role.Annotations[AnnotationIngressCtrlMigrated] = "true"
		}); err != nil {
			return fmt.Errorf("failed to tight permissions: %w", err)
		}
	}

	if err := p.updateSvcCtrl(ctx, func(d *appsv1.Deployment) {
		if d.Annotations == nil {
			d.Annotations = map[string]string{}
		}
		d.Annotations[AnnotationIngressCtrlMigrated] = "true"
	}); err != nil {
		return err
	}

	return nil
}

func (p *Processor) checkIngCtrlConfigMap(ctx context.Context) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli := p.Cluster.K8sCli.CoreV1()
	cm, err := cli.ConfigMaps(metav1.NamespaceSystem).Get(ctx, "tke-ingress-controller-config", metav1.GetOptions{})
	if err != nil {
		return err
	}
	if cm.Data["SILENT_START"] == "true" {
		return errors.New("the \"SILENT_START\" field of the tke-ingress-controller-config is true")
	}
	return nil
}

// 检查当前ingress-controller是否绑定了非默认的集群权限
func (p *Processor) checkRoleBindings(ctx context.Context, sa string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli := p.Cluster.K8sCli.RbacV1()
	if sa != "lb-ingress" {
		return errors.New("the service account is not lb-ingress")
	}
	roleBindings, err := cli.RoleBindings(metav1.NamespaceSystem).List(ctx, metav1.ListOptions{})
	if err != nil {
		return err
	}
	find := func(ss []rbacv1.Subject) bool {
		_, exists := lo.Find(ss, func(s rbacv1.Subject) bool {
			return s.Kind == "ServiceAccount" && s.Name == sa && s.Namespace == metav1.NamespaceSystem
		})
		return exists
	}
	unexpectedBindings := lo.FilterMap(roleBindings.Items, func(rb rbacv1.RoleBinding, _ int) (string, bool) {
		return rb.Name, find(rb.Subjects)
	})
	if len(unexpectedBindings) != 0 {
		return fmt.Errorf("unexpected role bindings %v related to sa %q", unexpectedBindings, sa)
	}

	clusterBindings, err := cli.ClusterRoleBindings().List(ctx, metav1.ListOptions{})
	if err != nil {
		return err
	}
	unexpectedClusterBindings := lo.FilterMap(clusterBindings.Items, func(b rbacv1.ClusterRoleBinding, _ int) (string, bool) {
		return b.Name, find(b.Subjects) && b.Name != "lb-ingress-clusterrole-nisa-binding"
	})
	if len(unexpectedClusterBindings) != 0 {
		return fmt.Errorf("unexpected cluster role bindings %v related to sa %q", unexpectedClusterBindings, sa)
	}
	return nil
}

func (p *Processor) updateIngCtrlRole(ctx context.Context, update func(role *rbacv1.ClusterRole)) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli := p.Cluster.K8sCli.RbacV1()
	return retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		cr, err := cli.ClusterRoles().Get(ctx, "lb-ingress-clusterrole", metav1.GetOptions{})
		if err != nil {
			if apierrors.IsNotFound(err) {
				return nil
			}
			return err
		}

		update(cr)
		_, err = cli.ClusterRoles().Update(ctx, cr, metav1.UpdateOptions{})
		return err
	})
}

func (p *Processor) startAndWaitMergedIngCtrl(ctx context.Context, svcCtrl *appsv1.Deployment) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	if p.inEKS {
		defer func() {
			if err := p.updateSvcCtrlConfig(ctx, func(cm *corev1.ConfigMap) {
				delete(cm.Data, keyEnableIngressController)
				delete(cm.Annotations, annotationEnableIngressControllerAwareness)
			}); err != nil {
				jaeger.LogError(span, err)
			}
		}()
	}

	if err := p.updateSvcCtrlConfig(ctx, func(cm *corev1.ConfigMap) {
		if cm.Data == nil {
			cm.Data = map[string]string{}
		}
		cm.Data[keyEnableIngressController] = "true"
		if p.inEKS {
			if cm.Annotations == nil {
				cm.Annotations = map[string]string{}
			}
			cm.Annotations[annotationEnableIngressControllerAwareness] = "true"
		}
	}); err != nil {
		return fmt.Errorf("failed to start merged ingress controller: %w", err)
	}
	if err := p.matchSvcCtrlLogs(ctx, svcCtrl, func(logs []string) bool {
		_, exists := lo.Find(logs, func(l string) bool {
			return strings.Contains(l, "Launching the leader election of ingress controller")
		})
		return exists
	}, 1*time.Minute); err != nil {
		return fmt.Errorf("failed to wait the merged ingress controller to start: %w", err)
	}
	return nil
}

func (p *Processor) disableMergedIngCtrl(ctx context.Context, deploy *appsv1.Deployment) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli := p.Cluster.K8sCli.CoreV1()
	cm, err := cli.ConfigMaps(metav1.NamespaceSystem).Get(ctx, serviceControllerConfigMapName, metav1.GetOptions{})
	if err != nil {
		return err
	}
	if cm.Data != nil && cm.Data[keyEnableIngressController] == "false" {
		return nil
	}

	if p.inEKS {
		defer func() {
			if err := p.updateSvcCtrlConfig(ctx, func(cm *corev1.ConfigMap) {
				delete(cm.Data, keyEnableIngressController)
				delete(cm.Annotations, annotationEnableIngressControllerAwareness)
			}); err != nil {
				jaeger.LogError(span, err)
			}
		}()
	}

	if err := p.updateSvcCtrlConfig(ctx, func(cm *corev1.ConfigMap) {
		if cm.Data == nil {
			cm.Data = map[string]string{}
		}
		cm.Data[keyEnableIngressController] = "false"
		if p.inEKS {
			if cm.Annotations == nil {
				cm.Annotations = map[string]string{}
			}
			cm.Annotations[annotationEnableIngressControllerAwareness] = "true"
		}
	}); err != nil {
		return fmt.Errorf("failed to update the tke-service-controller-config: %w", err)
	}
	if err := p.matchSvcCtrlLogs(ctx, deploy, func(logs []string) bool {
		for _, log := range lo.Reverse(logs) {
			if isIngressControllerStartedLog(log) {
				return false
			}
			if isIngressControllerExitLog(log) {
				return true
			}
		}
		return false
	}, 1*time.Minute); err != nil {
		return fmt.Errorf("failed to wait the merged ingress controller to exit: %w", err)
	}
	return nil
}

func (p *Processor) updateMaster(ctx context.Context, updateFunc func(*masterv1alpha1.Master)) error {
	cli := p.getMasterClient()
	return retry.RetryOnConflict(retry.DefaultRetry, func() error {
		m, err := cli.MasterV1alpha1().Masters(p.clsName).Get(ctx, p.clsName, metav1.GetOptions{})
		if err != nil {
			return err
		}
		d := m.DeepCopy()
		updateFunc(d)
		if reflect.DeepEqual(m, d) {
			return nil
		}
		_, err = cli.MasterV1alpha1().Masters(p.clsName).Update(ctx, d, metav1.UpdateOptions{})
		return err
	})
}

func (p *Processor) updateConfigmap(ctx context.Context, ns, name string, updateFn func(*corev1.ConfigMap)) error {
	cli := p.Cluster.K8sCli.CoreV1()
	return retry.RetryOnConflict(retry.DefaultRetry, func() error {
		cm, err := cli.ConfigMaps(ns).Get(ctx, name, metav1.GetOptions{})
		if err != nil {
			return err
		}
		d := cm.DeepCopy()
		updateFn(d)
		if reflect.DeepEqual(cm, d) {
			return nil
		}
		_, err = cli.ConfigMaps(ns).Update(ctx, d, metav1.UpdateOptions{})
		return err
	})
}

func (p *Processor) updateSvcCtrlConfig(ctx context.Context, updateFunc func(*corev1.ConfigMap)) error {
	return p.updateConfigmap(ctx, metav1.NamespaceSystem, serviceControllerConfigMapName, updateFunc)
}

func (p *Processor) updateIngCtrlConfig(ctx context.Context, updateFunc func(*corev1.ConfigMap)) error {
	return p.updateConfigmap(ctx, metav1.NamespaceSystem, ingressControllerConfigMapName, updateFunc)
}

func (p *Processor) updateSvcCtrl(ctx context.Context, updateFunc func(*appsv1.Deployment)) error {
	return retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		d, err := p.getSvcCtrlDeploy(ctx)
		if err != nil {
			return err
		}
		dd := d.DeepCopy()
		updateFunc(dd)
		if reflect.DeepEqual(d, dd) {
			return nil
		}
		_, err = p.getCliForSvcCtrl().K8sCli.AppsV1().Deployments(p.svcCtrlInfo.Namespace).Update(ctx, dd, metav1.UpdateOptions{})
		return err
	})
}

func (p *Processor) updateIngCtrl(ctx context.Context, updateFunc func(*appsv1.Deployment)) error {
	return retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		d, err := p.getIngCtrlDeploy(ctx)
		if err != nil {
			return err
		}
		if d == nil {
			return nil
		}
		dd := d.DeepCopy()
		updateFunc(dd)
		if reflect.DeepEqual(d, dd) {
			return nil
		}
		_, err = p.getCliForIngCtrl().K8sCli.AppsV1().Deployments(p.ingCtrlInfo.Namespace).Update(ctx, dd, metav1.UpdateOptions{})
		return err
	})
}

func (p *Processor) isTKEIncrementalCluster(_ context.Context, d *appsv1.Deployment) bool {
	if p.inEKS {
		return false
	}
	if p.getMasterClient() == nil {
		return false
	}

	if d.Annotations != nil && d.Annotations[AnnotationIngressCtrlMigrated] == "true" {
		return false
	}

	svcContainer, exists := lo.Find(d.Spec.Template.Spec.Containers, func(c corev1.Container) bool {
		return c.Name == "service-controller"
	})
	if !exists {
		return false
	}
	_, exists = lo.Find(svcContainer.Command, func(cmd string) bool { return cmd == "--enable-ingress-controller-default=true" })
	return exists
}

func (p *Processor) rollbackTKEIncrementalCluster(ctx context.Context) error {
	if err := p.updateMaster(ctx, func(m *masterv1alpha1.Master) {
		m.Annotations[annotationMasterEnableIngressController] = "true"
	}); err != nil {
		return err
	}

	if err := p.updateSvcCtrlConfig(ctx, func(cm *corev1.ConfigMap) {
		cm.Data[keyEnableIngressController] = "false"
	}); err != nil {
		return err
	}

	if err := p.updateIngCtrlConfig(ctx, func(cm *corev1.ConfigMap) {
		cm.Data["SILENT_START"] = "false"
	}); err != nil {
		return err
	}
	return nil
}

func (p *Processor) Rollback(ctx context.Context, dryrunIngressFunc func(context.Context, string, *appsv1.Deployment) error) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	d, err := p.getSvcCtrlDeploy(ctx)
	if err != nil {
		return err
	}

	if p.isTKEIncrementalCluster(ctx, d) {
		return p.rollbackTKEIncrementalCluster(ctx)
	}

	if !p.inEKS {
		if err := p.updateIngCtrlRole(ctx, func(role *rbacv1.ClusterRole) {
			_, index, exists := lo.FindIndexOf(role.Rules, func(r rbacv1.PolicyRule) bool {
				return lo.Contains(r.APIGroups, "") && lo.Contains(r.Verbs, "*")
			})
			if exists && !lo.Contains(role.Rules[index].Resources, "configmaps") {
				role.Rules[index].Resources = append(role.Rules[index].Resources, "configmaps")
			}
			_, exists = lo.Find(role.Rules, func(r rbacv1.PolicyRule) bool {
				return r.APIGroups[0] == "coordination.k8s.io" && r.Resources[0] == "leases" && r.Verbs[0] == "*"
			})
			if !exists {
				role.Rules = append(role.Rules, rbacv1.PolicyRule{
					APIGroups: []string{"coordination.k8s.io"},
					Resources: []string{"leases"},
					Verbs:     []string{"*"},
				})
			}
			delete(role.Annotations, AnnotationIngressCtrlMigrated)
		}); err != nil {
			return fmt.Errorf("failed to roll back rbac permissions: %w", err)
		}
	}

	if v, exists := getDeploymentAnnotation(d, annotationIngCtrlSpecBeforeMerge); exists {
		deploy := &appsv1.Deployment{}
		if err := json.Unmarshal([]byte(v), deploy); err != nil {
			return fmt.Errorf("failed to unmarshal the spec of the standalone ingress controller: %w", err)
		}
		if lo.FromPtrOr(deploy.Spec.Replicas, 1) > 0 {
			if dryrunIngressFunc != nil {
				cs := deploy.Spec.Template.Spec.Containers
				fmt.Printf("Ingress dry run need to be performed for %s before restoring standalone ingress controller\n", p.clsName)
				if err := dryrunIngressFunc(ctx, cs[0].Image, deploy.DeepCopy()); err != nil {
					return fmt.Errorf("failed to dry run ingress with image %q: %w", cs[0].Image, err)
				}
			}

			if err := p.disableMergedIngCtrl(ctx, d); err != nil {
				return fmt.Errorf("failed to disable the merged ingress controller: %w", err)
			}
		}

		if err := p.restoreStandaloneIngressController(ctx, deploy); err != nil {
			return fmt.Errorf("failed to restore standalone ingress controller: %w", err)
		}
	}

	if err := p.updateSvcCtrl(ctx, func(d *appsv1.Deployment) {
		delete(d.Annotations, annotationIngCtrlSpecBeforeMerge)
		delete(d.Annotations, AnnotationIngressCtrlMigrated)
	}); err != nil {
		return fmt.Errorf("failed to remove migration annotations: %w", err)
	}

	return nil
}

func (p Processor) getIngCtrlContainerName() string {
	if p.inEKS {
		return ingressContainerNameEKS
	}
	return ingressContainerNameTKE
}

func (p *Processor) checkIngCtrlContainer(d *appsv1.Deployment) error {
	cs := d.Spec.Template.Spec.Containers
	if len(cs) != 1 {
		return fmt.Errorf("the ingress-controller deployment should have exactly one container, but got %d", len(cs))
	}
	c := cs[0]
	name := p.getIngCtrlContainerName()
	if c.Name != name {
		return fmt.Errorf("the ingress-controller container name is %q, but expect %q", c.Name, name)
	}
	if !strings.Contains(c.Image, "ingress-controller") {
		return fmt.Errorf("unknown ingress controller image %q", c.Image)
	}
	return nil
}

func (p *Processor) checkSvcCtrlArgs(d *appsv1.Deployment) error {
	cs := d.Spec.Template.Spec.Containers
	if len(cs) != 1 {
		return fmt.Errorf("unexpected service controller container num %d", len(cs))
	}
	c := cs[0]
	args := c.Args
	args = append(args, c.Command...)
	var (
		listenerQuota, backendQuota int
	)
	for _, str := range args {
		parts := strings.Split(str, " ")
		for _, p := range parts {
			switch {
			case strings.HasPrefix(p, "--backend-quota="):
				backendQuota, _ = strconv.Atoi(strings.TrimPrefix(p, "--backend-quota="))
			case strings.HasPrefix(p, "--listener-quota="):
				listenerQuota, _ = strconv.Atoi(strings.TrimPrefix(p, "--listener-quota="))
			}
		}
	}
	if listenerQuota != 0 || backendQuota != 0 {
		return fmt.Errorf("the service controller should not have quota args, but got %d(listener-quota), %d(backend-quota)", listenerQuota, backendQuota)
	}
	return nil
}

// 检查当前的ingress-controller是否配置了一些非默认的启动参数
func (p *Processor) checkIngCtrlArgs(d *appsv1.Deployment) error {
	var argsStr string

	c := d.Spec.Template.Spec.Containers[0]
	argsStr = strings.Join(append(append([]string{}, c.Command...), c.Args...), " ")
	currentArgs := strings.Split(argsStr, " ")
	_, inEKS := lo.Find(c.Env, func(env corev1.EnvVar) bool {
		return env.Name == "TKE_ENV_FOR_EKS_CLUSTER"
	})
	return p.checkArgs(currentArgs, inEKS)
}

func (p *Processor) checkArgs(curArgs []string, inEKS bool) error {
	fs := pflag.NewFlagSet("", pflag.ContinueOnError)

	type flagSpec struct {
		name         string
		defaultValue string
	}

	acceptableSpecs := []flagSpec{
		{
			name:         "kube-config",
			defaultValue: lo.Ternary(inEKS, "/etc/kubernetes/config", ""),
		},
		{
			name:         "master",
			defaultValue: "",
		},
		{
			name:         "qps",
			defaultValue: "1000",
		},
		{
			name:         "burst",
			defaultValue: "10000",
		},
		{
			name:         "cluster-name",
			defaultValue: p.clsName,
		},
		{
			name:         "metric-port",
			defaultValue: "10254",
		},
		{
			name:         "backend-quota",
			defaultValue: "0",
		},
		{
			name:         "listener-quota",
			defaultValue: "0",
		},
		{
			name:         "workers",
			defaultValue: "20",
		},
		{
			name:         "rate-limit",
			defaultValue: "18",
		},
		{
			name:         "ingress-class",
			defaultValue: "qcloud",
		},
		{
			name:         "enable-tracing",
			defaultValue: "false",
		},
		{
			name:         "enable-multiclusteringress",
			defaultValue: "false",
		},
	}
	for _, f := range acceptableSpecs {
		fs.String(f.name, f.defaultValue, "")
	}
	if err := fs.Parse(curArgs); err != nil {
		return fmt.Errorf("unacceptable args of ingress-controller detected: %w", err)
	}

	var (
		errs []error
	)

	fs.Visit(func(f *pflag.Flag) {
		spec, exists := lo.Find(acceptableSpecs, func(s flagSpec) bool { return s.name == f.Name })
		if !exists {
			panic(fmt.Sprintf("unexpected flag %s", f.Name))
		}
		if f.Value.String() != spec.defaultValue {
			errs = append(errs, fmt.Errorf("the value of the flag %q is not the default, current: %v, expect: %v",
				f.Name, f.Value, spec.defaultValue))
		}
	})
	return errors.Join(errs...)
}

func (p *Processor) matchSvcCtrlLogs(ctx context.Context, d *appsv1.Deployment, matchFn func([]string) bool,
	timeout time.Duration) error {
	cli := p.getCliForSvcCtrl().K8sCli
	pods, err := listRunningPodsForDeploy(ctx, cli, d)
	if err != nil {
		return err
	}
	cctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()
	return wait.PollUntilWithContext(cctx, 2*time.Second, func(ctx context.Context) (done bool, err error) {
		logsByPod, err := retrievePodRecentLogs(ctx, cli, pods, "service-controller")
		if err != nil {
			return false, err
		}
		for _, logs := range logsByPod {
			if matchFn(logs) {
				return true, nil
			}
		}
		return false, nil
	})
}

// retrieve pod logs since 10min before
func retrievePodRecentLogs(ctx context.Context, cli kubernetes.Interface, pods []corev1.Pod, container string) (map[string][]string, error) {
	logsByPod := map[string][]string{}
	since := time.Now().Add(-10 * time.Minute)
	for _, p := range pods {
		var b bytes.Buffer
		req := cli.CoreV1().Pods(p.Namespace).GetLogs(p.Name, &corev1.PodLogOptions{
			Container: container,
			SinceTime: &metav1.Time{Time: since},
		})
		rc, err := req.Stream(ctx)
		if err != nil {
			return nil, err
		}
		defer rc.Close()
		b.ReadFrom(rc)
		logsByPod[p.Name] = strings.Split(b.String(), "\n")
	}
	return logsByPod, nil
}

func listRunningPodsForDeploy(ctx context.Context, cli kubernetes.Interface, d *appsv1.Deployment) ([]corev1.Pod, error) {
	res, err := cli.CoreV1().Pods(d.Namespace).List(ctx, metav1.ListOptions{
		LabelSelector: labels.SelectorFromSet(d.Spec.Template.Labels).String(),
	})
	if err != nil {
		return nil, err
	}
	return lo.Filter(res.Items, func(p corev1.Pod, _ int) bool {
		return p.Status.Phase == corev1.PodRunning
	}), nil
}

func getDeploymentAnnotation(deploy *appsv1.Deployment, key string) (string, bool) {
	if deploy.Annotations == nil {
		return "", false
	}
	v, ok := deploy.Annotations[key]
	return v, ok
}

func shouldDryrunIngress(svcDeploy, ingDeploy *appsv1.Deployment) (bool, error) {
	if ingDeploy != nil && lo.FromPtrOr(ingDeploy.Spec.Replicas, 1) > 0 {
		return true, nil
	}
	// tke托管或者独立环境下ingress-controller备份成功且被删除，需要从备份的spec里边查看ingress-controller的原始副本数
	raw, exists := getDeploymentAnnotation(svcDeploy, annotationIngCtrlSpecBeforeMerge)
	if exists {
		old := &appsv1.Deployment{}
		if err := json.Unmarshal([]byte(raw), old); err != nil {
			return false, err
		}
		if lo.FromPtrOr(old.Spec.Replicas, 1) > 0 {
			return true, nil
		}
	}
	return false, nil
}

func isIngressControllerExitLog(log string) bool {
	return strings.Contains(log, "Leader election of ingress controller exited")
}

func isIngressControllerStartedLog(log string) bool {
	return strings.Contains(log, "Ingress controller started successfully")
}
