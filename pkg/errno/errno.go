package errno

import (
	"fmt"

	"github.com/pkg/errors"
)

type Errno struct {
	Code int
	Msg  string
}

var (
	DefaultV3ErrorCode = "InternalError.DefaultUnknownError"
)

type CauseError struct {
	Code int
	Msg  string
	Err  error
}

func (c CauseError) Error() string {
	if c.Err != nil {
		return fmt.Sprintf("%s(%s)", c.Msg, c.Err.Error())
	}
	return fmt.Sprintf(c.Msg)
}

func Cause(errno Errno, err error) *CauseError {
	return &CauseError{Code: errno.Code, Msg: errno.Msg, Err: err}
}

func GetCause(err error) *CauseError {
	res, ok := errors.Cause(err).(*CauseError)
	if ok {
		return res
	}
	return Cause(UNEXCEPTED_INTERNAL_ERROR, fmt.Errorf("unexpected internal error"))
}

func Is(cause error, target Errno) bool {
	err, ok := cause.(*CauseError)
	if !ok {
		return false
	}
	return err.Code == target.Code
}

func CauseIs(err error, target Errno) bool {
	if err == nil {
		return false
	}
	return Is(GetCause(err), target)
}

var SUCCESS = Errno{Code: 0, Msg: "SUCCESS"}

var UNEXCEPTED_INTERNAL_ERROR = Errno{Code: -1, Msg: "unexcepted internal error occured"}

var PARAM_ERROR = Errno{Code: -10000, Msg: "PARAM_ERROR"}

var COMPONENT_CLINET_COMMON_ERROR = Errno{Code: -30000, Msg: "COMPONENT_CLIENT_COMMON_ERROR"}

var KUBE_CLIENT_CONNECTION_ERROR = Errno{Code: -160003, Msg: "KUBE_CLIENT_CONNECTION_ERROR"}

var CLUSTER_NOT_FOUND_ERROR_V3 = Errno{Code: -9016009, Msg: "CLUSTER_NOT_FOUND"}

var KUBE_CLIENT_CONF_ERROR = Errno{Code: -160002, Msg: "KUBE_CLIENT_CONF_ERROR"}

var KUBE_CLIENT_CREATE_ERROR = Errno{Code: -160001, Msg: "KUBE_CLIENT_CREATE_ERROR"} // new client的时候失败了
