package runtime

import (
	"fmt"
	"reflect"
	"runtime"
	"strings"
)

func ParseRawFuncName(raw string) string {
	index := strings.LastIndex(raw, ".")
	return strings.TrimSuffix(raw[index+1:], "-fm")
}

func RawFuncName(f any) string {
	return runtime.FuncForPC(reflect.ValueOf(f).Pointer()).Name()
}

func FuncName(f any) string {
	return ParseRawFuncName(RawFuncName(f))
}

func CallerFuncName(skips int) string {
	pc, _, _, ok := runtime.Caller(skips + 1)
	if !ok {
		panic("runtime.Caller error")
	}

	return ParseRawFuncName(runtime.FuncForPC(pc).Name())
}

func RawCallerFuncName(skips int) string {
	pc, _, _, ok := runtime.Caller(skips)
	if !ok {
		panic("runtime.Caller error")
	}

	return runtime.FuncForPC(pc).Name()
}

// GetPanicMessage logs the caller tree when a panic occurs.
func GetPanicError(r interface{}) error {
	const size = 64 << 10
	stacktrace := make([]byte, size)
	stacktrace = stacktrace[:runtime.Stack(stacktrace, false)]
	if _, ok := r.(string); !ok {
		return fmt.Errorf("observed a panic: %#v (%v)\n%s", r, r, stacktrace)
	}
	return fmt.Errorf("observed a panic: %s\n%s", r, stacktrace)
}
