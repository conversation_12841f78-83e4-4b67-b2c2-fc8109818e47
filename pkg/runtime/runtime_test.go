package runtime

import (
	"testing"

	"github.com/go-playground/assert/v2"
)

type T struct {
}

func (t T) Func1() {}

func Func2() {}

func TestFuncName(t *testing.T) {
	var obj T
	assert.Equal(t, "Func1", <PERSON><PERSON><PERSON><PERSON>(obj.Func1))
	assert.Equal(t, "Func2", Func<PERSON><PERSON>(Func2))
}

func TestCallerFuncName(t *testing.T) {
	assert.Equal(t, "TestCallerFuncName", CallerFuncName(0))
}
