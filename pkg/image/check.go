package image

import (
	"fmt"
	"strings"

	"git.woa.com/kateway/kateway-server/pkg/image/cmd"
)

type ErrMsg struct {
	Obj     cmd.ImageUrl
	Msg     string
	ErrType string
}

type ErrMsgs struct {
	Errs []ErrMsg
}

func (ems *ErrMsgs) Error() string {
	errStr := ""
	for index, em := range ems.Errs {
		errStr += fmt.Sprintf("%v", em.Error())
		// 检查是否为最后一个错误，如果不是，则添加换行符
		if index < len(ems.Errs)-1 {
			errStr += "\n"
		}
	}
	return errStr
}

func (em *ErrMsg) Error() string {
	return fmt.Sprintf("{错误类型:%v	镜像地址:%v\n详情:%v}", em.ErrType, em.Obj.GetStr(), em.Msg)
}

func getImgUrl(imagePath string, region string, version string) (cmd.ImageUrl, error) {
	parts := strings.Split(imagePath, "/")
	if len(parts) != 3 {
		return cmd.ImageUrl{}, fmt.Errorf("镜像地址错误")
	}
	return cmd.ImageUrl{
		Domain:    region + parts[0],
		Namespace: parts[1],
		ImageName: parts[2],
		Tag:       version,
	}, nil
}

func compareMap(curContain map[string]bool, needContain []string) []string {
	notContain := make([]string, 0)
	for _, item := range needContain {
		if _, ok := curContain[item]; !ok {
			notContain = append(notContain, item)
		}
	}
	return notContain
}

func checkSrcManifest(srcManifest cmd.DockerManifestList, needContainArch []string) error {
	curContain := make(map[string]bool)
	for _, item := range srcManifest.Manifests {
		curContain[item.Platform.Architecture] = true
	}
	notContain := compareMap(curContain, needContainArch)
	if len(notContain) != 0 {
		return fmt.Errorf("%s架构的镜像未发现", notContain)
	}
	return nil
}

func compareManifest(srcManifest cmd.DockerManifestList, curManifest cmd.DockerManifestList) error {
	curContain := make(map[string]bool)
	for _, item := range curManifest.Manifests {
		curContain[item.Digest] = true
	}
	needContain := []string{srcManifest.Manifests[0].Digest, srcManifest.Manifests[1].Digest}
	if len(curContain) != 2 {
		needContainArch := make([]string, 0)
		curContainArch := make([]string, 0)
		curContainArchMap := make(map[string]bool)
		for _, item := range srcManifest.Manifests {
			needContainArch = append(needContainArch, item.Platform.Architecture)
		}
		for _, item := range curManifest.Manifests {
			curContainArch = append(curContainArch, item.Platform.Architecture)
			curContainArchMap[item.Platform.Architecture] = true
		}
		notContainArch := compareMap(curContainArchMap, needContainArch)
		err1 := fmt.Errorf("%s目标镜像缺失", notContainArch)
		//如果只包含一个架构的镜像，对比源镜像的架构是否一致
		if len(curContain) == 1 {
			if curManifest.Manifests[0].Digest != needContain[0] && curManifest.Manifests[0].Digest != needContain[1] {
				err2 := fmt.Errorf(err1.Error()+","+"%s架构镜像与源不一致", curContainArch)
				return err2
			}
		}
		return err1
	}
	notContain := compareMap(curContain, needContain)
	if len(notContain) != 0 {
		digestToManifest := make(map[string]cmd.Manifest)
		for _, item := range srcManifest.Manifests {
			digestToManifest[item.Digest[7:]] = item
		}
		for index := range notContain {
			notContain[index] = notContain[index][7:]
		}
		notContainArch := make([]string, 0)
		for _, item := range notContain {
			if _, ok := digestToManifest[item]; ok {
				notContainArch = append(notContainArch, digestToManifest[item].Platform.Architecture)
			}
		}
		return fmt.Errorf("%s架构镜像与源不一致", notContainArch)
	}
	return nil
}

func Check(src string, versions []string, regions []string) error {
	var errs []ErrMsg
	srcImgUrl, err := getImgUrl(src, "", "")
	if err != nil {
		errs = append(errs, ErrMsg{srcImgUrl, strings.TrimSpace(err.Error()), "url格式错误"})
		return &ErrMsgs{errs}
	}
	targetImgPath := srcImgUrl.Domain + "/" + "tkeimages" + "/" + srcImgUrl.ImageName
	for _, version := range versions {
		// 确保源镜像正常
		srcImgUrl.Tag = version
		srcManifestList, err := srcImgUrl.GetManifest()
		if err != nil {
			errs = append(errs, ErrMsg{srcImgUrl, strings.TrimSpace(err.Error()), "获取manifest失败"})
			continue
		}
		err = checkSrcManifest(srcManifestList, []string{"amd64", "arm64"})
		if err != nil {
			errs = append(errs, ErrMsg{srcImgUrl, strings.TrimSpace(err.Error()), "源镜像缺失"})
			continue
		}
		// 检查目标镜像
		for _, region := range regions {
			curImgUrl, err := getImgUrl(targetImgPath, region, version)
			if err != nil {
				errs = append(errs, ErrMsg{curImgUrl, strings.TrimSpace(err.Error()), "url格式错误"})
				continue
			}
			curMainfest, err := curImgUrl.GetManifest()
			if err != nil {
				retryNum := 0
				for ; retryNum < 3; retryNum++ {
					curMainfest, err = curImgUrl.GetManifest()
					if err == nil {
						break
					}
				}
				if retryNum == 3 {
					//errs = append(errs, ErrMsg{curImgUrl, strings.TrimSpace(err.Error()), "获取manifest失败"})
					continue
				}
			}
			if err := compareManifest(srcManifestList, curMainfest); err != nil {
				errs = append(errs, ErrMsg{curImgUrl, strings.TrimSpace(err.Error()), "目标镜像异常"})
			}
		}
	}
	if len(errs) == 0 {
		return nil
	}
	return &ErrMsgs{errs}
}
