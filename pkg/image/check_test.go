package image

import (
	"fmt"
	"reflect"
	"testing"

	"git.woa.com/kateway/kateway-server/pkg/image/cmd"
)

func TestCheckImage(t *testing.T) {
	type args struct {
		imagePath string
		version   []string
		regions   []string
	}
	imagePath1 := "ccr.ccs.tencentyun.com/paas/service-controller"
	vesion1 := []string{"v2.3.3"}
	regions1 := []string{"hk"}
	args1 := args{imagePath1, vesion1, regions1}
	args2 := args{imagePath1, []string{"v2.3.9"}, regions1}
	vesion3 := []string{"v2.3.3"}
	args3 := args{imagePath1, vesion3, []string{"bj"}}
	args4 := args{imagePath1, []string{"v2.3.4-dirty-linux-amd64"}, regions1}
	tests := []struct {
		name string
		args args
		want []ErrMsg
	}{
		{"正常输出", args1, nil},
		{"源镜像不存在", args2, []ErrMsg{{cmd.ImageUrl{Domain: "ccr.ccs.tencentyun.com", Namespace: "paas", ImageName: "service-controller", Tag: "v2.3.9"}, "Error: no such manifest: ccr.ccs.tencentyun.com/paas/service-controller:v2.3.9", "get manifest error"}}},
		{"目标镜像获取异常", args3, []ErrMsg{{cmd.ImageUrl{Domain: "bjccr.ccs.tencentyun.com", Namespace: "tkeimages", ImageName: "service-controller", Tag: "v2.3.3"}, "Error: failed to configure transport: error pinging v2 registry: Get \"https://bjccr.ccs.tencentyun.com/v2/\": dial tcp: lookup bjccr.ccs.tencentyun.com: no such host", "get manifest error"}}},
		{"源镜像包含架构不符合预期", args4, []ErrMsg{{cmd.ImageUrl{Domain: "ccr.ccs.tencentyun.com", Namespace: "paas", ImageName: "service-controller", Tag: "v2.3.4-dirty-linux-amd64"}, "Error: arm64 not found", "source manifest error"}}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := Check(tt.args.imagePath, tt.args.version, tt.args.regions)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CheckImgConsistence() = %v, want %v", got, tt.want)
			}
			fmt.Println("error.Error()输出:")
			fmt.Println(got)
		})
	}
}

func Test_compareManifest(t *testing.T) {
	type args struct {
		srcManifest cmd.DockerManifestList
		curManifest cmd.DockerManifestList
	}
	amd64 := cmd.Platform{"amd64", "linux"}
	arm64 := cmd.Platform{"arm64", "linux"}
	srcM1 := cmd.Manifest{Digest: "sha256:5859b81835b1beb777823a1f41676b7ccbb1cf5aeb651f3a45ba1d1f82c6098a", Platform: amd64}
	srcM2 := cmd.Manifest{Digest: "sha256:e8534b90117c876b38309c7a1d7d44a80f46384a4a93ecca07702aa067df0b64", Platform: arm64}
	dstM1 := cmd.Manifest{Digest: "sha256:5859b81835b1beb777823a5f41676b7ccbb1cf5aeb651f3a45ba1d1f82c6098a", Platform: amd64}
	dstM2 := cmd.Manifest{Digest: "sha256:e8534b90117c876b38309c7a7d7d44a80f46384a4a93ecca07702aa067df0b64", Platform: arm64}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{name: "目标镜像架构缺失", args: args{cmd.DockerManifestList{Manifests: []cmd.Manifest{srcM1, srcM2}}, cmd.DockerManifestList{Manifests: []cmd.Manifest{srcM1}}}, wantErr: true},
		{name: "与源镜像不一致", args: args{cmd.DockerManifestList{Manifests: []cmd.Manifest{srcM1, srcM2}}, cmd.DockerManifestList{Manifests: []cmd.Manifest{dstM1, dstM2}}}, wantErr: true},
		{name: "目标镜像架构缺失且与源镜像不一致", args: args{cmd.DockerManifestList{Manifests: []cmd.Manifest{srcM1, srcM2}}, cmd.DockerManifestList{Manifests: []cmd.Manifest{dstM1}}}, wantErr: true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := compareManifest(tt.args.srcManifest, tt.args.curManifest)
			if (err != nil) != tt.wantErr {
				t.Errorf("compareManifest() error = %v, wantErr %v", err, tt.wantErr)
			}
			fmt.Println(err.Error())
		})
	}
}
