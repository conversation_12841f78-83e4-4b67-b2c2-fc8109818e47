package cmd

import (
	"bytes"
	"encoding/json"
	"fmt"
	"os/exec"
	"strings"
)

type ImageUrl struct {
	Domain    string
	Namespace string
	ImageName string
	Tag       string
}

type Manifest struct {
	MediaType string `json:"mediaType"`
	Size      int    `json:"size"`
	Digest    string `json:"digest"`
	Platform  Platform
}

type Platform struct {
	Architecture string `json:"architecture"`
	OS           string `json:"os"`
}

type DockerManifestList struct {
	SchemaVersion int        `json:"schemaVersion"`
	MediaType     string     `json:"mediaType"`
	Manifests     []Manifest `json:"manifests"`
}

func ParseStr(jsonStr string) (DockerManifestList, error) {
	var manifestList DockerManifestList
	err := json.Unmarshal([]byte(jsonStr), &manifestList)
	if err != nil {
		return manifestList, fmt.Errorf("json解析错误")
	}
	return manifestList, nil
}

func runCmd(cmdStr string, args ...string) (string, error) {
	cmd := exec.Command(cmdStr, args...)
	var out bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &out
	err := cmd.Run()
	return out.String(), err
}

func (imgUrl *ImageUrl) GetStr() string {
	return imgUrl.Domain + "/" + imgUrl.Namespace + "/" + imgUrl.ImageName + ":" + imgUrl.Tag
}

func (imgUrl *ImageUrl) DockerMainfest(cmd string, args ...string) (string, error) {
	cmdStr := "manifest" + " " + cmd + " " + imgUrl.GetStr()
	newArgs := append(strings.Fields(cmdStr), args...)
	res, err := runCmd("docker", newArgs...)
	if err != nil {
		return res, fmt.Errorf("%s", res)
	}
	return res, err
}

func (imgUrl *ImageUrl) DockerInspectMainfest(args ...string) (string, error) {
	return imgUrl.DockerMainfest("inspect", args...)
}

func (imgUrl *ImageUrl) GetManifest() (DockerManifestList, error) {
	manifestString, err := imgUrl.DockerInspectMainfest()
	if err != nil {
		return DockerManifestList{}, err
	}
	manifestList, err := ParseStr(manifestString)
	if err != nil {
		return DockerManifestList{}, err
	}
	return manifestList, nil
}
