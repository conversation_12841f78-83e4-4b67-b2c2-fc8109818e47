package cmd

import (
	"fmt"
	"reflect"
	"testing"
)

func TestImageUrl_GetManifest(t *testing.T) {
	type fields struct {
		Domain    string
		Namespace string
		ImageName string
		Tag       string
	}
	tests := []struct {
		name    string
		fields  fields
		want    DockerManifestList
		wantErr bool
	}{
		{"目标镜像不存在", fields{"ccr.ccs.tencentyun.com", "tkeimages", "service-controller", "v2.3.9"}, DockerManifestList{}, true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			imgUrl := &ImageUrl{
				Domain:    tt.fields.Domain,
				Namespace: tt.fields.Namespace,
				ImageName: tt.fields.ImageName,
				Tag:       tt.fields.Tag,
			}
			got, err := imgUrl.GetManifest()
			if (err != nil) != tt.wantErr {
				t.Errorf("GetManifest() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON><PERSON>("GetManifest() got = %v, want %v", got, tt.want)
			}
			if err != nil {
				fmt.Println("error.Error()输出:")
				fmt.Println(err)
			}
		})
	}
}
