package cron

import (
	"time"

	"github.com/go-logr/logr"

	"git.woa.com/kateway/pkg/telemetry/log"
)

type Scheduler struct {
	log logr.Logger
}

func New() *Scheduler {
	return &Scheduler{
		log: log.WithName("cron"),
	}
}

func (s *Scheduler) EveryDay(job func()) {
	for {
		now := time.Now()
		next := now.Add(time.Hour * 24)
		next = time.Date(next.Year(), next.Month(), next.Day(), 10, 0, 0, 0, next.Location())

		duration := next.Sub(now)
		s.log.Info("Next job", "time", next)

		timer := time.NewTimer(duration)
		<-timer.C

		s.log.Info("Running job")
		job()
		s.log.Info("Done job")
	}
}
