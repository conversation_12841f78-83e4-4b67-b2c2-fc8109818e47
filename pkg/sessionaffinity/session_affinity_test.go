package sessionaffinity

import "testing"

func TestShouldProcessSessionAffinity(t *testing.T) {
	currentVersion := "v2.4.3-29-g08e0b0d4"
	expectVersion := "v2.5.1-bowen-test"
	shouldProcess, err := ShouldProcess(currentVersion, expectVersion)
	if err != nil {
		t.<PERSON>al(err)
	}
	if !shouldProcess {
		t.<PERSON>al("should process")
	}

	expectVersion = "v2.5.0"
	shouldProcess, err = ShouldProcess(currentVersion, expectVersion)
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	if shouldProcess {
		t.<PERSON>al("should not process")
	}
}
