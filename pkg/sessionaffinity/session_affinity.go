package sessionaffinity

import (
	"context"
	"sync"

	"github.com/samber/lo"
	"github.com/tidwall/gjson"
	"golang.org/x/sync/errgroup"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8stypes "k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/util/retry"

	"git.woa.com/kateway/pkg/tencent/zhiyan/fmt"

	"git.woa.com/kateway/kateway-server/pkg/dryrun"
	"git.woa.com/kateway/kateway-server/pkg/version"
)

var (
	versionSessionAffinityFallback = version.MustParse("v2.5.1")
)

func ShouldProcess(currentVersion, expectVersion string) (bool, error) {
	current, err := version.Parse(currentVersion)
	if err != nil {
		return false, err
	}
	expect, err := version.Parse(expectVersion)
	if err != nil {
		return false, err
	}
	return expect.Compare(versionSessionAffinityFallback) >= 0 && current.Compare(versionSessionAffinityFallback) < 0, nil
}

type Processor struct {
	cli    kubernetes.Interface
	writer func(string, ...any)
}

func NewProcessor(cli kubernetes.Interface, writer func(string, ...any)) *Processor {
	return &Processor{cli: cli, writer: writer}
}

func (p *Processor) ProcessDryrunRecords(ctx context.Context, records dryrun.Records) (bool, error) {
	var (
		wg                = errgroup.Group{}
		processedServices []k8stypes.NamespacedName
		lock              sync.Mutex
	)
	wg.SetLimit(100)
	for _, r := range records {
		if nn, shouldProcess := p.parse(r); shouldProcess {
			wg.Go(func() error {
				if err := p.addAnnotation(ctx, nn); err != nil {
					if apierrors.IsNotFound(err) {
						return nil
					}
					return err
				}
				lock.Lock()
				defer lock.Unlock()
				processedServices = append(processedServices, nn)
				return nil
			})
		}
	}

	defer func() {
		if len(processedServices) > 0 {
			p.writer("Added session affinity annotation for services: %v\n",
				lo.Map(processedServices, func(nn k8stypes.NamespacedName, _ int) string { return nn.String() }))
		}
	}()

	if err := wg.Wait(); err != nil {
		return false, err
	}
	return len(processedServices) > 0, nil
}

// 会话保持配置修改失效方案文档：https://iwiki.woa.com/p/4012975360
func (p *Processor) parse(record dryrun.Record) (nn k8stypes.NamespacedName, shouldProcess bool) {
	if record.Error != "" {
		return
	}
	if record.Resource.Type != dryrun.ResourceTypeService {
		return
	}
	if record.Resource.Namespace == "" || record.Resource.Name == "" {
		return
	}
	// 当预检发现对某个资源调用ModifyListener接口，且接口参数中包含SessionExpireTime字段时，认为是由于会话保持修复导致的修改，需要添加annotation
	nn.Namespace = record.Resource.Namespace
	nn.Name = record.Resource.Name
	if record.Action != "ModifyListener" {
		return
	}
	res := gjson.Get(record.Request, "SessionExpireTime")
	if res.Exists() {
		shouldProcess = true
	}
	return
}

func (p *Processor) addAnnotation(ctx context.Context, nn k8stypes.NamespacedName) error {
	if err := retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		svc, err := p.cli.CoreV1().Services(nn.Namespace).Get(ctx, nn.Name, metav1.GetOptions{})
		if err != nil {
			return err
		}
		if svc.Annotations == nil {
			svc.Annotations = make(map[string]string)
		}
		svc.Annotations["service.cloud.tencent.com/session-affinity-fallback"] = "false"
		_, err = p.cli.CoreV1().Services(nn.Namespace).Update(ctx, svc, metav1.UpdateOptions{})
		return err
	}); err != nil {
		return fmt.Errorf("failed to add session affinity annotation for service %q: %w", nn.String(), err)
	}
	return nil
}
