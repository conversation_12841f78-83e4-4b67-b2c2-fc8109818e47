package cluster

import (
	"fmt"
	"sync"

	masterclient "cloud.tencent.com/tke/master-operator/pkg/client/clientset/versioned"
	"k8s.io/apiextensions-apiserver/pkg/client/clientset/clientset"
	"k8s.io/client-go/kubernetes"
	restclient "k8s.io/client-go/rest"

	mciClient "git.woa.com/kateway/multi-cluster-ingress-api/client/clientset/versioned"
	mcsClient "git.woa.com/kateway/multi-cluster-service-api/client/clientset/versioned"
	tscClient "git.woa.com/kateway/tke-service-config/pkg/client/clientset/versioned"
	loadbalancerresource "git.woa.com/misakazhou/loadbalancer-resource-api/pkg/client/clientset/versioned"

	"git.woa.com/kateway/kateway-server/pkg/context"
	"git.woa.com/kateway/kateway-server/pkg/errno"
	"git.woa.com/kateway/kateway-server/pkg/util/region"
)

const (
	ClusterTypeTKE = "tke"
	ClusterTypeEKS = "eks"
)

var ClusterTypeList = []string{ClusterTypeEKS, ClusterTypeTKE}

type BasicInfo struct {
	ApiserverHost string
	ApiserverPort string
	JnsGwEndpoint string
	Token         string
	K8sVersion    string
	Name          string
	KubeConfig    string
	VpcId         string
}

type ClientsSet struct {
	kubernetes.Interface
	K8sCli          kubernetes.Interface
	CRDCli          clientset.Interface
	MasterCli       masterclient.Interface
	LoadbalancerCli loadbalancerresource.Interface
	MCSCli          mcsClient.Interface
	MCICli          mciClient.Interface
	TSCCli          tscClient.Interface
}

type K8sCluster interface {
	ClientsSet(ctx *context.Context, appId uint64, clusterId string) (ClientsSet, error)
	RestConfig(ctx *context.Context, appId uint64, clusterId string) (*restclient.Config, error)
	BasicInfo(ctx *context.Context, appID uint64, clusterId string) (BasicInfo, error)
}

type K8sClusterFactory interface {
	ClientsSet(ctx *context.Context, region string, typ string, appId uint64, clusterId string) (ClientsSet, error)
	RestConfig(ctx *context.Context, region, typ string, appId uint64, clusterId string) (*restclient.Config, error)
	BasicInfo(ctx *context.Context, region string, typ string, appId uint64, clusterId string) (BasicInfo, error)
}

type ClusterFactory func(r *region.Region) (K8sCluster, error)

type Clusters struct {
	sync.Mutex
	cls map[string]map[string]K8sCluster
	fac map[string]ClusterFactory
}

func NewClusters(fac map[string]ClusterFactory) *Clusters {
	return &Clusters{
		cls: map[string]map[string]K8sCluster{},
		fac: fac,
	}
}

func (c *Clusters) Get(rg, typ string) (K8sCluster, error) {
	c.Lock()
	defer c.Unlock()
	if c.cls[rg] == nil {
		c.cls[rg] = map[string]K8sCluster{}
	}

	if c.cls[rg][typ] == nil {
		r := region.Get(rg)
		if r == nil {
			return nil, errno.Cause(errno.PARAM_ERROR, fmt.Errorf("unsupported region %s", rg))
		}

		fac := c.fac[typ]
		if fac == nil {
			return nil, errno.Cause(errno.PARAM_ERROR, fmt.Errorf("unsupported typ %s", typ))
		}

		cli, err := c.fac[typ](r)
		if err != nil {
			return nil, err
		}
		c.cls[rg][typ] = cli
	}

	return c.cls[rg][typ], nil
}

func (c *Clusters) ClientsSet(ctx *context.Context, region string, typ string, appId uint64, clusterId string) (ClientsSet, error) {
	cls, err := c.Get(region, typ)
	if err != nil {
		return ClientsSet{}, err
	}

	return cls.ClientsSet(ctx, appId, clusterId)
}

func (c *Clusters) RestConfig(ctx *context.Context, region, typ string, appId uint64, clusterId string) (*restclient.Config, error) {
	cli, err := c.Get(region, typ)
	if err != nil {
		return nil, err
	}

	return cli.RestConfig(ctx, appId, clusterId)
}

func (c *Clusters) BasicInfo(ctx *context.Context, region string, typ string, appId uint64, clusterId string) (BasicInfo, error) {
	cli, err := c.Get(region, typ)
	if err != nil {
		return BasicInfo{}, err
	}

	return cli.BasicInfo(ctx, appId, clusterId)
}
