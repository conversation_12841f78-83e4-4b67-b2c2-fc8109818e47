package cluster

import (
	"fmt"
	"time"

	"k8s.io/client-go/rest"

	"git.woa.com/kateway/kateway-server/pkg/component/eks"
	"git.woa.com/kateway/kateway-server/pkg/context"
)

type EKSService struct {
	cliManager     *eks.ClientManager
	clusterManager *eks.ClusterManager
	internalServer *eks.InternalServer

	timeout time.Duration

	ClientGen
}

var _ K8sCluster = &EKSService{}

func NewEKSService(eksClient *eks.ClientManager, eksCluster *eks.ClusterManager, eksInternal *eks.InternalServer, timeout ...time.Duration) *EKSService {
	e := &EKSService{
		cliManager:     eksClient,
		clusterManager: eksCluster,
		internalServer: eksInternal,
	}

	e.ClientGen = ClientGen{
		GetRestConfig: func(ctx *context.Context, appId uint64, clusterId string) (*rest.Config, error) {
			var t time.Duration
			if timeout == nil {
				t = defaultTimeout
			} else {
				t = timeout[0]
			}
			return eksClient.GetRestConfig(ctx, clusterId, t)
		},
	}

	return e
}

func (e *EKSService) BasicInfo(ctx *context.Context, appID uint64, clusterID string) (BasicInfo, error) {
	cluster, err := e.cliManager.ClusterInfo(ctx, clusterID)
	if err != nil {
		return BasicInfo{}, err
	}

	sec, err := e.cliManager.Credentials(ctx, cluster.Spec.ClusterCredentialRef.Name)
	if err != nil {
		return BasicInfo{}, err
	}

	info := BasicInfo{
		Token:      *sec.Token,
		K8sVersion: cluster.Spec.Version,
		Name:       cluster.Spec.DisplayName,
		VpcId:      cluster.Annotations["eks.tke.cloud.tencent.com/vpcid"],
	}

	for _, a := range cluster.Status.Addresses {
		if a.Type == "Support" {
			info.ApiserverHost = a.Host
			info.ApiserverPort = fmt.Sprint(a.Port)
		}

		if a.Type == "Internal" {
			info.JnsGwEndpoint = fmt.Sprintf("%s:%d", a.Host, a.Port)
			break
		}
	}

	return info, nil
}

func (e *EKSService) GetAccountType(ctx *context.Context) (string, error) {
	return e.internalServer.GetAccountType(ctx)
}
