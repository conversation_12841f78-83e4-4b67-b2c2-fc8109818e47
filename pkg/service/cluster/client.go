package cluster

import (
	"sync"
	"time"

	"cloud.tencent.com/tke/master-operator/pkg/apis/master.cloud.tencent.com/v1alpha1"
	masterclient "cloud.tencent.com/tke/master-operator/pkg/client/clientset/versioned"
	"github.com/pkg/errors"
	"k8s.io/apiextensions-apiserver/pkg/client/clientset/clientset"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
	restclient "k8s.io/client-go/rest"

	mciClient "git.woa.com/kateway/multi-cluster-ingress-api/client/clientset/versioned"
	mcsClient "git.woa.com/kateway/multi-cluster-service-api/client/clientset/versioned"
	tscClient "git.woa.com/kateway/tke-service-config/pkg/client/clientset/versioned"
	aotopilotv1beta1 "git.woa.com/kmetis/kmetis/pkg/apis/autopilot.k8s.io/v1beta1"
	portraitv1beta1 "git.woa.com/kmetis/kmetis/pkg/apis/portrait.k8s.io/v1beta1"
	loadbalancerresourcev1 "git.woa.com/misakazhou/loadbalancer-resource-api/pkg/apis/loadbalancerresource/v1alpha1"
	loadbalancerresource "git.woa.com/misakazhou/loadbalancer-resource-api/pkg/client/clientset/versioned"

	"git.woa.com/kateway/kateway-server/pkg/context"
	"git.woa.com/kateway/kateway-server/pkg/errno"
	"git.woa.com/kateway/kateway-server/pkg/ianvs"
)

var defaultTimeout = 150 * time.Minute

type ClientGen struct {
	lk            sync.Mutex
	GetRestConfig func(ctx *context.Context, appId uint64, clusterId string) (*restclient.Config, error)
}

func (e *ClientGen) ClientsSet(ctx *context.Context, appID uint64, clusterID string) (ClientsSet, error) {
	set := ClientsSet{}

	cfg, err := e.restConfig(ctx, appID, clusterID)
	if err != nil {
		return set, err
	}

	set.K8sCli, err = kubernetes.NewForConfig(cfg)
	if err != nil {
		return set, errno.Cause(errno.KUBE_CLIENT_CREATE_ERROR, err)
	}

	set.Interface = set.K8sCli
	set.CRDCli, err = clientset.NewForConfig(cfg)
	if err != nil {
		return set, errno.Cause(errno.KUBE_CLIENT_CREATE_ERROR, err)
	}

	set.MasterCli, err = masterclient.NewForConfig(cfg)
	if err != nil {
		return set, errno.Cause(errno.KUBE_CLIENT_CREATE_ERROR, err)
	}

	set.LoadbalancerCli, err = loadbalancerresource.NewForConfig(cfg)
	if err != nil {
		return set, errno.Cause(errno.KUBE_CLIENT_CREATE_ERROR, err)
	}

	set.MCICli, err = mciClient.NewForConfig(cfg)
	if err != nil {
		return set, errno.Cause(errno.KUBE_CLIENT_CREATE_ERROR, err)
	}

	set.MCSCli, err = mcsClient.NewForConfig(cfg)
	if err != nil {
		return set, errno.Cause(errno.KUBE_CLIENT_CREATE_ERROR, err)
	}

	set.TSCCli, err = tscClient.NewForConfig(cfg)
	if err != nil {
		return set, errno.Cause(errno.KUBE_CLIENT_CREATE_ERROR, err)
	}

	if err != nil {
		return set, errno.Cause(errno.KUBE_CLIENT_CREATE_ERROR, errors.Wrapf(err, "new custom cli client failed"))
	}

	return set, nil
}

func (e *ClientGen) RestConfig(ctx *context.Context, appId uint64, clusterId string) (*restclient.Config, error) {
	return e.restConfig(ctx, appId, clusterId)
}

func (e *ClientGen) restConfig(ctx *context.Context, appId uint64, clusterId string) (cfg *restclient.Config, err error) {
	if ianvs.Enable() {
		cfg, err = ianvs.GetRestConfig(clusterId, "", "")
		if err != nil {
			return nil, errors.Wrapf(err, "get restConfig from ianvs")
		}
		// fmt.Printf("restConfig from ianvs clusterId: %s server: %s token: %s\n", clusterId, cfg.Host, cfg.BearerToken)
	} else {
		cfg, err = e.GetRestConfig(ctx, appId, clusterId)
		if err != nil {
			return nil, errors.Wrapf(err, "get restConfig")
		}
	}

	return
}

func init() {
	utilruntime.Must(v1alpha1.AddToScheme(scheme.Scheme))
	utilruntime.Must(aotopilotv1beta1.AddToScheme(scheme.Scheme))
	utilruntime.Must(portraitv1beta1.AddToScheme(scheme.Scheme))
	utilruntime.Must(loadbalancerresourcev1.AddToScheme(scheme.Scheme))
}
