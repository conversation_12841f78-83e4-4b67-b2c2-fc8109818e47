package cluster

import (
	"fmt"
	"time"

	"github.com/pkg/errors"
	restclient "k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"

	"git.woa.com/kateway/kateway-server/pkg/component/tke"
	"git.woa.com/kateway/kateway-server/pkg/context"
	"git.woa.com/kateway/kateway-server/pkg/errno"
)

type TKEService struct {
	ClientGen
	tkeCli *tke.Client

	timeout *time.Duration
}

var _ K8sCluster = &TKEService{}

func NewTKEService(tkeCli *tke.Client, timeout ...time.Duration) *TKEService {
	t := &TKEService{
		tkeCli: tkeCli,
	}
	t.ClientGen.GetRestConfig = t.GetRestConfig

	if timeout == nil {
		t.timeout = &defaultTimeout
	} else {
		t.timeout = &timeout[0]
	}

	return t
}

func (t *TKEService) GetRestConfig(ctx *context.Context, appId uint64, clusterId string) (*restclient.Config, error) {
	sec, err := t.BasicInfo(ctx, appId, clusterId)
	if err != nil {
		return nil, errors.Wrapf(err, "get cluster BasicInfo failed")
	}

	overrides := &clientcmd.ConfigOverrides{}
	overrides.ClusterInfo.InsecureSkipTLSVerify = true
	overrides.AuthInfo.Token = sec.Token
	overrides.ClusterDefaults.Server = fmt.Sprintf("https://%s", sec.JnsGwEndpoint)
	// fmt.Printf("clusterId: %s server: %s token: %s\n", clusterId, overrides.ClusterDefaults.Server, overrides.AuthInfo.Token)
	clientConfig := clientcmd.NewNonInteractiveDeferredLoadingClientConfig(&clientcmd.ClientConfigLoadingRules{}, overrides)

	cfg, err := clientConfig.ClientConfig()
	if err != nil {
		return nil, errno.Cause(errno.KUBE_CLIENT_CONNECTION_ERROR, errors.Wrap(err, "generate client config failed"))
	}
	cfg.QPS = 1e6
	cfg.Burst = 1e6
	cfg.Timeout = *t.timeout

	// klog.Infof("Rest config with timeout %v minutes generated", cfg.Timeout.Minutes())

	return cfg, nil
}

func (t *TKEService) BasicInfo(ctx *context.Context, appID uint64, clusterID string) (BasicInfo, error) {
	cls, err := t.tkeCli.DescribeCluster(ctx, clusterID)
	if err != nil {
		return BasicInfo{}, errors.Wrapf(err, "DescribeCluster")
	}

	sec, err := t.tkeCli.DescribeSecurity(ctx, clusterID)
	if err != nil {
		return BasicInfo{}, errors.Wrapf(err, "DescribeSecurity")
	}

	return BasicInfo{
		ApiserverHost: "kubernetes.default.svc",
		ApiserverPort: "443",
		JnsGwEndpoint: *sec.JnsGwEndpoint,
		Token:         sec.Password,
		K8sVersion:    cls.ClusterVersion,
		Name:          cls.ClusterName,
		VpcId:         cls.ClusterNetworkSettings.VpcId,
	}, nil
}
