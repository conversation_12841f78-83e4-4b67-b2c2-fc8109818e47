package cluster

import (
	"sync"

	restclient "k8s.io/client-go/rest"

	"git.woa.com/kateway/kateway-server/pkg/context"
)

func NewCachedClusters(fac map[string]ClusterFactory) *CachedClusters {
	return &CachedClusters{
		delegate:        NewClusters(fac),
		clientSetCache:  map[string]ClientsSet{},
		basicInfoCache:  map[string]BasicInfo{},
		restConfigCache: map[string]*restclient.Config{},
	}
}

type CachedClusters struct {
	sync.Mutex
	delegate        *Clusters
	clientSetCache  map[string]ClientsSet
	basicInfoCache  map[string]BasicInfo
	restConfigCache map[string]*restclient.Config
}

func (c *CachedClusters) ClientsSet(ctx *context.Context, region string, typ string, appId uint64, clusterId string) (ClientsSet, error) {
	c.Lock()
	defer c.Unlock()

	old, exist := c.clientSetCache[clusterId]
	if exist {
		return old, nil
	}

	cli, err := c.delegate.ClientsSet(ctx, region, typ, appId, clusterId)
	if err != nil {
		return ClientsSet{}, err
	}

	c.clientSetCache[clusterId] = cli
	return cli, nil
}

func (c *CachedClusters) RestConfig(ctx *context.Context, region, typ string, appId uint64, clusterId string) (*restclient.Config, error) {
	c.Lock()
	defer c.Unlock()

	old, exist := c.restConfigCache[clusterId]
	if exist {
		return old, nil
	}

	cfg, err := c.delegate.RestConfig(ctx, region, typ, appId, clusterId)
	if err != nil {
		return nil, err
	}

	c.restConfigCache[clusterId] = cfg
	return cfg, nil
}

func (c *CachedClusters) BasicInfo(ctx *context.Context, region string, typ string, appId uint64, clusterId string) (BasicInfo, error) {
	c.Lock()
	defer c.Unlock()

	old, exist := c.basicInfoCache[clusterId]
	if exist {
		return old, nil
	}

	info, err := c.delegate.BasicInfo(ctx, region, typ, appId, clusterId)
	if err != nil {
		return BasicInfo{}, err
	}

	c.basicInfoCache[clusterId] = info
	return info, nil
}

func (c *CachedClusters) InvalidCache(clusterId string) {
	c.Lock()
	defer c.Unlock()
	delete(c.clientSetCache, clusterId)
	delete(c.basicInfoCache, clusterId)
	delete(c.restConfigCache, clusterId)
}
