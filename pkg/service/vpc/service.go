package vpc

import (
	"fmt"

	"github.com/pkg/errors"

	"git.woa.com/kateway/kateway-server/pkg/component/vpc"
	"git.woa.com/kateway/kateway-server/pkg/context"
	"git.woa.com/kateway/kateway-server/pkg/errno"
)

type VpcService struct {
	*vpc.Client
}

func NewVpcService(client *vpc.Client) *VpcService {
	return &VpcService{
		Client: client,
	}
}

func (v *VpcService) DescribeVpc(ctx *context.Context, uniqID string) (*vpc.GetVpcRsp, error) {
	res, err := v.GetVpc(ctx, ctx.AppID(), uniqID)
	if err != nil {
		return nil, err
	}
	if len(res) != 1 {
		return nil, errno.Cause(errno.UNEXCEPTED_INTERNAL_ERROR, fmt.Errorf("GetVpc failed"))
	}
	return &res[0], err
}

const (
	DefaultProtoTCP = "tcp"
	CVMVgwType      = 0
	LBVgwType       = 1025
)

func (v *VpcService) CreateCVMJnsGWEndpoint(ctx *context.Context, appId uint64, vpcId int64, pip string, pport int) (string, int, error) {
	// 获取用于snat的subnetId
	subnetId, err := v.GetReversedSubnet(ctx, appId, vpcId)
	if err != nil {
		return "", -1, errors.Wrap(err, "get reversed subnet failed")
	}

	return v.CreateJnsGWService(ctx, vpc.AddNatgwServiceReq{
		Owner:    fmt.Sprint(appId),
		Pip:      pip,
		Pport:    pport,
		Proto:    DefaultProtoTCP,
		SubnetID: &subnetId,
		VgwType:  CVMVgwType,
		VpcId:    vpcId,
	})
}

func (v *VpcService) CreateLBJnsGWEndpoint(ctx *context.Context, appId uint64, vpcId int64, pip string, pport int) (string, int, error) {
	// 获取用于snat的subnetId
	subnetId, err := v.GetReversedSubnet(ctx, appId, vpcId)
	if err != nil {
		return "", -1, errors.Wrap(err, "get reversed subnet failed")
	}

	return v.CreateJnsGWService(ctx, vpc.AddNatgwServiceReq{
		Owner:    fmt.Sprint(appId),
		Pip:      pip,
		Pport:    pport,
		Proto:    DefaultProtoTCP,
		SubnetID: &subnetId,
		VgwType:  LBVgwType,
		VpcId:    vpcId,
	})
}

func (v *VpcService) GetReversedSubnet(ctx *context.Context, appId uint64, vpcId int64) (int, error) {
	rsp, err := v.GetSubNet(ctx, vpc.GetSubNetReq{
		Owner: fmt.Sprint(appId),
		VpcId: vpcId,
		Type:  2,
	})
	if err != nil {
		return -1, errors.Wrap(err, "get reversed subnet failed")
	}
	if len(rsp) == 0 {
		return -1, errors.Wrapf(err, "can't find reversed subnet for vpc %d", vpcId)
	}
	return rsp[0].SubnetId, nil
}
