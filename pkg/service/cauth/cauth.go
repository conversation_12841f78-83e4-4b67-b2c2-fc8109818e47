package cauth

import (
	"github.com/pkg/errors"

	"git.woa.com/kateway/kateway-server/pkg/component/cauth"
	"git.woa.com/kateway/kateway-server/pkg/component/http"
	"git.woa.com/kateway/kateway-server/pkg/context"
)

type CAuthService struct {
	*cauth.Client
}

func NewCAuthService(url string, exec http.Executor) *CAuthService {
	return &CAuthService{
		Client: cauth.NewClient(url, exec),
	}
}

func (v *CAuthService) GetUINByAppID(ctx *context.Context, appId uint64) (string, error) {
	res, err := v.Client.GetUINByAppId(ctx, appId)
	if err != nil {
		return "", errors.Wrapf(err, "get uin by appId %v failed", appId)
	}
	return res, nil
}
