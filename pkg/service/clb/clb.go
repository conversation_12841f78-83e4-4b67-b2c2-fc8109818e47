package clb

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"github.com/tidwall/gjson"

	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
	"git.woa.com/kateway/kateway-server/pkg/tmp/tencentcloud"
	"git.woa.com/kateway/kateway-server/pkg/tmp/types/intstr"
	"git.woa.com/kateway/kateway-server/pkg/tmp/web/cloudctx"
	"git.woa.com/kateway/kateway-server/pkg/util/region"
)

type CLB struct {
	*clb.LoadBalancer
	URL string
}

func ctx(query string) (context.Context, error) {
	url := "http://defensor.vpc.tencentyun.com:8520/"

	filter := "vip"
	if strings.Contains(query, "tencentclb.work") {
		filter = "domain"
	} else if strings.HasPrefix(query, "lb-") {
		filter = "ulb_id"
	}
	requestBody := fmt.Sprintf(`{
		"Action": "DescribeNdcpData",
		"DataBase": "clb_data",
		"InfraTable": "t_big_data",
		"Fileds": ["region", "uin", "ulb_id", "lb_type", "app_id", "uin", "vip"],
		"Filters": [
			{
				"Name": "%s",
				"Values": ["%s"]
			},
		  {
			  "Name": "record_date",
			  "Values": ["%s"]
		  }
		]
	}`, filter, query, time.Now().AddDate(0, 0, -1).Format("2006-01-02"))

	req, err := http.NewRequest("POST", url, bytes.NewBufferString(requestBody))
	if err != nil {
		panic(err)
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		panic(err)
	}
	defer resp.Body.Close()

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		panic(err)
	}

	fmt.Println("响应:", string(responseBody))
	data := gjson.Parse(string(responseBody)).Get("Response.Data.0")

	if !data.Exists() {
		return nil, fmt.Errorf("未查询到数据:\n请求: %s \n响应: %s", requestBody, responseBody)
	}

	uin := data.Get("uin").String()
	appid := data.Get("app_id").String()
	r := region.MustGet(data.Get("region").String())
	id := data.Get("ulb_id").String()

	ctx := cloudctx.WithUin(context.Background(), uin)
	ctx = cloudctx.WithAppid(ctx, intstr.FromString(appid))
	ctx = cloudctx.WithRegion(ctx, r)
	ctx = cloudctx.WithInstanceID(ctx, id)

	return ctx, nil
}

func GetListeners(query string) ([]*clb.Listener, error) {
	ctx, err := ctx(query)
	if err != nil {
		return nil, err
	}
	return services.Get().CLB().ListListeners(ctx, cloudctx.InstanceID(ctx))
}

func GetBackends(query string) ([]*clb.ListenerBackend, error) {
	ctx, err := ctx(query)
	if err != nil {
		return nil, err
	}
	return services.Get().CLB().ListListenerBackends(ctx, cloudctx.InstanceID(ctx))
}

func GetHealth(query string) ([]*clb.LoadBalancerHealth, error) {
	ctx, err := ctx(query)
	if err != nil {
		return nil, err
	}
	request := clb.NewDescribeTargetHealthRequest()
	request.LoadBalancerIds = []*string{lo.ToPtr(cloudctx.InstanceID(ctx))}
	response, err := tencentcloud.Clb(ctx).DescribeTargetHealth(request)
	if err != nil {
		return nil, err
	}

	return response.Response.LoadBalancers, nil
}

func Get(query string) (*CLB, error) {
	ctx, err := ctx(query)
	if err != nil {
		return nil, err
	}
	lb, err := services.Get().CLB().GetLoadBalancer(ctx, cloudctx.InstanceID(ctx))
	if err != nil {
		return nil, err
	}

	result := &CLB{}
	result.LoadBalancer = lb
	result.URL = fmt.Sprintf("https://dfs.woa.com/clb/resource-overview-detail?id=%s&rid=%v&appId=%v&tab=info",
		query, cloudctx.Region(ctx).ID, cloudctx.Appid(ctx).String())

	return result, nil
}
