package credential

import (
	"fmt"
	"sync"
	"time"

	"git.woa.com/kateway/kateway-server/pkg/component/sts"
)

type CredentialService struct {
	secretId   string
	secretKey  string
	rolePrefix string
	roleName   string
	cache      map[string]sts.Tokener
	sync.Mutex
}

func NewCredentialService(secretId, secretKey, rolePrefix, roleName string) *CredentialService {
	return &CredentialService{
		secretId:   secretId,
		secretKey:  secretKey,
		rolePrefix: rolePrefix,
		roleName:   roleName,
		cache:      make(map[string]sts.Tokener, 0),
	}
}

func (c *CredentialService) GetCredentials(uin string) (*sts.Credentials, error) {
	c.Lock()
	if _, ok := c.cache[uin]; !ok {
		roleArn := fmt.Sprintf("qcs::cam::uin/%s:%s/%s", uin, c.rolePrefix, c.roleName)
		c.cache[uin] = sts.NewStsTokener(c.secretId, c.secret<PERSON>ey, roleArn, "", "tke-inspection-session", time.Hour, 5*time.Minute)
	}
	tokener := c.cache[uin]
	c.Unlock()
	return tokener.GetCredentials()
}
