package ianvs

import (
	"fmt"

	"github.com/samber/lo"
	"github.com/spf13/cobra"
	"k8s.io/client-go/rest"

	ianvs "git.woa.com/ianvs/ianvs-sdk/pkg/client"
)

type Config struct {
	User  string
	Token string
}

type Client struct {
	*ianvs.TmpTokenCient
	config *Config
}

var client *Client

func Init(config *Config) error {
	ianvsClient := ianvs.NewTmpTokenClient(config.User, config.Token)

	client = &Client{
		TmpTokenCient: ianvsClient,
		config:        config,
	}

	return nil
}

func Enable() bool {
	return client != nil
}

func GetRestConfig(clusterID string, user string, token string) (cfg *rest.Config, err error) {
	if user == "" && token == "" { // 兼容旧逻辑
		user = client.config.User
		token = client.config.Token
	}
	defer func() {
		if e := recover(); e != nil { // 兼容ianvs panic
			err = fmt.Errorf("ianvs获取restconfig失败: %v", e)
		}
	}()

	client := ianvs.NewTmpTokenClient(user, token)
	return client.GetRestConfig(clusterID)
}

func CheckToken(cmd *cobra.Command) error {
	if lo.Must(cmd.Flags().GetString("token")) == "" {
		return fmt.Errorf("--token 必填")
	}
	if lo.Must(cmd.Flags().GetString("user")) == "" {
		return fmt.Errorf("--user 必填")
	}

	return nil
}
