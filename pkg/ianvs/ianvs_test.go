package ianvs

import (
	"context"
	"fmt"
	"testing"

	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	ianvs "git.woa.com/ianvs/ianvs-sdk/pkg/client"
)

func TestGetRestConfig(t *testing.T) {
	restConfig, restConfigErr := GetRestConfig("cls-fve5sbyo", "wallaceqian", "26d04b5b262df139d93aa8ce9349be09")

	if restConfigErr != nil {
		panic(restConfigErr)
	}

	clientSet, clientSetErr := kubernetes.NewForConfig(restConfig)

	if clientSetErr != nil {
		panic(clientSetErr)
	}

	nodeList, nodeErr := clientSet.CoreV1().Nodes().List(context.Background(), v1.ListOptions{})

	if nodeErr != nil {

		panic(nodeErr)
	}

	fmt.Println(nodeList)
}

func Test_ianvs(t *testing.T) {

	// 需要指定变更实施人和申请到的临时token
	client := ianvs.NewTmpTokenClient("wallaceqian", "26d04b5b262df139d93aa8ce9349be09")

	// 获取指定集群的restConfig，临时token必须包含该集群内
	restConfig, restConfigErr := client.GetRestConfig("cls-lzu79k63")

	if restConfigErr != nil {
		panic(restConfigErr)
	}

	clientSet, clientSetErr := kubernetes.NewForConfig(restConfig)

	if clientSetErr != nil {
		panic(clientSetErr)
	}

	nodeList, nodeErr := clientSet.CoreV1().Nodes().List(context.Background(), v1.ListOptions{})

	if nodeErr != nil {

		panic(nodeErr)
	}

	fmt.Println(nodeList)

}
