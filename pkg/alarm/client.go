// Package alarm 对接星云告警平台
// 统一告警平台API：https://tapd.woa.com/wiki/markdown_wikis/show/#1210115251000477273
package alarm

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net"
	"net/http"
	"os"
	"time"

	"github.com/fatih/structs"
	"github.com/go-logr/logr"
	"github.com/spf13/cast"

	"git.woa.com/kateway/pkg/telemetry/log"
	monitor "git.woa.com/zhiyan-monitor/sdk/go-sdk/v3/api"
)

const (
	defaultURL = "http://nebula.alarm.production.polaris/api/alarm/receive_outer_alarm"
)

// Config for alarm.
type Config struct {
	URL          string        `yaml:"url"`
	Disable      bool          `yaml:"disable"`
	SecurityCode string        `yaml:"securityCode"`
	Zhiyan       *ZhiyanConfig `yaml:"zhiyan"`
}

type ZhiyanConfig struct {
	StringMark string `yaml:"stringMark"`
}

// Client is for alarm.
type Client struct {
	*Config
	client *http.Client
	log    logr.Logger
	agent  monitor.AgentCollector
}

var DefaultClient *Client

func Init(config Config) {
	DefaultClient = NewClient(config)
}

// NewClient creates a new alarm client.
func NewClient(config Config) *Client {
	if config.URL == "" {
		config.URL = defaultURL
	}
	if _, ok := os.LookupEnv("ALARM_DISABLE"); ok {
		config.Disable = true
	}

	agent, err := monitor.NewAgentCollectorWithConf(monitor.Config{
		Agent: monitor.Agent{
			AgentAccessAddrs: []string{"zhiyan.monitor.access.inner.woa.com:8080"},
		},
		LogConfig: monitor.LogConfig{
			Level: "debug",
		},
	})
	if err != nil {
		panic(fmt.Sprintf("init agent collector failed. err: %v", err))
	}

	return &Client{
		Config: &config,
		client: &http.Client{
			Transport: &http.Transport{
				DialContext: (&net.Dialer{
					Timeout:   30 * time.Second,
					KeepAlive: 30 * time.Second,
				}).DialContext,
				MaxIdleConns:          100,
				IdleConnTimeout:       90 * time.Second,
				ExpectContinueTimeout: 1 * time.Second,
			},
			Timeout: 30 * time.Second,
		},
		agent: agent,
		log:   log.WithName("alarm"),
	}
}

func Send(request *Request) error {
	if DefaultClient == nil {
		return errors.New("the default client has not been initialized")
	}
	return DefaultClient.Send(request)
}

func Close() {
	if DefaultClient.agent != nil {
		DefaultClient.agent.WaitAndStop()
	}
}

// Send sends alarm request.
func (c *Client) Send(request *Request) error {
	if c.Disable {
		return nil
	}

	if c.Zhiyan == nil {
		return c.SendToQcloud(request)
	}

	return c.SendToZhiyan(request)
}

func (c *Client) SendToZhiyan(request *Request) error {
	return c.agent.Event(c.Zhiyan.StringMark, request.Content, cast.ToStringMapString(request.labels), []string{})
}

func (c *Client) SendToQcloud(request *Request) error {
	request.SecurityCode = c.SecurityCode
	if request.HappenTime == 0 {
		request.HappenTime = time.Now().Unix()
	}

	var response Response
	err := c.send(request, &response)
	if err != nil {
		return err
	}
	if response.Code != 0 {
		return fmt.Errorf("send alarm error: %#v", response)
	}

	return nil
}

func (c *Client) send(request interface{}, response interface{}) error {
	data, err := json.Marshal(request)
	if err != nil {
		return err
	}

	c.log.V(4).Info("Send", "url", c.URL, "data", string(data))
	resp, err := c.client.Post(c.URL, "application/json", bytes.NewBuffer(data))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	responseData, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	if resp.StatusCode != 200 {
		return fmt.Errorf("http status code is not 200: %d %s", resp.StatusCode, resp.Status)
	}

	c.log.V(4).Info("Recv", "data", string(responseData))
	return json.Unmarshal(responseData, response)
}

// Request ...
type Request struct {
	// 必填,告警类型秘钥,字符串,需要事先在告警接入页面新增一个告警类型，申请后需5~10分钟生效
	SecurityCode string `json:"securityCode"`
	// 必填,对象类型,字符串,如：host(服务器)\netdevice(网络设备)\leasedline(专线)
	ObjType string `json:"objType"`
	// 必填，对象名称，字符串，如服务器IP、设备名称、专线名称等等
	ObjName string `json:"objName"`
	// 必填，对象的唯一标识，字符串，如uuid、管理IP、专线ID等能唯一标识对象的标识符
	ObjID string `json:"objId"`
	// 必填,告警内容，字符串
	Content string `json:"content"`
	// 必填,告警产生时间,时间戳，整型，精确到秒（这里需要注意的是，若告警产生时间距离当前时间过长会认为是异常告警从而不通知，当前系统默认是30分钟）
	HappenTime int64 `json:"happenTime"`
	// 选填,告警等级字段,整型,默认5（1认为是最高级）
	Level uint8 `json:"level,omitempty"`
	// 选填：动态标签字段，用来灵活对接不同的告警系统
	labels map[string]interface{}
}

func (r *Request) MarshalJSON() ([]byte, error) {
	dstMap := r.labels
	if dstMap == nil {
		dstMap = make(map[string]interface{})
	}
	s := structs.New(r)
	s.TagName = "json"
	s.FillMap(dstMap)

	return json.MarshalIndent(dstMap, "", "  ")
}

func (r *Request) WithLabels(keysAndValues ...interface{}) *Request {
	kvLen := len(keysAndValues)
	if kvLen%2 == 1 {
		panic("keysAndValues must be even")
	}

	if kvLen > 0 {
		if r.labels == nil {
			r.labels = make(map[string]interface{}, kvLen/2)
		}
		for i := 0; i < kvLen; i += 2 {
			key, ok := keysAndValues[i].(string)
			if !ok {
				panic(fmt.Errorf("key[%d](%T) must be string", i, keysAndValues[i]))
			}

			r.labels[key] = fmt.Sprint(keysAndValues[i+1])
		}
	}

	return r
}

func (r *Request) WithValues(keysAndValues ...interface{}) *Request {
	if len(keysAndValues)%2 == 1 {
		panic("keysAndValues must be even")
	}

	if len(keysAndValues) > 0 {
		contentMap := map[string]interface{}{
			"Content": r.Content,
		}
		for i := 0; i < len(keysAndValues); i += 2 {
			key, ok := keysAndValues[i].(string)
			if !ok {
				panic(fmt.Errorf("key[%d](%T) must be string", i, keysAndValues[i]))
			}

			contentMap[key] = keysAndValues[i+1]
		}

		data, err := json.MarshalIndent(contentMap, "", "  ")
		if err != nil {
			panic(err)
		}
		r.Content = string(data)
	}

	return r
}

// Response ...
type Response struct {
	Code    int    `json:"code"`
	Data    string `json:"data"`
	Message string `json:"message"`
}
