package alarm

import (
	"flag"
	"fmt"
	"testing"
	"time"
)

func init() {
	flag.CommandLine.Set("v", "4")
}

func TestClient_Send(t *testing.T) {
	Init(Config{
		SecurityCode: "f58e734827ca11efbe5c525400a423ee",
	})
	req := &Request{
		ObjID:   time.Now().String(),
		Content: "告警测试",
	}

	err := Send(req)
	fmt.Println(err)
}

func TestClient_SendZhiyan(t *testing.T) {
	Init(Config{
		Zhiyan: &ZhiyanConfig{StringMark: "kateway_server"},
	})
	defer Close()

	req := (&Request{
		ObjID:   time.Now().String(),
		Content: "告警测试",
	}).WithLabels("cluster", "cls-dyg9hwtz")

	err := Send(req)
	fmt.Println(err)
}
