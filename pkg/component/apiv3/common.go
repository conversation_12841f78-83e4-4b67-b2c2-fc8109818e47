/*
 * Tencent is pleased to support the open source community by making TKE
 * available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License"); you may not use this
 * file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package apiv3

import (
	"encoding/json"
	"fmt"

	"github.com/pborman/uuid"

	"git.woa.com/kateway/kateway-server/pkg/component/http"
	"git.woa.com/kateway/kateway-server/pkg/context"
)

const (
	Version = "v3"
)

type Request struct {
	AppId         uint64
	Uin           string
	SubAccountUin string
	Action        string
	RequestId     string
	Version       string
	Region        string
}

type Response struct {
	RequestId string `json:"RequestId"`
	Error     struct {
		Code    string
		Message string
	}
}

type Tag struct {
	// 标签健。
	Key string `json:"Key,omitempty"`

	// 标签值。
	Value string `json:"Value,omitempty"`
}

type Filter struct {
	// 属性名称, 若存在多个Filter时，Filter间的关系为逻辑与（AND）关系。
	Name string `json:"Name,omitempty"`

	// 属性值, 若同一个Filter存在多个Values，同一Filter下Values间的关系为逻辑或（OR）关系。
	Values []string `json:"Values,omitempty"`
}

func GetRequest(ctx *context.Context, region string, Action string, version string) Request {
	request := Request{}
	request.AppId = ctx.AppID()
	request.Uin = ctx.Uin()
	request.SubAccountUin = ctx.SubUin()
	request.RequestId = uuid.New()
	request.Action = Action
	request.Region = region
	// request.Version = "2018-05-25"
	request.Version = version
	return request
}

func DefaultResultChecker(ctx *context.Context, p *http.RequestParam, respJson []byte) error {
	respData := &struct {
		Response struct {
			Response
		}
	}{}

	if err := json.Unmarshal(respJson, respData); err != nil {
		return err
	}

	p.ResultCode = respData.Response.Error.Code
	p.ResultMsg = respData.Response.Error.Message

	if p.ResultCode != "" {
		return fmt.Errorf(p.ResultMsg)
	}

	return nil
}
