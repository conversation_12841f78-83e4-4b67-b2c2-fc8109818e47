package apiv3

import (
	"testing"

	"git.woa.com/kateway/kateway-server/pkg/component/http"
	"git.woa.com/kateway/kateway-server/pkg/context"
	"git.woa.com/kateway/kateway-server/pkg/logger/logrus"
)

func TestDefaultResultChecker(t *testing.T) {
	p := &http.RequestParam{}
	ctx := context.NewNoData(logrus.New())

	data := `
{"Response": {"Error": {"Code": "", "Message": ""}, "RequestId": "5caa6976-785f-4438-9475-cad5a2193396"}} 
`
	if err := DefaultResultChecker(ctx, p, []byte(data)); err != nil {
		t.Fatalf("return err when ErrCode is empty")
	}

	data = `
{"Response": {"Error": {"Code": "123", "Message": "1231"}, "RequestId": "5caa6976-785f-4438-9475-cad5a2193396"}} 
`

	if err := DefaultResultChecker(ctx, p, []byte(data)); err == nil {
		t.Fatalf("should reutrn an err")
	} else if err.Error() != "1231" {
		t.Fatalf("err should be '%s'", "1231")
	}
}
