package vpc

import (
	"fmt"
	"time"

	"github.com/pkg/errors"

	"git.woa.com/kateway/kateway-server/pkg/component/apiv2"
	"git.woa.com/kateway/kateway-server/pkg/component/apiv3"
	"git.woa.com/kateway/kateway-server/pkg/component/http"
	"git.woa.com/kateway/kateway-server/pkg/context"
)

const (
	Version = "2017-03-12"
)

const (
	DefaultLimit = 100
)

type Client struct {
	v2URL        string
	URL          string
	region       string
	httpExecutor http.Executor
	timeout      time.Duration
}

func NewClient(region string, url string, v2url string, httpExecutor http.Executor) *Client {
	return &Client{
		URL:          url,
		region:       region,
		httpExecutor: httpExecutor,
		timeout:      time.Second * 10,
		v2URL:        v2url,
	}
}

func (c *Client) DescribeSubnets(ctx *context.Context, uniqVpcId string, subnetIds []string) (map[string]Subnet, error) {
	Filters := make([]apiv3.Filter, 0)
	if uniqVpcId != "" {
		Filters = append(Filters, apiv3.Filter{
			Name:   "vpc-id",
			Values: []string{uniqVpcId},
		})
	}
	// subnetIds, vpc只支持一次传5个，所以先查出来后过滤
	if len(subnetIds) > 0 && len(subnetIds) <= 5 {
		Filters = append(Filters, apiv3.Filter{
			Name:   "subnet-id",
			Values: subnetIds,
		})
	}

	request := &DescribeSubnetsRequest{
		Request: apiv3.GetRequest(ctx, c.region, "DescribeSubnets", Version),
		Offset:  0,
		Limit:   100,
	}

	if len(Filters) > 0 {
		request.Filters = Filters
	}

	response := &DescribeSubnetsResponse{}

	h := &http.RequestParam{
		URL:         c.URL,
		Module:      "VpcV3",
		Action:      "DescribeSubnets",
		Version:     apiv3.Version,
		Timeout:     c.timeout,
		Request:     request,
		Response:    response,
		ResultCheck: apiv3.DefaultResultChecker,
	}

	if err := c.httpExecutor.DoPost(ctx, h); err != nil {
		return nil, err
	}

	result := response.Response.SubnetSet
	totalCount := response.Response.TotalCount
	for uint64(len(result)) < totalCount {
		request.Offset = len(result)
		resp := &DescribeSubnetsResponse{}

		h := &http.RequestParam{
			URL:         c.URL,
			Module:      "VpcV3",
			Action:      "DescribeSubnets",
			Version:     apiv3.Version,
			Timeout:     c.timeout,
			Request:     request,
			Response:    resp,
			ResultCheck: apiv3.DefaultResultChecker,
		}

		if err := c.httpExecutor.DoPost(ctx, h); err != nil {
			return nil, err
		}
		if len(resp.Response.SubnetSet) == 0 {
			break
		}

		result = append(result, resp.Response.SubnetSet...)
	}

	ret := make(map[string]Subnet)
	for _, subnet := range result {
		if len(subnetIds) > 0 {
			for _, subnetId := range subnetIds {
				if subnetId == subnet.SubnetId {
					ret[subnet.SubnetId] = subnet
				}
			}
		} else {
			ret[subnet.SubnetId] = subnet
		}
	}

	return ret, nil
}

func (c *Client) CreateSecurityGroupWithPolicies(ctx *context.Context, name, desc string, polices *SecurityGroupPolicySet) (*SecurityGroup, error) {
	request := &CreateSecurityGroupWithPoliciesRequest{
		Request:                apiv3.GetRequest(ctx, c.region, "CreateSecurityGroupWithPolicies", Version),
		GroupName:              name,
		GroupDescription:       desc,
		SecurityGroupPolicySet: polices,
	}
	response := &CreateSecurityGroupWithPoliciesResponse{}

	h := &http.RequestParam{
		URL:         c.URL,
		Module:      "VpcV3",
		Action:      "CreateSecurityGroupWithPolicies",
		Version:     apiv3.Version,
		Timeout:     c.timeout,
		Request:     request,
		Response:    response,
		ResultCheck: apiv3.DefaultResultChecker,
	}
	if err := c.httpExecutor.DoPost(ctx, h); err != nil {
		return nil, err
	}
	return response.Response.SecurityGroup, nil
}

func (c *Client) DescribeSecurityGroupAssociationStatistics(ctx *context.Context, sgIds []string) ([]SecurityGroupAssociationStatistics, error) {
	request := &DescribeSecurityGroupAssociationStatisticsRequest{
		Request:          apiv3.GetRequest(ctx, c.region, "DescribeSecurityGroupAssociationStatistics", Version),
		SecurityGroupIds: sgIds,
	}
	response := &DescribeSecurityGroupAssociationStatisticsResponse{}

	h := &http.RequestParam{
		URL:         c.URL,
		Module:      "VpcV3",
		Action:      "DescribeSecurityGroupAssociationStatistics",
		Version:     apiv3.Version,
		Timeout:     c.timeout,
		Request:     request,
		Response:    response,
		ResultCheck: apiv3.DefaultResultChecker,
	}
	if err := c.httpExecutor.DoPost(ctx, h); err != nil {
		return nil, err
	}

	return response.Response.SecurityGroupAssociationStatisticsSet, nil
}

func (c *Client) CreateSecurityGroup(ctx *context.Context, name, desc string, tags []apiv3.Tag) (*SecurityGroup, error) {
	request := &CreateSecurityGroupRequest{
		Request:          apiv3.GetRequest(ctx, c.region, "CreateSecurityGroup", Version),
		GroupName:        name,
		GroupDescription: desc,
		Tags:             tags,
	}
	response := &CreateSecurityGroupResponse{}

	h := &http.RequestParam{
		URL:         c.URL,
		Module:      "VpcV3",
		Action:      "CreateSecurityGroup",
		Version:     apiv3.Version,
		Timeout:     c.timeout,
		Request:     request,
		Response:    response,
		ResultCheck: apiv3.DefaultResultChecker,
	}
	if err := c.httpExecutor.DoPost(ctx, h); err != nil {
		return nil, err
	}
	return response.Response.SecurityGroup, nil
}

func (c *Client) DeleteSecurityGroup(ctx *context.Context, sgId string) error {
	request := &DeleteSecurityGroupRequest{
		Request:         apiv3.GetRequest(ctx, c.region, "DeleteSecurityGroup", Version),
		SecurityGroupId: sgId,
	}
	response := &DeleteSecurityGroupResponse{}

	h := &http.RequestParam{
		URL:         c.URL,
		Module:      "VpcV3",
		Action:      "DeleteSecurityGroup",
		Version:     apiv3.Version,
		Timeout:     c.timeout,
		Request:     request,
		Response:    response,
		ResultCheck: apiv3.DefaultResultChecker,
	}
	if err := c.httpExecutor.DoPost(ctx, h); err != nil {
		return err
	}
	return nil
}

func (c *Client) DescribeSecurityGroups(ctx *context.Context, sgIds []string, filters []apiv3.Filter, offset, limit uint64) ([]SecurityGroup, uint64, error) {
	if limit == 0 {
		limit = DefaultLimit
	}

	request := &DescribeSecurityGroupsRequest{
		Request:          apiv3.GetRequest(ctx, c.region, "DescribeSecurityGroups", Version),
		SecurityGroupIds: sgIds,
		Filters:          filters,
		Offset:           offset,
		Limit:            limit,
	}
	response := &DescribeSecurityGroupsResponse{}

	h := &http.RequestParam{
		URL:         c.URL,
		Module:      "VpcV3",
		Action:      "DescribeSecurityGroups",
		Version:     apiv3.Version,
		Timeout:     c.timeout,
		Request:     request,
		Response:    response,
		ResultCheck: apiv3.DefaultResultChecker,
	}
	if err := c.httpExecutor.DoPost(ctx, h); err != nil {
		return nil, 0, err
	}

	return response.Response.SecurityGroupSet, response.Response.TotalCount, nil
}

func (c *Client) CreateSecurityGroupPolicies(ctx *context.Context, sgId string, sgps SecurityGroupPolicySet) error {
	request := &CreateSecurityGroupPoliciesRequest{
		Request:                apiv3.GetRequest(ctx, c.region, "CreateSecurityGroupPolicies", Version),
		SecurityGroupId:        sgId,
		SecurityGroupPolicySet: sgps,
	}
	response := &CreateSecurityGroupPoliciesResponse{}

	h := &http.RequestParam{
		URL:         c.URL,
		Module:      "VpcV3",
		Action:      "CreateSecurityGroupPolicies",
		Version:     apiv3.Version,
		Timeout:     c.timeout,
		Request:     request,
		Response:    response,
		ResultCheck: apiv3.DefaultResultChecker,
	}
	if err := c.httpExecutor.DoPost(ctx, h); err != nil {
		return err
	}

	return nil
}

func (c *Client) DeleteSecurityGroupPolicies(ctx *context.Context, sgId string, sgps SecurityGroupPolicySet) error {
	request := &DeleteSecurityGroupPoliciesRequest{
		Request:                apiv3.GetRequest(ctx, c.region, "DeleteSecurityGroupPolicies", Version),
		SecurityGroupId:        sgId,
		SecurityGroupPolicySet: sgps,
	}
	response := &DeleteSecurityGroupPoliciesResponse{}

	h := &http.RequestParam{
		URL:         c.URL,
		Module:      "VpcV3",
		Action:      "DeleteSecurityGroupPolicies",
		Version:     apiv3.Version,
		Timeout:     c.timeout,
		Request:     request,
		Response:    response,
		ResultCheck: apiv3.DefaultResultChecker,
	}
	if err := c.httpExecutor.DoPost(ctx, h); err != nil {
		return err
	}

	return nil
}

func (c *Client) DescribeSecurityGroupPolicies(ctx *context.Context, sgId string) (SecurityGroupPolicySet, error) {
	request := &DescribeSecurityGroupPoliciesRequest{
		Request:         apiv3.GetRequest(ctx, c.region, "DescribeSecurityGroupPolicies", Version),
		SecurityGroupId: sgId,
	}
	response := &DescribeSecurityGroupPoliciesResponse{}

	h := &http.RequestParam{
		URL:         c.URL,
		Module:      "VpcV3",
		Action:      "DescribeSecurityGroupPolicies",
		Version:     apiv3.Version,
		Timeout:     c.timeout,
		Request:     request,
		Response:    response,
		ResultCheck: apiv3.DefaultResultChecker,
	}
	if err := c.httpExecutor.DoPost(ctx, h); err != nil {
		return SecurityGroupPolicySet{}, err
	}

	return response.Response.SecurityGroupPolicySet, nil
}

func (c *Client) GetVpc(ctx *context.Context, appId uint64, uniqVpcId string) ([]GetVpcRsp, error) {
	req := &GetVpcReq{
		Owner:     fmt.Sprint(appId),
		UniqVpcId: uniqVpcId,
	}

	rsp := make([]GetVpcRsp, 0)
	if err := c.commonPost(ctx, "TVPC.GetVpc", req, &vpcDetail{&rsp}); err != nil {
		return nil, err
	}

	return rsp, nil
}

func (c *Client) GetSubNet(ctx *context.Context, req GetSubNetReq) ([]GetSubNetRsp, error) {
	rsp := make([]GetSubNetRsp, 0)
	if err := c.commonPost(ctx, "TVPC.GetSubnet", req, &vpcDetail{&rsp}); err != nil {
		return nil, errors.Wrapf(err, "GetSubNet failed")
	}
	return rsp, nil
}

func (c *Client) CreateJnsGWService(ctx *context.Context, req AddNatgwServiceReq) (string, int, error) {
	rsp := make([]AddNatgwServiceRsp, 0)
	if err := c.commonPost(ctx, "TVPC.AddNatgwService", &vpcDetail{Detail: []AddNatgwServiceReq{req}}, &vpcDetail{&rsp}); err != nil {
		return "", 0, errors.Wrapf(err, "create jnsgw failed")
	}
	if len(rsp) != 1 {
		return "", 0, errors.Errorf("create jnsgw response item != 1")
	}
	return rsp[0].Vip, rsp[0].Vport, nil
}

func (c *Client) DeleteJnsGWService(ctx *context.Context, req DelNatgwServiceReq) error {
	rsp := make(map[string]interface{}, 0)
	if err := c.commonPost(ctx, "TVPC.DelNatgwService", &vpcDetail{Detail: []DelNatgwServiceReq{req}}, &rsp); err != nil {
		return errors.Wrapf(err, "delete jnsgw failed")
	}
	return nil
}

func (c *Client) GetVpcLbService(ctx *context.Context, req GetVpcLbServiceReq) ([]GetVpcLbServiceRsp, error) {
	rsp := make([]GetVpcLbServiceRsp, 0)
	if err := c.commonPost(ctx, "TVPC.GetService", req, &vpcDetail{&rsp}); err != nil {
		return nil, errors.Wrapf(err, "get vpc lb service failed")
	}
	return rsp, nil
}

func (c *Client) commonPost(ctx *context.Context, action string, reqData interface{}, respData interface{}) error {
	req := &Request{
		Header: Header{
			Version:   "1.0",
			Caller:    "dashboard",
			Password:  "what?",
			Callee:    "vpc",
			Timestamp: time.Now().Unix(),
		},
		Interface: RequestData{
			InterfaceName: action,
			Para:          reqData,
		},
	}

	resp := &Response{
		ReturnData: respData,
	}

	h := &http.RequestParam{
		URL:         c.v2URL,
		Module:      "VpcV2",
		Action:      action,
		Version:     apiv2.Version,
		Timeout:     c.timeout,
		Request:     req,
		Response:    resp,
		ResultCheck: apiv2.DefaultResultChecker,
	}
	return c.httpExecutor.DoPost(ctx, h)
}
