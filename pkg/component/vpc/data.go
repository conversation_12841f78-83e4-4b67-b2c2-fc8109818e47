package vpc

import "git.woa.com/kateway/kateway-server/pkg/component/apiv3"

type Subnet struct {
	// `VPC`实例`ID`。
	VpcId string `json:"VpcId,omitempty"`

	// 子网实例`ID`，例如：subnet-bthucmmy。
	SubnetId string `json:"SubnetId,omitempty"`

	// 子网名称。
	SubnetName string `json:"SubnetName,omitempty"`

	// 子网的 `IPv4` `CIDR`。
	CidrBlock string `json:"CidrBlock,omitempty"`

	// 是否默认子网。
	IsDefault bool `json:"IsDefault,omitempty"`

	// 是否开启广播。
	EnableBroadcast bool `json:"EnableBroadcast,omitempty"`

	// 可用区。
	Zone string `json:"Zone,omitempty"`

	// 路由表实例ID，例如：rtb-l2h8d7c2。
	RouteTableId string `json:"RouteTableId,omitempty"`

	// 创建时间。
	CreatedTime string `json:"CreatedTime,omitempty"`

	// 可用`IP`数。
	AvailableIpAddressCount uint64 `json:"AvailableIpAddressCount,omitempty"`

	// 子网的 `IPv6` `CIDR`。
	Ipv6CidrBlock string `json:"Ipv6CidrBlock,omitempty"`

	// 关联`ACL`ID
	NetworkAclId string `json:"NetworkAclId,omitempty"`

	// 是否为 `SNAT` 地址池子网。
	IsRemoteVpcSnat bool `json:"IsRemoteVpcSnat,omitempty"`
}

type DescribeSubnetsRequest struct {
	apiv3.Request
	Filters []apiv3.Filter `json:"Filters,omitempty"`
	Offset  int            `json:"Offset"`
	Limit   int            `json:"Limit"`
}

type DescribeSubnetsResponse struct {
	Response struct {
		apiv3.Response
		TotalCount uint64
		SubnetSet  []Subnet `json:"SubnetSet,omitempty"`
	} `json:"Response"`
}

type ServiceTemplateSpecification struct {
	// 协议端口ID，例如：ppm-f5n1f8da。
	ServiceId string `json:"ServiceId,omitempty"`

	// 协议端口组ID，例如：ppmg-f5n1f8da。
	ServiceGroupId string `json:"ServiceGroupId,omitempty"`
}

type AddressTemplateSpecification struct {
	// IP地址ID，例如：ipm-2uw6ujo6。
	AddressId string `json:"AddressId,omitempty"`

	// IP地址组ID，例如：ipmg-2uw6ujo6。
	AddressGroupId string `json:"AddressGroupId,omitempty"`
}

type SecurityGroup struct {
	// 安全组实例ID，例如：sg-ohuuioma。
	SecurityGroupId string `json:"SecurityGroupId,omitempty"`

	// 安全组名称，可任意命名，但不得超过60个字符。
	SecurityGroupName string `json:"SecurityGroupName,omitempty"`

	// 安全组备注，最多100个字符。
	SecurityGroupDesc string `json:"SecurityGroupDesc,omitempty"`

	// 项目id，默认0。可在qcloud控制台项目管理页面查询到。
	ProjectId string `json:"ProjectId,omitempty"`

	// 是否是默认安全组，默认安全组不支持删除。
	IsDefault bool `json:"IsDefault,omitempty"`

	// 安全组创建时间。
	CreatedTime string `json:"CreatedTime,omitempty"`

	// 标签键值对。
	TagSet []apiv3.Tag `json:"TagSet,omitempty"`
}

type SecurityGroupAssociationStatistics struct {
	// 安全组实例ID。
	SecurityGroupId string `json:"SecurityGroupId,omitempty"`

	// 云服务器实例数。
	CVM uint64 `json:"CVM,omitempty"`

	// 数据库实例数。
	CDB uint64 `json:"CDB,omitempty"`

	// 弹性网卡实例数。
	ENI uint64 `json:"ENI,omitempty"`

	// 被安全组引用数。
	SG uint64 `json:"SG,omitempty"`

	// 负载均衡实例数。
	CLB uint64 `json:"CLB,omitempty"`

	// 全量实例的绑定统计。
	InstanceStatistics []InstanceStatistic `json:"InstanceStatistics,omitempty"`

	// 所有资源的总计数（不包含被安全组引用数）。
	TotalCount uint64 `json:"TotalCount,omitempty"`
}

type InstanceStatistic struct {
	// 实例的类型
	InstanceType string `json:"InstanceType,omitempty"`

	// 实例的个数
	InstanceCount uint64 `json:"InstanceCount,omitempty"`
}

type SecurityGroupPolicy struct {
	// 安全组规则索引号。
	PolicyIndex int64 `json:"PolicyIndex,omitempty"`

	// 协议, 取值: TCP,UDP, ICMP。
	Protocol string `json:"Protocol,omitempty"`

	// 端口(all, 离散port,  range)。
	Port string `json:"Port,omitempty"`

	// 协议端口ID或者协议端口组ID。ServiceTemplate和Protocol+Port互斥。
	ServiceTemplate ServiceTemplateSpecification `json:"ServiceTemplate,omitempty"`

	// 网段或IP(互斥)。
	CidrBlock string `json:"CidrBlock,omitempty"`

	// 安全组实例ID，例如：sg-ohuuioma。
	SecurityGroupId string `json:"SecurityGroupId,omitempty"`

	// IP地址ID或者ID地址组ID。
	AddressTemplate AddressTemplateSpecification `json:"AddressTemplate,omitempty"`

	// ACCEPT 或 DROP。
	Action string `json:"Action,omitempty"`

	// 安全组规则描述。
	PolicyDescription string `json:"PolicyDescription,omitempty"`
}

type SecurityGroupPolicySet struct {
	// 安全组规则当前版本。用户每次更新安全规则版本会自动加1，防止更新的路由规则已过期，不填不考虑冲突。
	Version string `json:"EtcdVersion,omitempty"`

	// 出站规则。
	Egress []SecurityGroupPolicy `json:"Egress,omitempty"`

	// 入站规则。
	Ingress []SecurityGroupPolicy `json:"Ingress,omitempty"`
}

type CreateSecurityGroupPoliciesRequest struct {
	apiv3.Request
	// 安全组实例ID，例如sg-33ocnj9n，可通过DescribeSecurityGroups获取。
	SecurityGroupId string `json:"SecurityGroupId,omitempty"`

	// 安全组规则集合。
	SecurityGroupPolicySet SecurityGroupPolicySet `json:"SecurityGroupPolicySet,omitempty"`
}

type CreateSecurityGroupPoliciesResponse struct {
	Response struct {
		apiv3.Response
	} `json:"Response"`
}

type DeleteSecurityGroupPoliciesRequest struct {
	apiv3.Request
	// 安全组实例ID，例如sg-33ocnj9n，可通过DescribeSecurityGroups获取。
	SecurityGroupId string `json:"SecurityGroupId,omitempty"`

	// 安全组规则集合。一个请求中只能删除单个方向的一条或多条规则。支持指定索引（PolicyIndex） 匹配删除和安全组规则匹配删除两种方式，一个请求中只能使用一种匹配方式。
	SecurityGroupPolicySet SecurityGroupPolicySet `json:"SecurityGroupPolicySet,omitempty"`
}

type DeleteSecurityGroupPoliciesResponse struct {
	Response struct {
		apiv3.Response
	} `json:"Response"`
}

type DescribeSecurityGroupPoliciesRequest struct {
	apiv3.Request
	// 安全组实例ID，例如：sg-33ocnj9n，可通过DescribeSecurityGroups获取。
	SecurityGroupId string `json:"SecurityGroupId,omitempty"`
}

type DescribeSecurityGroupPoliciesResponse struct {
	Response struct {
		apiv3.Response
		// 安全组规则集合。
		SecurityGroupPolicySet SecurityGroupPolicySet `json:"SecurityGroupPolicySet,omitempty"`
	} `json:"Response"`
}

type DescribeSecurityGroupsRequest struct {
	apiv3.Request
	SecurityGroupIds []string       `json:"SecurityGroupIds,omitempty"`
	Filters          []apiv3.Filter `json:"Filters,omitempty"`
	Offset           uint64         `json:"Offset"`
	Limit            uint64         `json:"Limit"`
}

type DescribeSecurityGroupsResponse struct {
	Response struct {
		apiv3.Response
		SecurityGroupSet []SecurityGroup `json:"SecurityGroupSet,omitempty"`
		TotalCount       uint64          `json:"TotalCount"`
	} `json:"Response"`
}

type CreateSecurityGroupRequest struct {
	apiv3.Request

	// 安全组名称，可任意命名，但不得超过60个字符。
	GroupName string `json:"GroupName,omitempty"`

	// 安全组备注，最多100个字符。
	GroupDescription string `json:"GroupDescription,omitempty"`

	// 项目ID，默认0。可在qcloud控制台项目管理页面查询到。
	ProjectId *string `json:"ProjectId,omitempty"`

	// 指定绑定的标签列表，例如：[{"Key": "city", "Value": "shanghai"}]
	Tags []apiv3.Tag `json:"Tags,omitempty"`
}

type CreateSecurityGroupResponse struct {
	Response struct {
		apiv3.Response
		SecurityGroup *SecurityGroup `json:"SecurityGroup,omitempty"`
	} `json:"Response"`
}

type DeleteSecurityGroupRequest struct {
	apiv3.Request
	SecurityGroupId string `json:"SecurityGroupId,omitempty"`
}

type DeleteSecurityGroupResponse struct {
	Response struct {
		apiv3.Response
	} `json:"Response"`
}

type CreateSecurityGroupWithPoliciesRequest struct {
	apiv3.Request
	// 安全组名称，可任意命名，但不得超过60个字符。
	GroupName string `json:"GroupName,omitempty"`

	// 安全组备注，最多100个字符。
	GroupDescription string `json:"GroupDescription,omitempty"`

	// 项目ID，默认0。可在qcloud控制台项目管理页面查询到。
	ProjectId *string `json:"ProjectId,omitempty"`

	// 安全组规则集合。
	SecurityGroupPolicySet *SecurityGroupPolicySet `json:"SecurityGroupPolicySet,omitempty"`
}

type CreateSecurityGroupWithPoliciesResponse struct {
	Response struct {
		apiv3.Response
		SecurityGroup *SecurityGroup `json:"SecurityGroup,omitempty"`
	} `json:"Response"`
}

type DescribeSecurityGroupAssociationStatisticsRequest struct {
	apiv3.Request
	SecurityGroupIds []string `json:"SecurityGroupIds,omitempty"`
}

type DescribeSecurityGroupAssociationStatisticsResponse struct {
	Response struct {
		apiv3.Response
		SecurityGroupAssociationStatisticsSet []SecurityGroupAssociationStatistics `json:"SecurityGroupAssociationStatisticsSet,omitempty"`
	} `json:"Response"`
}

type GetVpcReq struct {
	UniqVpcId string `json:"uniqVpcId"`
	Owner     string `json:"owner,omitempty"`
}

type AssistCidr struct {
	IntMask    int    `json:"intMask"`
	Subnet     string `json:"subnet"`
	AssistType int    `json:"assistType"`
}

type GetVpcRsp struct {
	IntMask     int          `json:"intMask"`
	Subnet      string       `json:"subnet"`
	VpcID       int64        `json:"vpcId"`
	AssistCidrs []AssistCidr `json:"assistCidrs,omitempty"`
}

type GetSubNetReq struct {
	Owner string `json:"owner"`
	VpcId int64  `json:"vpcId"`
	Type  int    `json:"type"`
}

type GetSubNetRsp struct {
	SubnetId int `json:"subnetId"`
}

type Header struct {
	Version   string `json:"version"`
	Caller    string `json:"caller"`
	Password  string `json:"password"`
	Callee    string `json:"callee"`
	EventId   int64  `json:"eventId"`
	Timestamp int64  `json:"timestamp"`
}

type RequestData struct {
	InterfaceName string      `json:"interfaceName"`
	Para          interface{} `json:"para"`
}

type Request struct {
	Header
	Interface RequestData `json:"interface"`
}

type vpcDetail struct {
	Detail interface{} `json:"detail"`
}

type AddNatgwServiceReq struct {
	Owner string `json:"owner"`
	Pip   string `json:"pip"`
	Pport int    `json:"pport"`
	Proto string `json:"proto"`
	// 创建Master时不指定SubnetID
	SubnetID *int   `json:"subnetId,omitempty"`
	VgwIndex string `json:"vgwIndex,omitempty"`
	VgwType  int    `json:"vgwType"`
	VpcId    int64  `json:"vpcId"`
	// UniqVpcId string `json:"uniqVpcId"`
}

type AddNatgwServiceRsp struct {
	Owner    string `json:"owner"`
	Pip      string `json:"pip"`
	Pport    int    `json:"pport"`
	Proto    string `json:"proto"`
	SubnetID int    `json:"subnetId"`
	VgwIndex string `json:"vgwIndex"`
	VgwType  int    `json:"vgwType"`
	Vip      string `json:"vip"`
	VpcID    int    `json:"vpcId"`
	Vport    int    `json:"vport"`
}

type DelNatgwServiceReq struct {
	Owner    string  `json:"owner"`
	Pip      string  `json:"pip"`
	Pport    int     `json:"pport"`
	Proto    string  `json:"proto"`
	SubnetID *int    `json:"subnetId,omitempty"`
	VgwIndex *string `json:"vgwIndex,omitempty"`
	VpcID    int64   `json:"vpcId"`
}

type DelNatgwServiceRsp struct {
	Owner string `json:"owner"`
	Pip   string `json:"pip"`
	Pport int    `json:"pport"`
	Proto string `json:"proto"`
	VpcID int64  `json:"vpcId"`
}

type GetVpcLbServiceReq struct {
	VpcID   int64  `json:"vpcId,omitempty"`
	groupID int64  `json: "groupId,omitempty"`
	Proto   string `json:"proto,omitempty"`
	Vip     string `json:"vip,omitempty"`
	Vport   int    `json:"vport,omitempty"`
	Type    int    `json:"type,omitempty"`
}

type GetVpcLbServiceRsp struct {
	VpcID                int     `json:"vpcId"`
	VipSetID             int     `json:"vipSetId"`
	UniqVpcID            string  `json:"uniqVpcId"`
	VpcGwIP              string  `json:"vpcGwIp"`
	NoSnatFlag           int     `json:"noSnatFlag"`
	LbType               int     `json:"lbType"`
	Vip                  string  `json:"vip"`
	StickyMaxCount       int     `json:"stickyMaxCount"`
	Vport                int     `json:"vport"`
	GroupID              []int   `json:"groupId"`
	Proto                string  `json:"proto"`
	Route                []Route `json:"route"`
	RstFlag              int     `json:"rstFlag"`
	StickyFlag           int     `json:"stickyFlag"`
	UniqVpcgwID          string  `json:"uniqVpcgwId"`
	UniqClassicVpcgwID   string  `json:"uniqClassicVpcgwId"`
	SetID                int     `json:"setId"`
	Type                 int     `json:"type"`
	SubnetRouteAlgorithm int     `json:"subnetRouteAlgorithm"`
	StickyTimeout        int     `json:"stickyTimeout"`
}

type Route struct {
	Weight    int    `json:"weight"`
	Pport     int    `json:"pport"`
	ZoneID    int    `json:"zoneId"`
	UDPOption int    `json:"udpOption"`
	HostIP    string `json:"hostIp"`
	Pip       string `json:"pip"`
}

type Response struct {
	Header
	ReturnValue int         `json:"returnValue"`
	ReturnMsg   string      `json:"returnMsg"`
	ReturnData  interface{} `json:"returnData"`
}
