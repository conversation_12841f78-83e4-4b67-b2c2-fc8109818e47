package vpc

import (
	"reflect"
	"testing"
	"time"

	"git.woa.com/kateway/kateway-server/pkg/component/http"
	"git.woa.com/kateway/kateway-server/pkg/context"
	"git.woa.com/kateway/kateway-server/pkg/logger/logrus"
)

func TestClient_DescribeSubnets(t *testing.T) {
	type fields struct {
		v2URL        string
		URL          string
		region       string
		httpExecutor http.Executor
		timeout      time.Duration
	}
	type args struct {
		ctx       *context.Context
		uniqVpcId string
		subnetIds []string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[string]Subnet
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: fields{
				URL:          "http://hk.vpcapiv3.tencentyun.com:8520",
				region:       "ap-hongkong",
				timeout:      5 * time.Second,
				httpExecutor: http.NewHTTP(time.Second * 10),
			},
			args: args{
				ctx:       context.New(1251707795, "3321337994", "3321337994", "xxx", logrus.New()),
				uniqVpcId: "",
				subnetIds: []string{"subnet-rsnunlmt"},
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				v2URL:        tt.fields.v2URL,
				URL:          tt.fields.URL,
				region:       tt.fields.region,
				httpExecutor: tt.fields.httpExecutor,
				timeout:      tt.fields.timeout,
			}
			got, err := c.DescribeSubnets(tt.args.ctx, tt.args.uniqVpcId, tt.args.subnetIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("DescribeSubnets() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DescribeSubnets() got = %v, want %v", got, tt.want)
			}
		})
	}
}
