/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKE
 * available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License"); you may not use this
 * file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package eks

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/pkg/errors"
	restclient "k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"

	"git.woa.com/kateway/kateway-server/pkg/component/http"
	"git.woa.com/kateway/kateway-server/pkg/context"
	"git.woa.com/kateway/kateway-server/pkg/errno"
)

type ClientManager struct {
	Region        string
	URL           string
	httpsExecutor http.Executor
}

func NewClientManager(region, url string, httpExecutor http.Executor) *ClientManager {
	return &ClientManager{
		Region:        region,
		URL:           url,
		httpsExecutor: httpExecutor,
	}
}

func (c *ClientManager) GetRestConfig(ctx *context.Context, clusterId string, timeout time.Duration) (*restclient.Config, error) {
	cluster, err := c.ClusterInfo(ctx, clusterId)
	if err != nil {
		return nil, err
	}

	address := ""
	for _, a := range cluster.Status.Addresses {
		if a.Type == "Internal" {
			address = fmt.Sprintf("%s:%d", a.Host, a.Port)
			break
		}
	}
	sec, err := c.Credentials(ctx, cluster.Spec.ClusterCredentialRef.Name)
	if err != nil {
		return nil, err
	}

	overrides := &clientcmd.ConfigOverrides{}
	overrides.ClusterInfo.InsecureSkipTLSVerify = true
	overrides.AuthInfo.Token = *sec.Token
	overrides.ClusterDefaults.Server = fmt.Sprintf("https://%s", address)
	clientConfig := clientcmd.NewNonInteractiveDeferredLoadingClientConfig(&clientcmd.ClientConfigLoadingRules{}, overrides)

	cfg, err := clientConfig.ClientConfig()
	if err != nil {
		return nil, errno.Cause(errno.KUBE_CLIENT_CONNECTION_ERROR, errors.Wrap(err, "generate client config failed"))
	}
	cfg.QPS = 1e6
	cfg.Burst = 1e6
	cfg.Timeout = timeout

	// klog.Infof("Rest config with timeout %v minutes generated", cfg.Timeout.Minutes())

	return cfg, nil
}

func (c *ClientManager) ClusterInfo(ctx *context.Context, clusterId string) (*Cluster, error) {
	response := &Cluster{}
	// https://cq.api.tke.tencentyun.com:7443/apis/platform.tkestack.io/v1/clusters/cls-1q2f4kq4
	h := &http.RequestParam{
		URL:             c.URL + "/apis/platform.tkestack.io/v1/clusters/" + clusterId,
		Module:          "platform",
		Timeout:         time.Second * 10,
		Response:        response,
		ResultCheck:     clusterResultChecker,
		SkipResponseLog: false,
	}

	if err := c.httpsExecutor.DoGet(ctx, h); err != nil {
		return nil, errno.Cause(errno.UNEXCEPTED_INTERNAL_ERROR, err)
	}

	return response, nil
}

func (c *ClientManager) Credentials(ctx *context.Context, name string) (*ClusterCredential, error) {
	response := &ClusterCredential{}
	h := &http.RequestParam{
		URL:             c.URL + "/apis/platform.tkestack.io/v1/clustercredentials/" + name,
		Module:          "platform",
		Timeout:         time.Second * 10,
		Action:          "Credentials",
		Response:        response,
		ResultCheck:     clusterCredentialResultChecker,
		SkipResponseLog: false,
	}

	if err := c.httpsExecutor.DoGet(ctx, h); err != nil {
		return nil, errno.Cause(errno.PARAM_ERROR, err)
	}

	return response, nil
}

func clusterResultChecker(ctx *context.Context, p *http.RequestParam, respJson []byte) error {
	if err := json.Unmarshal(respJson, &Cluster{}); err != nil {
		return err
	}
	return nil
}

func clusterCredentialResultChecker(ctx *context.Context, p *http.RequestParam, respJson []byte) error {
	if err := json.Unmarshal(respJson, &ClusterCredential{}); err != nil {
		return err
	}
	return nil
}
