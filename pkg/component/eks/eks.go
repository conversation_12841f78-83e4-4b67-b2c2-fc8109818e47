/*
 * <PERSON>cent is pleased to support the open source community by making TKE
 * available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License"); you may not use this
 * file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package eks

import (
	"time"

	"git.woa.com/kateway/kateway-server/pkg/component/apiv3"
	"git.woa.com/kateway/kateway-server/pkg/component/http"
	"git.woa.com/kateway/kateway-server/pkg/context"
	"git.woa.com/kateway/kateway-server/pkg/errno"
)

var RequestSource = "etcd-cloud"

const (
	StatusInitializing = "Initializing"
	StatusRunning      = "Running"
	Terminating        = "Terminating"
)

type CreateClusterParam struct {
	K8SVersion      string   `json:"K8SVersion"`
	VpcId           string   `json:"VpcId"`
	ClusterName     string   `json:"ClusterName"`
	SubnetIds       []string `json:"SubnetIds"`
	ClusterDesc     string   `json:"ClusterDesc,omitempty"`
	ServiceSubnetId string   `json:"ServiceSubnetId,omitempty"`
	ProductName     string   `json:"ProductName"`
	ExtraParam      string   `json:"ExtraParam"`
}

// 目前仅需要修改子网，有需要再加
type UpdateClusterParam struct {
	ClusterId   string   `json:"ClusterId"`
	SubnetIds   []string `json:"SubnetIds"`
	ProductName string   `json:"ProductName"`
}

type ClusterManager struct {
	Region       string
	URL          string
	httpExecutor http.Executor
}

func NewClusterManager(region string, url string, httpExecutor http.Executor) *ClusterManager {
	return &ClusterManager{
		Region:       region,
		URL:          url,
		httpExecutor: httpExecutor,
	}
}

func (c *ClusterManager) Create(ctx *context.Context, para *CreateClusterParam, isHide bool) (string, error) {
	request := &createClusterV3Request{
		Request:            apiv3.GetRequest(ctx, c.Region, "CreateEKSCluster", "2018-05-25"),
		CreateClusterParam: *para,
		RequestSource:      "API",
	}

	if isHide {
		request.RequestSource = RequestSource
	}

	response := &createClusterV3Response{}

	h := &http.RequestParam{
		URL:         c.URL,
		Module:      "Eks",
		Action:      "CreateEKSCluster",
		Version:     apiv3.Version,
		Timeout:     time.Second * 10,
		Request:     request,
		Response:    response,
		ResultCheck: apiv3.DefaultResultChecker,
	}

	if err := c.httpExecutor.DoPost(ctx, h); err != nil {
		ctx.Errorf("failed to create eks cluster,err is %v", err)
		return "", errno.Cause(errno.PARAM_ERROR, err)
	}

	return response.Response.ClusterId, nil
}

func (c *ClusterManager) Update(ctx *context.Context, param *UpdateClusterParam, isHide bool) error {
	request := &updateClusterV3Request{
		Request:            apiv3.GetRequest(ctx, c.Region, "UpdateEKSCluster", "2018-05-25"),
		UpdateClusterParam: *param,
		RequestSource:      "API",
	}
	// TODO: 暂时使用主账号更新，避免无权限. 待优化
	request.SubAccountUin = ctx.Uin()

	if isHide {
		request.RequestSource = RequestSource
	}

	response := &updateClusterV3Response{}

	h := &http.RequestParam{
		URL:         c.URL,
		Module:      "Eks",
		Action:      "UpdateEKSCluster",
		Version:     apiv3.Version,
		Timeout:     time.Second * 10,
		Request:     request,
		Response:    response,
		ResultCheck: apiv3.DefaultResultChecker,
	}

	if err := c.httpExecutor.DoPost(ctx, h); err != nil {
		ctx.Errorf("failed to update eks cluster,err is %v", err)
		return errno.Cause(errno.UNEXCEPTED_INTERNAL_ERROR, err)
	}
	return nil
}

func (c *ClusterManager) Delete(ctx *context.Context, clusterId string, productName string, isHide bool) error {
	request := &deleteClusterV3Request{
		Request:       apiv3.GetRequest(ctx, c.Region, "DeleteEKSCluster", "2018-05-25"),
		ClusterId:     clusterId,
		ProductName:   productName,
		RequestSource: "API",
	}
	// TODO: 暂时使用主账号删除，避免无权限. 待优化
	request.SubAccountUin = ctx.Uin()

	if isHide {
		request.RequestSource = RequestSource
	}

	response := &deleteClusterV3Response{}

	h := &http.RequestParam{
		URL:         c.URL,
		Module:      "Eks",
		Action:      "DeleteEKSCluster",
		Version:     apiv3.Version,
		Timeout:     time.Second * 10,
		Request:     request,
		Response:    response,
		ResultCheck: apiv3.DefaultResultChecker,
	}

	if err := c.httpExecutor.DoPost(ctx, h); err != nil {
		ctx.Errorf("failed to delete eks cluster,cluster is %s,err is %v", clusterId, err)
		return errno.Cause(errno.PARAM_ERROR, err)
	}

	return nil
}

func (c *ClusterManager) List(ctx *context.Context, productName string, isHide bool, clusterIds []string, filters []apiv3.Filter, offset, limit int64) ([]EksCluster, error) {
	request := &describeEKSClustersV3Request{
		Request:       apiv3.GetRequest(ctx, c.Region, "DescribeEKSClusters", "2018-05-25"),
		ClusterIds:    clusterIds,
		ProductName:   productName,
		Filters:       filters,
		Offset:        offset,
		Limit:         limit,
		RequestSource: "API",
	}
	// TODO: 暂时使用主账号查询，避免无权限. 待优化
	request.SubAccountUin = ctx.Uin()

	if isHide {
		request.RequestSource = RequestSource
	}
	response := &describeEKSClustersV3Response{}

	h := &http.RequestParam{
		URL:         c.URL,
		Module:      "Eks",
		Action:      "DescribeEKSClusters",
		Version:     apiv3.Version,
		Timeout:     time.Second * 10,
		Request:     request,
		Response:    response,
		ResultCheck: apiv3.DefaultResultChecker,
	}

	if err := c.httpExecutor.DoPost(ctx, h); err != nil {
		ctx.Errorf("failed to describe eks clusters, err is %v", err)
		return nil, errno.Cause(errno.PARAM_ERROR, err)
	}

	return response.Response.Clusters, nil
}

func (c *ClusterManager) Get(ctx *context.Context, productName string, isHide bool, clusterId string) (*EksCluster, error) {
	clusters, err := c.List(ctx, productName, isHide, []string{clusterId}, nil, 0, 1)
	if err != nil {
		return nil, err
	}
	if len(clusters) > 0 {
		return &clusters[0], nil
	}
	return nil, nil
}

func (c *ClusterManager) DescribeStatus(ctx *context.Context, clusterId string, productName string, isHide bool) (string, error) {
	request := &describeClusterStatusV3Request{
		Request:       apiv3.GetRequest(ctx, c.Region, "DescribeEKSClusterStatus", "2018-05-25"),
		ClusterId:     clusterId,
		ProductName:   productName,
		RequestSource: "API",
	}
	// TODO: 暂时使用主账号查询，避免无权限. 待优化
	request.SubAccountUin = ctx.Uin()

	if isHide {
		request.RequestSource = RequestSource
	}
	response := &describeClusterStatusV3Response{}

	h := &http.RequestParam{
		URL:         c.URL,
		Module:      "Eks",
		Action:      "DescribeStatus",
		Version:     apiv3.Version,
		Timeout:     time.Second * 10,
		Request:     request,
		Response:    response,
		ResultCheck: apiv3.DefaultResultChecker,
	}

	if err := c.httpExecutor.DoPost(ctx, h); err != nil {
		ctx.Errorf("failed to describe eks cluster status,cluster is %s,err is %v", clusterId, err)
		return "", errno.Cause(errno.PARAM_ERROR, err)
	}

	return response.Response.Phase, nil
}
