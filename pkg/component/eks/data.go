/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKE
 * available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License"); you may not use this
 * file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package eks

import "git.woa.com/kateway/kateway-server/pkg/component/apiv3"

type createClusterV3Request struct {
	apiv3.Request
	CreateClusterParam
	RequestSource string `json:"RequestSource"`
}

type createClusterV3Response struct {
	Response struct {
		ClusterId string `json:"ClusterId,omitempty"`
		apiv3.Response
	}
}

type updateClusterV3Request struct {
	apiv3.Request
	UpdateClusterParam
	RequestSource string `json:"RequestSource"`
}

type updateClusterV3Response struct {
	Response struct {
		apiv3.Response
	}
}

type deleteClusterV3Request struct {
	apiv3.Request
	ClusterId     string `json:"ClusterId"`
	RequestSource string `json:"RequestSource"`
	ProductName   string `json:"ProductName"`
}

type deleteClusterV3Response struct {
	Response struct {
		apiv3.Response
	}
}

type describeClusterStatusV3Request struct {
	apiv3.Request
	ClusterId     string `json:"ClusterId"`
	ProductName   string `json:"ProductName"`
	RequestSource string `json:"RequestSource"`
}

type describeClusterStatusV3Response struct {
	Response struct {
		Phase string `json:"Phase,omitempty"`
		apiv3.Response
	}
}

type describeEKSClustersV3Request struct {
	apiv3.Request
	ClusterIds    []string       `json:"ClusterIds"`
	ProductName   string         `json:"ProductName"`
	RequestSource string         `json:"RequestSource"`
	Filters       []apiv3.Filter `json:"Filters"`
	Offset        int64          `json:"Offset"`
	Limit         int64          `json:"Limit"`
}

type EksCluster struct {
	ClusterId       string   `json:"ClusterId"`
	ClusterName     string   `json:"ClusterName"`
	ClusterDesc     string   `json:"ClusterDesc"`
	K8SVersion      string   `json:"K8SVersion"`
	VpcId           string   `json:"VpcId"`
	SubnetIds       []string `json:"SubnetIds"`
	Status          string   `json:"Status"`
	ServiceSubnetId string   `json:"ServiceSubnetId"`
	DnsServers      []string `json:"DnsServers"`
	CreatedTime     string   `json:"CreatedTime"`
}

type describeEKSClustersV3Response struct {
	Response struct {
		apiv3.Response
		Clusters []EksCluster `json:"Clusters"`
	}
}

type getAccountTypeV3Request struct {
	apiv3.Request
	// ProductName   string `json:"ProductName"`
	RequestSource string `json:"RequestSource"`
}

type getAccountTypeV3Response struct {
	Response struct {
		AccountType string `json:"AccountType,omitempty"`
		apiv3.Response
	}
}
