package eks

import (
	"time"

	"git.woa.com/kateway/kateway-server/pkg/component/apiv3"
	"git.woa.com/kateway/kateway-server/pkg/component/http"
	"git.woa.com/kateway/kateway-server/pkg/context"
)

type InternalServer struct {
	Region        string
	URL           string
	httpsExecutor http.Executor
}

func NewInternalServer(region, url string, httpExecutor http.Executor) *InternalServer {
	return &InternalServer{
		Region:        region,
		URL:           url,
		httpsExecutor: httpExecutor,
	}
}

func (c *InternalServer) GetAccountType(ctx *context.Context) (string, error) {
	request := &getAccountTypeV3Request{
		Request: apiv3.GetRequest(ctx, c.Region, "GetAccountType", "2018-05-25"),
		// ProductName:   "etcd",
		RequestSource: "API",
	}

	response := &getAccountTypeV3Response{}

	h := &http.RequestParam{
		URL:         c.URL,
		Module:      "Eks",
		Action:      "GetAccountType",
		Version:     apiv3.Version,
		Timeout:     time.Second * 10,
		Request:     request,
		Response:    response,
		ResultCheck: apiv3.DefaultResultChecker,
	}

	if err := c.httpsExecutor.DoPost(ctx, h); err != nil {
		return "", err
	}

	return response.Response.AccountType, nil
}
