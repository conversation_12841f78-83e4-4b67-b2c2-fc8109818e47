package cauth

import (
	"encoding/json"
	"fmt"
	"time"

	"git.woa.com/kateway/kateway-server/pkg/component/apiv2"
	"git.woa.com/kateway/kateway-server/pkg/component/http"
	"git.woa.com/kateway/kateway-server/pkg/context"
	"git.woa.com/kateway/kateway-server/pkg/errno"
)

const (
	CauthInterfaceGetuseridattr = "consoleLogicalSrv.cauth.getUserIdAttr"
)

type Client struct {
	URL          string
	httpExecutor http.Executor
}

func NewClient(url string, exe http.Executor) *Client {
	return &Client{
		URL:          url,
		httpExecutor: exe,
	}
}

func (c *Client) GetUINByAppId(ctx *context.Context, appId uint64) (string, error) {
	request := apiv2.GetRequest(CauthInterfaceGetuseridattr, CAuthGetUserAttrRequest{
		AppIdArr: []uint64{appId},
	})
	response := &apiv2.Response{}

	h := &http.RequestParam{
		URL:         c.URL,
		Module:      "CAuthV2",
		Action:      CauthInterfaceGetuseridattr,
		Version:     apiv2.Version,
		Timeout:     time.Second * 10,
		Request:     &request,
		Response:    response,
		ResultCheck: apiv2.DefaultResultChecker,
	}

	if err := c.httpExecutor.DoPost(ctx, h); err != nil {
		return "", err
	}

	data, err := json.Marshal(response.Data)
	if err != nil {
		return "", errno.Cause(errno.UNEXCEPTED_INTERNAL_ERROR, fmt.Errorf(err.Error()))
	}

	realResp := CauthGetUserAttrResponse{}
	if err = json.Unmarshal(data, &realResp); err != nil {
		return "", nil
	}
	if _, ok := realResp.AppIdMap[fmt.Sprintf("%d", appId)]; !ok {
		return "", errno.Cause(errno.UNEXCEPTED_INTERNAL_ERROR, fmt.Errorf("query uin failed"))
	}

	return realResp.AppIdMap[fmt.Sprintf("%d", appId)], nil
}
