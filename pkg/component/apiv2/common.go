package apiv2

import (
	"errors"
	"fmt"
	"math/rand"
	"time"

	"github.com/bitly/go-simplejson"

	"git.woa.com/kateway/kateway-server/pkg/component/http"
	"git.woa.com/kateway/kateway-server/pkg/context"
)

const (
	Version = "v2"
)

type Interface struct {
	InterfaceName string      `json:"interfaceName"`
	Para          interface{} `json:"para"`
}

type Request struct {
	Version       int       `json:"version"`
	ComponentName string    `json:"componentName"`
	EventId       int64     `json:"eventId"`
	Timestamp     int64     `json:"Timestamp"`
	User          string    `json:"user"`
	Interface     Interface `json:"interface"`
}

type Response struct {
	Version       int         `json:"version"`
	ComponentName string      `json:"componentName"`
	EventId       int64       `json:"eventId"`
	Timestamp     int64       `json:"timestamp"`
	User          string      `json:"user"`
	Code          int         `json:"returnCode"`
	Msg           string      `json:"returnMessage"`
	Data          interface{} `json:"data"`
}

func GetRequest(name string, para interface{}) Request {
	r := Request{
		Version:       1,
		ComponentName: "docker_dashboard",
		EventId:       rand.Int63n(999999),
		Timestamp:     time.Now().Unix(),
	}
	r.Interface.InterfaceName = name
	r.Interface.Para = para
	return r
}

func DefaultResultChecker(ctx *context.Context, p *http.RequestParam, respJson []byte) error {
	sJson, err := simplejson.NewJson(respJson)
	if err != nil {
		return err
	}

	if sJson.Get("returnCode").MustInt(0) != 0 {
		p.ResultCode = fmt.Sprint(sJson.Get("returnCode").MustInt(0))
	} else {
		p.ResultCode = fmt.Sprint(sJson.Get("returnValue").MustInt(0))
	}
	if sJson.Get("returnMsg").MustString("") != "" {
		p.ResultMsg = sJson.Get("returnMsg").MustString("")
	} else {
		p.ResultMsg = sJson.Get("returnMessage").MustString("")
	}

	if p.ResultCode != "0" {
		return errors.New(p.ResultMsg)
	}

	return nil
}
