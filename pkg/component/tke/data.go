package tke

import (
	"time"

	"git.woa.com/kateway/kateway-server/pkg/component/apiv3"
)

type DescribeClusterSecurityV3Request struct {
	apiv3.Request
	ClusterInstanceId   string `json:"ClusterId"`
	JnsGwEndpointEnable bool   `json:"JnsGwEndpointEnable"`

	Qtoken     string `json:"Token,omitempty"`
	SourceIp   string `json:"ClientIp,omitempty"`
	OperateUin string `json:"-"`
}

type DescribeClusterSecurityV3Response struct {
	Response struct {
		apiv3.Response
		Security
	}
}

type Security struct {
	UserName                string   `json:"UserName"`
	Password                string   `json:"Password"`
	CertificationAuthority  string   `json:"CertificationAuthority"`
	ClusterExternalEndpoint string   `json:"ClusterExternalEndpoint"`
	PgwEndpoint             string   `json:"PgwEndpoint"`
	Domain                  string   `json:"Domain"`
	SecurityPolicy          []string `json:"SecurityPolicy"`
	Kubeconfig              string   `json:"Kubeconfig"`
	JnsGwEndpoint           *string  `json:"JnsGwEndpoint"`
}

type DescribeClustersV3Request struct {
	apiv3.Request
	ClusterInstanceIds []string `json:"ClusterIds"`
}

type DescribeClustersV3Response struct {
	Response struct {
		TotalCount int64     `json:"TotalCount"`
		RequestId  string    `json:"RequestId"`
		Clusters   []Cluster `json:"Clusters"`
	}
}

type Cluster struct {
	ClusterId              string                 `json:"ClusterId"`
	ClusterName            string                 `json:"ClusterName"`
	ClusterDescription     string                 `json:"ClusterDescription"`
	ClusterVersion         string                 `json:"ClusterVersion"`
	ClusterOs              string                 `json:"ClusterOs"`
	ClusterType            string                 `json:"ClusterType"`
	ClusterStatus          string                 `json:"ClusterStatus"`
	ClusterNetworkSettings ClusterNetworkSettings `json:"ClusterNetworkSettings"`
	ClusterNodeNum         uint64                 `json:"ClusterNodeNum"`
	ClusterMaterNodeNum    uint64                 `json:"ClusterMaterNodeNum"`
	ImageId                string                 `json:"ImageId"`
	OsCustomizeType        string                 `json:"OsCustomizeType"`
	Property               string                 `json:"Property"`
	ProjectId              uint64                 `json:"ProjectId"`
	ContainerRuntime       string                 `json:"ContainerRuntime"`
	DeletionProtection     bool                   `json:"DeletionProtection"`
	CreatedTime            time.Time              `json:"CreatedTime"`
}

type ClusterNetworkSettings struct {
	ClusterCIDR               string `json:"ClusterCIDR"`
	IgnoreClusterCIDRConflict bool   `json:"IgnoreClusterCIDRConflict"`

	MaxNodePodNum        uint64 `json:"MaxNodePodNum"` // control cluster pod number and service
	MaxClusterServiceNum uint64 `json:"MaxClusterServiceNum"`
	IPVS                 bool   `json:"Ipvs"`
	CNI                  bool   `json:"Cni"`
	VpcId                string `json:"VpcId"`
	KubeProxyMode        string `json:"KubeProxyMode"`
}
