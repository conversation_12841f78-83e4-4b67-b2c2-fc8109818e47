package tke

import (
	"fmt"
	"strings"

	"git.woa.com/kateway/kateway-server/pkg/component/apiv3"
	"git.woa.com/kateway/kateway-server/pkg/component/http"
	"git.woa.com/kateway/kateway-server/pkg/context"
	"git.woa.com/kateway/kateway-server/pkg/errno"
)

type Client struct {
	url    string
	exec   http.Executor
	region string
}

func NewClient(region string, url string, exec http.Executor) *Client {
	return &Client{
		url:    url,
		exec:   exec,
		region: region,
	}
}

func (c *Client) DescribeCluster(ctx *context.Context, clusterId string) (*Cluster, error) {
	req := &DescribeClustersV3Request{
		Request:            apiv3.GetRequest(ctx, c.region, "DescribeClusters", "2018-05-25"),
		ClusterInstanceIds: []string{clusterId},
	}
	resp := &DescribeClustersV3Response{}

	p := &http.RequestParam{
		Module:      "Dashboard",
		Action:      "DescribeClusters",
		Version:     apiv3.Version,
		URL:         c.url,
		Request:     req,
		Response:    resp,
		ResultCheck: apiv3.DefaultResultChecker,
	}

	if err := c.exec.DoPost(ctx, p); err != nil {
		return nil, err
	}

	if resp.Response.TotalCount == 0 {
		return nil, errno.Cause(errno.CLUSTER_NOT_FOUND_ERROR_V3, fmt.Errorf("cluster not found"))
	}

	return &resp.Response.Clusters[0], nil
}

func (c *Client) DescribeSecurity(ctx *context.Context, clusterId string) (*Security, error) {
	req := &DescribeClusterSecurityV3Request{
		Request:             apiv3.GetRequest(ctx, c.region, "DescribeClusterSecurity", "2018-05-25"),
		ClusterInstanceId:   clusterId,
		JnsGwEndpointEnable: true,
	}

	resp := &DescribeClusterSecurityV3Response{}
	p := &http.RequestParam{
		Module:      "Dashboard",
		Action:      "DescribeClusterSecurity",
		Version:     apiv3.Version,
		URL:         c.url,
		Request:     req,
		Response:    resp,
		ResultCheck: apiv3.DefaultResultChecker,
	}
	if err := c.exec.DoPost(ctx, p); err != nil {
		if strings.Contains(err.Error(), "not found") {
			return nil, errno.Cause(errno.CLUSTER_NOT_FOUND_ERROR_V3, err)
		}
		return nil, err
	}

	return &resp.Response.Security, nil
}
