package http

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net"
	"net/http"
	"net/url"
	"time"

	"github.com/pkg/errors"

	"git.woa.com/kateway/kateway-server/pkg/context"
	"git.woa.com/kateway/kateway-server/pkg/errno"
)

type RequestParam struct {
	Module  string
	Action  string
	Version string

	URL          string
	Timeout      time.Duration
	GetParam     url.Values
	ExtraHeader  http.Header
	Request      interface{}
	RequestData  []byte
	Response     interface{}
	ResponseData []byte

	StatusCode      int
	StatusCodeCheck func(ctx *context.Context, p *RequestParam) error
	// ResultCheck should set ResultCode and ResultMsg
	ResultCheck func(ctx *context.Context, p *RequestParam, respData []byte) error
	ResultCode  string
	ResultMsg   string
	StartTime   time.Time

	SkipResponseLog bool
}

type Executor interface {
	DoPost(ctx *context.Context, p *RequestParam) error
	DoGet(ctx *context.Context, p *RequestParam) error
	DoDelete(ctx *context.Context, p *RequestParam) (err error)
	DoPut(ctx *context.Context, p *RequestParam) (err error)
}

type DefaultExecutor struct {
	cli            *http.Client
	requestWatcher []func(ctx *context.Context, p *RequestParam, err error)
	token          string
}

func NewHTTP(timeout time.Duration) *DefaultExecutor {
	return &DefaultExecutor{
		cli: &http.Client{
			Timeout: timeout,
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
			},
		},
		requestWatcher: []func(ctx *context.Context, p *RequestParam, err error){
			func(ctx *context.Context, p *RequestParam, err error) {
				if err != nil {
					ComponentWrongRequestStatics(p.Module, p.Action, err.Error(), p.Version)
				} else {
					ComponentRequestStatics(p.Module, p.Action, p.StatusCode, p.ResultCode, p.Version, p.StartTime)
				}
			},
		},
	}
}

func MustHTTPS(timeout time.Duration, crt, key, token string) *DefaultExecutor {
	cli, err := getClient(timeout, crt, key)
	if err != nil {
		panic(err)
	}
	d := NewHTTP(timeout)
	d.cli = cli
	d.token = token
	return d
}

func (d *DefaultExecutor) DoGet(ctx *context.Context, p *RequestParam) (err error) {
	p.StartTime = time.Now()
	URL, err := url.Parse(p.URL)
	if err != nil {
		return errno.Cause(errno.COMPONENT_CLINET_COMMON_ERROR, err)
	}

	if len(p.GetParam) != 0 {
		URL.RawQuery = p.GetParam.Encode()
	}
	// fmt.Println(URL.String())

	defer func() {
		if err != nil {
			ctx.Errorf("DoGet : module = %s, action = %s, url = %s ,response = %s",
				p.Module, p.Action, URL.String(), err.Error())
			return
		}
		resStr := string(p.ResponseData)
		if p.SkipResponseLog {
			resStr = "skip log"
		}
		ctx.Debugf("DoGet : module = %s, action = %s, url = %s ,response = %s",
			p.Module, p.Action, URL.String(), resStr)
	}()

	// response data
	resp, err := d.getResponse(ctx, URL.String(), http.MethodGet, p)
	if err != nil {
		d.doRequestWatchers(ctx, p, err)
		return errors.Wrap(err, "get response failed")
	}

	return d.dealResponse(ctx, resp, p)
}

func (d *DefaultExecutor) DoPost(ctx *context.Context, p *RequestParam) (err error) {
	defer func() {
		resStr := string(p.ResponseData)
		if p.SkipResponseLog {
			resStr = "skip log"
		}
		if err != nil {
			ctx.Errorf("DoPost : module = %s, action = %s, url = %s, request = %s, response = %s",
				p.Module, p.Action, p.URL, string(p.RequestData), err.Error())
			return
		}
		ctx.Debugf("DoPost : module = %s, action = %s, url = %s request = %s, response = %s",
			p.Module, p.Action, p.URL, string(p.RequestData), resStr)
	}()

	p.StartTime = time.Now()
	if p.Request != nil {
		p.RequestData, err = json.Marshal(p.Request)
		if err != nil {
			d.doRequestWatchers(ctx, p, err)
			return errno.Cause(errno.UNEXCEPTED_INTERNAL_ERROR, errors.Wrap(err, "marshal request failed"))
		}
	}

	// response data
	resp, err := d.getResponse(ctx, p.URL, http.MethodPost, p)
	if err != nil {
		d.doRequestWatchers(ctx, p, err)
		return errors.Wrap(err, "get response failed")
	}

	return d.dealResponse(ctx, resp, p)
}

func (d *DefaultExecutor) DoDelete(ctx *context.Context, p *RequestParam) (err error) {
	p.StartTime = time.Now()
	URL, err := url.Parse(p.URL)
	if err != nil {
		return errno.Cause(errno.COMPONENT_CLINET_COMMON_ERROR, err)
	}

	if len(p.GetParam) != 0 {
		URL.RawQuery = p.GetParam.Encode()
	}
	fmt.Println(URL.String())

	defer func() {
		if err != nil {
			ctx.Errorf("DoDelete : module = %s, action = %s, url = %s, request = %s, response = %s",
				p.Module, p.Action, p.URL, string(p.RequestData), err.Error())
			return
		}
		resStr := string(p.ResponseData)
		if p.SkipResponseLog {
			resStr = "skip log"
		}
		ctx.Debugf("DoDelete : module = %s, action = %s, url = %s ,response = %s",
			p.Module, p.Action, URL.String(), resStr)
	}()

	// response data
	resp, err := d.getResponse(ctx, URL.String(), http.MethodDelete, p)
	if err != nil {
		d.doRequestWatchers(ctx, p, err)
		return errors.Wrap(err, "get response failed")
	}

	return d.dealResponse(ctx, resp, p)
}

func (d *DefaultExecutor) DoPut(ctx *context.Context, p *RequestParam) (err error) {
	p.StartTime = time.Now()
	URL, err := url.Parse(p.URL)
	if err != nil {
		return errno.Cause(errno.COMPONENT_CLINET_COMMON_ERROR, err)
	}

	if len(p.GetParam) != 0 {
		URL.RawQuery = p.GetParam.Encode()
	}
	fmt.Println(URL.String())

	defer func() {
		if err != nil {
			ctx.Errorf("DoPut : module = %s, action = %s, url = %s ,response = %s",
				p.Module, p.Action, URL.String(), err.Error())
			return
		}
		resStr := string(p.ResponseData)
		if p.SkipResponseLog {
			resStr = "skip log"
		}
		ctx.Debugf("DoPut : module = %s, action = %s, url = %s ,response = %s",
			p.Module, p.Action, URL.String(), resStr)
	}()

	// response data
	resp, err := d.getResponse(ctx, URL.String(), http.MethodPut, p)
	if err != nil {
		d.doRequestWatchers(ctx, p, err)
		return errors.Wrap(err, "get response failed")
	}

	return d.dealResponse(ctx, resp, p)
}

func (d *DefaultExecutor) getResponse(ctx *context.Context, url string, method string, p *RequestParam) (*http.Response, error) {
	req, err := http.NewRequest(method, url, bytes.NewBuffer(p.RequestData))
	if err != nil {
		return nil, errno.Cause(errno.UNEXCEPTED_INTERNAL_ERROR, errors.Wrap(err, "new request failed"))
	}

	if p.Timeout == 0 {
		req = req.WithContext(ctx)
	} else {
		req = req.WithContext(ctx.WithTimeout(p.Timeout))
	}

	for k, v := range p.ExtraHeader {
		req.Header[k] = v
	}
	if d.token != "" {
		req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", d.token))
	}

	resp, err := d.cli.Do(req)
	if err != nil {
		return nil, errno.Cause(errno.UNEXCEPTED_INTERNAL_ERROR, errors.Wrap(err, "client do request failed"))
	}

	return resp, nil
}

func (d *DefaultExecutor) dealResponse(ctx *context.Context, resp *http.Response, p *RequestParam) error {
	defer func() {
		io.Copy(ioutil.Discard, resp.Body)
		_ = resp.Body.Close()
	}()

	// get response obj
	respData, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		d.doRequestWatchers(ctx, p, err)
		return errno.Cause(errno.UNEXCEPTED_INTERNAL_ERROR, fmt.Errorf("read response data failed"))
	}
	p.ResponseData = respData

	// check status code
	p.StatusCode = resp.StatusCode
	if p.StatusCodeCheck == nil {
		if resp.StatusCode != http.StatusOK {
			d.doRequestWatchers(ctx, p, fmt.Errorf("status code is %d", resp.StatusCode))
			return errno.Cause(errno.UNEXCEPTED_INTERNAL_ERROR, fmt.Errorf("status code is %d", resp.StatusCode))
		}
	} else {
		if err := p.StatusCodeCheck(ctx, p); err != nil {
			d.doRequestWatchers(ctx, p, err)
			return err
		}
	}

	if p.Response != nil {
		if err := json.Unmarshal(respData, p.Response); err != nil {
			d.doRequestWatchers(ctx, p, err)
			return errno.Cause(errno.UNEXCEPTED_INTERNAL_ERROR, fmt.Errorf("unmarshal json failed, err: %v, resp: %v", err, string(respData)))
		}
	}

	// do checker
	if p.ResultCheck != nil {
		if err := p.ResultCheck(ctx, p, respData); err != nil {
			d.doRequestWatchers(ctx, p, err)
			return errno.Cause(errno.UNEXCEPTED_INTERNAL_ERROR, fmt.Errorf(err.Error()))
		}
	}

	d.doRequestWatchers(ctx, p, err)
	return nil
}

func (d *DefaultExecutor) AddRequestWatcher(f func(ctx *context.Context, p *RequestParam, err error)) {
	d.requestWatcher = append(d.requestWatcher, f)
}

func (d *DefaultExecutor) doRequestWatchers(ctx *context.Context, p *RequestParam, err error) {
	for _, f := range d.requestWatcher {
		f(ctx, p, err)
	}
}

func getClient(timeout time.Duration, crt, key string) (*http.Client, error) {
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true,
	}

	cert, err := tls.LoadX509KeyPair(crt, key)
	if err != nil {
		return nil, errors.Wrapf(err, "LoadX509KeyPair failed")
	}
	tlsConfig.Certificates = []tls.Certificate{cert}

	transport := &http.Transport{
		Proxy: http.ProxyFromEnvironment,
		DialContext: (&net.Dialer{
			Timeout:   30 * time.Second,
			KeepAlive: 30 * time.Second,
			DualStack: true,
		}).DialContext,
		ForceAttemptHTTP2:     true,
		MaxIdleConns:          100,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
		TLSClientConfig:       tlsConfig,
	}

	client := &http.Client{
		Transport: transport,
	}

	return client, nil
}
