package sts

import (
	"encoding/json"
	"strconv"
	"sync"
	"time"

	"github.com/pkg/errors"
	v3common "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	tchttp "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/http"
	v3profile "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	stsv3 "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/sts/v20180813"

	"git.woa.com/kateway/kateway-server/pkg/util/retry"
)

type Tokener interface {
	GetCredentials() (*Credentials, error)
}

type Credentials struct {
	TmpSecretId  string
	TmpSecretKey string
	ExpiredTime  int64
	Token        string
}

func (c *Credentials) GetCredential() (string, string, string) {
	return c.TmpSecretId, c.TmpSecretKey, c.Token
}

func (c *Credentials) GetSecretId() string {
	return c.TmpSecretId
}

func (c *Credentials) GetSecretKey() string {
	return c.TmpSecretKey
}

func (c *Credentials) GetExpiredTime() int64 {
	return c.ExpiredTime
}

func (c *Credentials) GetToken() string {
	return c.Token
}

type StsTokener struct {
	stsClient   *stsv3.Client
	cred        *Credentials
	ExpiredTime int64
	// MarginTime: we wont't update token just in expire time accurately，
	// we just update it on 'MarginTime' ahead.
	MarginTime      time.Duration
	TokenDuration   time.Duration
	RoleArn         string
	RoleSessionName string
	UserUin         string
	sync.RWMutex
}

func NewStsTokener(secretID, secretKey, roleArn, userUin, sessionName string, duration, marginTime time.Duration) *StsTokener {
	cred := v3common.NewCredential(secretID, secretKey)
	cpf := v3profile.NewClientProfile()
	cpf.HttpProfile.RootDomain = "internal.tencentcloudapi.com"

	stsClient, _ := stsv3.NewClient(cred, "ap-guangzhou", cpf)

	return &StsTokener{stsClient: stsClient, MarginTime: marginTime, RoleSessionName: sessionName, TokenDuration: duration, RoleArn: roleArn, UserUin: userUin}
}

func (sts *StsTokener) GetCredentials() (*Credentials, error) {
	expire := sts.isTokenExpire()
	if !expire {
		return sts.cred, nil
	}
	err := sts.updateToken()
	if err != nil {
		return nil, err
	}
	return sts.cred, nil
}

func (sts *StsTokener) isTokenExpire() bool {
	sts.RLock()
	defer sts.RUnlock()

	return time.Now().Add(sts.MarginTime).After(time.Unix(sts.ExpiredTime, 0))
}

func (sts *StsTokener) updateToken() error {

	request := tchttp.NewCommonRequest("sts", stsv3.APIVersion, "AssumeRole")
	params := map[string]interface{}{
		"RoleArn":         sts.RoleArn,
		"RoleSessionName": sts.RoleSessionName,
		"DurationSeconds": uint64(sts.TokenDuration / time.Second),
	}
	if sts.UserUin != "" {
		uinInt, err := strconv.ParseInt(sts.UserUin, 10, 64)
		if err != nil {
			return errors.Wrap(err, "uin is not integer")
		}
		params["UserUin"] = uinInt
	}

	err := request.SetActionParameters(params)
	if err != nil {
		return err
	}

	response := tchttp.NewCommonResponse()
	if err := retry.DoIfNetError0(func() error {
		return sts.stsClient.Send(request, response)
	}, 1*time.Second); err != nil {
		return err
	}
	assumeResp := stsv3.NewAssumeRoleResponse()

	if err = json.Unmarshal(response.GetBody(), assumeResp); err != nil {
		return err
	}

	if assumeResp.Response != nil && assumeResp.Response.Credentials != nil {
		sts.Lock()
		defer sts.Unlock()
		cred := &Credentials{
			TmpSecretId:  *assumeResp.Response.Credentials.TmpSecretId,
			TmpSecretKey: *assumeResp.Response.Credentials.TmpSecretKey,
			Token:        *assumeResp.Response.Credentials.Token,
			ExpiredTime:  *assumeResp.Response.ExpiredTime,
		}
		sts.cred = cred
		sts.ExpiredTime = *assumeResp.Response.ExpiredTime
	}
	return nil
}
