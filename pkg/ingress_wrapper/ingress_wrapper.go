package ingress_wrapper

import (
	"context"

	"github.com/hashicorp/go-version"
	v1 "k8s.io/api/core/v1"
	extensionsv1beta1 "k8s.io/api/extensions/v1beta1"
	networkingv1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/client-go/kubernetes"

	cluster2 "git.woa.com/kateway/kateway-server/pkg/service/cluster"
)

type IngressWrapper interface {
	Namespace() string
	Name() string
	UID() string
	IngressClassName() *string
	Annotations() map[string]string
	RuntimeObject() runtime.Object
	TLS() []IngressTLSWrapper
	Rules() []IngressRuleWrapper
	StatusLoadBalancer() v1.LoadBalancerStatus

	UpdateAnnotation(kubeClient *kubernetes.Clientset, update map[string]string) error
	DeleteAnnotation(kubeClient *kubernetes.Clientset, update []string) error
	UpdateLoadBalancerStatus(kubeClient *kubernetes.Clientset, loadBalancerStatus v1.LoadBalancerStatus) error
}

type IngressTLSWrapper interface {
	Hosts() []string
	SecretName() string
}

type IngressRuleWrapper interface {
	Host() string
	HTTPPaths() []HTTPIngressPathWrapper
}

type HTTPIngressPathWrapper interface {
	Path() string
	PathType() *string
	Backend() IngressBackendWrapper
}

type IngressBackendWrapper interface {
	ServiceName() string
	ServicePort() intstr.IntOrString
}

func IngressListerClientWrapper(clientsSet *cluster2.ClientsSet, namespace string, selector labels.Selector) ([]IngressWrapper, error) {
	var err error
	semverBase, _ := version.NewSemver("v1.22") // Kubernetes do not support extensions crd group from v1.22
	serverVersion, err := clientsSet.K8sCli.Discovery().ServerVersion()
	if err != nil {
		return nil, err
	}
	serverSemver, err := version.NewSemver(serverVersion.GitVersion)
	if err != nil {
		return nil, err
	}
	if serverSemver.Compare(semverBase) < 0 {
		var ingresses *extensionsv1beta1.IngressList
		if namespace != v1.NamespaceAll {
			if ingresses, err = clientsSet.K8sCli.ExtensionsV1beta1().Ingresses(namespace).List(context.Background(), metav1.ListOptions{
				LabelSelector: selector.String(),
			}); err != nil {
				return nil, err
			}
		} else {
			if ingresses, err = clientsSet.K8sCli.ExtensionsV1beta1().Ingresses(v1.NamespaceAll).List(context.Background(), metav1.ListOptions{
				LabelSelector: selector.String(),
			}); err != nil {
				return nil, err
			}
		}
		result := make([]IngressWrapper, len(ingresses.Items))
		for index := range ingresses.Items {
			result[index] = NewIngressWrapperExtensions(&ingresses.Items[index])
		}
		return result, nil
	} else {
		var ingresses *networkingv1.IngressList
		if namespace != v1.NamespaceAll {
			if ingresses, err = clientsSet.K8sCli.NetworkingV1().Ingresses(namespace).List(context.Background(), metav1.ListOptions{
				LabelSelector: selector.String(),
			}); err != nil {
				return nil, err
			}
		} else {
			if ingresses, err = clientsSet.K8sCli.NetworkingV1().Ingresses(v1.NamespaceAll).List(context.Background(), metav1.ListOptions{
				LabelSelector: selector.String(),
			}); err != nil {
				return nil, err
			}
		}
		result := make([]IngressWrapper, len(ingresses.Items))
		for index := range ingresses.Items {
			result[index] = NewIngressWrapperNetworking(&ingresses.Items[index])
		}
		return result, nil
	}
}
