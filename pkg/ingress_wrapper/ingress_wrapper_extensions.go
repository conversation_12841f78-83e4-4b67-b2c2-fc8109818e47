package ingress_wrapper

import (
	"context"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	v1 "k8s.io/api/core/v1"
	extensions "k8s.io/api/extensions/v1beta1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/client-go/kubernetes"
)

type IngressWrapperExtensions struct {
	extensionsIngress *extensions.Ingress
}

type IngressTLSWrapperExtensions struct {
	extensionsIngress *extensions.IngressTLS
}

type IngressRuleWrapperExtensions struct {
	extensionsIngress *extensions.IngressRule
}

type HTTPIngressPathWrapperExtensions struct {
	extensionsIngress *extensions.HTTPIngressPath
}

type IngressBackendWrapperExtensions struct {
	extensionsIngress *extensions.IngressBackend
}

func NewIngressWrapperExtensions(ingress *extensions.Ingress) IngressWrapper {
	return &IngressWrapperExtensions{
		extensionsIngress: ingress,
	}
}

func NewIngressWrapperExtensionsList(ingresses []*extensions.Ingress) []IngressWrapper {
	ingressWrapperExtensionss := make([]IngressWrapper, len(ingresses))
	for index, ingress := range ingresses {
		ingressWrapperExtensionss[index] = NewIngressWrapperExtensions(ingress)
	}
	return ingressWrapperExtensionss
}

func (this *IngressWrapperExtensions) UpdateAnnotation(kubeClient *kubernetes.Clientset, update map[string]string) error {
	currentIngress, err := kubeClient.ExtensionsV1beta1().Ingresses(this.Namespace()).Get(context.Background(), this.Name(), metav1.GetOptions{})
	if err != nil {
		return err
	}
	if currentIngress.Annotations == nil {
		currentIngress.Annotations = make(map[string]string, 0)
	}
	isUpdated := false
	for key, value := range update {
		if oldvalue, exist := currentIngress.Annotations[key]; !exist || oldvalue != value {
			currentIngress.Annotations[key] = value
			isUpdated = true
		}
	}
	if isUpdated {
		if _, err := kubeClient.ExtensionsV1beta1().Ingresses(this.Namespace()).Update(context.Background(), currentIngress, metav1.UpdateOptions{}); err != nil {
			return err
		}
	}
	return nil
}

func (this *IngressWrapperExtensions) DeleteAnnotation(kubeClient *kubernetes.Clientset, update []string) error {
	currentIngress, err := kubeClient.ExtensionsV1beta1().Ingresses(this.Namespace()).Get(context.Background(), this.Name(), metav1.GetOptions{})
	if err != nil {
		return err
	}
	if currentIngress.Annotations == nil {
		currentIngress.Annotations = make(map[string]string, 0)
	}
	isUpdated := false
	for _, key := range update {
		if _, exist := currentIngress.Annotations[key]; exist {
			delete(currentIngress.Annotations, key)
			isUpdated = true
		}
	}
	if isUpdated {
		if _, err := kubeClient.ExtensionsV1beta1().Ingresses(this.Namespace()).Update(context.Background(), currentIngress, metav1.UpdateOptions{}); err != nil {
			return err
		}
	}
	return nil
}

func (this *IngressWrapperExtensions) UpdateLoadBalancerStatus(kubeClient *kubernetes.Clientset, loadBalancerStatus v1.LoadBalancerStatus) error {
	currentIngress, err := kubeClient.ExtensionsV1beta1().Ingresses(this.Namespace()).Get(context.Background(), this.Name(), metav1.GetOptions{})
	if err != nil {
		return err
	}
	currentIngress.Status = extensions.IngressStatus{
		LoadBalancer: loadBalancerStatus,
	}
	if _, err := kubeClient.ExtensionsV1beta1().Ingresses(this.Namespace()).UpdateStatus(context.Background(), currentIngress, metav1.UpdateOptions{}); err != nil {
		return err
	}
	return nil
}

func (this *IngressWrapperExtensions) RuntimeObject() runtime.Object {
	return this.extensionsIngress
}

func (this *IngressWrapperExtensions) Namespace() string {
	return this.extensionsIngress.Namespace
}

func (this *IngressWrapperExtensions) Name() string {
	return this.extensionsIngress.Name
}

func (this *IngressWrapperExtensions) Annotations() map[string]string {
	return this.extensionsIngress.Annotations
}

func (this *IngressWrapperExtensions) IngressClassName() *string {
	return this.extensionsIngress.Spec.IngressClassName
}

func (this *IngressWrapperExtensions) TLS() []IngressTLSWrapper {
	ingressTLSWrapperExtensions := make([]IngressTLSWrapper, len(this.extensionsIngress.Spec.TLS))
	for index := range this.extensionsIngress.Spec.TLS {
		ingressTLSWrapperExtensions[index] = &IngressTLSWrapperExtensions{
			extensionsIngress: &this.extensionsIngress.Spec.TLS[index],
		}
	}
	return ingressTLSWrapperExtensions
}

func (this *IngressTLSWrapperExtensions) Hosts() []string {
	return this.extensionsIngress.Hosts
}

func (this *IngressTLSWrapperExtensions) SecretName() string {
	return this.extensionsIngress.SecretName
}

func (this *IngressWrapperExtensions) Rules() []IngressRuleWrapper {
	ingressRuleWrapperExtensionss := make([]IngressRuleWrapper, len(this.extensionsIngress.Spec.Rules))
	for index := range this.extensionsIngress.Spec.Rules {
		ingressRuleWrapperExtensionss[index] = &IngressRuleWrapperExtensions{
			extensionsIngress: &this.extensionsIngress.Spec.Rules[index],
		}
	}
	return ingressRuleWrapperExtensionss
}

func (this *IngressRuleWrapperExtensions) Host() string {
	return this.extensionsIngress.Host
}

func (this *IngressRuleWrapperExtensions) HTTPPaths() []HTTPIngressPathWrapper {
	httpIngressPathWrapperExtensionss := make([]HTTPIngressPathWrapper, 0)
	if this.extensionsIngress.HTTP == nil {
		return httpIngressPathWrapperExtensionss
	}
	httpIngressPathWrapperExtensionss = make([]HTTPIngressPathWrapper, len(this.extensionsIngress.HTTP.Paths))
	for index := range this.extensionsIngress.HTTP.Paths {
		httpIngressPathWrapperExtensionss[index] = &HTTPIngressPathWrapperExtensions{
			extensionsIngress: &this.extensionsIngress.HTTP.Paths[index],
		}
	}
	return httpIngressPathWrapperExtensionss
}

func (this *HTTPIngressPathWrapperExtensions) Path() string {
	return this.extensionsIngress.Path
}

func (this *HTTPIngressPathWrapperExtensions) PathType() *string {
	if this.extensionsIngress.PathType == nil {
		return nil
	}
	return common.StringPtr(string(*this.extensionsIngress.PathType))
}

func (this *HTTPIngressPathWrapperExtensions) Backend() IngressBackendWrapper {
	return &IngressBackendWrapperExtensions{
		extensionsIngress: &this.extensionsIngress.Backend,
	}
}

func (this *IngressBackendWrapperExtensions) ServiceName() string {
	return this.extensionsIngress.ServiceName
}

func (this *IngressBackendWrapperExtensions) ServicePort() intstr.IntOrString {
	return this.extensionsIngress.ServicePort
}

func (this *IngressWrapperExtensions) StatusLoadBalancer() v1.LoadBalancerStatus {
	return this.extensionsIngress.Status.LoadBalancer
}

func (this *IngressWrapperExtensions) UID() string {
	return string(this.extensionsIngress.UID)
}
