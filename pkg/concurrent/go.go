package concurrent

type Option func(*options)

type Panic<PERSON><PERSON>ler func(stack string)

type options struct {
	panicHandler PanicHandler
	crash        bool
}

func WithPanic<PERSON>andler(h PanicHandler) Option {
	return func(o *options) {
		o.panicHandler = h
	}
}

func WithCrash() Option {
	return func(o *options) {
		o.crash = true
	}
}

func Go(f func(), opts ...Option) {
	go Wrap(f, opts...)()
}

func Wrap(f func(), opts ...Option) func() {
	options := new(options)
	for _, o := range opts {
		o(options)
	}

	return func() {
		defer func() {
			if r := recover(); r != nil {
				if options.panicHandler != nil {
					options.panicHandler(GetPanicStackTrace(r))
				}

				if options.crash {
					panic(r)
				}
			}
		}()

		f()
	}
}
