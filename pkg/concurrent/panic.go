package concurrent

import (
	"errors"
	"fmt"
	"net/http"
	"runtime"
)

func GetPanicStackTrace(r interface{}) string {
	if e, ok := r.(error); ok && errors.Is(e, http.ErrAbortHandler) {
		// honor the http.ErrAbortHandler sentinel panic value:
		//   ErrAbortHandler is a sentinel panic value to abort a handler.
		//   While any panic from ServeHTTP aborts the response to the client,
		//   panicking with ErrAbortHandler also suppresses logging of a stack trace to the server's error log.
		return ""
	}

	// Same as stdlib http server code. Manually allocate stack trace buffer size
	// to prevent excessively large logs
	const size = 64 << 10
	stacktrace := make([]byte, size)
	stacktrace = stacktrace[:runtime.Stack(stacktrace, false)]
	return fmt.Sprintf("Observed a panic: %#v (%v)\n%s", r, r, stacktrace)
}
