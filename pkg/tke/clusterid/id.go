package clusterid

import (
	"errors"
	"fmt"
	"strings"
)

// 将整型转换成字符串
// 输入
//  v 整型
//  base 转换的进制
//  buf 保存返回的字符串
//  len buf的长度
// 输出
//  无

// 求幂
// 输入
//
//	x 幂底
//	y 幂倍
//
// 输出
//
//	返回幂
//
// static uint64_t iPow(unsigned char x, unsigned char y)
func iPow(x, y uint8) uint64 {
	var ret uint64 = 1
	var i uint8
	for i = 0; i < y; i++ {
		ret = ret * uint64(x)
	}
	return ret
}

// 将字符串转换成整型
// 输入
//
//	buf 字符串
//	len 字符串长度
//	base 转换进制
//
// 输出
//
//	返回整型
//
// static uint64_t str_2_int(const char * buf, unsigned char len, unsigned char base)
func str2Int(buf []byte, len uint8, base uint8) uint64 {
	var ret uint64 = 0
	var m, i uint8

	for i = 0; i < len-1; i++ {
		m = buf[len-2-i]
		if m >= '0' && m <= '9' {
			m = m - '0'
		} else {
			m = m - 'a' + 10
		}
		ret += iPow(base, i) * uint64(m)
	}
	return ret
}

// 将加密字符串转换成整型
// 输入
//
//	buf 字符串
//	len 字符串长度
//	retLen 保留bit位
//
// 输出
//
//	返回转换后的整型
//
// static uint64_t encode_password(const char * buf, unsigned char len, unsigned char retLen)
func encodePassword(buf []byte, len uint8, retLen uint8) uint64 {
	var ret uint64 = 0
	var i uint8
	for i = 0; i < len-1; i++ {
		ret |= uint64(buf[len-2-i]) << (8 * i)
	}
	if retLen > 0 {
		ret &= ((uint64)(1) << retLen) - 1
	}
	return ret
}

// dxy ...
type dxy struct {
	d int64
	x int64
	y int64
}

// pair ...
type pair struct {
	x uint64 //id
	y uint64 //regionID
}

// 参考算法导论 欧几里德算法的推广形式
// d = ax + by
// 根据欧几里德算法推广形式计算出d,x,y
// 输入
//
//	a,b
//
// 输出
//
//	d,x,y
//
// static struct dxy extended_euclid(int64_t a, int64_t b)
func extendedEuclid(a, b int64) dxy {
	if b <= 0 {
		ret := dxy{a, 1, 0}
		return ret
	}
	tmp := extendedEuclid(b, a%b)
	ret := dxy{tmp.d, tmp.y, tmp.x - (a/b)*tmp.y}
	return ret
}

// 参考算法导论 推论31.26
// 当gcd(a, b) = 1, 则方程ax + ny = 1有唯一解,这个唯一解就是
// extended_euclid(a, b)算法得到的x
// 输入
//
//	a,b
//
// 输出
//
//	x
//
// static int64_t inverse(int64_t a, int64_t b)
func inverse(a, b int64) int64 {
	tmp := extendedEuclid(a, b)
	if tmp.x < 0 {
		tmp.x = tmp.x%b + b
	}
	return tmp.x
}

// 获取val从pos位置长度为len的bit
// 输入
//  val 整型
//  pos 起始位置
//  len 长度
// 输出
//  返回截取的bit
/**static uint64_t split_bit(uint64_t val, unsigned char pos, unsigned char len)
 **/
func splitBit(val uint64, pos uint8, len uint8) uint64 {
	return (val >> pos) & ((1 << len) - 1)
}

// 返回postfix插入prefix的平均步长
// 输入
//  prefixLen 前缀长度
//  postfixLen 后缀长度
// 输出
//  回postfix插入prefix的平均步长
/**static unsigned char step_len(unsigned char prefixLen, unsigned char postfixLen)
 **/

func stepLen(prefixLen, postfixLen uint8) uint8 {
	return prefixLen / postfixLen
}

// 回postfix入prefix的步长
// 输入
//  step_idx 步骤游标
//  step_len 平均步长
//  prefixLen 前缀长度
//  postfixLen 后缀长度
// 输出
//  返回postfix插入prefix的步长
/**static unsigned char step_postfixLen(unsigned char step_idx, unsigned char step_len,
		 unsigned char prefixLen, unsigned char postfixLen)
 **/
func stepPostfixLen(stepIdx, stepLen, prefixLen, postfixLen uint8) uint8 {
	if stepIdx == (postfixLen - 1) {
		return prefixLen - (stepIdx * stepLen)
	} else {
		return stepLen
	}
}

// 取出postfix以及prefix
// 输入
//  val 打散后的整型
//  prefixLen 前缀长度
//  postfixLen 后缀长度
// 输出
// 返回前缀、后缀
/**static pair split_prefix_postfix(uint64_t val, unsigned char prefixLen,
		 unsigned char postfixLen)
 **/
func splitPrefixPostfix(val uint64, prefixLen uint8,
	postfixLen uint8) pair {
	var prefix uint64 = 0
	var postfix uint64 = 0
	var i uint8
	step := stepLen(prefixLen, postfixLen)
	for i = 0; i < postfixLen; i++ {
		postfix |= splitBit(val, (step+1)*i, 1) << i
		prefix |= splitBit(val, (step+1)*i+1, stepPostfixLen(i, step, prefixLen, postfixLen)) << (step * i)
	}
	ret := pair{prefix, postfix}
	return ret
}

// decode ...
func decode(buf []byte, prefixLen uint8, postfixLen uint8,
	password []byte, base uint8, prime uint64) pair {
	iPassword := encodePassword(password, uint8(len(password)+1), prefixLen+postfixLen)
	ret := str2Int(buf, uint8(len(buf)+1), base)
	ret ^= iPassword

	pRet := splitPrefixPostfix(ret, prefixLen, postfixLen)
	pRet.x = uint64(inverse(int64(pRet.x), int64(prime)))
	return pRet
}

// 前缀长度映射信息
var prefixLenMap = [3]uint8{32, 36, 40}

//static const unsigned char prefixLenMap[] = {32, 36, 40};

// 后缀长度映射信息
// static const unsigned char postfixLenMap[] = {9, 10, 11};
var postfixLenMap = [3]uint8{9, 10, 11}

// 素数映射信息
// static const uint64_t primeMap[] = {4294967029, 68719476503, 1000000005721};
var primeMap = [3]uint64{4294967029, 68719476503, 1000000005721}

// 转换进制
// static unsigned char idBase = 36;
var idBase uint8 = 36

// ID分隔符
// static const char idSEPARATOR = '-';
var idSEPARATOR byte = '-'

// func DecodeId(buf []byte, password []byte, ret *pair) int {
func decodeID(uID string, password string) (x uint64, y uint64, err error) {
	p := pair{}

	index := strings.IndexByte(uID, idSEPARATOR)
	if index < 0 {
		return 0, 0, fmt.Errorf("failed to decode id:%s, ret:%v", uID, -1)
	}
	buf := []byte(uID)

	idBuf := buf[index+1:]
	var idLen = len(idBuf)
	if idLen < 8 || idLen > 10 {
		return 0, 0, fmt.Errorf("id %s len after - should between 8 and 10", uID)
	}
	var prefixLen = prefixLenMap[idLen-8]
	var postfixLen = postfixLenMap[idLen-8]
	var prime = primeMap[idLen-8]
	p = decode(idBuf, prefixLen, postfixLen, []byte(password), idBase, prime)
	return p.x, p.y, nil
}

const clsPassword = "dcoker-cls"

// DecodeTKE ...
func DecodeTKE(clusterID string) (id uint64, regionID uint64, err error) {
	return decodeID(clusterID, clsPassword)
}

/*
Background:

The id.GenClusterID actually use low **9** bits of region id.

History:

1. In order to use the prefix cls- for products uniformly.
EKS regions already online add 1000 to differentiate from tke cluster.
Actually only add 488!

The biggest region `th` id is **23** at 20200602.

old eks 00000001 11101000 488

2. To avoid future conflicts between products.
Use the high two digits of region id to distinguish different product types

the biggest region is 127(0x7f)

tke     00 00000000 00000000 0
eks     01 00000000 10000000 128
edge    10 00000001 00000000 256
*/

// DecodeEKS ...
func DecodeEKS(clusterID string) (uint64, uint64, error) {
	id, regionID, err := DecodeTKE(clusterID)
	if regionID >= 128+24 && regionID <= 128+127 {
		regionID -= 128
	} else if regionID >= 488+1 && regionID <= 488+23 {
		regionID -= 488
	} else {
		return 0, 0, errors.New("invalid eks region id ")
	}

	return id, regionID, err
}

func Decode(clusterID string) (clusterType string, regionID uint64, err error) {
	_, regionID, err = DecodeTKE(clusterID)
	if err != nil {
		return
	}
	if regionID < 128 {
		clusterType = "tke"
	} else if regionID < 256 {
		clusterType = "eks"
	} else if regionID < 384 {
		clusterType = "edge"
	} else if regionID < 488 {
		clusterType = "tdcc"
	} else {
		clusterType = "eks"
	}
	if clusterType == "eks" {
		_, regionID, err = DecodeEKS(clusterID)
	}
	return
}
