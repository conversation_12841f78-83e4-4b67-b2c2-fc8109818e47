package clusterid

import (
	"testing"
)

func TestDecodeID(t *testing.T) {
	tests := []struct {
		clusterID           string
		expectedClusterType string
		expectedRegionID    uint64
	}{
		{"cls-c6s0uiey", "tke", 1},
		{"cls-ku9sizjw", "tke", 1},
		{"cls-khe5wdp0", "eks", 1},
		{"cls-o5qla0hr", "tke", 4},
		{"cls-1q4k02n3", "eks", 4},
	}
	for _, tt := range tests {
		t.Run(tt.clusterID, func(t *testing.T) {
			clusterType, regionID, err := Decode(tt.clusterID)
			if err != nil && tt.expectedClusterType != "" {
				t.<PERSON>rf("unexpected error: %v", err)
			}
			if clusterType != tt.expectedClusterType {
				t.<PERSON><PERSON><PERSON>("expected clusterType %s, got %s", tt.expectedClusterType, clusterType)
			}
			if regionID != tt.expectedRegionID {
				t.<PERSON><PERSON>("expected regionID %d, got %d", tt.expectedRegionID, regionID)
			}
		})
	}
}
