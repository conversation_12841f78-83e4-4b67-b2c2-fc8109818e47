package store

import (
	"git.woa.com/kateway/kateway-server/pkg/context"
	"git.woa.com/kateway/kateway-server/pkg/models"
)

type NodeQueryOption struct {
	ID           uint64
	ClusterID    string
	AppID        uint64
	VmInstanceID []string
	LanIp        []string
	NotLanIp     bool
	// if LifeState and NotInLifeState both empty
	// deleting , deleted ,removing, removed life state will be add to NotInLifeState
	LifeState      []string
	NotInLifeState []string
	Role           []uint
	NodePoolId     string
	NodePoolState  string
}

type NodeStore interface {
	GetNodes(ctx *context.Context, option NodeQueryOption) ([]*models.VmInstance, error)
	GetNode(ctx *context.Context, uInstanceID string) (*models.VmInstance, error)
}
