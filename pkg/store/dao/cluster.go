package dao

import (
	"github.com/jinzhu/gorm"
	"github.com/mitchellh/mapstructure"
	"github.com/pkg/errors"

	"git.woa.com/kateway/kateway-server/pkg/models"
	"git.woa.com/kateway/kateway-server/pkg/util/dbhelper"
	"git.woa.com/kateway/kateway-server/pkg/util/typeutil"
)

type ClusterStore struct {
	db *gorm.DB
}

func NewClusterStore(db *gorm.DB) *ClusterStore {
	s := &ClusterStore{
		db: db,
	}
	return s
}

func (s *ClusterStore) ListCluster() ([]models.Cluster, error) {
	var clusters []models.Cluster
	if err := s.db.Where("lifeState NOT IN (?)", []string{"abnormal", "idle", "deleted", "deleting", "charge_destroyed"}).Find(&clusters).Error; err != nil {
		return nil, err
	}
	return clusters, nil
}

func (s *ClusterStore) GetCluster(clusterID string) (*models.Cluster, error) {
	if clusterID == "" {
		return nil, errors.New("cluster id is empty")
	}
	cluster := &models.Cluster{}
	if err := s.db.Where(&models.Cluster{ClusterInstanceId: clusterID}).First(cluster).Error; err != nil {
		return nil, err
	}
	return cluster, nil
}

func (s *ClusterStore) GetClustersBySqlSelector(sqlDetail string) ([]models.Cluster, error) {
	clusters := make([]models.Cluster, 0, 0)

	ret := s.db.Raw(sqlDetail).Scan(&clusters)
	if ret.Error != nil {
		return []models.Cluster{}, ret.Error
	}
	return clusters, nil
}

func (s *ClusterStore) UpdateCluster(c *models.Cluster, value map[string]interface{}) (int64, error) {
	data := models.Cluster{}
	err := mapstructure.Decode(value, &data)
	if err != nil {
		return 0, errors.Wrapf(err, "mapstructure.Decode")
	}

	valueMap, err := dbhelper.ToGormDBMap(data, typeutil.MapKeySlice(value))
	if err != nil {
		return 0, errors.Wrap(err, "get gorm fields map failed")
	}

	result := s.db.Where("id = ?", c.ID).
		Model(&models.Cluster{}).Update(valueMap)
	if result.Error != nil {
		return 0, errors.Wrap(result.Error, "update cluster failed")
	}

	err = mapstructure.Decode(value, c)
	if err != nil {
		return 0, errors.Wrapf(err, "mapstructure.Decode")
	}

	return result.RowsAffected, nil
}
