package dao

import (
	"github.com/jinzhu/gorm"
	"github.com/pkg/errors"

	"git.woa.com/kateway/kateway-server/pkg/models"
)

type ClusterSecurityStore struct {
	db *gorm.DB
}

func NewClusterSecurityStore(db *gorm.DB) *ClusterSecurityStore {
	s := &ClusterSecurityStore{
		db: db,
	}
	return s
}

func (s *ClusterSecurityStore) GetClusterSecurity(clusterID string) (*models.ClusterSecurity, error) {
	if clusterID == "" {
		return nil, errors.New("cluster id is empty")
	}
	clusterSecurity := &models.ClusterSecurity{}
	if err := s.db.Where(&models.ClusterSecurity{ClusterInstanceId: clusterID}).First(clusterSecurity).Error; err != nil {
		return nil, err
	}
	return clusterSecurity, nil
}
