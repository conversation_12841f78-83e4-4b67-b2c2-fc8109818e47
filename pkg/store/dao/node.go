package dao

import (
	"github.com/jinzhu/gorm"
	"github.com/pkg/errors"

	"git.woa.com/kateway/kateway-server/pkg/context"
	"git.woa.com/kateway/kateway-server/pkg/models"
	"git.woa.com/kateway/kateway-server/pkg/store"
)

type NodeStore struct {
	db *gorm.DB
}

func NewNodeStore(db *gorm.DB) *NodeStore {
	s := &NodeStore{
		db: db,
	}
	return s
}

// GetNode return target node with appID and uInstanceID
func (s *NodeStore) GetNode(ctx *context.Context, uInstanceID string) (*models.VmInstance, error) {
	if uInstanceID == "" {
		return nil, errors.New("uInstanceID is empty")
	}
	ret := &models.VmInstance{}
	if err := s.db.Where(&models.VmInstance{VmInstanceId: &uInstanceID}).First(ret).Error; err != nil {
		return nil, err
	}
	return ret, nil
}

// GetNodes return nodes according to option
func (s *NodeStore) GetNodes(ctx *context.Context, option store.NodeQueryOption) ([]*models.VmInstance, error) {
	db := s.getNodeQueryDB(option)
	ret := make([]*models.VmInstance, 0)
	err := db.Find(&ret).Error
	if err != nil {
		return nil, err
	}
	return ret, nil
}

func (s *NodeStore) getNodeQueryDB(option store.NodeQueryOption) *gorm.DB {
	db := s.db
	if option.ID != 0 {
		db = db.Where("id = ?", option.ID)
	}

	if option.AppID != 0 {
		db = db.Where("appId = ?", option.AppID)
	}

	if option.ClusterID != "" {
		db = db.Where("clusterInstanceId = ?", option.ClusterID)
	}

	if len(option.LanIp) != 0 {
		db = db.Where("lanIp IN (?)", option.LanIp)
	}

	if len(option.VmInstanceID) != 0 {
		db = db.Where("uInstanceId IN (?)", option.VmInstanceID)
	}

	if len(option.LifeState) != 0 {
		db = db.Where("lifeState IN (?)", option.LifeState)
	}

	if len(option.NotInLifeState) != 0 {
		db = db.Where("lifeState not IN (?)", option.NotInLifeState)
	}

	if len(option.Role) != 0 {
		db = db.Where("isMaster IN (?)", option.Role)
	}

	if option.NodePoolState != "" {
		db = db.Where("nodePoolState = ?", option.NodePoolState)
	}

	if option.NodePoolId != "" {
		db = db.Where("nodePoolId = ?", option.NodePoolId)
	}

	if option.NotLanIp {
		db = db.Where("lanIp IS NULL")
	}

	return db
}
