package dao

import (
	"github.com/jinzhu/gorm"
	"github.com/pkg/errors"

	store2 "git.woa.com/kateway/kateway-server/pkg/store"
)

type Store struct {
	db *gorm.DB
	*ClusterStore
	*ClusterSecurityStore
	*NodeStore
}

type StoreConfig struct {
	// target db type
	// DB_TYPE_SQLITE3 "sqlite3"
	// DB_TYPE_MYSQL   "mysql"
	DBType string

	// target db arg
	// DB_TYPE_SQLITE3 "/tmp/gorm.db"
	// DB_TYPE_MYSQL   "user:password@/dbname?charset=utf8&parseTime=True&loc=Local"
	DBArg interface{}
}

var _ store2.Store = &Store{}

func NewStore(config *StoreConfig) (*Store, error) {
	var err error
	s := &Store{}
	if s.db, err = gorm.Open(config.DBType, config.DBArg); err != nil {
		return nil, errors.Wrap(err, "failed to open db")
	}
	s.ClusterStore = NewClusterStore(s.db)
	s.ClusterSecurityStore = NewClusterSecurityStore(s.db)
	s.NodeStore = NewNodeStore(s.db)
	return s, nil
}
