package context

import (
	goctx "context"
	"testing"
	"time"

	"github.com/pkg/errors"

	"git.woa.com/kateway/kateway-server/pkg/logger/logrus"
)

func TestNew(t *testing.T) {
	ctx, cancel := NewNoData(logrus.New()).WithCancel()
	if !ctx.IsEmptyData() {
		t.Fatalf("ctx should be a empty data context ")
	}

	ctx2 := New(1, "1", "2", "123", logrus.New()).
		WithContext(ctx).
		WithRegion("cq").WithLabels(map[string]interface{}{
		"test": "xxxx",
	})
	ctx2.Infof("test")

	if ctx2.AppID() != 1 {
		t.Fatalf("wrong appID")
	}

	if ctx2.Uin() != "1" {
		t.Fatalf("wrong uin")
	}

	if ctx2.SubUin() != "2" {
		t.<PERSON>al<PERSON>("wrong subUin")
	}

	if ctx2.RequestID() != "123" {
		t.Fatalf("wrong requestID")
	}

	rg := ctx2.Region()
	if rg != "cq" {
		t.Fatalf("wrong region")
	}

	cancel()
	if err := ctx2.Err(); err != goctx.Canceled {
		t.Fatalf("cancel failed")
	}
}

func TestContext_WithTimeout(t *testing.T) {
	ctx := NewNoData(logrus.New()).WithTimeout(time.Second)
	time.Sleep(time.Second * 2)
	if ctx.Err() != goctx.DeadlineExceeded {
		t.Fatalf("should timeout")
	}
}

func TestIsCanceledErr(t *testing.T) {
	ctx, cancel := NewNoData(logrus.New()).WithCancel()
	cancel()
	err := errors.Wrap(ctx.Err(), "test")

	if !IsCanceledErr(err) {
		t.Fatalf("should be canceled err")
	}
}

func TestIsDeadLineErr(t *testing.T) {
	ctx := NewNoData(logrus.New()).WithTimeout(0)
	<-ctx.Done()
	err := errors.Wrap(ctx.Err(), "")
	if !IsDeadLineErr(err) {
		t.Fatalf("should be DeadLine err")
	}
}

func TestIsContextErr(t *testing.T) {
	ctx, cancel := NewNoData(logrus.New()).WithCancel()
	cancel()
	err := errors.Wrap(ctx.Err(), "test")
	if !IsContextErr(err) {
		t.Fatalf("should be context err")
	}

	ctx = NewNoData(logrus.New()).WithTimeout(0)
	<-ctx.Done()
	err = errors.Wrap(ctx.Err(), "")
	if !IsContextErr(err) {
		t.Fatalf("should be context err")
	}
}
