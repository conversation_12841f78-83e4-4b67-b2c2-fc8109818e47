/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKE
 * available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License"); you may not use this
 * file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

package context

import (
	"context"
	"strings"
	"time"

	"github.com/pkg/errors"

	"git.woa.com/kateway/kateway-server/pkg/logger"
)

// Context handles goroutine context and common data ,like logger
type Context struct {
	context.Context
	logger.Logger
	region string
}

type contextData struct {
	AppID     uint64
	Uin       string
	SubUin    string
	RequestID string
}

// New create a new Context with common data
func New(appID uint64, uin string, subUin string, RequestID string, logger logger.Logger) *Context {
	newCtx := &Context{
		Context: context.WithValue(context.Background(), "data", contextData{
			AppID:     appID,
			Uin:       uin,
			SubUin:    subUin,
			RequestID: RequestID,
		}),
		region: "default",
	}
	newCtx.Logger = logger
	newCtx = newCtx.WithLabels(map[string]interface{}{
		"RequestID": RequestID,
	})
	return newCtx
}

func NewNoData(logger logger.Logger) *Context {
	newCtx := &Context{
		Context: context.Background(),
		region:  "default",
	}
	newCtx.Logger = logger
	return newCtx
}

// WithContext create a new Context with target context.Context
// use case:
//         ctx := NewContext(context.Background(), fmt.NewLogger())
//         ctxTimeout,_ := context.WithTimeout(ctx, time.Second)
//         ctx2 := ctx.WithContext(ctxTimeout)
//         go xxx(ctx2)

func (c *Context) WithContext(ctx context.Context) *Context {
	return &Context{
		Context: context.WithValue(ctx, "data", c.Value("data")),
		Logger:  c.Logger,
		region:  c.region,
	}
}

// WithLabels create a new Context with logger with labels
// labels in origin Context will be add to new logger
func (c *Context) WithLabels(labels map[string]interface{}) *Context {
	return &Context{
		Logger:  c.Logger.With(labels),
		Context: c.Context,
		region:  c.region,
	}
}

type CancelFunc func()

func (c *Context) WithCancel() (*Context, CancelFunc) {
	ctx, cancel := context.WithCancel(c.Context)
	return c.WithContext(ctx), CancelFunc(cancel)
}

func (c *Context) WithTimeout(t time.Duration) *Context {
	ctx, _ := context.WithTimeout(c.Context, t)
	return c.WithContext(ctx)
}

func (c *Context) WithRegion(region string) *Context {
	if c.Value("data") == nil {
		return &Context{
			Context: c.Context,
			Logger:  c.Logger,
			region:  region,
		}
	}
	oldValue := c.Value("data").(contextData)
	return &Context{
		Context: context.WithValue(c.Context, "data", oldValue),
		Logger:  c.Logger,
		region:  region,
	}
}

func (c *Context) AppID() uint64 {
	return c.Value("data").(contextData).AppID
}

func (c *Context) Uin() string {
	return c.Value("data").(contextData).Uin
}

func (c *Context) SubUin() string {
	return c.Value("data").(contextData).SubUin
}

func (c *Context) RequestID() string {
	return c.Value("data").(contextData).RequestID
}

func (c *Context) Region() string {
	return c.region
}

func (c *Context) IsEmptyData() bool {
	return c.Value("data") == nil
}

func IsCanceledErr(err error) bool {
	return strings.Contains(errors.Cause(err).Error(), context.Canceled.Error())
}

func IsDeadLineErr(err error) bool {
	return strings.Contains(errors.Cause(err).Error(), context.DeadlineExceeded.Error())
}

func IsContextErr(err error) bool {
	return IsCanceledErr(err) || IsDeadLineErr(err)
}
