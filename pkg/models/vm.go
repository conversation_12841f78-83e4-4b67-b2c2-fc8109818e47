/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKE
 * available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License"); you may not use this
 * file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

package models

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"
)

const (
	VM_LIFESTATE_CREATING          = "creating"     // cvm发货中
	VM_LIFESTATE_REINSTALLING      = "reInstalling" // cvm重装中
	VM_LIFESTATE_INIT              = "Initializing" // cvm或vs将VM交付成功,但是agent还没有正式初始化成功
	VM_LIFESTATE_NORMAL            = "normal"
	VM_LIFESTATE_UPGRADING         = "upgrading"
	VM_LIFESTATE_RUNTIME_UPGRADING = "runtime_upgrading"
	VM_LIFESTATE_UPGRADED          = "upgraded"
	VM_LIFESTATE_DELETED           = "deleted"
	VM_LIFESTATE_DELETING          = "deleting"
	VM_LIFESTATE_REMOVING          = "removing"
	VM_LIFESTATE_REMOVED           = "removed"
	VM_LIFESTATE_DELETING_RETURN   = "returning" // 移除并退还式删除
	VM_LIFESTATE_DELETING_REMOVE   = "removing"  // 仅移除式删除
	VM_LIFESTATE_ABNORMAL          = "abnormal"  // 机器异常，如初始化过程中机器被退还。该状态用户可以删除节点
	VM_DRAIN_STATUS_DRAINING       = "draining"
	VM_DRAIN_STATUS_DRAINED        = "drained"

	NODE_ROLE_WORKER          uint = 0
	NODE_ROLE_MASTER          uint = 1
	NODE_ROLE_ETCD            uint = 2
	NODE_ROLE_ETCD_AND_MASTER uint = 3
	NODE_ROLE_ALL             uint = 9

	NODE_ROLE_WORKER_STR          = "WORKER"
	NODE_ROLE_MASTER_STR          = "MASTER"
	NODE_ROLE_ETCD_STR            = "ETCD"
	NODE_ROLE_ETCD_AND_MASTER_STR = "MASTER_ETCD"
	NODE_ROLE_ALL_STR             = "ALL"
)

const (
	StateShutdown            = "State:Shutdown"               // 对应cvm的停止待销毁，也就是机器已经到期，但是还未超过7天
	StateStopWithCharging    = "State:StoppedWithCharging"    // 对应cvm侧的关机继续收费
	StateStopWithoutCharging = "State:StoppedWithoutCharging" // 对应cvm侧的关机停止收费
	StateStopFailed          = "State:StopFailed"             // 对应cvm侧的关机失败
	StateStopping            = "State:Stopping"               // 对应cvm侧的关机中
)

type InstanceFailedReason string

const (
	INSTANCE_FAILEDREASON_PENDING       InstanceFailedReason = "CVM is PENDING"
	INSTANCE_FAILEDREASON_REINSTALLING  InstanceFailedReason = "CVM is REINSTALLING"
	INSTANCE_FAILEDREASON_LAUNCH_FAILED InstanceFailedReason = "CVM is LAUNCH_FAILED"
	INSTANCE_FAILEDREASON_STOPPED       InstanceFailedReason = "CVM is STOPPED"
	INSTANCE_FAILEDREASON_SHUTDOWN      InstanceFailedReason = "CVM is SHUTDOWN"
	INSTANCE_FAILEDREASON_NOTFOUND      InstanceFailedReason = "CVM is NOTFOUND"
	INSTANCE_FAILEDREASON_ABORMAL       InstanceFailedReason = "CVM is ABNORMAL"
	INSTANCE_FAILEDREASON_VPC_NOTEQUAL  InstanceFailedReason = "VPC not EQUAL"
	INSTANCE_FAILEDREASON_UNKNOWN       InstanceFailedReason = "FAILED is UNKNOWN"

	INSTANCE_FAILEDREASON_K8SNODE_NOTFOUND InstanceFailedReason = "K8SNODE is NOTFOUND"
	INSTANCE_FAILEDREASON_K8SNODE_NOTREADY InstanceFailedReason = "K8SNODE is NOTREADY"
	INSTANCE_FAILEDREASON_K8SNODE_ABNORMAL InstanceFailedReason = "K8SNODE is ABNORMAL"

	INSTANCE_FAILEDREASON_NODEOPERATOR_PENDING InstanceFailedReason = "NODEOPERATOR is PENDING"
	INSTANCE_FAILEDREASON_NODEOPERATOR_SUCCEED InstanceFailedReason = "NODEOPERATOR is SUCCEED"
)

type VmInstance struct {
	ID                uint64    `gorm:"primary_key;AUTO_INCREMENT"`
	AppId             uint64    `gorm:"column:appId;index;"`
	ClusterInstanceId string    `gorm:"column:clusterInstanceId;unique;"`
	UUID              *string   `gorm:"column:vmUuid;type:varchar(50);unique"`
	VmInstanceId      *string   `gorm:"column:uInstanceId;type:varchar(32);unique"`
	LanIp             *string   `gorm:"column:lanIp;type:varchar(32)"`
	NodeName          *string   `gorm:"column:nodename;type:varchar(256)"`
	LifeState         string    `gorm:"column:lifeState;type:varchar(32)"`
	Zone              *string   `gorm:"column:zone;type:varchar(32)"`
	InstanceType      *string   `gorm:"column:instanceType;type:varchar(50)"`
	Cpu               uint      `gorm:"column:cpu"`
	Mem               uint      `gorm:"column:mem"`
	Gpu               *float64  `gorm:"column:gpu;type:float(6,2);default:'0.00'"`
	CvmPayMode        uint      `gorm:"column:cvmPayMode"`                // 0按月结算后付费，1包年包月，2按量计费 默认是2
	NodeRole          uint      `gorm:"column:isMaster;type:tinyint(1);"` // create table中默认值必须为0不为null
	Version           uint      `gorm:"column:version"`
	Os                *string   `gorm:"column:os"`
	Arch              string    `gorm:"column:arch"`
	Progress          string    `gorm:"column:progress"`
	CreateContext     string    `gorm:"column:context"`
	Note              string    `gorm:"column:note"`
	MountTarget       string    `gorm:"column:mountTarget"`
	DockerGraphPath   string    `gorm:"column:dockerGraphPath"`
	DrainStatus       string    `gorm:"column:drainStatus"`
	CreatedAt         time.Time `gorm:"column:createdAt"`
	UpdatedAt         time.Time `gorm:"column:updatedAt"`
	K8sVersion        string    `gorm:"column:k8sVersion"`
	Upgrading         string    `gorm:"column:upgrading"`

	NodeCondition  string `gorm:"column:nodeCondition;type:varchar(128)"`
	IsReady        int    `gorm:"column:isReady"`                          // TODO 是否要存到dashboard DB中?
	IsNormal       int    `gorm:"column:isNormal"`                         // k8s node status
	AbnormalReason string `gorm:"column:abnormalReason;type:varchar(256)"` // k8s node abnormal reason
	InstanceState  string `gorm:"column:instanceState;type:varchar(32)"`
	FailedReason   string `gorm:"column:failedReason;type:varchar(256)"`

	InitMasterNodeTimestamp    string `gorm:"column:initMasterNodeTimestamp"`
	InitMasterNodeRestartCount uint   `gorm:"column:initMasterNodeRestartCount"`

	InitMaterClusterTimestamp     string `gorm:"column:initMaterClusterTimestamp"`
	InitMasterClusterRestartCount uint   `gorm:"column:initMasterClusterRestartCount"`

	// TODO 注释
	NodePoolId         string `gorm:"column:nodePoolId"`
	AutoScalingGroupId string `gorm:"column:autoScalingGroupId"`
	NodePoolState      string `gorm:"column:nodePoolState"`

	Unschedulable bool `gorm:"column:unschedulable"`
	DesiredPodNum int  `gorm:"column:desiredPodNum;type:int(11);default:'0'"`
}

func (VmInstance) TableName() string {
	return "vmInstance"
}

type InstanceExtraArgs struct {
	Kubelet   []string `json:"Kubelet"`
	KubeProxy []string `json:"KubeProxy,omitempty"` // not support
}

type DataDisk struct {
	DiskType           string `json:"DiskType"`
	FileSystem         string `json:"FileSystem"` // 决定是否进行格式化, AutoFormatAndMount为true且FileSystem不为空时进行格式化;AutoFormatAndMount为true且FilsSystem为空时只挂载，不格式化
	DiskSize           int    `json:"DiskSize"`
	AutoFormatAndMount bool   `json:"AutoFormatAndMount"`    // 决定是否进行挂载，false的话既不格式化也不挂载
	MountTarget        string `json:"MountTarget,omitempty"` // 挂载路径
	DiskId             string `json:"DiskId"`
	DiskPartition      string `json:"DiskPartition"` // 设备名称，判断是否新的方式
}

func NodeRole2Str(role uint) string {
	switch role {
	case NODE_ROLE_ETCD:
		return NODE_ROLE_ETCD_STR
	case NODE_ROLE_WORKER:
		return NODE_ROLE_WORKER_STR
	case NODE_ROLE_MASTER:
		return NODE_ROLE_MASTER_STR
	case NODE_ROLE_ETCD_AND_MASTER:
		return NODE_ROLE_ETCD_AND_MASTER_STR
	default:
		return NODE_ROLE_WORKER_STR
	}
}

func IsMasterEtcdRole(role string) bool {
	return strings.ToUpper(role) == NODE_ROLE_ETCD_AND_MASTER_STR
}

func IsMasterRole(role string) bool {
	return strings.ToUpper(role) == NODE_ROLE_MASTER_STR || strings.ToUpper(role) == NODE_ROLE_ETCD_AND_MASTER_STR
}

func IsEtcdRole(role string) bool {
	return strings.ToUpper(role) == NODE_ROLE_ETCD_STR || strings.ToUpper(role) == NODE_ROLE_ETCD_AND_MASTER_STR
}

func IsWorkerRole(role string) bool {
	return strings.ToUpper(role) == NODE_ROLE_WORKER_STR
}

func NodeRole2Uint(role string) uint {
	switch strings.ToUpper(role) {
	case NODE_ROLE_ETCD_STR:
		return NODE_ROLE_ETCD
	case NODE_ROLE_WORKER_STR:
		return NODE_ROLE_WORKER
	case NODE_ROLE_MASTER_STR:
		return NODE_ROLE_MASTER
	case NODE_ROLE_ETCD_AND_MASTER_STR:
		return NODE_ROLE_ETCD_AND_MASTER
	default:
		return NODE_ROLE_WORKER
	}
}

func NodeRoleInt2Str(role uint) string {
	switch role {
	case NODE_ROLE_ETCD:
		return NODE_ROLE_ETCD_STR
	case NODE_ROLE_WORKER:
		return NODE_ROLE_WORKER_STR
	case NODE_ROLE_MASTER:
		return NODE_ROLE_MASTER_STR
	case NODE_ROLE_ETCD_AND_MASTER:
		return NODE_ROLE_ETCD_AND_MASTER_STR
	default:
		return NODE_ROLE_WORKER_STR
	}
}

func ValidNodeRole(role string) bool {
	switch strings.ToUpper(role) {
	case NODE_ROLE_ETCD_STR:
		return true
	case NODE_ROLE_WORKER_STR:
		return true
	case NODE_ROLE_MASTER_STR:
		return true
	case NODE_ROLE_ETCD_AND_MASTER_STR:
		return true
	case NODE_ROLE_ALL_STR:
		return true
	default:
		return false
	}
}

type Param struct {
	DockerGraphPath   *string
	MountTarget       *string
	DataDiskPartition *string // 此参数表示"挂载分区"，在添加"已有节点"到集群时，支持传递此参数
	UniqVpcID         *string
	Unschedulable     bool
	UserScript        *string
	LabelMaps         map[string]string
	DataDisks         []DataDisk
	UseNewMountMode   bool // 	添加已有节点使用新的挂盘方式，即使用DataDisks传参且DiskPartition非空
	InstanceExtraArgs InstanceExtraArgs
	GPUArgs           *GPUArgs
	// DEPRECATED
	NeedInternetAccess bool
	// DEPRECATED
	AsEnabled bool
}

type MiddleParam struct {
	AddingNode AddingNode
}

type AddingNode struct {
	InstanceId    string
	ResetTaskId   int
	ProjectId     int
	SuccResetUuid string
	SafeGroup     string
	SafeGroups    []string
	Mounted       bool
	LanIp         string
	NodePoolId    string
	IsSgAppend    bool
	AddExistNode  bool
	// DEPRECATED
	ASEnable bool
	// DEPRECATED
	IsASTemplateNode bool
	// DEPRECATED
	IsASTemplateCreated bool
}

type VmContext struct {
	AppId             uint64
	OwnerUin          string
	OperateUin        string
	SubUin            string
	ClusterId         uint64
	ClusterInstanceId string
	VpcId             int64
	ServiceName       string
	TimeEventId       string
	Data              map[string]interface{}
	Param             Param
	MiddleParam       MiddleParam
}

type GPUArgs struct {
	CustomDriver CustomDriver `json:"CustomDriver"`
	StandardDriver
}

type StandardDriver struct {
	Driver    DriverVersion `json:"Driver"`
	CUDA      DriverVersion `json:"CUDA"`
	CUDNN     CUDNN         `json:"CUDNN"`
	MIGEnable bool          `json:"MIGEnable"`
}

type CustomDriver struct {
	Address string `json:"Address"`
}

type DriverVersion struct {
	Version string `json:"Version"`
	Name    string `json:"Name"`
}

type CUDNN struct {
	Version string `json:"Version"`
	Name    string `json:"Name"`
	DocName string `json:"DocName"`
	DevName string `json:"DevName"`
}

func (g *GPUArgs) String() string {
	return fmt.Sprintf("GPUDriver version:%s name:%s Cuda version:%s name:%s Cudnn version:%s name:%s",
		g.Driver.Version, g.Driver.Name, g.CUDA.Version, g.CUDA.Name, g.CUDNN.Version, g.CUDNN.Name)
}

func (v VmContext) String() string {
	data, _ := json.Marshal(v)
	return string(data)
}
