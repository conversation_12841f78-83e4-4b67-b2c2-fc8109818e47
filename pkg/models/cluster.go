package models

import (
	"encoding/json"
	"math"
	"strconv"
	"strings"
	"time"
)

const (
	CLUSTER_LIFESTATE_TRADING           = "trading"
	CLUSTER_LIFESTATE_INIT              = "initializing"
	CLUSTER_LIFESTATE_NORMAL            = "normal"
	CLUSTER_LIFESTATE_DELETED           = "deleted"
	CLUSTER_LIFESTATE_DELETING          = "deleting"
	CLUSTER_LIFESTATE_ISOLATED          = "isolated"
	CLUSTER_LIFESTATE_ABNORMAL          = "abnormal"
	CLUSTER_LIFESTATE_UPGRADING         = "upgrading"         // 集群升级中
	CLUSTER_LIFESTATE_PAUSE             = "pause"             // 集群升级暂停
	CLUSTER_LIFESTATE_NODE_UPGRADING    = "node_upgrading"    // 集群升级中
	CLUSTER_LIFESTATE_RUNTIME_UPGRADING = "runtime_upgrading" // 节点运行时升级中
	CLUSTER_LIFESTATE_IDLE              = "idle"              // 集群闲置中，所有master Pod资源将被回收
	CLUSTER_LIFESTATE_RECOCERING        = "recovering"        // 集群唤醒中，也即空闲集群已经添加节点，但是api-server暂时还无法访问的状态
	CLUSTER_LIFESTATE_MASTER_SCALING    = "master_scaling"    // 集群处于master扩容中
	CLUSTER_LIFESTATE_WAIT_FOR_CONNECT  = "wait_for_connect"

	// 集群资源状态
	CLUSTER_LIFESTATE_CHARGE_ISOLATE   = "charge_isolate"   // 集群执行隔离
	CLUSTER_LIFESTATE_CHARGE_ISOLATED  = "charge_isolated"  // 集群隔离
	CLUSTER_LIFESTATE_CHARGE_REVERSE   = "charge_reverse"   // 集群执行冲正
	CLUSTER_LIFESTATE_CHARGE_REVERSAL  = "charge_reversal"  // 集群冲正
	CLUSTER_LIFESTATE_CHARGE_DESTROY   = "charge_destroy"   // 集群执行销毁
	CLUSTER_LIFESTATE_CHARGE_DESTROYED = "charge_destroyed" // 集群销毁

	// 托管集群规格状态
	CLUSTER_LIFESTATE_LEVEL_UPGRADE_TRADING = "cluster_level_trading"   // 集群变配交易中
	CLUSTER_LIFESTATE_LEVEL_UPGRADING       = "cluster_level_upgrading" // 集群变配中

	METAFEATURE_STATUS_PENDING = "pending"
	METAFEATURE_STATUS_DOING   = "running"
	METAFEATURE_STATUS_DONE    = "done"
	METAFEATURE_STATUS_ERROR   = "error"
)

// ClusterLifeStateHealthy return true if cluster kube-apiserver is reachable
func APIServerHealthy(lifeState string) bool {
	return lifeState == CLUSTER_LIFESTATE_NORMAL || lifeState == CLUSTER_LIFESTATE_RUNTIME_UPGRADING ||
		lifeState == CLUSTER_LIFESTATE_NODE_UPGRADING || lifeState == CLUSTER_LIFESTATE_UPGRADING
}

func APIServerMayUnhealthy(lifeState string) bool {
	return lifeState == CLUSTER_LIFESTATE_INIT || lifeState == CLUSTER_LIFESTATE_ABNORMAL ||
		lifeState == CLUSTER_LIFESTATE_RECOCERING
}

func APIServerUnhealthy(lifeState string) bool {
	return !APIServerHealthy(lifeState) && !APIServerUnhealthy(lifeState)
}

const (
	CLUSTER_TASKSTATE_ADD_NODES            = "addNodes"
	CLUSTER_TASKSTATE_SCALING              = "scaling"   // 集群规模调整
	CLUSTER_TASKSTATE_UPGRADING            = "upgrading" // 集群升级中
	CLUSTER_TASKSTATE_NORMAL               = "normal"
	CLUSTER_TASKSTATE_DELETE_NODES         = "deleteNodes"
	CLUSTER_TASKSTATE_ENABLE_EXTERNAL_NODE = "enableExternalNode"
)

const (
	CLUSTER_NODE_STATUS_ALL_NORMAL       = "AllNormal"
	CLUSTER_NODE_STATUS_ALL_ABNORMAL     = "AllAbnormal"
	CLUSTER_NODE_STATUS_PARTIAL_ABNORMAL = "PartialAbnormal"
	CLUSTER_NODE_STATUS_UNKNOW           = "-"
)

const (
	CLUSTER_NODENAME_TYPE_HOSTNAME          = "hostname" // 节点名称类型
	CLUSTER_NODENAME_TYPE_LANIP             = "lan-ip"
	CLUSTER_NETWORK_TYPE_GR                 = "GR"
	CLUSTER_NETWORK_TYPE_VPC_CNI            = "VPC-CNI"
	CLUSTER_NETWORK_TYPE_VPC_CNI_DIRECT_ENI = "tke-direct-eni"
	CLUSTER_NETWORK_TYPE_VPC_CNI_ROUTE_ENI  = "tke-route-eni"
	CLUSTER_NETWORK_TYPE_CILIUM_OVERLAY     = "CiliumOverlay" // 集群网络类型,适配前端命名规范，改为驼峰
)

const (
	MasterEmptyRecycle = "cloud.tencent.com/tke-cluster-idle"
)

type ClusterType int

const (
	CLUSTER_TYPE_CLUSTER_ORIGIN                     ClusterType = 0  // 老的托管集群
	CLUSTER_TYPE_CLUSTER_DEPLOY_NATIVE              ClusterType = 1  // 集群化部署原生逻辑
	CLUSTER_TYPE_CLUSTER_DEPLOY_MIGRATION           ClusterType = 2  // 集群化部署迁移，老的master不工作了
	CLUSTER_TYPE_CLUSTER_DEPLOY_MIGRATION_MASTER    ClusterType = 3  // 集群化部署迁移，老的master还在工作
	CLUSTER_TYPE_CLUSTER_INDEPENDENT                ClusterType = 4  // 独立集群
	CLUSTER_TYPE_CLUSTER_MANAGED_ASYNC_PROCESS      ClusterType = 5  // 异步处理的托管集群(非集群化部署)
	CLUSTER_TYPE_CLUSTER_DEPLOY_ASYNC_PROCESS       ClusterType = 6  // 异步处理的托管集群(集群化部署)
	CLUSTER_TYPE_CLUSTER_DEPLOY_MIGRATE_TO_OPERATOR ClusterType = 7  // 集群化部署迁移至 Operator
	CLUSTER_TYPE_CLUSTER_DEPLOY_ON_OPERATOR         ClusterType = 8  // 使用 Operator 创建的集群
	CLUSTER_TYPE_CLUSTER_HUB                        ClusterType = 9  // Hub集群
	CLUSTER_TYPE_CLUSTER_EXTERNAL                   ClusterType = 10 // 第三方集群
)

func (c ClusterType) IndependentCluster() bool {
	return c == CLUSTER_TYPE_CLUSTER_INDEPENDENT
}

func (c ClusterType) ManagedCluster() bool {
	return c != CLUSTER_TYPE_CLUSTER_INDEPENDENT
}

func (c ClusterType) String() string {
	if c.IndependentCluster() {
		return CLUSTER_TYPE_CLUSTER_INDEPENDENT_STR
	}
	return CLUSTER_TYPE_CLUSTER_MANAGED_STR
}

const (
	CLUSTER_TYPE_CLUSTER_MANAGED_STR     = "MANAGED_CLUSTER"
	CLUSTER_TYPE_CLUSTER_HUB_STR         = "HUB_CLUSTER"
	CLUSTER_TYPE_CLUSTER_EXTERNAL_STR    = "EXTERNAL_CLUSTER"
	CLUSTER_TYPE_CLUSTER_INDEPENDENT_STR = "INDEPENDENT_CLUSTER"
)

const (
	CONTAINER_TYPE_DOCKER     = "docker"     // 容器类型：docker
	CONTAINER_TYPE_CONTAINERD = "containerd" // 容器类型：containerd
)

const (
	KubeProxyModeDefault = "kube-proxy"
	KubeProxyModeBpf     = "kube-proxy-bpf"
	KubeProxyModeEni     = "kube-proxy-eni"
)

const (
	CILIUM_MODE_CLUSTERIP = "clusterIP"
)

const (
	// DefaultClusterLevel is the default cluster level
	DefaultClusterLevel = "L5"
)

// AutoUpgradeClusterLevel is auto upgrade cluster level
type AutoUpgradeClusterLevel struct {
	IsAutoUpgrade bool
}

type Cluster struct {
	ID                       uint64      `gorm:"primary_key;AUTO_INCREMENT;column:id"`
	AppId                    uint64      `gorm:"column:appId;index"`
	ClusterInstanceId        string      `gorm:"column:clusterInstanceId;unique;"`
	Name                     string      `gorm:"column:name"`
	Description              string      `gorm:"column:description"`
	LifeState                string      `gorm:"column:lifeState"`
	TaskState                string      `gorm:"column:taskState"`
	VpcId                    int64       `gorm:"column:vpcId"` // 非唯一键，单个VPC下可能会有多个集群
	UniqVpcID                string      `gorm:"column:uniqVpcID"`
	MasterLbUniqSubnetId     string      `gorm:"column:masterLbUniqSubnetId"`
	Os                       string      `gorm:"column:os"`
	ImageId                  string      `gorm:"column:imageId"`
	ClusterCIDR              string      `gorm:"column:clusterCIDR"` // K8S CIDR
	ServiceCIDR              string      `gorm:"column:serviceCIDR"`
	NodeCIDRMaskSize         int         `gorm:"column:nodeCIDRMaskSize"`
	ExternalEndpoint         *string     `gorm:"column:externalEndpoint"` // 提供给用户直接外网访问k8s api server，固定的域名
	TgwEndpoint              *string     `gorm:"column:tgwEndpoint"`      // 外网域名绑定的vip，公网ip，固定的域名会解析到这个ip上
	VpcLbEndpoint            *string     `gorm:"column:vpcLbEndpoint"`    // 提供给nodekubelet 访问k8s api server，集群内部访问，是个169.254的地址
	MasterEndpoint           *string     `gorm:"column:masterEndpoint"`   // 用于dashboard访问k8s api server，目前是jnsgw的地址，用于从管控面访问租户端
	MasterListenPort         int         `gorm:"column:masterListenPort"`
	IsSecure                 int         `gorm:"column:isSecure"`
	ClusterType              ClusterType `gorm:"column:isClusterDeploy"`        // 是否是集群化部署 1 是集群化部署，默认是0
	EtcdClusterVpcLbEndpoint string      `gorm:"column:etcdClusterEndpoint"`    // 用于k8s api server访问etcd
	EtcdClusterJNSGWEndpoint string      `gorm:"column:etcdClusterJNSEndpoint"` // 用于支撑环境api server访问etcd
	EtcdServersOverrides     string      `gorm:"column:etcdServersOverrides;type:varchar(1024)"`
	CreatedAt                time.Time   `gorm:"column:createdAt"` // 时间信息
	UpdatedAt                time.Time   `gorm:"column:updatedAt"`
	DeleteAt                 time.Time   `gorm:"column:deletedAt"`
	OwedAt                   time.Time   `gorm:"column:owedAt"`
	K8sVersion               string      `gorm:"column:k8sVersion"`
	DockerVersion            string      `gorm:"column:dockerVersion"`
	ProjectId                int         `gorm:"column:projectId"`
	OsCustomizeType          *string     `gorm:"column:osCustomizeType"`
	MasterUSG                string      `gorm:"column:masterUSG"`
	WorkUSG                  string      `gorm:"column:workUSG"`
	RuntimeConifg            string      `gorm:"column:runtimeConfig"`
	MetaClusterID            string      `gorm:"column:metaClusterId"`

	MonitorStorageId string `gorm:"column:monitorStorageId"`
	BMonitor         *bool  `gorm:"column:bMonitor"`

	InitClusterResourceTimestamp    string `gorm:"column:initClusterResourceTimestamp"`
	InitClusterResourceRestartCount uint   `gorm:"column:initClusterResourceRestartCount"`

	Progress string `gorm:"column:progress"`
	Property string `gorm:"column:property"`

	ExtraArgs string `gorm:"column:extraArgs"`

	Context                 string `gorm:"column:context"`
	DeletionProtection      bool   `gorm:"column:deletionProtection"`
	AuditEnabled            bool   `gorm:"column:auditEnabled"`
	ExternalNodeConfig      string `gorm:"column:externalNodeConfig"`
	ParentClusterID         string `gorm:"column:parentClusterID"`
	CloudVendor             string `gorm:"column:cloudVendor"`
	ClusterLevel            string `gorm:"column:clusterLevel;not null;default:''"`
	AutoUpgradeClusterLevel string `gorm:"column:autoUpgradeClusterLevel;type:text"`

	Region string `json:"omitempty"`
}

func (c *Cluster) MaxServiceNum() uint64 {
	split := strings.Split(c.ServiceCIDR, "/")
	if len(split) != 2 {
		return 256
	} else {
		maskSize, _ := strconv.Atoi(split[1])
		return uint64(math.Exp2(float64(32 - maskSize)))
	}
}

func (c *Cluster) MaxPodsNumPerNode() uint64 {
	return uint64(math.Exp2(float64(32 - c.NodeCIDRMaskSize)))
}

func (c *Cluster) IsIndependentCluster() bool {
	return c.ClusterType == CLUSTER_TYPE_CLUSTER_INDEPENDENT
}

func (c *Cluster) IsExternalCluster() bool {
	return c.ClusterType == CLUSTER_TYPE_CLUSTER_EXTERNAL
}

func (c *Cluster) RuntimeConfig() *RuntimeConfig {
	res := &RuntimeConfig{}
	if c.RuntimeConifg != "" {
		if err := json.Unmarshal([]byte(c.RuntimeConifg), res); err != nil {
			return nil
		}
	}
	return res
}

func (c *Cluster) IPVS() bool {
	runtime := c.RuntimeConfig()
	if runtime == nil {
		return false
	}
	if runtime.KubeProxyPara.IPVS > 0 {
		return true
	}
	return false
}

func (c *Cluster) CNI() bool {
	runtime := c.RuntimeConfig()
	if runtime == nil {
		return false
	}
	if runtime.KubeletPara.EnableCNI == "true" {
		return true
	} else {
		return false
	}
}

func (c *Cluster) CustomizedPodCIDR() bool {
	property, err := GetClusterProperty(c.Property)
	if err != nil {
		return false
	}
	if !property.IsNetworkPureVPCCNI() && property.EnableCustomizedPodCIDR {
		return true
	}
	return false
}

func (c *Cluster) BasePodNumber() int {
	property, err := GetClusterProperty(c.Property)
	if err != nil {
		return 0
	}
	return property.BasePodNumberPerNode
}

func (c *Cluster) PropertyS() *ClusterProperty {
	property, _ := GetClusterProperty(c.Property)
	return &property
}

func (c *Cluster) ClusterTypeStr() string {
	switch c.ClusterType {
	case CLUSTER_TYPE_CLUSTER_INDEPENDENT:
		return CLUSTER_TYPE_CLUSTER_INDEPENDENT_STR
	case CLUSTER_TYPE_CLUSTER_EXTERNAL:
		return CLUSTER_TYPE_CLUSTER_EXTERNAL_STR
	default:
		return CLUSTER_TYPE_CLUSTER_MANAGED_STR
	}
}

func (c *Cluster) ClusterState() string {
	switch c.LifeState {
	case CLUSTER_LIFESTATE_IDLE:
		return "Idling"
	case CLUSTER_LIFESTATE_RECOCERING:
		return "Recovering"
	case CLUSTER_LIFESTATE_NORMAL:
		switch c.TaskState {
		case CLUSTER_TASKSTATE_NORMAL:
			return "Running"
		case CLUSTER_TASKSTATE_SCALING:
			return "Scaling"
		case CLUSTER_TASKSTATE_ADD_NODES:
			return "Scaling"
		case CLUSTER_TASKSTATE_DELETE_NODES:
			return "Scaling"
		case CLUSTER_TASKSTATE_UPGRADING:
			return "Upgrading"
		case CLUSTER_LIFESTATE_WAIT_FOR_CONNECT:
			return "WaittingForConnect"
		}
	case CLUSTER_LIFESTATE_TRADING:
		return "Trading"
	case CLUSTER_LIFESTATE_INIT:
		return "Creating"
	case CLUSTER_LIFESTATE_DELETING:
		return "Deleting"
	case CLUSTER_LIFESTATE_ISOLATED:
		return "Isolated"
	case CLUSTER_LIFESTATE_UPGRADING:
		return "Upgrading"
	case CLUSTER_LIFESTATE_PAUSE:
		return "Pause"
	case CLUSTER_LIFESTATE_NODE_UPGRADING:
		return "NodeUpgrading"
	case CLUSTER_LIFESTATE_RUNTIME_UPGRADING:
		return "RuntimeUpgrading"
	case CLUSTER_LIFESTATE_MASTER_SCALING:
		return "MasterScaling"
	case CLUSTER_LIFESTATE_WAIT_FOR_CONNECT:
		return "WaittingForConnect"
	case CLUSTER_LIFESTATE_LEVEL_UPGRADE_TRADING:
		return "ClusterLevelUpgrading"
	case CLUSTER_LIFESTATE_LEVEL_UPGRADING:
		return "ClusterLevelUpgrading"
	case CLUSTER_LIFESTATE_CHARGE_ISOLATE:
		return "ResourceIsolate"
	case CLUSTER_LIFESTATE_CHARGE_ISOLATED:
		return "ResourceIsolated"
	case CLUSTER_LIFESTATE_CHARGE_REVERSE:
		return "ResourceReverse"
	}

	// TODO 防止逻辑更新后此处的代码未更新，折衷返回一个不准确的状态
	if c.LifeState == CLUSTER_LIFESTATE_NORMAL {
		return "Running"
	} else {
		return "Abnormal"
	}
}

// SetAutoUpgradeClusterLevel is set auto upgrade cluster level
func (c *Cluster) SetAutoUpgradeClusterLevel(obj AutoUpgradeClusterLevel) {
	data, err := json.Marshal(obj)
	if err != nil {
		return
	}

	c.AutoUpgradeClusterLevel = string(data)
}

// GetAutoUpgradeClusterLevel is get auto upgrade cluster level
func (c *Cluster) GetAutoUpgradeClusterLevel() (obj AutoUpgradeClusterLevel) {
	if c.AutoUpgradeClusterLevel == "" {
		return
	}

	if err := json.Unmarshal([]byte(c.AutoUpgradeClusterLevel), &obj); err != nil {
		return
	}

	return
}

func (c *Cluster) TableName() string {
	return "cluster"
}

type ClusterInfo struct {
	ClusterId         int64     `json:"-"`
	ClusterInstanceId string    `json:"clusterId"`
	ClusterName       string    `json:"clusterName"`
	Description       string    `json:"description"`
	Status            string    `json:"status"`
	UnVpcId           string    `json:"unVpcId"`
	VpcId             int64     `json:"vpcId"`
	ClusterCIDR       string    `json:"clusterCIDR"`
	ServiceCIDR       string    `json:"serviceCIDR"`
	CreatedAt         time.Time `json:"createdAt"`
	UpdatedAt         time.Time `json:"updatedAt"`
	NodeStatus        string    `json:"nodeStatus"`
	NodeNum           int       `json:"nodeNum"`
	MasterNum         int       `json:"masterNum"`
	EtcdNum           int       `json:"etcdNum"`
	OS                string    `json:"os"`
	ImageId           string    `json:"imageId"`
	TotalCpu          uint      `json:"totalCpu"`
	TotalMem          uint      `json:"totalMem"`
	TotalGpu          uint      `json:"totalGpu"`
	InstanceIds       []string  `json:"-"`
	RegionId          uint      `json:"regionId"`
	K8sVersion        string    `json:"k8sVersion"`
	NodeK8sVersion    []string  `json:"nodeK8sVersion"`
	UpdateStatus      int       `json:"updateStatus"`
	// 提供给用户直接外网访问k8s api server
	ClusterExternalEndpoint *string `json:"clusterExternalEndpoint"`
	OpenHttps               int     `json:"openHttps"`
	MasterLBSubnetId        string  `json:"masterLbSubnetId"`
	MaxNodePodNum           int     `json:"maxNodePodNum"` // control cluster pod number and service
	MaxClusterServiceNum    int     `json:"maxClusterServiceNum"`
	ProjectId               int     `json:"projectId"`
	Property                string  `json:"Property"`
	IPVS                    int     `json:"ipvs"`
	CNI                     int     `json:"cni"`
	ClusterType             string  `json:"clusterType"`
	ContainerRuntime        string  `json:"ContainerRuntime"`
	MonitorResourceId       string  `json:"-"`
	BMonitor                *bool   `json:"bMonitor"`
	OsCustomizeType         string  `json:"osCustomizeType"`
	DeletionProtection      bool    `json:"DeletionProtection"`
	AuditEnabled            bool    `json:"AuditEnabled"`
	// kube-proxy的代理模式，目前仅支持的kube-proxy-bpf，ipvs和iptable的切换开关还由之前的IPVS参数控制
	KubeProxyMode string `json:"KubeProxyMode"`
	CiliumMode    string `json:"CiliumMode"`
}

type ClusterGroupInfo struct {
	ClusterInstanceId string `json:"clusterId"`
	AppId             uint64 `json:"appId"`
	VpcId             int64  `json:"vpcId"`
}

type DockerdRuntimePara struct {
	LogOptions  string `json:"logger-opts"`
	LiveRestore string `json:"live-restore"` // true or false
}

type KubeletRuntimePara struct {
	TurnOnKubeletReserved    string `json:"kubelet-reserved"`       // true or false
	TurnOnKubletHardEviction string `json:"kubelet-hard-eviction"`  // true or false
	DisableAnonymousAuth     string `json:"disable-anonymous-auth"` // true or false
	EnableCNI                string `json:"cni-enable,omitempty"`   // true or false
	EvictionHard             string `json:"eviction-hard"`
	LogLevel                 string `json:"v,omitempty"`
}

type KubeProxyRuntimePara struct {
	LogLevel string `json:"v,omitempty"`
	IPVS     int    `json:"ipvs,omitempty"`
}

type RuntimeConfig struct {
	ContainerRuntime              string               `json:"ContainerRuntime"` // 1-docker; 2-containerd
	RuntimeVersion                string               `json:"RuntimeVersion"`   // 18.6.3, 19.2.2
	DockerdPara                   DockerdRuntimePara   `json:"dockerd"`
	KubeletPara                   KubeletRuntimePara   `json:"kubelet"`
	KubeProxyPara                 KubeProxyRuntimePara `json:"kube-proxy"`
	KubeProxyMode                 string               `json:"KubeProxyMode"`
	KubeProxyExtraArgs            []string             `json:"KubeProxyExtraArgs,omitempty"`
	CiliumMode                    string               `json:"CiliumMode"`
	MonitorPara                   []string             `json:"monitorPara"`
	IsKubeProxyDS                 bool                 `json:"IsKubeProxyDS"`
	EniIpamdPara                  EniIpamdPara         `json:"eniIpamdPara"`
	CloudControllerManagerOutTree bool                 `json:"CloudControllerManagerOutTree"`
}

type EniIpamdPara struct {
	EniSubnetIds        []string `json:"eniSubnetIds,omitempty"`
	MaxEniIpNum         int      `json:"maxEniIpNum,omitempty"`
	ClaimExpiredSeconds int32    `json:"claimExpiredSeconds,omitempty"`
}

type ClusterProperty struct {
	NodeNameType              string       `json:"NodeNameType,omitempty"`
	NetworkType               string       `json:"NetworkType,omitempty"`
	IsNonStaticIpMode         bool         `json:"IsNonStaticIpMode,omitempty"`
	VpcCniType                string       `json:"VpcCniType,omitempty"`
	MetaFeatureParam          *MetaFeature `json:"MetaFeatureParam,omitempty"`
	EnableCustomizedPodCIDR   bool         `json:"EnableCustomizedPodCIDR,omitempty"`
	BasePodNumberPerNode      int          `json:"BasePodNumberPerNode,omitempty"`
	EnableMultiClusterCIDR    bool         `json:"EnableMultiClusterCIDR,omitempty"`
	MultiClusterCIDR          string       `json:"MultiClusterCIDR,omitempty"`
	IgnoreClusterCIDRConflict bool         `json:"IgnoreClusterCIDRConflict,omitempty"`
	// IsSupportMultiENI 指新共享网卡固定 IP 模式，版本号 >= v3.4，可支持多网卡，去除子网独占限制
	IsSupportMultiENI bool `json:"IsSupportMultiENI,omitempty"`
}

type MetaFeature struct {
	FeatureType string       `json:"FeatureType,omitempty"` // metaCluster，crossTenant 二选一
	NeedVpcLb   bool         `json:"NeedVpcLb,omitempty"`   // 会影响是否添加enilb_Controller 监听添加
	RouteConfig []RouteItem  `json:"RouteConfig,omitempty"` // 路由条目，用于配置tke-route的路由明细
	TenantParam *TenantParam `json:"TenantParam,omitempty"`
	StaticMode  bool         `json:"StaticMode"` // 是否静态模式。 false - 非静态模式，单集群单租户，具备预留网卡能力， true - 静态模式，单集群多租户，支持静态ENI IP
}

type TenantParam struct {
	// 对端AppID
	AppId uint64 `json:"AppId,omitempty" name:"AppId"`

	// 对端UIN
	Uin string `json:"Uin,omitempty" name:"Uin"`

	// 对端VpcId
	UniqVpcId string `json:"UniqVpcId,omitempty" name:"VpcId"`

	// 对端SubnetId，需要申请非保留IP地址时才配置
	SubnetId string `json:"SubnetId,omitempty" name:"SubnetId"`

	// 单节点跨租户弹性网卡个数限制，可以通过全局设置来限制单Node最大网卡数，不超过特定值。需要该值地域VPC侧的限制值；配置中该值可以不提供
	ENILimit int `json:"ENILimit,omitempty" name:"ENILimit"`

	// IfName 是Pod内跨租户网卡会使用到的网卡名，与独立网卡共存时会用到
	IfName string `json:"IfName,omitempty" name=IfName"`

	// Business 是跨租户网卡功能使用方的业务简称，默认从db中读取
	Business string `json:"Business,omitempty" name=Business"`
}

type CustomizedGrPara struct {
	ClusterCidrs  []string `json:"ClusterCidrs"`
	ServiceCidr   string   `json:"ServiceCidr"`
	BasePodNumber int      `json:"BasePodNumber"`
}

type RouteItem struct {
	Type   string `json:"Type,omitempty"`
	Subnet string `json:"Subnet,omitempty"`
	Dev    string `json:"Dev,omitempty"`
}

func (c *ClusterProperty) IsNetworkGR() bool {
	return c.NetworkType == CLUSTER_NETWORK_TYPE_GR
}

func (c *ClusterProperty) IsNetworkCiliumOverlay() bool {
	return c.NetworkType == CLUSTER_NETWORK_TYPE_CILIUM_OVERLAY
}

func (c *ClusterProperty) IsNetworkPureVPCCNI() bool {
	return c.NetworkType == CLUSTER_NETWORK_TYPE_VPC_CNI
}

func (c *ClusterProperty) IsPureDirectENI() bool {
	if c.IsNetworkPureVPCCNI() {
		return c.VpcCniType == CLUSTER_NETWORK_TYPE_VPC_CNI_DIRECT_ENI
	} else {
		return false
	}
}

func (c *ClusterProperty) IsPureRouteENI() bool {
	if c.IsNetworkPureVPCCNI() {
		return c.VpcCniType == CLUSTER_NETWORK_TYPE_VPC_CNI_ROUTE_ENI
	} else {
		return false
	}
}

func (c *ClusterProperty) IsMixRouteENI() bool {
	if !c.IsNetworkPureVPCCNI() {
		return c.VpcCniType == CLUSTER_NETWORK_TYPE_VPC_CNI_ROUTE_ENI
	} else {
		return false
	}
}

func (c *ClusterProperty) IsMixDirectENI() bool {
	if !c.IsNetworkPureVPCCNI() {
		return c.VpcCniType == CLUSTER_NETWORK_TYPE_VPC_CNI_DIRECT_ENI
	} else {
		return false
	}
}

// 是否是混合 or 纯 DirectENI
func (c *ClusterProperty) IsDirectENIMode() bool {
	return c.VpcCniType == CLUSTER_NETWORK_TYPE_VPC_CNI_DIRECT_ENI
}

func (c *ClusterProperty) IsRouteENIMode() bool {
	return c.VpcCniType == CLUSTER_NETWORK_TYPE_VPC_CNI_ROUTE_ENI
}

func (c *ClusterProperty) IsMixMode() bool {
	if c.NetworkType == CLUSTER_NETWORK_TYPE_GR && (c.VpcCniType == CLUSTER_NETWORK_TYPE_VPC_CNI_DIRECT_ENI || c.VpcCniType == CLUSTER_NETWORK_TYPE_VPC_CNI_ROUTE_ENI) {
		return true
	} else {
		return false
	}
}

type ClusterExtraArgs struct {
	KubeAPIServer          []string `json:"KubeAPIServer"`
	KubeControllerManager  []string `json:"KubeControllerManager"`
	KubeScheduler          []string `json:"KubeScheduler"`
	CloudControllerManager []string `json:"CloudControllerManager"`
	Etcd                   []string `json:"Etcd"`
}

type ClusterExtraArgsManager struct {
	HistoryExtraArgs *ClusterExtraArgsManager `json:"HistoryExtraArgs,omitempty"`
	DefaultExtraArgs *ClusterExtraArgs        `json:"DefaultExtraArgs,omitempty"`
	Version          string                   `json:"Version"`
	ClusterExtraArgs
}

type ResourceType string

const (
	ResourceTypeCBS ResourceType = "CBS"
)

type DeleteMode string

const (
	DeleteModeTerminate DeleteMode = "terminate"
	DeleteModeRetain    DeleteMode = "retain"
)

type ResourceDeleteOption struct {
	ResourceType ResourceType `json:"ResourceType"` // CBS
	DeleteMode   DeleteMode   `json:"DeleteMode"`   // retain, terminate
}

type DeleteClusterCtx struct {
	OwnerUin               string                 `json:"Uin"`
	SubAccountUin          string                 `json:"SubAccountUin"`
	ResourceDeleteOptions  []ResourceDeleteOption `json:"ResourceDeleteOptions"`
	InstanceDeleteMode     string                 `json:"InstanceDeleteMode"`
	RequestId              string                 `json:"RequestId"`
	ForceDelete            bool                   `json:"ForceDelete"`
	BillingResourceDestroy bool                   `json:"BillingResourceDestroy"`
}

// DestroyClusterCtx is the context for destroy cluster
type DestroyClusterCtx struct {
	OwnerUin string `json:"Uin"`
}

// 存储CreateCluster用到的上下文
type CreateClusterCtx struct {
	OwnerUin        string           `json:"Uin"`
	SubAccountUin   string           `json:"SubAccountUin"`
	ReadyToInit     *bool            `json:"ReadyToInit"`
	MasterCount     uint64           `json:"MasterCount"` // 独立集群需要等master机器都买出来之后才能初始化
	UseRBACAuth     bool             `json:"UseRBACAuth"`
	Language        string           `json:"Language"`
	ExtensionAddons []ExtensionAddon `json:"ExtensionAddons"`
}

type ExtensionAddon struct {
	AddonName  string `json:"AddonName"`
	AddonParam string `json:"AddonParam"`
}

// 仅用于异步流程传递参数, 不能当成数据存储来用
type ClusterCtx struct {
	CreateCtx  *CreateClusterCtx  `json:"CreateCtx"`
	DeleteCtx  *DeleteClusterCtx  `json:"DeleteCtx"`
	DestroyCtx *DestroyClusterCtx `json:"DestroyCtx"`
}

func (c ClusterCtx) String() string {
	b, _ := json.Marshal(c)
	return string(b)
}

type ClusterProgress struct {
	Master []*Step `json:"master"`
}

type EnableExternalNodeProgress struct {
	Progress []*Step `json:"Progress"`
}

type WorkerProgress struct {
	Worker []*Step `json:"worker"`
}

type Step struct {
	Name    string     `json:"Name"`
	StartAt *time.Time `json:"StartAt"`
	EndAt   *time.Time `json:"EndAt"`
	Status  string     `json:"Status"`
	Message string     `json:"Message"`
}

func GetClusterProperty(clusterPropertyStr string) (ClusterProperty, error) {
	var property ClusterProperty
	if clusterPropertyStr != "" {
		err := json.Unmarshal([]byte(clusterPropertyStr), &property)
		if err != nil {
			return ClusterProperty{}, err
		}
	}

	return property, nil
}

func GetClusterTypeName(clusterType ClusterType) string {
	switch clusterType {
	case CLUSTER_TYPE_CLUSTER_INDEPENDENT:
		return "独立集群"
	case CLUSTER_TYPE_CLUSTER_HUB:
		return "Hub集群"
	case CLUSTER_TYPE_CLUSTER_EXTERNAL:
		return "注册集群"
	case CLUSTER_TYPE_CLUSTER_DEPLOY_ON_OPERATOR:
		return "托管集群"
	default:
		return "托管集群"
	}
}

func GetClusterTypeNameInternational(clusterType ClusterType) string {
	switch clusterType {
	case CLUSTER_TYPE_CLUSTER_INDEPENDENT:
		return "Independent Cluster"
	case CLUSTER_TYPE_CLUSTER_HUB:
		return "Hub Cluster"
	case CLUSTER_TYPE_CLUSTER_EXTERNAL:
		return "External Cluster"
	case CLUSTER_TYPE_CLUSTER_DEPLOY_ON_OPERATOR:
		return "Managed Cluster"
	default:
		return "Managed Cluster"
	}
}

func IsUnableAutoRecover(clusterState string) bool {
	switch clusterState {
	case CLUSTER_LIFESTATE_DELETED:
		return true
	case CLUSTER_LIFESTATE_DELETING:
		return true
	case CLUSTER_LIFESTATE_CHARGE_DESTROY, CLUSTER_LIFESTATE_CHARGE_DESTROYED:
		return true
	default:
		return false
	}
}

func IsManagedCluster(clusterType ClusterType) bool {
	switch clusterType {
	case CLUSTER_TYPE_CLUSTER_INDEPENDENT:
		return false
	case CLUSTER_TYPE_CLUSTER_HUB:
		return false
	case CLUSTER_TYPE_CLUSTER_EXTERNAL:
		return false
	case CLUSTER_TYPE_CLUSTER_DEPLOY_ON_OPERATOR:
		return true
	default:
		return true
	}
}

// UnableAutoRecoverLifeStates is a list of cluster lifestates that are unable to be automatically recovered
func UnableAutoRecoverLifeStates() []string {
	return []string{CLUSTER_LIFESTATE_DELETED, CLUSTER_LIFESTATE_DELETING, CLUSTER_LIFESTATE_CHARGE_DESTROY, CLUSTER_LIFESTATE_CHARGE_DESTROYED}
}
