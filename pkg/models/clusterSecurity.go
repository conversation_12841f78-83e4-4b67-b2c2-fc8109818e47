package models

import (
	"time"
)

type ClusterSecurity struct {
	ClusterInstanceId  string    `gorm:"column:clusterInstanceId"`
	CACrt              string    `gorm:"column:caCrt"`
	CAKey              string    `gorm:"column:caKey"`
	ApiServerCrt       string    `gorm:"column:apiServerCrt"`
	ApiServerKey       string    `gorm:"column:apiServerKey"`
	ApiServerClientCrt string    `gorm:"column:apiServerClientCrt"`
	ApiServerClientKey string    `gorm:"column:apiServerClientKey"`
	AdminPasswd        string    `gorm:"column:adminPasswd"`
	KubeletPasswd      string    `gorm:"column:kubeletPasswd"`
	KubeProxyPasswd    string    `gorm:"column:kubeproxyPasswd"`
	EtcdServerCrt      string    `gorm:"column:etcdServerCrt"`
	EtcdServerKey      string    `gorm:"column:etcdServerKey"`
	ServiceAccount     string    `gorm:"column:serviceAccount"`
	CreatedAt          time.Time `gorm:"column:createdAt"`
	UpdatedAt          time.Time `gorm:"column:updatedAt"`
	DeleteAt           time.Time `gorm:"column:deletedAt"`
}

func (c *ClusterSecurity) TableName() string {
	return "clusterSecurity"
}
