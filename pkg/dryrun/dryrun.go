package dryrun

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/samber/lo"

	"git.woa.com/kateway/kateway-server/cmd/kops/kops/common"
)

const (
	ResourceTypeIngress = "Ingress"
	ResourceTypeService = "Service"
)

type Resource struct {
	Type      string
	Namespace string
	Name      string
}

func (r Resource) String() string {
	return fmt.Sprintf("%s/%s/%s", r.Type, r.Namespace, r.Name)
}

type Record struct {
	Resource  Resource
	Action    string
	Request   string
	Reason    string
	Error     string
	Ignorable bool
}

type Records []Record

func ParseRecordsFromLogs(raws []string) Records {
	rs := []Record{}
	for _, raw := range raws {
		if r := ParseRecordFromLog(raw); r != nil {
			rs = append(rs, *r)
		}
	}
	rs = append(rs, NewRecordFromMapSlice(CollectPanicErrors(raws))...)
	return rs
}

func (rs Records) ToMap() []map[string]string {
	m := make([]map[string]string, 0)
	for _, r := range rs {
		m = append(m, r.ToMap())
	}
	return m
}

func NewRecordFromMapSlice(ms []map[string]string) Records {
	rs := []Record{}
	for _, m := range ms {
		rs = append(rs, NewRecordFromMap(m))
	}
	return rs
}

func NewRecordFromMap(m map[string]string) Record {
	r := Record{}
	if m["Error"] != "" && m["Error"] != "nil" {
		r.Error = m["Error"]
		return r
	}
	if rt, nn, err := common.ParseTypedName(m["ServiceName"]); err == nil {
		r.Resource = Resource{
			Type:      rt,
			Namespace: nn.Namespace,
			Name:      nn.Name,
		}
	}
	r.Action = m["Action"]
	r.Request = m["Request"]
	r.Reason = m["Reason"]
	r.Ignorable = m["Analysis"] == "Ignore"
	return r
}

func ParseRecordFromLog(log string) *Record {
	var r *Record
	if strings.HasPrefix(log, "MockError") {
		split := strings.Split(log, "\t")
		if len(split) >= 5 {
			rt, ns, name := common.MustParseTypedName(split[1])
			r = &Record{
				Resource: Resource{
					Type:      rt,
					Namespace: ns,
					Name:      name,
				},
			}
			r.Action = split[2]
			r.Request = split[3]
			r.Reason = split[4]
		}
	}
	return r
}

func (r Record) ToMap() map[string]string {
	if r.Error != "" {
		return map[string]string{
			"Error": r.Error,
		}
	}
	return map[string]string{
		"ServiceName": strings.Join([]string{r.Resource.Type, r.Resource.Namespace, r.Resource.Name}, "/"),
		"Action":      r.Action,
		"Request":     r.Request,
		"Reason":      r.Reason,
		"Analysis":    r.GetAnalysis(),
		"Error":       "nil",
	}
}

func (r Record) GetAnalysis() string {
	if r.Ignorable {
		return "Ignore"
	}
	return "<None>"
}

func BuildErrorFromResult(result []map[string]string) error {
	records := make([]map[string]string, 0)
	for index, record := range result {
		if analysis, exist := record["Analysis"]; exist && strings.HasPrefix(analysis, "Ignore") {
			continue
		}
		records = append(records, result[index])
	}
	actionRecords := map[string]map[string]int{}
	if len(records) != 0 {
		dryrunErrors := lo.FilterMap(records, func(r map[string]string, _ int) (error, bool) {
			msg := r["Error"]
			return errors.New(msg), msg != "" && msg != "nil"
		})
		if dryrunErr := errors.Join(dryrunErrors...); dryrunErr != nil {
			return dryrunErr
		}

		for _, r := range records {
			var name string
			if v, exists := r["ServiceName"]; exists {
				name = v
			} else if v, exists := r["IngressName"]; exists {
				name = v
			}
			if name != "" {
				if action, actionExists := r["Action"]; actionExists {
					if _, exists := actionRecords[name]; !exists {
						actionRecords[name] = map[string]int{}
					}
					actionRecords[name][action]++
				}
			}
		}

		return fmt.Errorf("result contains non-readonly actions %s", string(lo.Must(json.Marshal(actionRecords))))
	}
	return nil
}

func CollectPanicErrors(lines []string) []map[string]string {
	messages := []string{}
	for index := 0; index < len(lines); index++ {
		startLine := lines[index]
		if !strings.Contains(startLine, "E6007") {
			continue
		}
		endIndex := index + 1
		i := strings.Index(startLine, "E6007")
		startLine = startLine[i:]
		trace := []string{startLine}
		if strings.HasPrefix(startLine, "E6007 internal error") {
			for ; endIndex < len(lines) && lines[endIndex] != "[stacktrace done]"; endIndex++ {
				trace = append(trace, lines[endIndex])
			}
		}
		messages = append(messages, strings.Join(trace, "\n"))
		index = endIndex
	}
	return lo.Map(messages, func(msg string, _ int) map[string]string {
		return map[string]string{
			"Error": msg,
		}
	})
}
