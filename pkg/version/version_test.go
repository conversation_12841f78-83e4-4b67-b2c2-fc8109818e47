package version

import (
	"testing"

	"github.com/Masterminds/semver"
	"github.com/stretchr/testify/assert"
)

func TestParse(t *testing.T) {
	tests := []struct {
		input    string
		expected *semver.Version
		hasError bool
	}{
		{"1.2.3", semver.MustParse("1.2.3"), false},
		{"1.2.3-13-abcdefef-sfesfes", semver.MustParse("1.2.3"), false},
		{"v1.2.3", semver.MustParse("v1.2.3"), false},
	}

	for _, test := range tests {
		t.Run(test.input, func(t *testing.T) {
			result, err := Parse(test.input)
			if test.hasError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.expected, result)
			}
		})
	}
}

func TestCheck(t *testing.T) {
	tests := []struct {
		name              string
		version           string
		constraint        string
		want              bool
		wantErr           bool
		wantConstraintErr bool
	}{
		// 正常版本比较
		// 正常版本比较
		{
			name:       "基础匹配_成功",
			version:    "1.2.3",
			constraint: ">= 1.0.0",
			want:       true,
		},
		{
			name:       "基础匹配_失败",
			version:    "0.9.0",
			constraint: ">= 1.0.0",
			want:       false,
		},

		// 带预发布版本处理
		{
			name:       "预发布版本_匹配成功",
			version:    "2.3.4-beta",
			constraint: ">= 2.0.0",
			want:       true,
		},
		{
			name:       "预发布版本_匹配失败",
			version:    "1.9.9-rc1",
			constraint: ">= 2.0.0",
			want:       false,
		},

		// 边界测试
		{
			name:       "精确版本匹配",
			version:    "3.2.1",
			constraint: "3.2.1",
			want:       true,
		},
		{
			name:       "补丁版本差异匹配",
			version:    "3.2.2",
			constraint: "~3.2.1",
			want:       true,
		},

		// 错误处理
		{
			name:              "非法版本格式",
			version:           "invalid.version",
			constraint:        ">= 1.0.0",
			wantErr:           true,
			wantConstraintErr: false,
		},
		{
			name:              "非法约束条件",
			version:           "1.2.3",
			constraint:        "invalid constraint",
			wantErr:           false,
			wantConstraintErr: true,
		},
		// 复杂约束组合
		{
			name:       "多条件组合_满足",
			version:    "2.5.0",
			constraint: ">=2.4.0, <3.0.0",
			want:       true,
		},
		{
			name:       "多条件组合_满足",
			version:    "v2.5.0",
			constraint: ">=2.4.0",
			want:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := Check(tt.version, tt.constraint)

			// 错误处理验证
			if (err != nil) != (tt.wantErr || tt.wantConstraintErr) {
				t.Errorf("Check() error = %v, wantErr %v | constraintErr %v",
					err, tt.wantErr, tt.wantConstraintErr)
				return
			}

			// 当存在预期错误时不验证结果
			if tt.wantErr || tt.wantConstraintErr {
				return
			}

			if got != tt.want {
				t.Errorf("Check() = %v, want %v (version=%s constraint=%s)",
					got, tt.want, tt.version, tt.constraint)
			}
		})
	}
}
