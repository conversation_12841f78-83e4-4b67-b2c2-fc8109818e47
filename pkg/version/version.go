package version

import (
	"strings"

	"github.com/Masterminds/semver"
	"github.com/samber/lo"
)

func MustParse(v string) *semver.Version {
	return lo.Must(Parse(v))
}

func Parse(v string) (*semver.Version, error) {
	i := strings.Index(v, "-")
	if i > 0 {
		v = v[:i]
	}
	return semver.NewVersion(v)
}

// Check 比较版本规则 https://github.com/Masterminds/semver?tab=readme-ov-file#basic-comparisons
func Check(version string, versionConstraint string) (bool, error) {
	// 重要！移除预发布版本信息，在TKE集群中都带有这个，所以统一处理下，否则比较版本限制会失败
	// 具体参考：https://github.com/Masterminds/semver/issues/21
	v, err := Parse(version)
	if err != nil {
		return false, err
	}
	c, err := semver.NewConstraint(versionConstraint)
	if err != nil {
		return false, err
	}

	return c.Check(v), nil
}
