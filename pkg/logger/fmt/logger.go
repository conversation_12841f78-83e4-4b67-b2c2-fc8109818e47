/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKE
 * available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License"); you may not use this
 * file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

package fmt

import (
	"fmt"
	"log"
	"os"
	"sort"

	"git.woa.com/kateway/kateway-server/pkg/logger"
)

var std = log.New(os.Stderr, "", log.LstdFlags)

type Logger struct {
	labels map[string]interface{}
}

func New() *Logger {
	return &Logger{
		labels: map[string]interface{}{},
	}
}

func (l *Logger) With(labels map[string]interface{}) logger.Logger {
	nLogger := New()
	newLabels := map[string]interface{}{}
	for k, v := range l.labels {
		newLabels[k] = v
	}

	for k, v := range labels {
		newLabels[k] = v
	}

	nLogger.labels = newLabels
	return nLogger
}

func (l *Logger) WithField(k string, v string) *Logger {
	nLogger := &Logger{
		labels: map[string]interface{}{},
	}
	for k, v := range l.labels {
		nLogger.labels[k] = v
	}

	nLogger.labels[k] = v

	return nLogger
}

func (l *Logger) Message(prefix string, format string, args ...interface{}) string {
	message := prefix + " "
	message += fmt.Sprintf(format, args...)
	message += " | "

	keys := make([]string, 0)
	for k := range l.labels {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	for _, k := range keys {
		message += fmt.Sprintf("%s = %s | ", k, l.labels[k])
	}

	return message
}

func (l *Logger) Debugf(format string, args ...interface{}) {
	std.Println(l.Message("[DEBUG]", format, args...))
}

func (l *Logger) Infof(format string, args ...interface{}) {
	std.Println(l.Message("[INFO]", format, args...))
}

func (l *Logger) Warnf(format string, args ...interface{}) {
	std.Println(l.Message("[WARN]", format, args...))
}

func (l *Logger) Warningf(format string, args ...interface{}) {
	std.Println(l.Message("[WARN]", format, args...))
}

func (l *Logger) Errorf(format string, args ...interface{}) {
	std.Println(l.Message("[ERROR]", format, args...))
}
