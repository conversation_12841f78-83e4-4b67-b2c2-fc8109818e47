/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKE
 * available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License"); you may not use this
 * file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

package logrus

import (
	"fmt"
	"os"
	"runtime"
	"time"

	log "github.com/sirupsen/logrus"

	"git.woa.com/kateway/kateway-server/pkg/logger"
)

type Logger struct {
	lg *log.Logger
	*log.Entry
	labels map[string]interface{}
}

func New() *Logger {
	lg := log.New()
	lg.SetFormatter(&log.TextFormatter{
		CallerPrettyfier: func(f *runtime.Frame) (string, string) {
			return fmt.Sprintf("%v:L%v", f.Function, f.Line), ""
		},
		DisableQuote:    true,
		FullTimestamp:   true,
		TimestampFormat: time.StampMilli,
	})
	lg.SetOutput(os.Stdout)
	lg.SetLevel(log.InfoLevel)
	lg.SetReportCaller(true)
	return &Logger{
		lg:    lg,
		Entry: lg.WithFields(map[string]interface{}{}),
	}
}

func (l *Logger) With(labels map[string]interface{}) logger.Logger {
	nLogger := New()
	newLabels := map[string]interface{}{}
	for k, v := range l.labels {
		newLabels[k] = v
	}

	for k, v := range labels {
		newLabels[k] = v
	}

	nLogger.labels = newLabels
	nLogger.Entry = nLogger.Logger.WithFields(newLabels)
	return nLogger
}
