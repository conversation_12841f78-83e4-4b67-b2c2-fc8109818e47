package task

import (
	"context"
	"errors"
	"fmt"
	"os"
	"reflect"
	"time"

	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/util/workqueue"

	"git.woa.com/kateway/pkg/telemetry/jaeger"

	"git.woa.com/kateway/kateway-server/pkg/alarm"
	"git.woa.com/kateway/kateway-server/pkg/concurrent"
	"git.woa.com/kateway/kateway-server/pkg/task/model"
	"git.woa.com/kateway/kateway-server/pkg/tmp/telemetry/log"
)

type Engine struct {
	queue            workqueue.RateLimitingInterface
	workerPoolByTask map[string]*errgroup.Group
	concurrency      int
	syncTimeout      time.Duration
	resync           time.Duration
	svc              *Service
}

func NewEngine(taskSvc *Service, concurrency int, timeout, resync time.Duration) *Engine {
	return &Engine{
		queue:            workqueue.NewRateLimitingQueue(workqueue.NewItemExponentialFailureRateLimiter(1*time.Second, 5*time.Minute)),
		workerPoolByTask: map[string]*errgroup.Group{},
		concurrency:      concurrency,
		syncTimeout:      timeout,
		resync:           resync,
		svc:              taskSvc,
	}
}

func (e *Engine) Shutdown(ctx context.Context) {
	log.FromContext(ctx).Info("Shutting down...")

	e.queue.ShutDown()

	for _, pool := range e.workerPoolByTask {
		_ = pool.Wait()
	}
}

func (e *Engine) Run(ctx context.Context) chan error {
	log.FromContext(ctx).Info("Starting...")

	errCh := make(chan error, 1)
	go func() {
		var (
			lastCreatedAt time.Time
		)

		_ = wait.PollImmediateUntilWithContext(ctx, e.resync, func(ctx context.Context) (done bool, err error) {
			opts := []ListOption{
				WithStates(model.TaskStateRunning, model.TaskStatePending),
				WithSelectColumns("ID", "CreatedAt"),
				WithTypes(GetRegistered()...),
				WithDescOrder("CreatedAt"),
			}

			if !lastCreatedAt.IsZero() {
				opts = append(opts, WithColumnOp("CreatedAt", ">", lastCreatedAt))
			}

			tasks, _, err := e.svc.List(ctx, opts...)
			if err != nil {
				errCh <- err
				return true, nil
			}

			for _, t := range tasks {
				e.queue.Add(t.ID)
			}

			if len(tasks) > 0 {
				lastCreatedAt = tasks[0].CreatedAt
			}

			return
		})
	}()

	go func() {
		dispatch := func() bool {
			key, quit := e.queue.Get()
			if quit {
				return false
			}

			k, _ := key.(string)
			if err := e.dispatch(ctx, k); err != nil {
				errCh <- err
				return false
			}
			return true
		}

		for dispatch() {
		}
	}()

	return errCh
}

func (e *Engine) dispatch(ctx context.Context, taskID string) error {
	task, err := e.svc.GetByID(ctx, taskID)
	if err != nil {
		defer e.queue.Done(taskID)

		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		return err
	}
	pool, exists := e.workerPoolByTask[task.Type]
	if !exists {
		cfg := GetHandler(task.Type).Config()
		pool = &errgroup.Group{}
		if cfg.Concurrency != ConcurrencyInfinite {
			pool.SetLimit(cfg.Concurrency)
		}
		e.workerPoolByTask[task.Type] = pool
	}
	pool.Go(func() error {
		defer e.queue.Done(taskID)

		e.process(ctx, task)
		return nil
	})
	return nil
}

func (e *Engine) process(ctx context.Context, tt *model.Task) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	if tt.IsFinished() {
		return
	}

	span.SetTag("task_id", tt.ID)
	span.SetTag("task_state", tt.State)
	span.SetTag("task_type", tt.Type)
	span.LogKV("task", jaeger.JSON(tt))
	if tt.ParentID != "" {
		span.SetTag("task_parent_id", tt.ParentID)
	}

	ctx, cancel := context.WithTimeout(ctx, e.syncTimeout)
	defer cancel()

	logger := log.FromContext(ctx)
	ctx = log.WithContext(ctx, logger.WithName(tt.ID))
	ctx = ContextWithTask(ctx, tt)
	ctx = ContextWithService(ctx, e.svc)
	func() {
		span, _ := jaeger.StartSpanFromContext(ctx, jaeger.WithOperationName(fmt.Sprintf("task.%s.%s", tt.Type, tt.ID)))
		defer span.Finish()

		span.SetTag("task_id", tt.ID)
		span.SetTag("task_state", tt.State)
		span.SetTag("task_type", tt.Type)
		span.LogKV("task", jaeger.JSON(tt))
		if tt.ParentID != "" {
			span.SetTag("task_parent_id", tt.ParentID)
		}
	}()

	startTime := time.Now()
	var err error
	defer func() {
		duration := time.Since(startTime).String()

		if s := recover(); s != nil {
			if stack := concurrent.GetPanicStackTrace(s); stack != "" {
				err = errors.New(stack)

				req := (&alarm.Request{
					ObjType: "module",
					ObjName: reflect.TypeOf(*e).String(),
					ObjID:   fmt.Sprint(tt.ID),
					Content: "Panic",
				}).WithLabels("jaeger", jaeger.QueryURL(jaeger.WithTags("key", fmt.Sprint(tt.ID)))).
					WithValues("Stack", stack)
				if err := alarm.Send(req); err != nil {
					log.FromContext(ctx).Error(err, "Failed to send alarm")
				}
			}
			e.queue.Forget(tt.ID)
		}

		if err != nil {
			logger.Error(err, "Sync error", "duration", duration)

			jaeger.LogError(span, err)
		}

		span.LogKV("msg", "sync done", "duration", duration)
		span.Finish()
	}()

	if err = e.handleTask(ctx, tt); err != nil {
		e.queue.AddRateLimited(tt.ID)
		return
	}
	e.queue.Forget(tt.ID)
}

func (e *Engine) handleTask(ctx context.Context, tt *model.Task) (err error) {
	cfg := GetHandler(tt.Type).Config()
	if tt.Status.CumulativeExecutionTime > model.Duration(cfg.Timeout.Duration) {
		abandon, err := e.onTaskTimeout(ctx, tt)
		if err != nil || abandon {
			return err
		}
	}

	start := time.Now()
	defer func() {
		duration := time.Since(start)
		tt.Status.ExecutingOn = ""
		tt.Status.CumulativeExecutionTime += model.Duration(duration)

		var fail bool
		if err != nil {
			tt.LastError = err.Error()
			if ew, ok := lo.ErrorsAs[*wrapError](err); ok && ew.fail {
				fail = true
				// 不返回错误，防止重试
				err = nil
			}
		}

		if !tt.IsFinished() && fail {
			tt.State = model.TaskStateFailed
		}

		if tt.IsFinished() {
			tt.FinishedAt = lo.ToPtr(time.Now())
		}

		_ = e.svc.Updates(ctx, tt, WithUpdateColumns("State", "Status", "FinishedAt", "LastError"))
	}()

	return e.executeTask(ctx, tt)
}

func (e *Engine) executeTask(ctx context.Context, tt *model.Task) (err error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	taskHandler := GetHandler(tt.Type)
	if !tt.IsUnfinished() {
		panic(fmt.Errorf("invalid task state %q", tt.State))
	}
	driver := &stepDriver{
		nextState: model.TaskStateSucceeded,
		svc:       e.svc,
		handler:   taskHandler,
	}

	err = func() error {
		span, ctx := jaeger.StartSpanFromContext(ctx, jaeger.WithOperationName("Init"))
		defer span.Finish()

		return taskHandler.Init(ctx, tt.Input)
	}()
	if err != nil {
		return fmt.Errorf("task init error: %w", err)
	}

	err = func() error {
		span, ctx := jaeger.StartSpanFromContext(ctx, jaeger.WithOperationName("DetectConflicts"))
		defer span.Finish()

		return taskHandler.DetectConflicts(ctx, func() error {
			tt.State = model.TaskStateRunning
			tt.Status.ExecutionsTotal++
			tt.Status.ExecutingOn, _ = os.Hostname()
			return e.svc.Updates(ctx, tt, WithUpdateColumns("State", "Status"))
		})
	}()
	if err != nil {
		return fmt.Errorf("detect conflicts error: %w", err)
	}

	err = func() error {
		span, ctx := jaeger.StartSpanFromContext(ctx, jaeger.WithOperationName(string(tt.State)))
		defer span.Finish()

		return driver.Run(ctx, tt)
	}()
	if err != nil {
		return fmt.Errorf("run task steps error: %w", err)
	}

	tt.State = driver.nextState
	return e.svc.Updates(ctx, tt, WithUpdateColumns("State"))
}

func (e *Engine) onTaskTimeout(ctx context.Context, tt *model.Task) (abandon bool, err error) {
	req := (&alarm.Request{
		ObjType: "module",
		ObjName: "Controller/Task",
		ObjID:   tt.ID,
		Content: "任务执行超时",
	}).WithLabels("jaeger", jaeger.QueryURL(jaeger.WithTags("task", tt.ID))).
		WithValues("Task", tt)
	if err := alarm.Send(req); err != nil {
		log.FromContext(ctx).Error(err, "send alarm error")
	}
	th := GetHandler(tt.Type)
	abandon = th.Config().Timeout.Strategy == TimeoutStrategyFail
	if abandon {
		tt.State = model.TaskStateFailed
		tt.FinishedAt = lo.ToPtr(time.Now())
		err = e.svc.Updates(ctx, tt, WithUpdateColumns("State", "FinishedAt"))
	}
	return
}

func resetTask(h *Handler, tt *model.Task) {
	tt.Progress = model.TaskProgress{}
	for _, step := range h.Steps() {
		tt.Progress.AddInitStep(step.Name)
	}
}
