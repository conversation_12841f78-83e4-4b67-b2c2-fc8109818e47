package task

import (
	"context"

	"gorm.io/gorm"

	"git.woa.com/kateway/kateway-server/pkg/task/model"
)

type ctxKey int

const (
	ctxKeyTask ctxKey = iota
	ctxKeyService
	ctxKeyDB
)

func dbFromContext(ctx context.Context) *gorm.DB {
	v := ctx.Value(ctxKeyDB)
	if v == nil {
		return nil
	}
	db, _ := v.(*gorm.DB)
	return db
}

func contextWithDB(ctx context.Context, db *gorm.DB) context.Context {
	return context.WithValue(ctx, ctxKeyDB, db)
}

func ContextWithService(ctx context.Context, svc *Service) context.Context {
	return context.WithValue(ctx, ctxKeyService, svc)
}

func ServiceFromContext(ctx context.Context) *Service {
	svc, _ := ctx.Value(ctxKeyService).(*Service)
	return svc
}

func ContextWithTask(ctx context.Context, task *model.Task) context.Context {
	return context.WithValue(ctx, ctxKeyTask, task)
}

func FromContext(ctx context.Context) *model.Task {
	task, _ := ctx.Value(ctxKeyTask).(*model.Task)
	return task
}
