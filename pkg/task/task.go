package task

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"sync"
	"time"

	"github.com/samber/lo"

	"git.woa.com/kateway/kateway-server/pkg/concurrent"
	"git.woa.com/kateway/kateway-server/pkg/runtime"
)

func init() {
	Register(&Batch{})
}

type TimeoutStrategy int

const (
	TimeoutStrategyRetry TimeoutStrategy = iota
	TimeoutStrategyFail
)

const (
	TimeoutDurationInfinite = time.Duration(0)
)

const (
	ConcurrencyInfinite = -1
)

type TimeoutConfig struct {
	Duration time.Duration
	Strategy TimeoutStrategy
}

type Config struct {
	Timeout     TimeoutConfig
	Concurrency int
}

func GetDefaultConfig() Config {
	return Config{
		Timeout: TimeoutConfig{
			Duration: 10 * time.Minute,
			Strategy: TimeoutStrategyFail,
		},
		Concurrency: 10,
	}
}

type Configurable interface {
	Config() Config
}

type ConflictsDetector interface {
	DetectConflicts(ctx context.Context) error
}

type WithExtensionStatus interface {
	Status(context.Context) any
}

type WithInitializer interface {
	Init(context.Context) error
}

type Task interface {
	Steps() Steps
}

type Steps []Step

func (s Steps) Names() []string {
	var names []string
	for _, item := range s {
		names = append(names, item.Name)
	}

	return names
}

func (s *Steps) AddStep(step Step) {
	*s = append(*s, step)
}

func (s *Steps) AddStepFuncs(fns ...StepFunc) {
	for _, fn := range fns {
		s.AddNamedStepFunc(runtime.FuncName(fn), fn)
	}
}

func (s *Steps) AddNamedStepFunc(name string, fn StepFunc) {
	exe := StepExecutor{
		Fn: fn,
		// TimeoutDuration: 1 * time.Minute,
	}
	*s = append(*s, Step{Name: name, Executor: exe})
}

type StepExecutor struct {
	Fn StepFunc
	// TimeoutDuration time.Duration
}

func (e *StepExecutor) Run(ctx context.Context) (err error) {
	defer func() {
		if s := recover(); s != nil {
			if stack := concurrent.GetPanicStackTrace(s); stack != "" {
				err = errors.New(stack)
			}
		}
	}()

	err = e.Fn(ctx)
	return
}

type Step struct {
	Name     string
	Executor StepExecutor
}

type StepFunc func(context.Context) error

type Handler struct {
	t    Task
	lock *sync.Mutex
}

func (h *Handler) Steps() Steps {
	return h.t.Steps()
}

func (h *Handler) Init(ctx context.Context, input string) error {
	if err := json.Unmarshal([]byte(input), h.t); err != nil {
		return err
	}
	if i, ok := h.t.(WithInitializer); ok {
		return i.Init(ctx)
	}
	return nil
}

func (h *Handler) Config() Config {
	c, ok := h.t.(Configurable)
	if !ok {
		return GetDefaultConfig()
	}
	return c.Config()
}

func (h *Handler) DetectConflicts(ctx context.Context, cb func() error) error {
	dc, ok := h.t.(ConflictsDetector)
	if !ok {
		return cb()
	}

	h.lock.Lock()
	defer h.lock.Unlock()

	if err := dc.DetectConflicts(ctx); err != nil {
		return err
	}
	return cb()
}

func (h *Handler) Status(ctx context.Context) any {
	s, ok := h.t.(WithExtensionStatus)
	if !ok {
		return nil
	}
	return s.Status(ctx)
}

type taskSpec struct {
	rt          reflect.Type
	centralLock *sync.Mutex
}

var (
	m     sync.RWMutex
	tasks = map[string]taskSpec{}
)

func IsRegistered(taskType string) bool {
	m.RLock()
	defer m.RUnlock()

	_, ok := tasks[taskType]
	return ok
}

func GetRegistered() []string {
	m.RLock()
	defer m.RUnlock()

	return lo.Keys(tasks)
}

func Register(task Task) {
	m.Lock()
	defer m.Unlock()

	rt := reflect.TypeOf(task)
	for rt.Kind() == reflect.Ptr {
		rt = rt.Elem()
	}
	tasks[rt.Name()] = taskSpec{
		rt:          rt,
		centralLock: &sync.Mutex{},
	}
}

func GetTaskType[T any]() string {
	return reflect.TypeOf(new(T)).Elem().Name()
}

func GetHandler(taskType string) *Handler {
	m.RLock()
	defer m.RUnlock()

	spec, ok := tasks[taskType]
	if !ok {
		panic(fmt.Sprintf("Task %q does not exist", taskType))
	}
	receiver := reflect.New(spec.rt)
	initialPointerValue(receiver)
	t, _ := receiver.Interface().(Task)
	return &Handler{
		t:    t,
		lock: spec.centralLock,
	}
}
