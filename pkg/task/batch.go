package task

import (
	"context"
	"errors"
	"fmt"

	"git.woa.com/kateway/kateway-server/pkg/task/model"
)

type BatchStatus struct {
	SubTaskTotal      int
	FinishedTotal     int
	ExecutionDuration string `json:",omitempty"`
}

type Batch struct {
	svc    *Service
	parent *model.Task
}

func (b *Batch) Init(ctx context.Context) error {
	mt := FromContext(ctx)
	svc := ServiceFromContext(ctx)

	b.svc = svc
	b.parent = mt
	return nil
}

func (b *Batch) Steps() (steps Steps) {
	steps.AddStepFuncs(
		b.waitSubTasks,
	)
	return
}

func (b *Batch) waitSubTasks(ctx context.Context) error {
	tasks, _, err := b.svc.List(ctx, WithParentIDs(b.parent.ID), WithSelectColumns("ID", "LastError", "State"))
	if err != nil {
		return fmt.Errorf("failed to list sub tasks: %w", err)
	}

	finished := true
	var final error
	for _, t := range tasks {
		if t.LastError != "" {
			final = errors.Join(final, fmt.Errorf("sub task %s error: %s", t.ID, t.LastError))
		}
		finished = finished && t.IsFinished()
	}
	if finished {
		return nil
	}
	if final != nil {
		return final
	}
	return errors.New("unfinished")
}

func (b *Batch) Status(ctx context.Context) any {
	subTaskTotal, err := b.svc.Count(ctx, WithParentIDs(b.parent.ID))
	if err != nil {
		return fmt.Errorf("count sub tasks error: %w", err)
	}
	finishedTotal, err := b.svc.Count(ctx, WithParentIDs(b.parent.ID), WithStates(model.TaskStatesFinished...))
	if err != nil {
		return fmt.Errorf("count finished tasks error: %w", err)
	}

	var executionTime string
	if subTaskTotal == finishedTotal {
		tasks, _, err := b.svc.List(ctx,
			WithParentIDs(b.parent.ID),
			WithDescOrder("FinishedAt"),
			WithLimit(1),
		)
		if err != nil {
			return fmt.Errorf("list sub tasks error: %w", err)
		}
		executionTime = (*tasks[0].FinishedAt).Sub(b.parent.CreatedAt).String()
	}

	return BatchStatus{
		SubTaskTotal:      subTaskTotal,
		FinishedTotal:     finishedTotal,
		ExecutionDuration: executionTime,
	}
}
