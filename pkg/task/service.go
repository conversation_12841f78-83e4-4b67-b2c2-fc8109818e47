package task

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"
	"k8s.io/apimachinery/pkg/util/wait"

	"git.woa.com/kateway/kateway-server/pkg/task/model"
)

var (
	ErrResourceNotFound = errors.New("resource not found")
)

type Service struct {
	db *gorm.DB
}

func NewService(db *gorm.DB) *Service {
	s := &Service{
		db: db,
	}
	return s
}

func (s Service) GetByID(ctx context.Context, id string) (*model.Task, error) {
	var t model.Task

	result := s.getDB(ctx).Where("id = ?", id).First(&t)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, ErrResourceNotFound
		}
		return nil, result.Error
	}
	return &t, nil
}

type UpdateOption func(*gorm.DB) *gorm.DB

func WithUpdateColumns(cols ...string) UpdateOption {
	return func(db *gorm.DB) *gorm.DB {
		return db.Select(cols[0], lo.ToAnySlice(cols[1:])...)
	}
}

func (s Service) Updates(ctx context.Context, task *model.Task, opts ...UpdateOption) error {
	db := s.getDB(ctx)
	for _, opt := range opts {
		db = opt(db)
	}

	result := db.Model(task).Updates(task)
	return result.Error
}

type ListOption func(*gorm.DB) *gorm.DB

func WithColumnOp(col, op string, v any) ListOption {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where(fmt.Sprintf("%s %s ?", col, op), v)
	}
}

func WithoutTaskID(id string) ListOption {
	return WithColumnOp("ID", "!=", id)
}

func WithInputLike(pattern string) ListOption {
	return WithColumnOp("Input", "LIKE", pattern)
}

func WithStates(states ...model.TaskState) ListOption {
	return WithColumnOp("State", "IN", states)
}

func WithTaskIDs(ids ...string) ListOption {
	return WithColumnOp("ID", "IN", ids)
}

func WithSubTypes(subTypes ...string) ListOption {
	return WithColumnOp("SubType", "IN", subTypes)
}

func WithParentIDs(parentIDs ...string) ListOption {
	return WithColumnOp("ParentID", "IN", parentIDs)
}

func WithTypes(types ...string) ListOption {
	return WithColumnOp("Type", "IN", types)
}

func WithSelectColumns(cols ...string) ListOption {
	return func(db *gorm.DB) *gorm.DB {
		return db.Select(cols[0], lo.ToAnySlice(cols[1:])...)
	}
}

func WithAscOrder(cols ...string) ListOption {
	return func(db *gorm.DB) *gorm.DB {
		return db.Order(strings.Join(cols, ", ") + " ASC")
	}
}

func WithDescOrder(cols ...string) ListOption {
	return func(db *gorm.DB) *gorm.DB {
		return db.Order(strings.Join(cols, ", ") + " DESC")
	}
}

func WithLimit(limit int) ListOption {
	return func(db *gorm.DB) *gorm.DB {
		return db.Limit(limit)
	}
}

func WithOffset(offset int) ListOption {
	return func(db *gorm.DB) *gorm.DB {
		return db.Offset(offset)
	}
}

func (s Service) List(ctx context.Context, opts ...ListOption) ([]model.Task, int, error) {
	var tasks []model.Task

	db := s.getDB(ctx)

	for _, opt := range opts {
		db = opt(db)
	}

	if err := db.Find(&tasks).Error; err != nil {
		return nil, 0, err
	}

	count, err := s.Count(ctx, opts...)
	if err != nil {
		return nil, 0, err
	}

	return tasks, count, err
}

func newModel(taskType string, input any) *model.Task {
	mt := &model.Task{
		Type:  taskType,
		Input: string(lo.Must(json.Marshal(input))),
	}

	return mt
}

func (s Service) Count(ctx context.Context, opts ...ListOption) (int, error) {
	var count int64

	db := s.getDB(ctx)

	for _, opt := range opts {
		db = opt(db)
	}
	db = db.Offset(-1)

	err := db.Model(&model.Task{}).Count(&count).Error
	return int(count), err
}

func (s Service) CreateByTasks(ctx context.Context, tasks ...*model.Task) error {
	if len(tasks) == 0 {
		return nil
	}

	for _, t := range tasks {
		t.ID = generateID()
		t.State = model.TaskStatePending
	}

	result := s.getDB(ctx).Model(&model.Task{}).Create(tasks)
	return result.Error
}

type CreateOption func(*model.Task)

func WithParent(parent string) CreateOption {
	return func(t *model.Task) {
		t.ParentID = parent
	}
}

func WithCreator(creator string) CreateOption {
	return func(t *model.Task) {
		t.Creator = creator
	}
}

func (s Service) CreateBatchTask(ctx context.Context, inputs []any, opts ...CreateOption) (taskID string, err error) {
	taskType := GetTaskType[Batch]()

	subType := parseTaskTypeFromInput(inputs[0])
	t := newModel(taskType, Batch{})
	t.SubType = subType

	for _, opt := range opts {
		opt(t)
	}

	err = s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		ctx = contextWithDB(ctx, tx)
		if err := s.CreateByTasks(ctx, t); err != nil {
			return err
		}
		taskID = t.ID

		subTasks := lo.Map(inputs, func(i any, _ int) *model.Task {
			sub := newModel(subType, i)
			sub.ParentID = taskID
			sub.Creator = t.Creator
			return sub
		})
		return s.CreateByTasks(ctx, subTasks...)
	})

	return
}

func (s Service) Create(ctx context.Context, input any, opts ...CreateOption) (string, error) {
	taskType := parseTaskTypeFromInput(input)

	t := newModel(taskType, input)
	for _, opt := range opts {
		opt(t)
	}
	if err := s.CreateByTasks(ctx, t); err != nil {
		return "", err
	}
	return t.ID, nil
}

func (s Service) getDB(ctx context.Context) *gorm.DB {
	db := dbFromContext(ctx)
	if db == nil {
		return s.db.WithContext(ctx)
	}
	return db.WithContext(ctx)
}

func (s Service) WaitTask(ctx context.Context, taskID string, cb func(*model.Task)) error {
	return wait.PollImmediateInfiniteWithContext(ctx, 1*time.Second, func(ctx context.Context) (done bool, err error) {
		t, err := s.GetByID(ctx, taskID)
		if err != nil {
			return false, err
		}
		cb(t)
		return t.IsFinished(), nil
	})
}

func (s Service) Abort(ctx context.Context, taskID string) error {
	task := &model.Task{
		ID:    taskID,
		State: model.TaskStateAborted,
	}

	return s.Updates(ctx, task, WithUpdateColumns("State"))
}
