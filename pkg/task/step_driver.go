package task

import (
	"context"
	"fmt"

	"git.woa.com/kateway/kateway-server/pkg/task/model"
	"git.woa.com/kateway/kateway-server/pkg/tmp/telemetry/jaeger"
)

type stepDriver struct {
	nextState model.TaskState
	svc       *Service
	handler   *Handler
}

func (s *stepDriver) Run(ctx context.Context, task *model.Task) error {
	for _, step := range s.handler.Steps() {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}
		// 重置进度，兼容新增step
		if task.Progress.Steps.Get(step.Name) == nil {
			resetTask(s.handler, task)
		}

		if task.Progress.Steps.IsSucceeded(step.Name) {
			continue
		}

		err := func() (err error) {
			span, ctx := jaeger.StartSpanFromContext(ctx, jaeger.WithOperationName(step.Name))
			defer func() {
				if err != nil {
					task.Progress.Steps.Fail(step.Name, err.Error())
				} else {
					task.Progress.Steps.Succeed(step.Name)
				}
				task.Status.Extension = s.handler.Status(ctx)
				_ = s.svc.Updates(ctx, task, WithUpdateColumns("Status", "Progress"))

				span.LogKV("Progress", jaeger.JSON(task.Progress.Steps))
				span.Finish()
			}()

			task.Progress.Steps.Start(step.Name)
			span.LogKV("Progress", jaeger.JSON(task.Progress.Steps))
			if err = s.svc.Updates(ctx, task, WithUpdateColumns("Progress")); err != nil {
				return err
			}

			return step.Executor.Run(ctx)
		}()

		if err != nil {
			return fmt.Errorf("run step %q error: %w", step.Name, err)
		}
	}

	return nil
}
