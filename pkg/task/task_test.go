package task

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
)

type params struct {
	P1 string
	P2 int
}

type T struct {
	Input params
	step1 string
}

func (t *T) Init(_ context.Context) error {
	t.step1 = "init"
	return nil
}

func (t *T) Steps() (steps Steps) {
	if t.Input.P1 == "p1" {
		steps.AddNamedStepFunc("p1", t.Step1)
	} else {
		steps.AddNamedStepFunc("test", t.Step1)
	}
	return
}

func (t *T) Step1(_ context.Context) error {
	if t.step1 == "init" {
		t.step1 = "test"
	}
	return nil
}

func (T) DetectConflicts(_ context.Context, p params) error {
	fmt.Println(p)
	return nil
}

// nolint:errcheck
func TestTask(t *testing.T) {
	ctx := context.Background()
	Register(&T{})
	h := GetHandler("T")
	input := T{
		Input: params{
			P1: "p1",
			P2: 2,
		},
	}
	err := h.Init(ctx, string(lo.Must(json.<PERSON>(input))))
	if err != nil {
		t.Fatal(err)
	}

	steps := h.Steps()

	assert.Equal(t, "p1", h.t.(*T).Input.P1)
	assert.Equal(t, 2, h.t.(*T).Input.P2)
	for _, step := range steps {
		if err := step.Executor.Run(ctx); err != nil {
			t.Fatal(err)
		}
	}
	assert.Equal(t, "test", h.t.(*T).step1)
	assert.Equal(t, GetDefaultConfig(), h.Config())
}
