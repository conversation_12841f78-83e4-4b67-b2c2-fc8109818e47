package task

import (
	"fmt"
	"reflect"

	utilrand "k8s.io/apimachinery/pkg/util/rand"
)

func initialPointerValue(v reflect.Value) {
	for v.Kind() == reflect.Ptr {
		if v.IsNil() {
			v.Set(reflect.New(v.Type().Elem()))
		}
		v = v.Elem()
	}
}

func parseTaskTypeFromInput(input any) string {
	rt := reflect.TypeOf(input)
	for rt.Kind() == reflect.Pointer || rt.Kind() == reflect.Interface {
		rt = rt.Elem()
	}
	return rt.Name()
}

func generateID() string {
	return fmt.Sprintf("%s-%s", "task", utilrand.String(8))
}
