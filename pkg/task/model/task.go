package model

import (
	"encoding/json"
	"time"

	"github.com/samber/lo"
)

type TaskState string

const (
	TaskStatePending   TaskState = "Pending"
	TaskStateRunning   TaskState = "Running"
	TaskStateSucceeded TaskState = "Succeeded"
	TaskStateFailed    TaskState = "Failed"
	TaskStateAborted   TaskState = "Aborted"
)

var (
	TaskStatesFinished = []TaskState{
		TaskStateSucceeded,
		TaskStateFailed,
		TaskStateAborted,
	}
)

type Task struct {
	ID        string `gorm:"primarykey"`
	ParentID  string
	Type      string
	SubType   string
	State     TaskState
	Input     string
	Progress  TaskProgress `gorm:"serializer:json"`
	LastError string
	Creator   string
	Status    TaskStatus `gorm:"serializer:json"`

	CreatedAt  time.Time
	UpdatedAt  time.Time
	FinishedAt *time.Time
}

type Duration time.Duration

func (d Duration) MarshalJSON() ([]byte, error) {
	return json.Marshal(time.Duration(d).String())
}

func (d *Duration) UnmarshalJSON(b []byte) error {
	var v string
	if err := json.Unmarshal(b, &v); err != nil {
		return err
	}
	tmp, err := time.ParseDuration(v)
	if err != nil {
		return err
	}
	*d = Duration(tmp)
	return nil
}

type TaskStatus struct {
	CumulativeExecutionTime Duration
	ExecutionsTotal         uint64
	ExecutingOn             string
	Extension               any
}

//func (ts TaskStatus) String() string {
//	b, _ := json.Marshal(ts)
//	return string(b)
//}
//
//func (ts TaskStatus) MarshalJSON() ([]byte, error) {
//	type wrap struct {
//		CumulativeExecutionTime Duration
//		ExecutionsTotal         uint64
//		ExecutingOn             string `json:",omitempty"`
//	}
//
//	if ts.Extension == nil {
//		w := wrap{
//			CumulativeExecutionTime: ts.CumulativeExecutionTime,
//			ExecutionsTotal:         ts.ExecutionsTotal,
//			ExecutingOn:             ts.ExecutingOn,
//		}
//		return json.Marshal(w)
//	}
//	ext := ts.Extension
//	extRaw, err := json.Marshal(ext)
//	if err != nil {
//		return nil, err
//	}
//	res := gjson.ParseBytes(extRaw)
//	if !res.IsObject() {
//		return nil, errors.New("field Extension must be a struct or map")
//	}
//
//	ts.Extension = nil
//	base, err := json.Marshal(ts)
//	if err != nil {
//		return nil, err
//	}
//	res.ForEach(func(key, value gjson.Result) bool {
//		base, err = sjson.SetRawBytes(base, key.String(), []byte(value.Raw))
//		return err == nil
//	})
//	if err != nil {
//		return nil, err
//	}
//	return base, nil
//}

func (t Task) IsUnfinished() bool {
	return t.State == TaskStateRunning || t.State == TaskStatePending
}

func (t Task) IsRunning() bool {
	return t.State == TaskStateRunning
}

func (t Task) IsFinished() bool {
	return lo.Contains(TaskStatesFinished, t.State)
}

func (t Task) IsAborted() bool {
	return t.State == TaskStateAborted
}

type TaskProgress struct {
	Steps TaskSteps
}

func (p TaskProgress) String() string {
	b, _ := json.Marshal(p)
	return string(b)
}

type TaskStepState string

const (
	TaskStepStateRunning   TaskStepState = "Running"
	TaskStepStatePending   TaskStepState = "Pending"
	TaskStepStateSucceeded TaskStepState = "Succeeded"
	TaskStepStateFailed    TaskStepState = "Failed"
)

type TaskStep struct {
	Name      string
	State     TaskStepState
	StartedAt *time.Time `json:",omitempty"`
	EndedAt   *time.Time `json:",omitempty"`
	FailedMsg string     `json:",omitempty"`
}

type TaskSteps []TaskStep

func (p *TaskProgress) AddInitStep(name string) {
	step := TaskStep{
		Name:  name,
		State: TaskStepStatePending,
	}

	p.AddSteps(step)
}

func (p *TaskProgress) AddSteps(step ...TaskStep) {
	p.Steps = append(p.Steps, step...)
}

func (s TaskSteps) Get(name string) *TaskStep {
	_, index, exist := lo.FindIndexOf(s, func(t TaskStep) bool {
		return t.Name == name
	})
	if !exist {
		return nil
	}
	return &s[index]
}

func (s TaskSteps) IsStarted(name string) bool {
	step := s.Get(name)
	if step == nil {
		return false
	}

	return step.StartedAt != nil
}

func (s TaskSteps) IsSucceeded(name string) bool {
	step := s.Get(name)
	if step == nil {
		return false
	}

	return step.State == TaskStepStateSucceeded
}

func (s TaskSteps) Start(name string) {
	step := s.Get(name)
	if step == nil {
		return
	}

	if step.State == TaskStepStatePending || step.State == TaskStepStateFailed {
		step.State = TaskStepStateRunning
		step.StartedAt = lo.ToPtr(time.Now())
		step.FailedMsg = ""
	}
}

func (s TaskSteps) Succeed(name string) {
	step := s.Get(name)
	if step == nil {
		return
	}

	if step.State != TaskStepStateSucceeded {
		step.FailedMsg = ""
		step.State = TaskStepStateSucceeded
		step.EndedAt = lo.ToPtr(time.Now())
	}
}

func (s TaskSteps) Fail(name, msg string) {
	step := s.Get(name)
	if step == nil {
		return
	}

	step.State = TaskStepStateFailed
	step.FailedMsg = msg
}
