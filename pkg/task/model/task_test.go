package model

/*import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestTaskStatus_MarshalJSON(t *testing.T) {
	tests := []struct {
		name    string
		ts      TaskStatus
		want    string
		wantErr bool
	}{
		{
			name: "normal case without extension",
			ts: TaskStatus{
				CumulativeExecutionTime: Duration(2 * time.Second),
				ExecutionsTotal:         1,
				ExecutingOn:             "node-1",
				Extension:               nil,
			},
			want:    `{"CumulativeExecutionTime":"2s","ExecutionsTotal":1,"ExecutingOn":"node-1"}`,
			wantErr: false,
		},
		{
			name: "with struct extension",
			ts: TaskStatus{
				CumulativeExecutionTime: Duration(3 * time.Second),
				ExecutionsTotal:         2,
				ExecutingOn:             "node-2",
				Extension: struct {
					Extra string   `json:"extra"`
					Count int      `json:"count"`
					Names []string `json:"names"`
				}{
					Extra: "test",
					Count: 42,
					Names: []string{"a", "b", "c"},
				},
			},
			want:    `{"CumulativeExecutionTime":"3s","ExecutionsTotal":2,"ExecutingOn":"node-2","extra":"test","count":42,"names":["a","b","c"]}`,
			wantErr: false,
		},
		{
			name: "with map extension",
			ts: TaskStatus{
				CumulativeExecutionTime: Duration(1 * time.Second),
				ExecutionsTotal:         3,
				ExecutingOn:             "node-3",
				Extension: map[string]interface{}{
					"key1": "value1",
					"key2": 123,
				},
			},
			want:    `{"CumulativeExecutionTime":"1s","ExecutionsTotal":3,"ExecutingOn":"node-3","key1":"value1","key2":123}`,
			wantErr: false,
		},
		{
			name: "with invalid extension type",
			ts: TaskStatus{
				CumulativeExecutionTime: Duration(1 * time.Second),
				ExecutionsTotal:         4,
				ExecutingOn:             "node-4",
				Extension:               "invalid", // string is not an object
			},
			want:    "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.ts.MarshalJSON()
			if tt.wantErr {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)

			// 验证生成的 JSON 是否符合预期
			var gotMap, wantMap map[string]interface{}
			err = json.Unmarshal(got, &gotMap)
			assert.NoError(t, err)
			err = json.Unmarshal([]byte(tt.want), &wantMap)
			assert.NoError(t, err)

			assert.Equal(t, wantMap, gotMap)
		})
	}
}

// 测试边缘情况
func TestTaskStatus_MarshalJSON_EdgeCases(t *testing.T) {
	t.Run("with empty extension map", func(t *testing.T) {
		ts := TaskStatus{
			CumulativeExecutionTime: Duration(1 * time.Second),
			ExecutionsTotal:         1,
			Extension:               map[string]interface{}{},
		}
		got, err := ts.MarshalJSON()
		assert.NoError(t, err)
		assert.JSONEq(t, `{"CumulativeExecutionTime":"1s","ExecutionsTotal":1}`, string(got))
	})

	t.Run("with nil extension map", func(t *testing.T) {
		ts := TaskStatus{
			CumulativeExecutionTime: Duration(1 * time.Second),
			ExecutionsTotal:         1,
			Extension:               nil,
		}
		got, err := ts.MarshalJSON()
		assert.NoError(t, err)
		assert.JSONEq(t, `{"CumulativeExecutionTime":"1s","ExecutionsTotal":1}`, string(got))
	})

	t.Run("with extension overriding base fields", func(t *testing.T) {
		ts := TaskStatus{
			CumulativeExecutionTime: Duration(1 * time.Second),
			ExecutionsTotal:         1,
			Extension: map[string]interface{}{
				"ExecutionsTotal": 999, // 尝试覆盖基础字段
			},
		}
		got, err := ts.MarshalJSON()
		assert.NoError(t, err)
		assert.JSONEq(t, `{"CumulativeExecutionTime":"1s","ExecutionsTotal":999}`, string(got))
	})
}
*/
