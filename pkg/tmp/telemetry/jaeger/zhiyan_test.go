package jaeger

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/opentracing/opentracing-go"
	"github.com/uber/jaeger-client-go/config"
)

func TestZhiYanURL(t *testing.T) {
	id := time.Now().String()
	Init(Config{
		Web: "https://zhiyan.woa.com/apm_monitor",
		ZhiYan: &<PERSON><PERSON><PERSON>an{
			ID:        id,
			Env:       "prod",
			ProjectID: "14315",
		},
		Configuration: config.Configuration{
			ServiceName: "tops",
			Reporter: &config.ReporterConfig{
				QueueSize:           1000,
				BufferFlushInterval: 1,
				LogSpans:            true,
				LocalAgentHostPort:  "trace.zhiyan.tencent-cloud.net:6831",
			},
			Tags: []opentracing.Tag{
				{
					Key:   "tps.tenant.id",
					Value: "4138#apm-log-bfd69ec5fdc2gab2#14315_109284___apm",
				},
			},
		},
	})
	span, _ := StartSpanFromContext(context.Background())
	span.SetTag("id", id)
	defer func() {
		span.Finish()
		Close()
	}()
	fmt.Println(ZhiYanURL())
}
