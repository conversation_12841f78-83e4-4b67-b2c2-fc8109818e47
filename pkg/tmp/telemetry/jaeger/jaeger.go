package jaeger

import (
	"context"
	"fmt"
	"io"
	"os"
	"os/user"
	"runtime"
	"strings"
	"time"

	"github.com/opentracing/opentracing-go"
	"github.com/opentracing/opentracing-go/log"
	"github.com/uber/jaeger-client-go"
	"github.com/uber/jaeger-client-go/config"
)

type Config struct {
	Enable               bool
	Web                  string `yaml:"web"`
	<PERSON><PERSON><PERSON>an               *ZhiYan
	config.Configuration `yaml:",inline"`
	startTime            time.Time
}

var defaultConfig *Config

var (
	tracer opentracing.Tracer
	closer io.Closer
)

// Init returns an instance of <PERSON><PERSON><PERSON> tracer that samples 100% of traces and logs all spans to stdout.
func Init(c Config) {
	defaultConfig = &c

	if !c.Enable {
		return
	}

	c.startTime = time.Now()
	c.Web = strings.TrimSuffix(c.Web, "/")

	if c.<PERSON> == nil {
		c.<PERSON> = &config.SamplerConfig{
			Type:  "const",
			Param: 1,
		}
	}

	var err error
	tracer, closer, err = c.NewTracer(config.Logger(jaeger.StdLogger))
	if err != nil {
		panic(fmt.Errorf("init jaeger error: %w", err))
	}

	opentracing.SetGlobalTracer(tracer)
}

func Close() {
	if !defaultConfig.Enable {
		return
	}
	closer.Close()
}

type SpanOption func(options *spanOptions)

type spanOptions struct {
	skip          int
	operationName string
	options       []opentracing.StartSpanOption
}

func WithSkip(n int) SpanOption {
	return func(options *spanOptions) {
		options.skip = n
	}
}

func WithOperationName(name string) SpanOption {
	return func(options *spanOptions) {
		options.operationName = name
	}
}

func WithStartSpanOptions(opts ...opentracing.StartSpanOption) SpanOption {
	return func(options *spanOptions) {
		options.options = opts
	}
}

func LogSystem(ctx context.Context, id string) {
	if !defaultConfig.Enable {
		return
	}

	span, _ := StartSpanFromContext(ctx)
	defer span.Finish()

	span.SetTag("id", id)
	span.LogSystem()
}

type Span struct {
	opentracing.Span
}

func (span *Span) LogSystem() {
	u, _ := user.Current()
	span.LogKV("user", u.Username)
	wd, _ := os.Getwd()
	span.LogKV("workDir", wd)
	span.LogKV("env", os.Environ())
	span.LogKV("args", os.Args)
}

func (span *Span) LogError(err error) {
	if !defaultConfig.Enable {
		return
	}

	if err == nil {
		return
	}

	span.SetTag("error", true)
	span.LogFields(log.Error(err))
}

func (span *Span) LogAny(key string, value any) {
	if !defaultConfig.Enable {
		return
	}

	span.LogKV(key, transValue(value))
}

func StartSpanFromContext(ctx context.Context, opts ...SpanOption) (*Span, context.Context) {
	options := new(spanOptions)
	for _, o := range opts {
		o(options)
	}

	if options.skip == 0 {
		options.skip = 1
	}

	pc, file, line, ok := runtime.Caller(options.skip)
	if !ok {
		panic("runtime.Caller error")
	}

	if options.operationName == "" {
		options.operationName = runtime.FuncForPC(pc).Name()
	}

	span, ctx := opentracing.StartSpanFromContext(ctx, options.operationName)
	span.LogKV("file:line", fmt.Sprintf("%v:%v", file, line))

	return &Span{span}, ctx
}
