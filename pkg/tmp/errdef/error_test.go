package errdef

import (
	"errors"
	"fmt"
	"testing"
)

func TestError(t *testing.T) {
	root := errors.New("root error")
	second := ErrLimitExceeded.Wrap(root)
	outter1 := fmt.<PERSON><PERSON>rf("outter1: %w", second)
	outter2 := fmt.<PERSON><PERSON><PERSON>("outter2: %w", second)

	if !errors.Is(outter1, second) {
		t.<PERSON>("not a second error")
	}
	if !errors.Is(outter1, root) {
		t.<PERSON>("not a root error")
	}
	if !errors.Is(outter1, ErrLimitExceeded) {
		t.<PERSON>("not a limit exceeded error")
	}
	if errors.Is(outter1, outter2) {
		t.<PERSON>("error outter1 is outter2")
	}
}
