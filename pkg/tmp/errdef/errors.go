package errdef

import (
	"fmt"
)

type Code int

const (
	errCodeResourceNotFound = iota
	errCodeResourceAlreadyExists
	errCodeUnauthorizedOperation
	errCodeConflictOperation
	errCodeLimitExceeded
)

type Error struct {
	code Code
	msg  string
	err  error
}

func New(c Code, msg string) *Error {
	return &Error{
		code: c,
		msg:  msg,
	}
}

func (e *Error) Wrap(err error) error {
	return &Error{
		code: e.code,
		msg:  e.msg,
		err:  err,
	}
}

func (e *Error) Error() string {
	if e.err == nil {
		return e.msg
	}
	return fmt.Sprintf("%s: %v", e.msg, e.err)
}

func (e *Error) Unwrap() error {
	return e.err
}

func (e *Error) Is(err error) bool {
	internal, ok := err.(*Error)
	if ok {
		return internal.code == e.code
	}
	return false
}

var (
	// ErrResourceNotFound means one or more resources required for the operation cloud not be found.
	ErrResourceNotFound = New(errCodeResourceNotFound, "not found")
	// ErrResourceAlreadyExists means the resource you are creating already exists.
	ErrResourceAlreadyExists = New(errCodeResourceAlreadyExists, "already exists")
	// ErrUnauthorizedOperation means the operation can not be fulfilled because of the lack of credentials.
	ErrUnauthorizedOperation = New(errCodeUnauthorizedOperation, "unauthorized operation")
	// ErrOperationDenied means the requested operation cannot be completed due to a conflict in the operation.
	// the caller may redo the request later.
	ErrOperationDenied = New(errCodeConflictOperation, "conflict operation")
	// ErrLimitExceeded means the requested operation cannot be fulfilled because of some resource limits. e.g. maximum value of resource quota.
	ErrLimitExceeded = New(errCodeLimitExceeded, "limit exceeded")
)
