package zhiyan

import (
	"encoding/json"
	"fmt"
	"net/url"
	"os"
	"time"

	"github.com/imdario/mergo"
	"github.com/segmentio/ksuid"

	logsdk "git.woa.com/zhiyan-log/sdk-go/v2"
)

type Config struct {
	host       string
	ProjectID  string
	Env        string
	EnvID      string
	ViewID     string
	Topic      string
	ServerAddr string
	Proto      string
}

type Client struct {
	config    *Config
	id        string
	fileds    map[string]any
	startTime time.Time

	client *logsdk.LogClient
}

var (
	config *Config
)

func Init(c *Config) {
	config = c
	config.host, _ = os.Hostname()
}

func New(fileds map[string]any) *Client {
	return &Client{
		config:    config,
		id:        ksuid.New().String(),
		fileds:    fileds,
		startTime: time.Now(),
		client:    logsdk.NewLogWithServerAddr(config.ServerAddr, config.Topic, config.Proto, config.host),
	}
}

func (c *Client) Send(msg string) error {
	obj := map[string]interface{}{
		"id":      c.id,
		"message": msg,
	}
	mergo.Merge(&obj, c.fileds)
	data, _ := json.Marshal(obj)
	return c.client.SendMessage(string(data))
}

func (c *Client) Sendf(format string, a ...any) error {
	return c.Send(fmt.Sprintf(format, a...))
}

func (c *Client) URL() string {
	params := url.Values{}
	params.Add("id", config.ViewID)
	params.Add("env", config.Env)
	params.Add("env_id", config.EnvID)
	params.Add("startTimeType", "absolute")
	params.Add("startTimeValue", fmt.Sprint(c.startTime.UnixMilli()))
	params.Add("endTimeType", "absolute")
	params.Add("endTimeValue", fmt.Sprint(time.Now().Add(time.Minute).UnixMilli()))
	filters, _ := json.Marshal([]map[string]interface{}{
		{
			"field":     "id",
			"operation": "eq",
			"selected":  "=",
			"fieldValue": map[string]interface{}{
				"value": c.id,
			},
		},
	})
	params.Add("filters", string(filters))

	return fmt.Sprintf("https://zhiyan.woa.com/log/%s/dataflow/#/analyze/query/view?%s", config.ProjectID, params.Encode())
}
