package yunapi

import (
	"context"

	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	tag "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tag/v20180813"

	"git.woa.com/kateway/kateway-server/pkg/tmp/telemetry/jaeger"
	"git.woa.com/kateway/kateway-server/pkg/tmp/tencentcloud/common/region"
	"git.woa.com/kateway/kateway-server/pkg/tmp/tencentcloud/sts"
	"git.woa.com/kateway/kateway-server/pkg/tmp/web/cloudctx"
)

type option struct {
	Region string
	UIN    string
}

type Option func(o *option)

func WithRegion(s string) Option {
	return func(o *option) {
		o.Region = region.Get(s).Name
	}
}

func WithUIN(s string) Option {
	return func(o *option) {
		o.UIN = s
	}
}

func newOption(ctx context.Context, options ...Option) *option {
	o := &option{
		Region: cloudctx.Region(ctx).Name,
		UIN:    cloudctx.UIN(ctx),
	}
	for _, f := range options {
		f(o)
	}

	return o
}

func Tag(ctx context.Context, options ...Option) *tag.Client {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	o := newOption(ctx, options...)

	credential, err := sts.AssumeRole(ctx, o.UIN)
	if err != nil {
		panic(err)
	}
	c, _ := tag.NewClient(credential, o.Region, profile.NewClientProfile())

	return c
}

func Clb(ctx context.Context, options ...Option) *clb.Client {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	o := newOption(ctx, options...)

	credential, err := sts.AssumeRole(ctx, o.UIN)
	if err != nil {
		panic(err)
	}
	c, _ := clb.NewClient(credential, o.Region, profile.NewClientProfile())

	return c
}
