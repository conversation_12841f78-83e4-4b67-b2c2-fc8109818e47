package region

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/samber/lo"
)

type Region struct {
	ID      int
	Name    string
	Alias   string
	CNName  string
	Area    string
	Oversea bool
	PreCCR  bool
}

const (
	AreaHuaNan       = "华南地区"
	AreaHuaDong      = "华东地区"
	AreaHuaBei       = "华北地区"
	AreaHuaZhong     = "华中地区"
	AreaXiNan        = "西南地区"
	AreaXiBei        = "西北地区"
	AreaGangAoTai    = "港澳台地区"
	AreaAPSoutheasst = "亚太东南"
	AreaAPSouth      = "亚太南部"
	AreaAPNortheast  = "亚太东北"
	AreaNA           = "北美地区"
	AreaSA           = "南美地区"
	AreaUSWestern    = "美国西部"
	AeraUSEastern    = "美国东部"
	AreaEU           = "欧洲地区"
)

var List = []Region{
	{
		ID:      1,
		Name:    "ap-guangzhou",
		Alias:   "gz",
		CNName:  "广州",
		Area:    AreaHuaNan,
		Oversea: false,
	}, {
		ID:      12,
		Name:    "ap-guangzhou-open",
		Alias:   "gzopen",
		CNName:  "广州Open",
		Area:    AreaHuaNan,
		Oversea: false,
	}, {
		ID:      54,
		Name:    "ap-qingyuan",
		CNName:  "清远",
		Alias:   "qy",
		Area:    AreaHuaNan,
		Oversea: false,
	}, {
		ID:      37,
		Name:    "ap-shenzhen",
		Alias:   "szx",
		CNName:  "深圳",
		Area:    AreaHuaNan,
		Oversea: false,
	}, {
		ID:      11,
		Name:    "ap-shenzhen-fsi",
		Alias:   "szjr",
		CNName:  "深圳金融",
		Area:    AreaHuaNan,
		Oversea: false,
		PreCCR:  true,
	}, {
		ID:      4,
		Name:    "ap-shanghai",
		Alias:   "sh",
		CNName:  "上海",
		Area:    AreaHuaDong,
		Oversea: false,
	}, {
		ID:      7,
		Name:    "ap-shanghai-fsi",
		Alias:   "shjr",
		CNName:  "上海金融",
		Area:    AreaHuaDong,
		Oversea: false,
		PreCCR:  true,
	}, {
		ID:      33,
		Name:    "ap-nanjing",
		Alias:   "nj",
		CNName:  "南京",
		Area:    AreaHuaDong,
		Oversea: false,
	}, {
		ID:      31,
		Name:    "ap-jinan-ec",
		Alias:   "jnec",
		CNName:  "济南EC",
		Area:    AreaHuaDong,
		Oversea: false,
	}, {
		ID:      32,
		Name:    "ap-hangzhou-ec",
		Alias:   "hzec",
		CNName:  "杭州EC",
		Area:    AreaHuaDong,
		Oversea: false,
	}, {
		ID:      34,
		Name:    "ap-fuzhou-ec",
		Alias:   "fzec",
		CNName:  "福州EC",
		Area:    AreaHuaDong,
		Oversea: false,
	}, {
		ID:      8,
		Name:    "ap-beijing",
		Alias:   "bj",
		CNName:  "北京",
		Area:    AreaHuaBei,
		Oversea: false,
	}, {
		ID:      36,
		Name:    "ap-tianjin",
		Alias:   "tsn",
		CNName:  "天津",
		Area:    AreaHuaBei,
		Oversea: false,
	}, {
		ID:      53,
		Name:    "ap-shijiazhuang-ec",
		Alias:   "sjwec",
		CNName:  "石家庄EC",
		Area:    AreaHuaBei,
		Oversea: false,
	}, {
		ID:      46,
		Name:    "ap-beijing-fsi",
		Alias:   "bjjr",
		CNName:  "北京金融",
		Area:    AreaHuaBei,
		Oversea: false,
		PreCCR:  true,
	}, {
		ID:      35,
		Name:    "ap-wuhan-ec",
		Alias:   "whec",
		CNName:  "武汉EC",
		Area:    AreaHuaZhong,
		Oversea: false,
	}, {
		ID:      45,
		Name:    "ap-changsha-ec",
		Alias:   "csec",
		CNName:  "长沙EC",
		Area:    AreaHuaZhong,
		Oversea: false,
	}, {
		ID:      16,
		Name:    "ap-chengdu",
		Alias:   "cd",
		CNName:  "成都",
		Area:    AreaXiNan,
		Oversea: false,
	}, {
		ID:      19,
		Name:    "ap-chongqing",
		Alias:   "cq",
		CNName:  "重庆",
		Area:    AreaXiNan,
		Oversea: false,
	}, {
		ID:      39,
		Name:    "ap-taipei",
		Alias:   "tpe",
		CNName:  "中国台北",
		Area:    AreaGangAoTai,
		Oversea: true,
		PreCCR:  true,
	}, {
		ID:      5,
		Name:    "ap-hongkong",
		Alias:   "hk",
		CNName:  "中国香港",
		Area:    AreaGangAoTai,
		Oversea: true,
		PreCCR:  true,
	}, {
		ID:      9,
		Name:    "ap-singapore",
		Alias:   "sg",
		CNName:  "新加坡",
		Area:    AreaAPSoutheasst,
		Oversea: true,
		PreCCR:  true,
	}, {
		ID:      23,
		Name:    "ap-bangkok",
		Alias:   "th",
		CNName:  "曼谷",
		Area:    AreaAPSoutheasst,
		Oversea: true,
		PreCCR:  true,
	}, {
		ID:      21,
		Name:    "ap-mumbai",
		Alias:   "in",
		CNName:  "孟买",
		Area:    AreaAPSouth,
		Oversea: true,
		PreCCR:  true,
	}, {
		ID:      18,
		Name:    "ap-seoul",
		Alias:   "kr",
		CNName:  "首尔",
		Area:    AreaAPNortheast,
		Oversea: true,
		PreCCR:  true,
	}, {
		ID:      25,
		Name:    "ap-tokyo",
		Alias:   "jp",
		CNName:  "东京",
		Area:    AreaAPNortheast,
		Oversea: true,
		PreCCR:  true,
	}, {
		ID:      15,
		Name:    "na-siliconvalley",
		Alias:   "usw",
		CNName:  "硅谷",
		Area:    AreaUSWestern,
		Oversea: true,
		PreCCR:  true,
	}, {
		ID:      22,
		Name:    "na-ashburn",
		Alias:   "use",
		CNName:  "弗吉尼亚",
		Area:    AeraUSEastern,
		Oversea: true,
		PreCCR:  true,
	}, {
		ID:      6,
		Name:    "na-toronto",
		Alias:   "ca",
		CNName:  "多伦多",
		Area:    AreaNA,
		Oversea: true,
		PreCCR:  true,
	}, {
		ID:      17,
		Name:    "eu-frankfurt",
		Alias:   "de",
		CNName:  "法兰克福",
		Area:    AreaEU,
		Oversea: true,
		PreCCR:  true,
	}, {
		ID:      24,
		Name:    "eu-moscow",
		Alias:   "ru",
		CNName:  "莫斯科",
		Area:    AreaEU,
		Oversea: true,
		PreCCR:  true,
	}, {
		ID:      56,
		Name:    "ap-shenyang-ec",
		Alias:   "syec",
		CNName:  "沈阳EC",
		Area:    AreaHuaBei,
		Oversea: false,
	}, {
		ID:      55,
		Name:    "ap-hefei-ec",
		Alias:   "hfec",
		CNName:  "合肥EC",
		Area:    AreaHuaDong,
		Oversea: false,
	}, {
		ID:      57,
		Name:    "ap-xian-ec",
		Alias:   "xaec",
		CNName:  "西安EC",
		Area:    AreaXiBei,
		Oversea: false,
	}, {
		ID:      74,
		Name:    "sa-saopaulo",
		Alias:   "sao",
		CNName:  "圣保罗",
		Area:    AreaSA,
		Oversea: true,
		PreCCR:  true,
	}, {
		ID:      72,
		Name:    "ap-jakarta",
		Alias:   "jkt",
		CNName:  "雅加达",
		Area:    AreaAPSoutheasst,
		Oversea: true,
		PreCCR:  true,
	}, {
		ID:      78,
		Name:    "ap-shanghai-adc",
		Alias:   "shadc",
		CNName:  "上海自动驾驶云",
		Area:    AreaHuaDong,
		Oversea: false,
		PreCCR:  false,
	}, {
		ID:      83,
		Name:    "ap-shanghai-wxzf",
		Alias:   "shwxzf",
		CNName:  "上海微信支付",
		Area:    AreaHuaDong,
		Oversea: false,
		PreCCR:  false,
	}, {
		ID:      82,
		Name:    "ap-guangzhou-wxzf",
		Alias:   "gzwxzf",
		CNName:  "广州微信支付",
		Area:    AreaHuaNan,
		Oversea: false,
		PreCCR:  false,
	}, {
		ID:      84,
		Name:    "ap-shenzhen-jxcft",
		Alias:   "szjxcft",
		CNName:  "深圳锦绣财付通",
		Area:    AreaHuaNan,
		Oversea: false,
		PreCCR:  false,
	},
	{
		ID:      92,
		Name:    "ap-shanghai-hq-cft",
		Alias:   "shhqcft",
		CNName:  "上海花桥财付通",
		Area:    AreaHuaDong,
		Oversea: false,
		PreCCR:  false,
	},
}

func FindIndexOfName(name string) (Region, int, bool) {
	return lo.FindIndexOf(List, func(r Region) bool {
		return r.Name == name
	})
}

func findByID(id int) *Region {
	for i := range List {
		if List[i].ID == id {
			return &List[i]
		}
	}
	return nil
}

func findByRegionID(id string) *Region {
	for i := range List {
		if List[i].Name == id {
			return &List[i]
		}
	}
	return nil
}

func findByShortName(name string) *Region {
	for i := range List {
		if List[i].Alias == name {
			return &List[i]
		}
	}
	return nil
}

func MustGet(key interface{}) Region {
	region := Get(key)
	if region == nil {
		panic(fmt.Errorf("invalid region key %s", key))
	}

	return *region
}

func Get(key interface{}) *Region {
	var region *Region
	switch k := key.(type) {
	case string:
		if strings.Contains(k, "-") {
			region = findByRegionID(k)
		} else {
			region = findByShortName(k)
		}
	case int, uint, int32, uint32, int64, uint64:
		id, err := strconv.Atoi(fmt.Sprintf("%v", k))
		if err != nil {
			region = nil
		} else {
			region = findByID(id)
		}
	default:
		panic("unsupport key type")
	}

	return region
}
