package tencentcloud

import (
	"context"
	"fmt"
	"sync"

	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	tencenterrors "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	tag "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tag/v20180813"

	"git.woa.com/kateway/kateway-server/pkg/tmp/errdef"
	"git.woa.com/kateway/kateway-server/pkg/tmp/telemetry/jaeger"
	"git.woa.com/kateway/kateway-server/pkg/tmp/tencentcloud/common/region"
	"git.woa.com/kateway/kateway-server/pkg/tmp/tencentcloud/sts"
	"git.woa.com/kateway/kateway-server/pkg/tmp/web/cloudctx"
)

var clients struct {
	sync.Mutex
	clb map[string]*clb.Client
}

type option struct {
	Region string
	UIN    string
}

type Option func(o *option)

func WithRegion(s string) Option {
	return func(o *option) {
		o.Region = region.Get(s).Name
	}
}

func WithUIN(s string) Option {
	return func(o *option) {
		o.UIN = s
	}
}

func newOption(ctx context.Context, options ...Option) *option {
	o := &option{
		Region: cloudctx.Region(ctx).Name,
		UIN:    cloudctx.UIN(ctx),
	}
	for _, f := range options {
		f(o)
	}

	return o
}

func Tag(ctx context.Context, options ...Option) *tag.Client {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	o := newOption(ctx, options...)

	credential, err := sts.AssumeRole(ctx, o.UIN)
	if err != nil {
		panic(fmt.Errorf("AssumeRole(%s) error: %w", o.UIN, err))
	}
	c, _ := tag.NewClient(credential, o.Region, profile.NewClientProfile())

	return c
}

func Clb(ctx context.Context, options ...Option) *clb.Client {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	o := newOption(ctx, options...)

	clients.Lock()
	defer clients.Unlock()
	if client, ok := clients.clb[o.Region+o.UIN]; ok {
		return client
	}

	credential, err := sts.AssumeRole(ctx, o.UIN)
	if err != nil {
		panic(fmt.Errorf("AssumeRole(%s) error: %w", o.UIN, err))
	}
	c, _ := clb.NewClient(credential, o.Region, profile.NewClientProfile())

	if clients.clb == nil {
		clients.clb = make(map[string]*clb.Client)
		clients.clb[o.Region+o.UIN] = c
	}

	return c
}

func HandleError(err error) error {
	if err == nil {
		return nil
	}
	tencentErr, ok := lo.ErrorsAs[*tencenterrors.TencentCloudSDKError](err)
	if !ok {
		return err
	}
	switch tencentErr.GetCode() {
	case "UnauthorizedOperation":
		return fmt.Errorf("%s: %w", tencentErr.Error(), errdef.ErrUnauthorizedOperation)
	case "ResourceNotFound":
		return fmt.Errorf("%s: %w", tencentErr.Error(), errdef.ErrResourceNotFound)
	case "LimitExceeded":
		return fmt.Errorf("%s: %w", tencentErr.Error(), errdef.ErrLimitExceeded)
	}
	return err
}
