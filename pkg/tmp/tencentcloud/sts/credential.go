package sts

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	tencenterrors "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	tchttp "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/http"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"

	"git.woa.com/kateway/kateway-server/pkg/tmp/errdef"
)

const APIVersion = "2018-08-13"

type client struct {
	common.Client
	config Config
}

var defaultClient *client

func Init(c Config) {
	credential := common.NewCredential(c.SecretID, c.<PERSON>ey)
	cpf := profile.NewClientProfile()
	if c.ValidSeconds == 0 {
		c.ValidSeconds = 3600
	}
	defaultClient = &client{
		config: c,
	}
	defaultClient.Init("ap-guangzhou").WithCredential(credential).WithProfile(cpf)
}

type assumeRoleRequest struct {
	*tchttp.BaseRequest
	RoleArn         string `json:"RoleArn,omitempty" name:"RoleArn"`
	RoleSessionName string `json:"RoleSessionName,omitempty" name:"RoleSessionName"`
	DurationSeconds int    `json:"DurationSeconds,omitempty" name:"DurationSeconds"`
}

type assumeRoleResponse struct {
	*tchttp.BaseResponse
	Response *struct {
		Credentials *struct {
			Token        *string `json:"Token,omitempty" name:"Token"`
			TmpSecretId  *string `json:"TmpSecretId,omitempty" name:"TmpSecretId"`
			TmpSecretKey *string `json:"TmpSecretKey,omitempty" name:"TmpSecretKey"`
		}
		ExpiredTime int
	}
}

func newAssumeRoleResponse() *assumeRoleResponse {
	return &assumeRoleResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
}

func newAssumeRoleRequest(uin string, serviceRole bool) *assumeRoleRequest {
	roleArnTpl := "qcs::cam::uin/%s:roleName/%s"
	if serviceRole {
		roleArnTpl = "qcs::cam::uin/%s:role/tencentcloudServiceRoleName/%s"
	}
	request := &assumeRoleRequest{
		BaseRequest:     &tchttp.BaseRequest{},
		RoleArn:         fmt.Sprintf(roleArnTpl, uin, defaultClient.config.RoleName),
		RoleSessionName: defaultClient.config.RoleName,
		DurationSeconds: defaultClient.config.ValidSeconds,
	}
	request.Init().WithApiInfo("sts", APIVersion, "AssumeRole")
	return request
}

func AssumeRole(ctx context.Context, uin string) (*common.Credential, error) {
	if defaultClient == nil {
		panic("nil sts client, call Init first")
	}
	request := newAssumeRoleRequest(uin, defaultClient.config.ServiceRole)
	request.SetContext(ctx)
	response := newAssumeRoleResponse()

	if err := defaultClient.Send(request, response); err != nil {
		if tencentError, ok := lo.ErrorsAs[*tencenterrors.TencentCloudSDKError](err); ok {
			code := tencentError.GetCode()
			if code == "InternalError.GetRoleError" || code == "UnauthorizedOperation" {
				return nil, errdef.ErrUnauthorizedOperation
			}
		}

		return nil, err
	}

	tmpSecretInfo := response.Response.Credentials

	credential := common.NewTokenCredential(
		*tmpSecretInfo.TmpSecretId,
		*tmpSecretInfo.TmpSecretKey,
		*tmpSecretInfo.Token)

	return credential, nil
}
