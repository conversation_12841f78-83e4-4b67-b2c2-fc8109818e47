package retry

import (
	"time"

	"github.com/samber/lo"
	tencentErrors "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"k8s.io/apimachinery/pkg/util/wait"
)

func DoIfNetError0(fn func() error, interval time.Duration) error {
	return wait.PollImmediateInfinite(interval, func() (bool, error) {
		err := fn()
		if err == nil {
			return true, nil
		}
		if terr, ok := lo.ErrorsAs[*tencentErrors.TencentCloudSDKError](err); ok && terr.Code == "ClientError.NetworkError" {
			return false, nil
		}
		return false, err
	})
}

func DoIfNetError[T any](fn func() (T, error), interval time.Duration) (T, error) {
	var res T
	err := DoIfNetError0(func() error {
		var err error
		res, err = fn()
		return err
	}, interval)
	return res, err
}
