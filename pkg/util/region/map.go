package region

import (
	_ "embed"
	"fmt"
	"strconv"
	"strings"

	"github.com/samber/lo"
	"github.com/tidwall/gjson"
)

type Region struct {
	ID          int    `json:"regionID,string"`
	Name        string `json:"region"`
	Alias       string `json:"regionShortName"`
	CNName      string `json:"regionName"`
	Area        string `json:"area"`
	Oversea     bool
	PreCCR      bool
	ImagePrefix string `json:"imagePrefix"`
	CountryName string `json:"countryName"`
}

//go:embed region.json
var data string

func init() {
	result := gjson.Get(data, "data")

	for _, item := range result.Array() {
		item.Get("regionID").Int()
		region := Region{
			ID:          int(item.Get("regionID").Int()),
			Name:        item.Get("region").String(),
			Alias:       item.Get("regionShortName").String(),
			CNName:      item.Get("regionName").String(),
			Area:        item.Get("area").String(),
			Oversea:     item.Get("international").Bool(),
			ImagePrefix: item.Get("imagePrefix").String(),
			CountryName: item.Get("countryName").String(),
		}
		region.PreCCR = region.ImagePrefix != ""

		List = append(List, region)
	}
}

var List []Region

func FindIndexOfName(name string) (Region, int, bool) {
	return lo.FindIndexOf(List, func(r Region) bool {
		return r.Name == name
	})
}

func findByID(id int) *Region {
	for i := range List {
		if List[i].ID == id {
			return &List[i]
		}
	}
	return nil
}

func findByRegionID(id string) *Region {
	for i := range List {
		if List[i].Name == id {
			return &List[i]
		}
	}
	return nil
}

func findByShortName(name string) *Region {
	for i := range List {
		if List[i].Alias == name {
			return &List[i]
		}
	}
	return nil
}

func MustGet(key interface{}) Region {
	region := Get(key)
	if region == nil {
		panic(fmt.Errorf("invalid region key(%v)", key))
	}

	return *region
}

func Get(key interface{}) *Region {
	var region *Region
	switch k := key.(type) {
	case string:
		if strings.Contains(k, "-") {
			region = findByRegionID(k)
		} else {
			region = findByShortName(k)
		}
	case int, uint, int32, uint32, int64, uint64:
		id, err := strconv.Atoi(fmt.Sprintf("%v", k))
		if err != nil {
			region = nil
		} else {
			region = findByID(id)
		}
	default:
		panic("unsupport key type")
	}

	return region
}
