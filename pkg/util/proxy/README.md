# 基于kube-apiserver端口转发的隧道
## 使用场景
通过kube-apiserver进行代理，访问集群内任意服务。kube-apisever的proxy subsource仅支持简单的7层转发，且不支持鉴权，
在需要鉴权的场景下无法很好的满足需求(如在没有jnsgw的场景下需要通过metacluster的apiserver访问租户的apiserver)，而通过隧道的方式，可以实现透明代理的效果。
## 实现原理
通过kube-apiserver提供的port-forward subsource实现。
参考K8s代码: `test/e2e/storage/drivers/proxy/portproxy.go`
### port-forward原理：
1. 客户端通过spdy的方式和kube-apiserver建立连接，kube-apiserver为pod创建port-forward subsource时，会创建一个代理的Handler。
2. 代理的Handler会将请求Upgrade为spdy协议，客户端后续通过spdy的stream协议来和后端进行通信（此时kube-apiserver相当于一个http协议的隧道）。
3. kube-apiserver将portforward请求转发给kubelet，kubelet和CRI通信，通过GetPortForward获取到和CRI通信的URL（URL中会携带一个token，用于识别请求，避免每次都将请求参数encode到URL）。
4. kubelet使用该URL将请求通过proxyStream的方式进行Upgrade，转发到CRI的PortForward方法（可以将kube-apiserver和kubelet都视为转发流量的透明代理，相当于客户端直接和CRI通信）。
5. CRI的ServePortForward方法使用spdy协议和客户端通信，PortForward会在PodSandBox所在的ns和目标端口建立连接，然后将dataStream的流量转发到对应端口，客户端读写对应的dataStream就相当于直接和目标端口通信。
![img.png](portforward.png)
### 基于port-forward的代理实现原理：
golang的http Transport和Dial可以自定义，可以通过hook Transport的Dial方法，来返回自定义的net.Conn。
通过实现自定义的net.Conn，可以将Conn的流量通过port-forward的方式转发到目标端口。
## 使用方式
```golang
package main


import (
	"context"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

func main() {

	overrides := &clientcmd.ConfigOverrides{}
	overrides.ClusterDefaults.Server = "https://*******:12345"
	overrides.ClusterDefaults.InsecureSkipTLSVerify = true
	overrides.AuthInfo.Token = "xxxxxxxxxxx"

	clientConfig := clientcmd.NewNonInteractiveDeferredLoadingClientConfig(&clientcmd.ClientConfigLoadingRules{}, overrides)

	metaClusterConfig, err := clientConfig.ClientConfig()
	if err != nil {
		panic(err)
	}

	userOverrides := &clientcmd.ConfigOverrides{}
	userOverrides.ClusterDefaults.Server = "https://*******:23456"
	userOverrides.ClusterInfo.InsecureSkipTLSVerify = true
	// userOverrides.AuthInfo.Token = "xxx"
	userOverrides.AuthInfo.ClientCertificateData = []byte(`xxx`)
	userOverrides.AuthInfo.ClientKeyData = []byte(`xxx`)
	
	userClientConfig := clientcmd.NewNonInteractiveDeferredLoadingClientConfig(&clientcmd.ClientConfigLoadingRules{}, userOverrides)
	userClusterConfig, err := userClientConfig.ClientConfig()
	if err != nil {
		panic(err)
	}

	podProxy := NewPodProxy(metaClusterConfig, Addr{
		Namespace:     "cls-abcd1234",
		PodName:       "cls-abcd1234-apiserver-5997684bc-mt4dx",
		ContainerName: "apiserver",
		Port:          60002,
	})
	userClusterConfig.Dial = podProxy.Dial
	
	userClient, err := kubernetes.NewForConfig(userClusterConfig)
	if err != nil {
		panic(err)
	}
	ns, err := userClient.CoreV1().Namespaces().List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		panic(err)
	}
	for _, n := range ns.Items {
		fmt.Printf("namespace: %s\n", n.Name)
	}
}
```