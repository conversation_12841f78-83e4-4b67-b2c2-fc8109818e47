package proxy

import (
	"context"
	"fmt"
	"io"
	"net"
	"net/http"
	"time"

	"github.com/pkg/errors"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/httpstream"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/portforward"
	"k8s.io/client-go/transport/spdy"
	"k8s.io/klog/v2"
)

type PodProxy struct {
	proxyConfig *rest.Config
	addr        Addr
}

func NewPodProxy(proxyConfig *rest.Config, addr Addr) *PodProxy {
	return &PodProxy{
		proxyConfig: proxyConfig,
		addr:        addr,
	}
}

func (p *PodProxy) Dial(ctx context.Context, net, addr string) (net.Conn, error) {
	client, err := kubernetes.NewForConfig(p.proxyConfig)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create kubernetes client")
	}
	klog.V(6).Infof("dialing %s %s", net, addr)

	req := client.CoreV1().RESTClient().Post().
		Resource("pods").
		Namespace(p.addr.Namespace).
		Name(p.addr.PodName).
		SubResource("portforward")
	transport, upgrader, err := spdy.RoundTripperFor(p.proxyConfig)
	if err != nil {
		return nil, fmt.Errorf("create round tripper: %v", err)
	}

	dialer := spdy.NewDialer(upgrader, &http.Client{Transport: transport}, "POST", req.URL())

	stream, errorChan, err := createPortForwardStream(dialer, p.addr.Port)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to create port forward stream for %s", p.addr)
	}
	conn := &conn{
		stream:  stream,
		addr:    p.addr,
		errChan: errorChan,
	}
	go conn.handleError()

	return conn, nil
}

// Addr contains all relevant parameters for a certain port in a pod.
// The container must be running before connections are attempted.
type Addr struct {
	Namespace, PodName, ContainerName string
	Port                              int
}

var _ net.Addr = Addr{}

func (a Addr) Network() string {
	return "port-forwarding"
}

func (a Addr) String() string {
	return fmt.Sprintf("%s/%s:%d", a.Namespace, a.PodName, a.Port)
}

type stream struct {
	httpstream.Stream
	streamConn httpstream.Connection
}

func createPortForwardStream(dialer httpstream.Dialer, port int) (s *stream, errorChan chan error, finalErr error) {
	streamConn, _, err := dialer.Dial(portforward.PortForwardProtocolV1Name)
	if err != nil {
		return nil, nil, fmt.Errorf("dialer failed: %v", err)
	}
	requestID := "1"
	defer func() {
		if finalErr != nil {
			streamConn.Close()
		}
	}()

	// create error stream
	headers := http.Header{}
	headers.Set(v1.StreamType, v1.StreamTypeError)
	headers.Set(v1.PortHeader, fmt.Sprintf("%d", port))
	headers.Set(v1.PortForwardRequestIDHeader, requestID)

	// We're not writing to this stream, just reading an error message from it.
	// This happens asynchronously.
	errorStream, err := streamConn.CreateStream(headers)
	if err != nil {
		return nil, nil, fmt.Errorf("error creating error stream: %v", err)
	}
	errorStream.Close()

	errorChan = make(chan error)
	go func() {
		message, err := io.ReadAll(errorStream)
		switch {
		case err != nil:
			errorChan <- fmt.Errorf("reading from error stream: %v", err)
		case len(message) > 0:
			errorChan <- fmt.Errorf("an error occurred connecting to the remote port %d: %v", port, string(message))
		}
		close(errorChan)
	}()

	// create data stream
	headers.Set(v1.StreamType, v1.StreamTypeData)
	dataStream, err := streamConn.CreateStream(headers)
	if err != nil {
		return nil, nil, fmt.Errorf("error creating data stream: %v", err)
	}

	return &stream{
		Stream:     dataStream,
		streamConn: streamConn,
	}, errorChan, nil
}

func (s *stream) Close() {
	s.Stream.Close()
	s.streamConn.Close()
}

type conn struct {
	stream  *stream
	addr    Addr
	errChan chan error
}

var _ net.Conn = &conn{}

func (c *conn) LocalAddr() net.Addr {
	return c.addr
}

func (c *conn) RemoteAddr() net.Addr {
	return c.addr
}

func (c *conn) SetDeadline(t time.Time) error {
	return nil
}

func (c *conn) SetReadDeadline(t time.Time) error {
	return nil
}

func (c *conn) SetWriteDeadline(t time.Time) error {
	return nil
}

func (c *conn) Read(b []byte) (int, error) {
	n, err := c.stream.Read(b)
	if errors.Is(err, io.EOF) {
		klog.V(5).Infof("forward conn for %s: remote side closed the stream", c.addr)
	}
	return n, err
}

func (c *conn) Write(b []byte) (int, error) {
	n, err := c.stream.Write(b)
	if errors.Is(err, io.EOF) {
		klog.V(5).Infof("forward conn for %s: remote side closed the stream", c.addr)
	}
	return n, err
}

func (c *conn) Close() error {
	c.stream.Close()
	return nil
}

// handleError block until the error channel is closed or streamConn closed.
func (c *conn) handleError() {
	select {
	case err := <-c.errChan:
		if err != nil {
			klog.Errorf("an error occurred when forward conn for %s: %v", c.addr, err)
			c.Close()
		}
	case <-c.stream.streamConn.CloseChan():
	}
}
