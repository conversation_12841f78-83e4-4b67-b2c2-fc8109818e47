package ssh

import (
	"fmt"
	"io/ioutil"
	"net"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"golang.org/x/crypto/ssh"
)

type Config struct {
	User        string         `yaml:"user"`
	KeyFile     string         `yaml:"keyFile"`
	Password    string         `yaml:"password"`
	Host        string         `yaml:"host"`
	Port        int            `yaml:"port"`
	DialTimeout *time.Duration `yaml:"dialTimeout"`
}

func (c *Config) HostPort() string {
	return net.JoinHostPort(c.Host, strconv.Itoa(c.Port))
}

func (c *Config) Auth() ([]ssh.AuthMethod, error) {
	var auth []ssh.AuthMethod
	if c.KeyFile != "" {
		signer, err := MakePrivateKeySignerFromFile(c.KeyFile)
		if err != nil {
			return nil, err
		}
		auth = append(auth, ssh.PublicKeys(signer))
	}
	if c.Password != "" {
		auth = append(auth, ssh.Password(c.Password))
	}
	if len(auth) == 0 {
		return nil, errors.New("you must specify either a keyFile or password")
	}
	return auth, nil
}

func (c *Config) Validate() error {
	if c.User == "" {
		return errors.New("missing user")
	}
	if c.KeyFile == "" && c.Password == "" {
		return errors.New("must specify either keyFile or password")
	}
	if c.Host == "" {
		return errors.New("missing host")
	}
	if c.Port == 0 {
		return errors.New("missing port")
	}
	return nil
}

func MakePrivateKeySignerFromFile(key string) (ssh.Signer, error) {
	// Create an actual signer.
	buffer, err := ioutil.ReadFile(key)
	if err != nil {
		return nil, fmt.Errorf("error reading SSH key %s: '%v'", key, err)
	}
	return MakePrivateKeySignerFromBytes(buffer)
}

func MakePrivateKeySignerFromBytes(buffer []byte) (ssh.Signer, error) {
	signer, err := ssh.ParsePrivateKey(buffer)
	if err != nil {
		return nil, fmt.Errorf("error parsing SSH key: '%v'", err)
	}
	return signer, nil
}
