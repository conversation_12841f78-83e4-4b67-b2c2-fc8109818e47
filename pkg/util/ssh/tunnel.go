/*
Copyright 2015 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package ssh

import (
	"context"
	"net"
	"strconv"

	"github.com/pkg/errors"
	"golang.org/x/crypto/ssh"
)

type Tunnel interface {
	Open() error
	Close() error
	Dial(network, address string) (net.Conn, error)
	DialContext(ctx context.Context, network, address string) (net.Conn, error)
}

func NewSSHTunnel(c Config) (Tunnel, error) {
	auth, err := c.Auth()
	if err != nil {
		return nil, err
	}
	config := ssh.ClientConfig{
		User:            c.User,
		Auth:            auth,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}
	dialer := &timeoutDialer{
		dialer:  &realSSHDialer{},
		timeout: defaultSSHDialTimeout,
	}
	if c.DialTimeout != nil {
		dialer.timeout = *c.DialTimeout
	}
	return &sshTunnel{
		Config:  &config,
		dialer:  dialer,
		Host:    c.Host,
		SSHPort: strconv.Itoa(c.Port),
	}, nil
}

type sshTunnel struct {
	Config  *ssh.ClientConfig
	dialer  sshDialer
	Host    string
	SSHPort string
	client  *ssh.Client
}

func (s *sshTunnel) Open() error {
	var err error
	s.client, err = s.dialer.Dial("tcp", net.JoinHostPort(s.Host, s.SSHPort), s.Config)
	return err
}

func (s *sshTunnel) Dial(network, address string) (net.Conn, error) {
	if s.client == nil {
		return nil, errors.New("Tunnel is not opened.")
	}
	// This Dial method does not allow to pass a context unfortunately
	return s.client.Dial(network, address)
}

func (s *sshTunnel) DialContext(ctx context.Context, network, address string) (net.Conn, error) {
	return s.Dial(network, address)
}

func (s *sshTunnel) Close() error {
	if s.client == nil {
		return errors.New("Cannot close Tunnel. Tunnel was not opened.")
	}
	if err := s.client.Close(); err != nil {
		return err
	}
	return nil
}

func EnableSSHTCPForwarding(c Config) (bool, error) {
	_, stderr, code, err := RunSSHCommand("grep -E '^AllowTcpForwarding[[:space:]]no' /etc/ssh/sshd_config", c)
	if err != nil {
		return false, err
	}
	if code != 0 && stderr != "" {
		return false, errors.Errorf("Error checking /etc/ssh/sshd_config for AllowTcpForwarding, code: %d, err: %s", code, stderr)
	} else if code != 0 {
		// code != 0 && stderr == "" -> AllowTcpForwarding yes
		return false, nil
	}
	// code == 0 && stderr == "" -> AllowTcpForwarding no
	_, stderr, code, err = RunSSHCommand("sed -i 's/^AllowTcpForwarding.*/AllowTcpForwarding yes/' /etc/ssh/sshd_config && systemctl reload sshd", c)
	if err != nil {
		return false, err
	}
	if code == 0 {
		return true, nil
	}
	return false, errors.Errorf("Failed to reload sshd config: %s", stderr)
}
