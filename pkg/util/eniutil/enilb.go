package eniutil

import (
	v1 "k8s.io/api/core/v1"
)

func CopyEniLbMultiplex(src *v1.Service, dst *v1.Service) bool {
	changed := false
	if src.Annotations == nil {
		return false
	}
	if dst.Annotations == nil {
		dst.Annotations = make(map[string]string)
	}
	if dst.Annotations["service.kubernetes.io/qcloud-loadbalancer-multiplex"] != src.Annotations["service.kubernetes.io/qcloud-loadbalancer-multiplex"] {
		dst.Annotations["service.kubernetes.io/qcloud-loadbalancer-multiplex"] = src.Annotations["service.kubernetes.io/qcloud-loadbalancer-multiplex"]
		changed = true
	}
	if dst.Annotations["service.kubernetes.io/qcloud-loadbalancer-uniq-instance-id"] != src.Annotations["service.kubernetes.io/qcloud-loadbalancer-uniq-instance-id"] {
		dst.Annotations["service.kubernetes.io/qcloud-loadbalancer-uniq-instance-id"] = src.Annotations["service.kubernetes.io/qcloud-loadbalancer-uniq-instance-id"]
		changed = true
	}
	return changed
}

func AddEniLbMultiplex(svc *v1.Service, uniqId string) bool {
	changed := false
	if svc.Annotations == nil {
		return false
	}
	if svc.Annotations["service.kubernetes.io/qcloud-loadbalancer-eni"] != "true" {
		return false
	}
	if svc.Annotations["service.kubernetes.io/qcloud-loadbalancer-multiplex"] != "true" {
		svc.Annotations["service.kubernetes.io/qcloud-loadbalancer-multiplex"] = "true"
		changed = true
	}
	if svc.Annotations["service.kubernetes.io/qcloud-loadbalancer-uniq-instance-id"] != uniqId {
		svc.Annotations["service.kubernetes.io/qcloud-loadbalancer-uniq-instance-id"] = uniqId
		changed = true
	}
	return changed
}

func IsEniLbMultiplex(svc *v1.Service) bool {
	if svc.Annotations == nil {
		return false
	}
	return svc.Annotations["service.kubernetes.io/qcloud-loadbalancer-multiplex"] == "true"
}

func GetVpcLBIP(svc *v1.Service) string {
	if svc.Annotations == nil {
		return ""
	}
	return svc.Annotations["service.kubernetes.io/qcloud-loadbalancer-enilb"]
}
