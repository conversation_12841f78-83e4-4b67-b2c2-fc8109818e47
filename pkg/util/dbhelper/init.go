package dbhelper

import (
	"fmt"
	"math/rand"

	"github.com/jinzhu/gorm"
)

func NewMemGorm() *gorm.DB {
	name := fmt.Sprintf("file:%d.DB?cache=shared&mode=memory", rand.Uint64())
	db, err := gorm.Open("sqlite3", name)
	if err != nil {
		panic(err)
	}
	db.DB().SetMaxOpenConns(1)
	return db
}

func NewFileGorm(file string) *gorm.DB {
	name := fmt.Sprintf("file:%s?cache=shared", file)
	db, err := gorm.Open("sqlite3", name)
	if err != nil {
		panic(err)
	}
	db.DB().SetMaxOpenConns(1)
	return db
}

func NewGorm(ty string, arg string) *gorm.DB {
	db, err := gorm.Open(ty, arg)
	if err != nil {
		panic(err)
	}

	return db
}
