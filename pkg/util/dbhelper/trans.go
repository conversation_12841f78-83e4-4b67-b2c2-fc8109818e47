package dbhelper

import (
	"github.com/jinzhu/gorm"
	"github.com/pkg/errors"
)

func DoInTrans(db *gorm.DB, do func(db *gorm.DB) error) (err error) {
	db = db.Begin()
	if err := do(db); err != nil {
		if e := db.Rollback().Error; e != nil {
			return errors.Wrapf(e, "rollback failed")
		}

		if gorm.ErrRecordNotFound == err {
			return errors.Wrapf(err, "record not found")
		}
		return err
	}

	if err := db.Commit().Error; err != nil {
		return errors.Wrapf(err, "commit failed")
	}

	return nil
}
