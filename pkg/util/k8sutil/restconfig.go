package k8sutil

import (
	restclient "k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

func GetRestConfigByToken(server string, token string) (*restclient.Config, error) {
	overrides := &clientcmd.ConfigOverrides{}
	overrides.ClusterDefaults.Server = server
	overrides.ClusterDefaults.InsecureSkipTLSVerify = true
	overrides.AuthInfo.Token = token

	clientConfig := clientcmd.NewNonInteractiveDeferredLoadingClientConfig(&clientcmd.ClientConfigLoadingRules{}, overrides)
	return clientConfig.ClientConfig()
}

func GetRestConfigByCertFile(server string, cert string, key string) (*restclient.Config, error) {
	overrides := &clientcmd.ConfigOverrides{}
	overrides.ClusterDefaults.Server = server
	overrides.ClusterDefaults.InsecureSkipTLSVerify = true
	overrides.AuthInfo.ClientCertificate = cert
	overrides.AuthInfo.ClientKey = key

	clientConfig := clientcmd.NewNonInteractiveDeferredLoadingClientConfig(&clientcmd.ClientConfigLoadingRules{}, overrides)
	return clientConfig.ClientConfig()
}
