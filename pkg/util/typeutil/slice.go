/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKE
 * available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License"); you may not use this
 * file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

package typeutil

import "strings"

// FindString return true if target in slice, return false if not
func FindString(slice []string, target string) bool {
	for _, str := range slice {
		if str == target {
			return true
		}
	}
	return false
}

// FindSubString return true if target in slice, return false if not
func FindSubString(slice []string, sub string) bool {
	for _, str := range slice {
		if strings.Contains(str, sub) {
			return true
		}
	}
	return false
}

// SubStringInSlice return true if the slice contains any substring of source
func SubStringInSlice(source string, slice []string) bool {
	for _, str := range slice {
		if strings.Contains(source, str) {
			return true
		}
	}
	return false
}

// RemoveElementByCondition remove element from slice which match condition
func RemoveElementByCondition(slice []string, condition func(string) bool) []string {
	for i := 0; i < len(slice); i++ {
		if condition(slice[i]) {
			slice = append(slice[:i], slice[i+1:]...)
			return RemoveElementByCondition(slice, condition)
		}
	}
	return slice
}

// FindInt return true if target in slice, return false if not
func FindInt(slice []int, target int) bool {
	for _, str := range slice {
		if str == target {
			return true
		}
	}
	return false
}

// FindUint return true if target in slice, return false if not
func FindUint(slice []uint, target uint) bool {
	for _, str := range slice {
		if str == target {
			return true
		}
	}
	return false
}

func MapKeySlice(m map[string]interface{}) []string {
	f := make([]string, 0)
	for k := range m {
		f = append(f, k)

	}
	return f
}

func OffsetLimit(max, o, l int) (int, int) {
	if l == 0 {
		l = max
	}

	if o > max-1 {
		o = max - 1
	}

	if o+l > max {
		l = max - o
	}

	return o, l
}
