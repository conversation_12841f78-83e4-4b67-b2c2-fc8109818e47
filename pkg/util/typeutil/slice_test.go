package typeutil

import (
	"reflect"
	"testing"
)

func TestRemoveElementByCondition(t *testing.T) {
	type args struct {
		slice     []string
		condition func(string) bool
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "normal",
			args: args{
				slice: []string{"a", "b", "c", "a"},
				condition: func(a string) bool {
					return a == "a"
				},
			},
			want: []string{"b", "c"},
		},
		{
			name: "nil slice",
			args: args{
				slice: nil,
				condition: func(a string) bool {
					return a == "a"
				},
			},
			want: nil,
		},
		{
			name: "remove all",
			args: args{
				slice: []string{"a", "a", "a", "a"},
				condition: func(a string) bool {
					return a == "a"
				},
			},
			want: []string{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := RemoveElementByCondition(tt.args.slice, tt.args.condition); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("RemoveElementByCondition() = %v, want %v", got, tt.want)
			}
		})
	}
}
