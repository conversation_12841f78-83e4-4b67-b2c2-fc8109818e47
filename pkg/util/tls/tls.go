package tls

import (
	"crypto/tls"
	"crypto/x509"
	"encoding/pem"
	"fmt"
)

func NewTLSFromString(caCert, cert, key string) (*tls.Config, error) {
	tlsCert, err := tls.X509KeyPair([]byte(cert), []byte(key))
	if err != nil {
		return nil, err
	}

	cfg := &tls.Config{
		Certificates: []tls.Certificate{tlsCert},
		MinVersion:   tls.VersionTLS10,
	}

	// decode ca cert
	block, _ := pem.Decode([]byte(caCert))
	if block == nil {
		return nil, fmt.Errorf("NewTransportFromString parse failed")
	}
	caCertPemDecoded, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return nil, err
	}

	certPool := x509.NewCertPool()
	certPool.AddCert(caCertPemDecoded)
	cfg.RootCAs = certPool
	cfg.InsecureSkipVerify = true

	return cfg, nil
}
