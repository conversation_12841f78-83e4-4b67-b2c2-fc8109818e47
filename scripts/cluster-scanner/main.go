package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/spf13/cobra"
	"github.com/xuri/excelize/v2"
)

// ClusterInfo 集群信息结构体
type ClusterInfo struct {
	ClusterName                   string `json:"ClusterName"`
	ClusterID                     string `json:"ClusterID"`
	ClusterRegion                 string `json:"ClusterRegion"`
	ClusterType                   string `json:"ClusterType"`
	TotalServiceCount             int    `json:"TotalServiceCount"`
	TotalDirectAccessServiceCount int    `json:"TotalDirectAccessServiceCount"`
}

// ClusterScanner 集群扫描器
type ClusterScanner struct {
	baseURL    string
	httpClient *http.Client
	results    []ClusterInfo
	mu         sync.Mutex
}

// NewClusterScanner 创建新的集群扫描器
func NewClusterScanner(baseURL string) *ClusterScanner {
	return &ClusterScanner{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		results: make([]ClusterInfo, 0),
	}
}

// ScanCluster 扫描单个集群
func (cs *ClusterScanner) ScanCluster(ctx context.Context, clusterID string) error {
	url := fmt.Sprintf("%s/inspection/cluster/info/get?clusterID=%s", cs.baseURL, clusterID)
	
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	resp, err := cs.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP错误: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	var clusterInfo ClusterInfo
	if err := json.Unmarshal(body, &clusterInfo); err != nil {
		return fmt.Errorf("解析JSON失败: %w", err)
	}

	cs.mu.Lock()
	cs.results = append(cs.results, clusterInfo)
	cs.mu.Unlock()

	log.Printf("成功扫描集群: %s (%s)", clusterInfo.ClusterName, clusterInfo.ClusterID)
	return nil
}

// ScanClusters 并发扫描多个集群
func (cs *ClusterScanner) ScanClusters(ctx context.Context, clusterIDs []string, concurrency int) error {
	semaphore := make(chan struct{}, concurrency)
	var wg sync.WaitGroup
	var errors []error
	var errorsMu sync.Mutex

	for _, clusterID := range clusterIDs {
		wg.Add(1)
		go func(id string) {
			defer wg.Done()
			
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			if err := cs.ScanCluster(ctx, id); err != nil {
				errorsMu.Lock()
				errors = append(errors, fmt.Errorf("扫描集群 %s 失败: %w", id, err))
				errorsMu.Unlock()
				log.Printf("扫描集群 %s 失败: %v", id, err)
			}
		}(clusterID)
	}

	wg.Wait()

	if len(errors) > 0 {
		log.Printf("扫描完成，共有 %d 个错误", len(errors))
		for _, err := range errors {
			log.Printf("错误: %v", err)
		}
	}

	return nil
}

// ExportToExcel 导出结果到Excel文件
func (cs *ClusterScanner) ExportToExcel(filename string) error {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			log.Printf("关闭Excel文件失败: %v", err)
		}
	}()

	sheetName := "集群信息"
	index, err := f.NewSheet(sheetName)
	if err != nil {
		return fmt.Errorf("创建工作表失败: %w", err)
	}

	// 设置表头
	headers := []string{"集群名称", "集群ID", "集群区域", "集群类型", "Service总数", "直连Service总数"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		f.SetCellValue(sheetName, cell, header)
	}

	// 设置表头样式
	headerStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#E0E0E0"},
			Pattern: 1,
		},
	})
	if err == nil {
		f.SetCellStyle(sheetName, "A1", fmt.Sprintf("%c1", 'A'+len(headers)-1), headerStyle)
	}

	// 填充数据
	for i, cluster := range cs.results {
		row := i + 2
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), cluster.ClusterName)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), cluster.ClusterID)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), cluster.ClusterRegion)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), cluster.ClusterType)
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), cluster.TotalServiceCount)
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), cluster.TotalDirectAccessServiceCount)
	}

	// 自动调整列宽
	for i := 0; i < len(headers); i++ {
		col := fmt.Sprintf("%c:%c", 'A'+i, 'A'+i)
		f.SetColWidth(sheetName, col, col, 20)
	}

	f.SetActiveSheet(index)
	f.DeleteSheet("Sheet1")

	if err := f.SaveAs(filename); err != nil {
		return fmt.Errorf("保存Excel文件失败: %w", err)
	}

	return nil
}

// GetResults 获取扫描结果
func (cs *ClusterScanner) GetResults() []ClusterInfo {
	cs.mu.Lock()
	defer cs.mu.Unlock()
	return cs.results
}

func main() {
	var (
		baseURL     string
		clusterFile string
		outputFile  string
		concurrency int
	)

	rootCmd := &cobra.Command{
		Use:   "cluster-scanner",
		Short: "集群信息扫描工具",
		Long:  "扫描指定集群的信息并生成Excel报告",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runScanner(baseURL, clusterFile, outputFile, concurrency)
		},
	}

	rootCmd.Flags().StringVar(&baseURL, "base-url", "http://21.4.98.242", "API基础URL")
	rootCmd.Flags().StringVar(&clusterFile, "cluster-file", "clusters.txt", "包含集群ID列表的文件")
	rootCmd.Flags().StringVar(&outputFile, "output", "cluster_report.xlsx", "输出Excel文件名")
	rootCmd.Flags().IntVar(&concurrency, "concurrency", 10, "并发扫描数量")

	if err := rootCmd.Execute(); err != nil {
		log.Fatal(err)
	}
}

func runScanner(baseURL, clusterFile, outputFile string, concurrency int) error {
	// 读取集群ID列表
	clusterIDs, err := readClusterIDs(clusterFile)
	if err != nil {
		return fmt.Errorf("读取集群ID文件失败: %w", err)
	}

	if len(clusterIDs) == 0 {
		return fmt.Errorf("未找到任何集群ID")
	}

	log.Printf("开始扫描 %d 个集群，并发数: %d", len(clusterIDs), concurrency)

	// 创建扫描器并开始扫描
	scanner := NewClusterScanner(baseURL)
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	startTime := time.Now()
	if err := scanner.ScanClusters(ctx, clusterIDs, concurrency); err != nil {
		return fmt.Errorf("扫描失败: %w", err)
	}

	results := scanner.GetResults()
	log.Printf("扫描完成，耗时: %v，成功扫描 %d/%d 个集群", 
		time.Since(startTime), len(results), len(clusterIDs))

	// 导出到Excel
	if err := scanner.ExportToExcel(outputFile); err != nil {
		return fmt.Errorf("导出Excel失败: %w", err)
	}

	log.Printf("Excel报告已生成: %s", outputFile)
	return nil
}

func readClusterIDs(filename string) ([]string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var clusterIDs []string
	content, err := io.ReadAll(file)
	if err != nil {
		return nil, err
	}

	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" && !strings.HasPrefix(line, "#") {
			clusterIDs = append(clusterIDs, line)
		}
	}

	return clusterIDs, nil
}
