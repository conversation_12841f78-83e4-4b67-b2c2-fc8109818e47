serviceControllerImageRepo: /tkeimages/service-controller
ingressControllerImageRepo: /tkeimages/ingress-controller
#serviceControllerImageRepo: /paas/service-controller
#ingressControllerImageRepo: /paas/ingress-controller

skipAppIds:
skipClusterIds:
- "cls-rbnichlc" # tke meta gz
- "cls-1s75hexc" # tke meta gz-2
- "cls-rb4l34r9" # tke meta sh
- "cls-gofhtn3t" # tke meta sh-2
- "cls-lr2pvld2" # tke meta cq
- "cls-9klxgg4i" # tke meta cq-2
- "cls-ntwx40ak" # tke meta hk
- "cls-ne0wmuuw" # tke meta hk-2
- "cls-nkxkhgvb" # tke meta ca
- "cls-b83zt9nz" # tke meta ca-2
- "cls-7we9vmys" # tke meta shjr
- "cls-cmdg60g2" # tke meta shjr-2
- "cls-qbc3zefo" # tke meta sg
- "cls-75j3tu60" # tke meta sg-2
- "cls-6e4edn6y" # tke meta szjr
- "cls-cz5m04em" # tke meta szjr-2
- "cls-6e4edn6y" # tke meta gzopen
- "cls-7xvp0tcw" # tke meta usw
- "cls-1vxwfay6" # tke meta usw-2
- "cls-lizl7svv" # tke meta cd
- "cls-js4zt243" # tke meta cd-2
- "cls-3r01ruwo" # tke meta de
- "cls-5t7brmqa" # tke meta de-2
- "cls-4i52bq23" # tke meta kr
- "cls-lybhszor" # tke meta kr-2
- "cls-kftyr0my" # tke meta in
- "cls-rqp0oyc2" # tke meta in-2
- "cls-52jdb4dl" # tke meta use
- "cls-kud6wwa5" # tke meta use-2
- "cls-7wycntkc" # tke meta th
- "cls-el5s5huy" # tke meta th-2
- "cls-prfp27at" # tke meta ru
- "cls-kzbhgkj9" # tke meta ru-2
- "cls-nx3v4et0" # tke meta jp
- "cls-00s8dvf8" # tke meta jp-2
- "cls-46jrda2y" # tke meta jnec
- "cls-epm3yjlh" # tke meta hzec
- "cls-3qs30g2e" # tke meta nj
- "cls-cnnoyaqm" # tke meta nj-2
- "cls-jyi5zllr" # tke meta tsn
- "cls-mh1d842k" # tke meta szx
- "cls-e2d8nvih" # tke meta bjjr
- "cls-kvd8ybzd" # tke meta bjjr-2
- "cls-epm3yjl1" # tke meta fzec
- "cls-epm3yjl0" # tke meta whec
- "cls-epm3ymyc" # tke meta csec
- "cls-qz9ifaxa" # tke meta tpe
- "cls-my1izq74" # tke meta tpe-2
- "cls-cnas00yb" # tke meta bj
- "cls-0lk56led" # tke meta bj-2
- "cls-epm3x584" # tke meta sjwec
- "cls-4tqcs5rb" # tke meta xbec
- "cls-epm3x57o" # tke meta hfeec
- "cls-epm3x86t" # tke meta sheec
- "cls-9cgvijy6" # tke meta xiyec
- "cls-9cgvijy6" # tke meta cgoec
- "cls-9cgvijy6" # tke meta qyxa
- "cls-bydrsad1" # tke meta jkt
- "cls-1r8flj07" # tke meta jkt-2
- "cls-3js0y19h" # tke meta sao
- "cls-0ckworoh" # tke meta sao-2
- "cls-3zcffj0n" # tke meta shadc
- "cls-h4629lhl" # tke meta qy
- "cls-8d0h5nyy" # tke meta szx
- "cls-ky90hr3p" # tke meta tsn
- "cls-mlwo2v7b" # tke meta bj
- "cls-qvvptqc3" # tke meta sh
- "cls-pgl5mqfo" # tke meta gz
- "cls-rzasdlks" # tke meta nj
- "cls-3efjbva1" # tke meta cd
- "cls-igj9pcga" # tke meta cq
- "cls-gcz1z1f6" # tke meta sg
- "cls-9o38eplq" # tke meta hk
- "cls-1zmp6uy3" # tke meta ca
- "cls-jn7xp6cm" # tke meta usw
- "cls-1fl6rdoy" # tke meta de
- "cls-p9g45e01" # tke meta kr
- "cls-5hnnf5ay" # tke meta in
- "cls-mq5b0wtf" # tke meta use
- "cls-el7if67c" # tke meta th
- "cls-4623qke7" # tke meta ru
- "cls-fd0y2nas" # tke meta jp
- "cls-afgk2s1j" # tke meta jkt
- "cls-oo4yn6y1" # tke meta sao
- "cls-32zy1hh3" # eks meta bj
- "cls-0ooumt67" # eks meta bjjr
- "cls-0l8nw56p" # eks meta ca
- "cls-g1xoolej" # eks meta cd
- "cls-8gwakjc0" # eks meta cq
- "cls-13jssaqe" # eks meta csec
- "cls-7wn2shoc" # eks meta de
- "cls-cigjh4yr" # eks meta fzec
- "cls-gkgefl4q" # eks meta gz
- "cls-cigjfqle" # eks meta hfeec
- "cls-rfkyco6q" # eks meta hk
- "cls-cigjh4z7" # eks meta hzec
- "cls-2kcywybi" # eks meta in
- "cls-mlnmf6vn" # eks meta jkt
- "cls-cup5z68w" # eks meta jnec
- "cls-8gmtvrjk" # eks meta jp
- "cls-0nskj1m5" # eks meta kr
- "cls-k7l72s8u" # eks meta nj
- "cls-l6ft4s8h" # eks meta ru
- "cls-71mi45vx" # eks meta sao
- "cls-g9amr2n2" # eks meta sg
- "cls-fwsjd1df" # eks meta sh
- "cls-f2fseugh" # eks meta sh-3
- "cls-6dhiel97" # eks meta sh-2
- "cls-dq3itro3" # eks meta sh-4
- "cls-dn4i3bob" # eks meta sh-5
- "cls-md3ael05" # eks meta sh-6
- "cls-6dhiel97" # eks meta sh-wxzf
- "cls-kmnt5mwr" # eks meta sheec
- "cls-hgsdzo0y" # eks meta shjr
- "cls-cigjfqlu" # eks meta sjwec
- "cls-10iznk6c" # eks meta szjr
- "cls-26yncp9w" # eks meta szx
- "cls-crhlsahw" # eks meta th
- "cls-if9zp2hy" # eks meta tpe
- "cls-bzv9ksbf" # eks meta tsn
- "cls-f9tl6ee9" # eks meta use
- "cls-8cgiip7d" # eks meta usw
- "cls-cigjh4yq" # eks meta whec
- "cls-c73owjg0" # eks meta xiyec
- "cls-8byl25fp" # eks meta shadc
- "cls-g0zvpnr3" # eks meta tsn
- "cls-gxjv4spk" # eks meta szx
- "cls-cajt2ulz" # eks meta bj
- "cls-pk0wwx1t" # eks meta sh
- "cls-5psnrzpw" # eks meta gz
- "cls-6u5hdkj6" # eks meta nj
- "cls-ijpmpffj" # eks meta cd
- "cls-lqd6g202" # eks meta cq
- "cls-71jyr0rg" # eks meta sg
- "cls-jk5zz14i" # eks meta hk
- "cls-ifste2mf" # eks meta ca
- "cls-ly1349uq" # eks meta usw
- "cls-ma2cu9ni" # eks meta de
- "cls-n1iajjpf" # eks meta kr
- "cls-3zb2kfyw" # eks meta in
- "cls-3jsphtpf" # eks meta use
- "cls-3j12wxji" # eks meta th
- "cls-0k9ivlg5" # eks meta ru
- "cls-2jtmdvxs" # eks meta jp
- "cls-pclgo4ez" # eks meta jkt
- "cls-8ss0546d" # eks meta sao
- "cls-kfb5f28i" # cass xiyec
- "cls-3udn7s3t" # cass shadc
- "cls-kmnt6ybf" # cass hzec
- "cls-bqhtbuow" # cass jnec
- "cls-p7mpgajj" # cass ru
- "cls-23ha3lf1" # cass ca
- "cls-pvsmo6ig" # cass de
- "cls-2jtmdsrl" # cass kr
- "cls-43shmal4" # cass sg
- "cls-ngj2wvrf" # cass sao
- "cls-2f2s92d2" # cass nj
- "cls-46jrzo0a" # cass hfeec
- "cls-46js12dn" # cass fzec
- "cls-46jrzo0q" # cass sjwec
- "cls-kmnt71oa" # cass csec
- "cls-46js12dm" # cass whec
- "cls-cigjftkj" # cass sheec
- "cls-qo0wk4y3" # cass xbec
- "cls-ldjwb2oj" # cass jkt
- "cls-5xm6e9a0" # cass jp
- "cls-jmw910x3" # cass use
- "cls-79rs0y94" # cass th
- "cls-5illtbng" # cass in
- "cls-3ja1neug" # cass tpe
- "cls-1spi5ea8" # cass hk
- "cls-po65oeqs" # cass usw
- "cls-qz3edezt" # cass bjjr
- "cls-otmtwkg0" # cass szjr
- "cls-c6mdgc88" # cass cq
- "cls-rekhlagg" # cass szx
- "cls-dhzp4gjm" # cass shjr
- "cls-94v9pg5y" # cass szx
- "cls-ph1sfqcb" # cass tsn
- "cls-p56igkpl" # cass cd
- "cls-6mics35j" # cass bj
- "cls-dak5z2ab" # cass sh
- "cls-eujp1li2" # cass gz
- "cls-dyp4pnhh" # cass qy
- "cls-56ruggls" # 跨越特殊需求 https://zhiyan.woa.com/release_approval/#/approval-detail/info?task_id=215734
- "cls-20ms7fx6" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
- "cls-r3ge0w0i" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
- "cls-e65w6gd2" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
- "cls-bqhyyfqo" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
- "cls-barwz3zs" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
- "cls-gdtjvd2q" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
- "cls-478pofci" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
- "cls-7gxrpc6o" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
- "cls-hnchnzzu" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
- "cls-q8qdff0w" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
- "cls-18wfan5i" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
- "cls-6em27ln9" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
- "cls-7gmpjac6" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
- "cls-9cp3gjr2" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
- "cls-18wfan5i" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
- "cls-rfv0fsc9" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
- "cls-qzst1bg5" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
