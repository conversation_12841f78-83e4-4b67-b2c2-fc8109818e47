dashboard_db:
  szjr:
    name: "dashboard"
    host: "*************"
    port: 3306
    user: "dash_dash_rw"
    pass: "QXiHcuUUsJ8ufUUK"
  shjr:
    name: "dashboard"
    host: "**************"
    port: 3306
    user: "dash_dash_rw"
    pass: "PtmYnyKzocIPdOcW"
  bjjr:
    name: "dashboard"
    host: "*************"
    port: 3306
    user: "dash_dash_rw"
    pass: "QZvAmdnCnvss3LkR"
  cq:
    name: "dashboard"
    host: "*************"
    port: 3306
    user: "dash_dash_rw"
    pass: "G9gDOpAdaLaZj1QY"
  jkt:
    name: "dashboard"
    host: "*************"
    port: 3306
    user: "dash_dash_rw"
    pass: "Z4QQ7rI3cTgJd06H"
  jp:
    name: "dashboard"
    host: "************"
    port: 3306
    user: "dash_dash_rw"
    pass: "CWOn8SPEdsWmVTFT"
  ca:
    name: "dashboard"
    host: "*************"
    port: 3306
    user: "dash_dash_rw"
    pass: "GsrtLgCIqA6ycWwC"
  use:
    name: "dashboard"
    host: "************"
    port: 3306
    user: "dash_dash_rw"
    pass: "V4Egy9wI4J7p9SGR"
  in:
    name: "dashboard"
    host: "*************"
    port: 3306
    user: "dash_dash_rw"
    pass: "Tvp9XPFrrqlcOQBZ"
  th:
    name: "dashboard"
    host: "************"
    port: 3306
    user: "dash_dash_rw"
    pass: "HenVTDPmvYAU9hWL"
  ru:
    name: "dashboard"
    host: "*************"
    port: 3306
    user: "dash_dash_rw"
    pass: "VbKBJoK48AxVq1uH"
  kr:
    name: "dashboard"
    host: "*************"
    port: 3306
    user: "dash_dash_rw"
    pass: "CW4LRasrdVDfSKyG"
  de:
    name: "dashboard"
    host: "*************"
    port: 3306
    user: "dash_dash_rw"
    pass: "JNbsabPjrEJfWe9H"
  usw:
    name: "dashboard"
    host: "************"
    port: 3306
    user: "dash_dash_rw"
    pass: "D3zjSxDfNMwTFROJ"
  cd:
    name: "dashboard"
    host: "***********"
    port: 3306
    user: "dash_dash_rw"
    pass: "FlDqqva535AJVnXJ"
  sg:
    name: "dashboard"
    host: "*************"
    port: 3306
    user: "dash_dash_rw"
    pass: "EGquQgjvV7lZdiGK"
  hk:
    name: "dashboard"
    host: "***********"
    port: 3306
    user: "dash_dash_rw"
    pass: "J3B0ixDohn3uDj9O"
  tsn:
    name: "dashboard"
    host: "*************"
    port: 3306
    user: "dash_dash_rw"
    pass: "PGvLxfFUkEAbNjsU"
  szx:
    name: "dashboard"
    host: "************"
    port: 3306
    user: "dash_dash_rw"
    pass: "COG2qPgu1FD6TAzK"
  bj:
    name: "dashboard"
    host: "***********"
    port: 3306
    user: "dash_dash_rw"
    pass: "K2a4xQHV9CBIiwiN"
  nj:
    name: "dashboard"
    host: "************"
    port: 3306
    user: "dash_dash_rw"
    pass: "KXQuTXva6hNAEL5R"
  gz:
    name: "dashboard"
    host: "************"
    port: 3306
    user: "dash_dash_rw"
    pass: "MglGDIeC7n7jOHMB"
  sh:
    name: "dashboard"
    host: "*************"
    port: 3306
    user: "dash_dash_rw"
    pass: "NR8huBSZ2ST7hq8P"
  sao:
    name: "dashboard"
    host: "*************"
    port: 3306
    user: "dash_dash_rw"
    pass: "JPhRuWU2GPkDeviM"
  tpe:
    name: "dashboard"
    host: "*************"
    port: 3306
    user: "dash_dash_rw"
    pass: "OE4KEg2YxFFvqBSY"
cauth:
  server:
    cq: "http://logical.server.console.tencentyun.com:8080/interface.php"
    tpe: "http://logical.server.console.tencentyun.com:8080/interface.php"
    jkt: "http://logical.server.console.tencentyun.com:8080/interface.php"
    jp: "http://logical.server.console.tencentyun.com:8080/interface.php"
    ca: "http://logical.server.console.tencentyun.com:8080/interface.php"
    use: "http://logical.server.console.tencentyun.com:8080/interface.php"
    th: "http://logical.server.console.tencentyun.com:8080/interface.php"
    in: "http://logical.server.console.tencentyun.com:8080/interface.php"
    ru: "http://logical.server.console.tencentyun.com:8080/interface.php"
    kr: "http://logical.server.console.tencentyun.com:8080/interface.php"
    de: "http://logical.server.console.tencentyun.com:8080/interface.php"
    sao: "http://logical.server.console.tencentyun.com:8080/interface.php"
    usw: "http://logical.server.console.tencentyun.com:8080/interface.php"
    cd: "http://logical.server.console.tencentyun.com:8080/interface.php"
    hk: "http://logical.server.console.tencentyun.com:8080/interface.php"
    sg: "http://logical.server.console.tencentyun.com:8080/interface.php"
    tsn: "http://logical.server.console.tencentyun.com:8080/interface.php"
    szx: "http://logical.server.console.tencentyun.com:8080/interface.php"
    gz: "http://logical.server.console.tencentyun.com:8080/interface.php"
    bj: "http://logical.server.console.tencentyun.com:8080/interface.php"
    nj: "http://logical.server.console.tencentyun.com:8080/interface.php"
    sh: "http://logical.server.console.tencentyun.com:8080/interface.php"
    bjjr: "http://logical.server.console.tencentyun.com:8080/interface.php"
    shjr: "http://logical.server.console.tencentyun.com:8080/interface.php"
    szjr: "http://logical.server.console.tencentyun.com:8080/interface.php"
vpc:
  vpcOss:
    url: "http://REGION.oss.vpc.tencentyun.com:8080/tvpc/api"
  apiV3:
    url: "http://REGION.vpcapiv3.tencentyun.com:8520"
domain:
  url: "http://api.gslb.tencentyun.com"
  sysId: 1923420120
roleCfg:
  tkeQcsRole:
    secretId: "AKIDXco1EUjCkIuZK5qfaXWbKQTWcq3JdyOq"
    secretKey: "Y6X2Z7kO6sGe2VXZXVQNOQUZYTXZdkaQ"
    rolePrefix: "roleName"
    roleName: "TKE_QCSRole"
tke:
  dashboard:
    url: "http://REGION.tke.caas.tencentyun.com/dashboard"
  tkeApiserver:
    url: "https://REGION.api.tke.caas.tencentyun.com"
    cert: "conf/tke-api-cert/admin.pem"
    key: "conf/tke-api-cert/admin-key.pem"
  tkePlatform:
    url: "https://REGION.tke.caas.tencentyun.com:9443"
    cert: "conf/tke-platform-cert/platform.pem"
    key: "conf/tke-platform-cert/platform-key.pem"
  tkeApplication:
    url: "https://REGION.tke.caas.tencentyun.com:9463"
    cert: "conf/tke-platform-cert/platform.pem"
    key: "conf/tke-platform-cert/platform-key.pem"
  defaultMetaCluster:
    gz: "cls-rbnichlc"
    szjr: "cls-6e4edn6y"
    sh: "cls-rb4l34r9"
    shjr: "cls-7we9vmys"
    bj: "cls-cnas00yb"
    cd: "cls-lizl7svv"
    cq: "cls-lr2pvld2"
    hk: "cls-ntwx40ak"
    sg: "cls-qbc3zefo"
    th: "cls-7wycntkc"
    in: "cls-kftyr0my"
    kr: "cls-4i52bq23"
    jp: "cls-nx3v4et0"
    usw: "cls-7xvp0tcw"
    use: "cls-52jdb4dl"
    de: "cls-3r01ruwo"
    ru: "cls-prfp27at"
    nj: "cls-3qs30g2e"
    tsn: "cls-jyi5zllr"
    szx: "cls-mh1d842k"
    tpe: "cls-qz9ifaxa"
    ca: "cls-nkxkhgvb"
    qy: "cls-h4629lhl"
    bjjr: "cls-e2d8nvih"
    xbec: "cls-4tqcs5rb"
    jkt: "cls-bydrsad1"
    jnec: "cls-46jrda2y"
    hzec: "cls-epm3yjlh"
    whec: "cls-epm3yjl0"
    csec: "cls-epm3ymyc"
    sheec: "cls-epm3x86t"
    sjwec: "cls-epm3x584"
    fzec: "cls-epm3yjl1"
    hfeec: "cls-epm3x57o"
    sao: "cls-3js0y19h"
    xiyec: "cls-9cgvijy6"
    shadc: "cls-3zcffj0n"
internetSSHTunnelConfig:
  user: "root"
  keyFile: "/etc/cls/tke.key"
  host: "*************"
  port: 11006
migration:
  global:
    cvmEtcdRootClientCertFile: "conf/etcd-cert-cvm/etcd-client.crt"
    cvmEtcdRootClientKeyFile: "conf/etcd-cert-cvm/etcd-client.key"
    kstoneEtcdRootClientCertFile: "conf/etcd-cert-kstone/etcd-client.crt"
    kstoneEtcdRootClientKeyFile: "conf/etcd-cert-kstone/etcd-client.key"
    oldCvmEtcdCACertFile: "conf/etcd-root-ca/old-etcd-ca.crt"
    newCvmEtcdCACertFile: "conf/etcd-root-ca/new-etcd-ca.crt"
    backupCluster:
      kubeconfig: "conf/kube/config/backup-resource-kubeconfig-cd"
    backupEtcd:
      prefix: "/cls-bfpx4mje"
      endpoint: "https://**************:18336"
      cert: "conf/etcd-cert-backup/etcd-client.crt"
      key: "conf/etcd-cert-backup/etcd-client.key"
  metaclusters:
    cq:
      srcMetaCluster: "cls-lr2pvld2"
      dstMetaCluster: "cls-9klxgg4i"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "***********"
        port: 22
    jkt:
      srcMetaCluster: "cls-bydrsad1"
      dstMetaCluster: "cls-1r8flj07"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "conf/key/meta.key"
        host: "*********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "***********"
        port: 22
    jp:
      srcMetaCluster: "cls-nx3v4et0"
      dstMetaCluster: "cls-00s8dvf8"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "***********"
        port: 22
    ca:
      srcMetaCluster: "cls-nkxkhgvb"
      dstMetaCluster: "cls-b83zt9nz"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "conf/key/meta.key"
        host: "*********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
    use:
      srcMetaCluster: "cls-52jdb4dl"
      dstMetaCluster: "cls-kud6wwa5"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
    in:
      srcMetaCluster: "cls-kftyr0my"
      dstMetaCluster: "cls-rqp0oyc2"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
    th:
      srcMetaCluster: "cls-7wycntkc"
      dstMetaCluster: "cls-el5s5huy"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "***********"
        port: 22
    tpe:
      srcMetaCluster: "cls-qz9ifaxa"
      dstMetaCluster: "cls-my1izq74"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
    kr:
      srcMetaCluster: "cls-4i52bq23"
      dstMetaCluster: "cls-lybhszor"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "***********"
        port: 22
    ru:
      srcMetaCluster: "cls-prfp27at"
      dstMetaCluster: "cls-kzbhgkj9"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "***********"
        port: 22
    de:
      srcMetaCluster: "cls-3r01ruwo"
      dstMetaCluster: "cls-5t7brmqa"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
    usw:
      srcMetaCluster: "cls-7xvp0tcw"
      dstMetaCluster: "cls-1vxwfay6"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
    cd:
      srcMetaCluster: "cls-lizl7svv"
      dstMetaCluster: "cls-js4zt243"
      srcMetaTunnelConfig:
        user: "root"
        password: "TEG#@rpqd@4327"
        host: "**************"
        port: 22022
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
      srcMetaEtcd:
        endpoint: "http://*************:11706"
    sh:
      srcMetaCluster: "cls-rb4l34r9"
      dstMetaCluster: "cls-gofhtn3t"
      srcMetaTunnelConfig:
        user: "root"
        password: "TEG#@rpqd@4327"
        host: "**************"
        port: 22022
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "***********"
        port: 22
      srcMetaEtcd:
        endpoint: "http://**********:10565"
    gz:
      srcMetaCluster: "cls-rbnichlc"
      dstMetaCluster: "cls-1s75hexc"
      srcMetaTunnelConfig:
        user: "root"
        password: "TEG#@rpqd@4327"
        host: "**************"
        port: 22022
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
      srcMetaEtcd:
        endpoint: "http://**************:14800"
    hk:
      srcMetaCluster: "cls-ntwx40ak"
      dstMetaCluster: "cls-ne0wmuuw"
      srcMetaTunnelConfig:
        user: "root"
        password: "TEG#@rpqd@4327"
        host: "*************"
        port: 22022
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "*********"
        port: 22
      srcMetaEtcd:
        endpoint: "http://************:10726"
    sg:
      srcMetaCluster: "cls-qbc3zefo"
      dstMetaCluster: "cls-75j3tu60"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "conf/key/meta.key"
        host: "*********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "*********"
        port: 22
    tsn:
      srcMetaCluster: "cls-jyi5zllr"
      dstMetaCluster: "cls-ky90hr3p"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/ziyan_meta.key"
        host: "***********"
        port: 36000
    szx:
      srcMetaCluster: "cls-mh1d842k"
      dstMetaCluster: "cls-8d0h5nyy"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/ziyan_meta.key"
        host: "************"
        port: 36000
    bj:
      srcMetaCluster: "cls-cnas00yb"
      dstMetaCluster: "cls-0lk56led"
      srcMetaTunnelConfig:
        user: "root"
        password: "TEG#@rpqd@4327"
        host: "**************"
        port: 22022
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
      srcMetaEtcd:
        endpoint: "http://************:11353"
    sao:
      srcMetaCluster: "cls-3js0y19h"
      dstMetaCluster: "cls-0ckworoh"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "***********"
        port: 22
    nj:
      srcMetaCluster: "cls-3qs30g2e"
      dstMetaCluster: "cls-cnnoyaqm"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
    bjjr:
      srcMetaCluster: "cls-e2d8nvih"
      dstMetaCluster: "cls-kvd8ybzd"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "***********"
        port: 22
    shjr:
      srcMetaCluster: "cls-7we9vmys"
      dstMetaCluster: "cls-cmdg60g2"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "conf/key/meta.key"
        host: "********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "*********"
        port: 22
    szjr:
      srcMetaCluster: "cls-6e4edn6y"
      dstMetaCluster: "cls-cz5m04em"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "conf/key/meta.key"
        host: "*********"
        port: 22
#      srcMetaEtcd:
#        endpoint: "http://*******:2379"
