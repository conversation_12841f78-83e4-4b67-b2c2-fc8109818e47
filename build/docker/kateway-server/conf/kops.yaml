kops:
  version: 20250605-827c9a56-3-gca9d141
  disallowUpdate:
    appids:
      - 1253687700 # tke 生产账号 metacluster等
    clusters:
      - "cls-rbnichlc" # tke meta gz
      - "cls-1s75hexc" # tke meta gz-2
      - "cls-rb4l34r9" # tke meta sh
      - "cls-gofhtn3t" # tke meta sh-2
      - "cls-lr2pvld2" # tke meta cq
      - "cls-9klxgg4i" # tke meta cq-2
      - "cls-ntwx40ak" # tke meta hk
      - "cls-ne0wmuuw" # tke meta hk-2
      - "cls-nkxkhgvb" # tke meta ca
      - "cls-b83zt9nz" # tke meta ca-2
      - "cls-7we9vmys" # tke meta shjr
      - "cls-cmdg60g2" # tke meta shjr-2
      - "cls-qbc3zefo" # tke meta sg
      - "cls-75j3tu60" # tke meta sg-2
      - "cls-6e4edn6y" # tke meta szjr
      - "cls-cz5m04em" # tke meta szjr-2
      - "cls-6e4edn6y" # tke meta gzopen
      - "cls-7xvp0tcw" # tke meta usw
      - "cls-1vxwfay6" # tke meta usw-2
      - "cls-lizl7svv" # tke meta cd
      - "cls-js4zt243" # tke meta cd-2
      - "cls-3r01ruwo" # tke meta de
      - "cls-5t7brmqa" # tke meta de-2
      - "cls-4i52bq23" # tke meta kr
      - "cls-lybhszor" # tke meta kr-2
      - "cls-kftyr0my" # tke meta in
      - "cls-rqp0oyc2" # tke meta in-2
      - "cls-52jdb4dl" # tke meta use
      - "cls-kud6wwa5" # tke meta use-2
      - "cls-7wycntkc" # tke meta th
      - "cls-el5s5huy" # tke meta th-2
      - "cls-prfp27at" # tke meta ru
      - "cls-kzbhgkj9" # tke meta ru-2
      - "cls-nx3v4et0" # tke meta jp
      - "cls-00s8dvf8" # tke meta jp-2
      - "cls-46jrda2y" # tke meta jnec
      - "cls-epm3yjlh" # tke meta hzec
      - "cls-3qs30g2e" # tke meta nj
      - "cls-cnnoyaqm" # tke meta nj-2
      - "cls-jyi5zllr" # tke meta tsn
      - "cls-mh1d842k" # tke meta szx
      - "cls-e2d8nvih" # tke meta bjjr
      - "cls-kvd8ybzd" # tke meta bjjr-2
      - "cls-epm3yjl1" # tke meta fzec
      - "cls-epm3yjl0" # tke meta whec
      - "cls-epm3ymyc" # tke meta csec
      - "cls-qz9ifaxa" # tke meta tpe
      - "cls-my1izq74" # tke meta tpe-2
      - "cls-cnas00yb" # tke meta bj
      - "cls-0lk56led" # tke meta bj-2
      - "cls-epm3x584" # tke meta sjwec
      - "cls-4tqcs5rb" # tke meta xbec
      - "cls-epm3x57o" # tke meta hfeec
      - "cls-epm3x86t" # tke meta sheec
      - "cls-9cgvijy6" # tke meta xiyec
      - "cls-9cgvijy6" # tke meta cgoec
      - "cls-9cgvijy6" # tke meta qyxa
      - "cls-bydrsad1" # tke meta jkt
      - "cls-1r8flj07" # tke meta jkt-2
      - "cls-3js0y19h" # tke meta sao
      - "cls-0ckworoh" # tke meta sao-2
      - "cls-3zcffj0n" # tke meta shadc
      - "cls-h4629lhl" # tke meta qy
      - "cls-8d0h5nyy" # tke meta szx
      - "cls-ky90hr3p" # tke meta tsn
      - "cls-mlwo2v7b" # tke meta bj
      - "cls-qvvptqc3" # tke meta sh
      - "cls-pgl5mqfo" # tke meta gz
      - "cls-rzasdlks" # tke meta nj
      - "cls-3efjbva1" # tke meta cd
      - "cls-igj9pcga" # tke meta cq
      - "cls-gcz1z1f6" # tke meta sg
      - "cls-9o38eplq" # tke meta hk
      - "cls-1zmp6uy3" # tke meta ca
      - "cls-jn7xp6cm" # tke meta usw
      - "cls-1fl6rdoy" # tke meta de
      - "cls-p9g45e01" # tke meta kr
      - "cls-5hnnf5ay" # tke meta in
      - "cls-mq5b0wtf" # tke meta use
      - "cls-el7if67c" # tke meta th
      - "cls-4623qke7" # tke meta ru
      - "cls-fd0y2nas" # tke meta jp
      - "cls-afgk2s1j" # tke meta jkt
      - "cls-oo4yn6y1" # tke meta sao
      - "cls-32zy1hh3" # eks meta bj
      - "cls-0ooumt67" # eks meta bjjr
      - "cls-0l8nw56p" # eks meta ca
      - "cls-g1xoolej" # eks meta cd
      - "cls-8gwakjc0" # eks meta cq
      - "cls-13jssaqe" # eks meta csec
      - "cls-7wn2shoc" # eks meta de
      - "cls-cigjh4yr" # eks meta fzec
      - "cls-gkgefl4q" # eks meta gz
      - "cls-cigjfqle" # eks meta hfeec
      - "cls-rfkyco6q" # eks meta hk
      - "cls-cigjh4z7" # eks meta hzec
      - "cls-2kcywybi" # eks meta in
      - "cls-mlnmf6vn" # eks meta jkt
      - "cls-cup5z68w" # eks meta jnec
      - "cls-8gmtvrjk" # eks meta jp
      - "cls-0nskj1m5" # eks meta kr
      - "cls-k7l72s8u" # eks meta nj
      - "cls-l6ft4s8h" # eks meta ru
      - "cls-71mi45vx" # eks meta sao
      - "cls-g9amr2n2" # eks meta sg
      - "cls-fwsjd1df" # eks meta sh
      - "cls-f2fseugh" # eks meta sh-3
      - "cls-6dhiel97" # eks meta sh-2
      - "cls-dq3itro3" # eks meta sh-4
      - "cls-dn4i3bob" # eks meta sh-5
      - "cls-md3ael05" # eks meta sh-6
      - "cls-6dhiel97" # eks meta sh-wxzf
      - "cls-kmnt5mwr" # eks meta sheec
      - "cls-hgsdzo0y" # eks meta shjr
      - "cls-cigjfqlu" # eks meta sjwec
      - "cls-10iznk6c" # eks meta szjr
      - "cls-26yncp9w" # eks meta szx
      - "cls-crhlsahw" # eks meta th
      - "cls-if9zp2hy" # eks meta tpe
      - "cls-bzv9ksbf" # eks meta tsn
      - "cls-f9tl6ee9" # eks meta use
      - "cls-8cgiip7d" # eks meta usw
      - "cls-cigjh4yq" # eks meta whec
      - "cls-c73owjg0" # eks meta xiyec
      - "cls-8byl25fp" # eks meta shadc
      - "cls-g0zvpnr3" # eks meta tsn
      - "cls-gxjv4spk" # eks meta szx
      - "cls-cajt2ulz" # eks meta bj
      - "cls-pk0wwx1t" # eks meta sh
      - "cls-5psnrzpw" # eks meta gz
      - "cls-6u5hdkj6" # eks meta nj
      - "cls-ijpmpffj" # eks meta cd
      - "cls-lqd6g202" # eks meta cq
      - "cls-71jyr0rg" # eks meta sg
      - "cls-jk5zz14i" # eks meta hk
      - "cls-ifste2mf" # eks meta ca
      - "cls-ly1349uq" # eks meta usw
      - "cls-ma2cu9ni" # eks meta de
      - "cls-n1iajjpf" # eks meta kr
      - "cls-3zb2kfyw" # eks meta in
      - "cls-3jsphtpf" # eks meta use
      - "cls-3j12wxji" # eks meta th
      - "cls-0k9ivlg5" # eks meta ru
      - "cls-2jtmdvxs" # eks meta jp
      - "cls-pclgo4ez" # eks meta jkt
      - "cls-8ss0546d" # eks meta sao
      - "cls-kfb5f28i" # cass xiyec
      - "cls-3udn7s3t" # cass shadc
      - "cls-kmnt6ybf" # cass hzec
      - "cls-bqhtbuow" # cass jnec
      - "cls-p7mpgajj" # cass ru
      - "cls-23ha3lf1" # cass ca
      - "cls-pvsmo6ig" # cass de
      - "cls-2jtmdsrl" # cass kr
      - "cls-43shmal4" # cass sg
      - "cls-ngj2wvrf" # cass sao
      - "cls-2f2s92d2" # cass nj
      - "cls-46jrzo0a" # cass hfeec
      - "cls-46js12dn" # cass fzec
      - "cls-46jrzo0q" # cass sjwec
      - "cls-kmnt71oa" # cass csec
      - "cls-46js12dm" # cass whec
      - "cls-cigjftkj" # cass sheec
      - "cls-qo0wk4y3" # cass xbec
      - "cls-ldjwb2oj" # cass jkt
      - "cls-5xm6e9a0" # cass jp
      - "cls-jmw910x3" # cass use
      - "cls-79rs0y94" # cass th
      - "cls-5illtbng" # cass in
      - "cls-3ja1neug" # cass tpe
      - "cls-1spi5ea8" # cass hk
      - "cls-po65oeqs" # cass usw
      - "cls-qz3edezt" # cass bjjr
      - "cls-otmtwkg0" # cass szjr
      - "cls-c6mdgc88" # cass cq
      - "cls-rekhlagg" # cass szx
      - "cls-dhzp4gjm" # cass shjr
      - "cls-94v9pg5y" # cass szx
      - "cls-ph1sfqcb" # cass tsn
      - "cls-p56igkpl" # cass cd
      - "cls-6mics35j" # cass bj
      - "cls-dak5z2ab" # cass sh
      - "cls-eujp1li2" # cass gz
      - "cls-dyp4pnhh" # cass qy
      - "cls-20ms7fx6" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
      - "cls-r3ge0w0i" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
      - "cls-e65w6gd2" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
      - "cls-bqhyyfqo" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
      - "cls-barwz3zs" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
      - "cls-gdtjvd2q" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
      - "cls-478pofci" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
      - "cls-7gxrpc6o" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
      - "cls-hnchnzzu" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
      - "cls-q8qdff0w" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
      - "cls-18wfan5i" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
      - "cls-6em27ln9" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
      - "cls-7gmpjac6" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
      - "cls-9cp3gjr2" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
      - "cls-18wfan5i" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
      - "cls-rfv0fsc9" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
      - "cls-qzst1bg5" # 元梦之星项目，禁止一切组件发布，接口人：wessonli
  allowUpdate:
    appids:
      - 1251707795 #  3321337994 容器中心体验账号
      - 1255429721 # 现网测试账号 leohjye(叶虹江) langwei(尉烺)
    clusters:
      # teg-tkex 测试集群
      - cls-7m2mnrz0
      - cls-ggs3qzcq
      - cls-hjr6h1rz
      - cls-bfi82gu1
      # camp 测试 父集群
      - cls-c4wxs2uu
      - cls-1imqg5fb
      # camp 测试 子集群
      - cls-3daxhf1b
      - cls-1i62zg8m
      - cls-394m4jli
      - cls-fsiq95a4
      - cls-fve5sbyo
      - cls-i9et79ll
      - cls-mg7ol3cm
      - cls-ogsfrmms
      - cls-pz4wglp7
      - cls-q8axllhf

skipCluster:

service:
  mockVersion: v2.3.3
  expectedVersion: ">=v2.2.2"
  versions:
    - v2.4.3
    - v2.4.2
    - v2.4.1
    - v2.4.0
    - v2.3.4
    - v2.3.3
    - v2.3.2
    - v2.3.1
    - v2.3.0
    - v2.1.3

ingress:
  mockVersion: v2.2.7
  expectedVersion: ">=v2.2.2"
  versions:
    - v2.4.2
    - v2.4.1
    - v2.4.0
    - v2.1.3

mysql:
  host: ************
  port: 3306
  user: root
  password: rnM%%uCqG@VZ
  database: kateway
  migrate:
    enable: false

stke:
  host: **************
  port: 3838
  user: tkex_reader
  password: reader#123789
  database: db_stke

ianvs:
  host: ************
  port: 3306
  user: tkeoss
  password: 3J-iHiXf,GMqQHm7h4op
  database: tkeoss

tkeoss:
  host: ***********
  port: 3306
  user: root
  password: r2Ekg37T^K
  database: tkeoss

sts:
  roleName: "TKE_QCSRole"
  secretID: "AKIDXco1EUjCkIuZK5qfaXWbKQTWcq3JdyOq"
  secretKey: "Y6X2Z7kO6sGe2VXZXVQNOQUZYTXZdkaQ"

checker:
  tag:
    deletionThreshold: 15
    maxDeletionNumPerUser: 500
    deletionEnabled: true
    targetKeys:
      - "tke-lb-multi-cluster-service-uuid"
      - "tke-lb-multi-cluster-ingress-uuid"
      - "tke-lb-serviceuuid"
      - "tke-lb-serviceId"
      - "tke-lb-ingress-uuid"
      - "tke-name"

inspection: # 健康巡检/后检
  maxConcurrentSyncLBRPerCluster: 10 # 单任务最大并发LB数
  maxConcurrentRunningTaskCount: 20 # 最大并发任务数
  minTaskSyncInterval: "1m" # 巡检周期最小间隔
  rateLimit: 50 # yun api 每秒最大QPS

regions:
  - bj
  - bjjr
  - ca
  - cd
  - cq
  - csec
  - de
  - fzec
  - gz
  - gzwxzf
  - hfeec
  - hk
  - hzec
  - in
  - jkt
  - jnec
  - jp
  - kr
  - nj
  - qy
  - sao
  - sg
  - sh
  - shadc
  - sheec
  - shjr
  - shwxzf
  - sjwec
  - szjr
  - szx
  - th
  - tpe
  - tsn
  - use
  - usw
  - whec
  - xbec
  - xiyec

lbcf:
  clusters:
    - cls-7m2mnrz0
    - cls-rfbqgjul
    - cls-bjv674lw
    - cls-a3jxgqvm
    - cls-i7tg9zcv
    - cls-my9s32fl
    - cls-047889q5
    - cls-0ooen5a8
    - cls-ciyxgivo
    - cls-b0cp87v4
    - cls-0laq0gd0
    - cls-3uddjd86
    - cls-9klt16nk
    - cls-aquc5yjg
    - cls-e14qx3ay
    - cls-348su9lc
    - cls-1gmy3kxe
    - cls-6dzdkd5o
    - cls-4n16ipo2
    - cls-revggsrc
    - cls-l9kyvwi8
    - cls-e6tix8u2
    - cls-a0lx60km
    - cls-rb7dlpsg
    - cls-4txr3tf6
    - cls-i73tpwwp
    - cls-r6rl14v2
    - cls-g8dv2xll
    - cls-3rye1exk
    - cls-dhhf67nf
    - cls-e9zy6r4m
    - cls-q3pvu00s
    - cls-mhq5w76k
    - cls-l6fr2vmi
    - cls-97qgae49
    - cls-pvw10uyl
    - cls-4ycwcjyq
    - cls-r777iid2
    - cls-8g81paji
    - cls-0kcf2iu4
    - cls-94tjx15d
    - cls-cf6gkt8z
    - cls-rc58zvjy
    - cls-27r0um6y
    - cls-n1ggq1hy
    - cls-i46xytdo
    - cls-5tzo7u0p
    - cls-3z3dvyok
    - cls-pt5y8im1
    - cls-mtdojpqt
    - cls-7d5mj1w7
    - cls-arkb59w4
    - cls-2riiqz8x
    - cls-f5oe5jdk
    - cls-4fc3xlys
    - cls-eleovi9e
    - cls-7m451th8
    - cls-hng4e4hm
    - cls-dq30sxjw
    - cls-8th4z7ec
    - cls-opemp6je

monitor:
  clusters:
    - cls-dyg9hwtz # wallaceqian(钱成龙) 测试 独立集群
    - cls-389935y5 # wallaceqian(钱成龙) 测试 托管集群
    - cls-lzu79k63 # wallaceqian(钱成龙) 测试 eks集群
    - cls-7m2mnrz0 # wallaceqian(钱成龙) 测试 tkex集群

    - cls-oljaaoij # lwbowenyan(颜博文) 测试 托管集群

    # # 225账号下生产集群
    - cls-2iww0kcv
    - cls-4wyn3tm3
    - cls-qnd1usfo
    - cls-cha0isrp
    - cls-bi33zk3i
    - cls-pxf79nau
    - cls-c24bq70i
    - cls-g0gb48qj
    - cls-pkzrrrmm
    - cls-76nzfqmw
    - cls-kr2wsko3
    - cls-pkt63qcl
    - cls-ot6y7hir
    - cls-ozzakd62
    - cls-6fi4byzm
    - cls-98r2yvqh
    - cls-qm36xe13
    - cls-euaasesv
    - cls-hvtik026
    - cls-5qgmf29k
    - cls-1mual6co
    - cls-fxigemrg
    - cls-mgz40ovg
    - cls-b6dzopwr
    - cls-g5f9x8fa
    - cls-5ae2l8m1
    - cls-lmfqy6t0
    - cls-7zsjigsj
    - cls-ne2lpnoy
    - cls-1ohw4q52
    - cls-oi55bsvg
    - cls-8vbh7rrk
    - cls-qtb03njd
    - cls-0kkmuwvy
    - cls-lqdgxh35
    - cls-gzbibbio
    - cls-5qtzbqk2
    - cls-ad36nase
    - cls-7f8v8tde
    - cls-i1sj59w2
    - cls-b0ps0ay1
    - cls-4o4bx1k6
    - cls-s26a7e1k
    - cls-bad9jp06
    - cls-1gklfdci
    - cls-h3022r86
    - cls-islul8m4
    - cls-17f10sog
    - cls-3i6s4mu0
    - cls-hq6qk3i6
    - cls-5wj1cw2w
    - cls-loqnuvkp
    - cls-q8axllhf
    - cls-5peu35md
    - cls-cizjhqv4
    - cls-6ho2h6pw
    - cls-65s55vil
    - cls-gwhr8c2q
    - cls-k2pmso5v
    - cls-l2xpq2bd
    - cls-5pot95jg
    - cls-i0glf6xu
    - cls-a415svbz
    - cls-mtxwtw7w
    - cls-f4yqkx53
    - cls-fwu23264
    - cls-6lxrn54l
    - cls-c5xm9oy9
    - cls-ligi87gk
    - cls-5cin2jwm
    - cls-jli1o1oi
    - cls-8y9g3o53
    - cls-2b6va5kj
    - cls-5potwxu4
    - cls-le0uv950
    - cls-j89ti0q5
    - cls-4j61i547
    - cls-nloqerri
    - cls-46o8xh67
    - cls-5qlo8oe2
    - cls-9i0d540n
    - cls-8yaiklm0
    - cls-o78lz8zw
    - cls-n55ul29p
    - cls-bcv02ap0
    - cls-47c312d5
    - cls-kfu0vyyf
    - cls-7cwxf597
    - cls-991r4bpx
    - cls-882xu0r7
    - cls-bubewpm9
    - cls-o5molbf9
    - cls-dt6vsjrg
    - cls-8ghvr0wg
    - cls-fsver17c
    - cls-fdsx31r2
    - cls-fkspzt20
    - cls-lnc10ksw
    - cls-6u3brswu
    - cls-dn618ezi
    - cls-gpcoh5mm
    - cls-5tyj9918
    - cls-jjxvfdvj
    - cls-d25uucsx
    - cls-g9syempk
    - cls-2mpslu8m
    - cls-198pmfde
    - cls-fm3v5r9w
    - cls-7ly1mcce
    - cls-az26192y
    - cls-8w8fh6wi
    - cls-owj0g590
    - cls-ggyxim0q
    - cls-elrc7cfq
    - cls-kaf4ph2u
    - cls-4rko5dqq
    - cls-p83rfhn6
    - cls-5647mq0k
    - cls-gs2bydvi
    - cls-1sfwjl4i
    - cls-gcjk2ahk
    - cls-qkdwattc
    - cls-lzlj7ofz
    - cls-ecuw9193
    - cls-kuiqicol
    - cls-fz17501r
    - cls-3ykw83bp
    - cls-7a89cqhd
    - cls-5vzjfwoh
    - cls-9xkt3r3r
    - cls-i5zow2ut
    - cls-llcobzwf
    - cls-gvsndjeh
    - cls-9o4af9yx
    - cls-5wcdgp3l
    - cls-f281sygh
    - cls-58ncn6et
    - cls-8xtdr6kd
    - cls-cbb5aocb
    - cls-nfyilxg3
    - cls-3l7lszpv
    - cls-cdcjfxmd
    - cls-0a9obzx3
    - cls-5t0xhgwl
    - cls-2zu08x03
    - cls-1qcpfedt
    - cls-5twsaho3
    - cls-7wgltlzp
    - cls-c3tdrb23
    - cls-a5xnlo1h
    - cls-rth8v32t
    - cls-e5bwryfr
    - cls-5icnpmi9
    - cls-hvalxv9x
    - cls-bv0h5uph
    - cls-c4zby36d
    - cls-fts78987
    - cls-cuieh455
    - cls-bdcra183
    - cls-iduhpo4b
    - cls-bhbzq583
    - cls-lm7dx0up
    - cls-rums21bv
    - cls-emn2z7p1
    - cls-s0xc2aid
    - cls-2isflzq7
    - cls-ihudy1zp
    - cls-juig7fub
    - cls-p4s1watv
    - cls-pqj91k9f
    - cls-3ws8e9jl
    - cls-rtal7gt7
    - cls-ckt4ylvb
    - cls-5nv7eqii
    - cls-hpsdonp4
    - cls-fqydkw36
    - cls-jpaqm42m
    - cls-lp0pze2e
    - cls-9293sj5m
    - cls-ar00unyy
    - cls-owdndj1a
    - cls-c4wxs2uu
    - cls-2nj92sz4
    - cls-4z99rb59
    - cls-8w7zxpzy
    - cls-5n1y5boy
    - cls-98zz20xk
    - cls-5rbfpcey
    - cls-elombkxi
    - cls-gsurpfaq
    - cls-5p7pptv4
    - cls-mpnlem56
    - cls-85ja4vti
    - cls-cm5adqse
    - cls-ny1orexc
    - cls-7s2qq2eq
    - cls-7vszvd12
    - cls-6c3fyjjq
    - cls-ixvinumy
    - cls-lq3pomd8
    - cls-9t0pue8k
    - cls-cw53vmvq
    - cls-gjuzd92o
    - cls-2dr86szg
    - cls-4b6esjcs
    - cls-nsvxernc
    - cls-isdkjo0u
    - cls-q73xuilw
    - cls-er5syccc
    - cls-a9c7673u
    - cls-nrhh618a
    - cls-q11hd6gg
    - cls-46h1l9x8
    - cls-j2kpd19u
    - cls-m67q0m7o
    - cls-2p5fa09i
    - cls-ls1le8aa
    - cls-qzr0esc6
    - cls-i1si65a6
    - cls-94j616u2
    - cls-d79zjy46
    - cls-h2hx3tqu
    - cls-4g9b5b18
    - cls-5vzjheeo
    - cls-pm40lmeq
    - cls-m6mqfwik
    - cls-2dhu4ljm
    - cls-op6fy4vm
    - cls-gxajr3sg
    - cls-gkge9who
    - cls-93w6bcqc
    - cls-fh8mhdcg
    - cls-o0aabxas
    - cls-6gup3mfs
    - cls-jizxabw8
    - cls-103emv82
    - cls-q3gmzhci
    - cls-9gn8v8ja
    - cls-lig17rni
    - cls-85alctde
    - cls-0cojh5by
    - cls-28r6d4wq
    - cls-7gn5m4ey
    - cls-nwl1mpoy
    - cls-5u7h2kcc
    - cls-gsrzdcag
    - cls-gwj8jkfk
    - cls-bk30o0tw
    - cls-3vel91mg
    - cls-ibde5djs
    - cls-qzm5oy1l
    - cls-dqiet1p1
    - cls-93lsw59u
    - cls-ryrujt8c
    - cls-eqdwbo1o
    - cls-kqvd826s
    - cls-nsf1yh4u
    - cls-7ojtpsja
    - cls-fs9f2frw
    - cls-9w88ylw5
    - cls-d25nokb9
    - cls-8s8vpiwu
    - cls-ijoj5use
    - cls-5u5t2fzq
    - cls-00zwkb20
    - cls-ddpc71su
    - cls-leqrynlq
    - cls-51lhs1cs
    - cls-3c76tud4
    - cls-l366dtjm
    - cls-36z3fs0u
    - cls-cb7rcblq
    - cls-i867a9ng
    - cls-iyprn1ma
    - cls-ghh42xnc
    - cls-p94zvb2o
    - cls-o9e17c7y

    # CLB 父集群 cluster 列表（正式环境）
    - cls-bzekj5w8
    - cls-jqetd4fo
    - cls-ibcuk7ss
    - cls-ki0bad94
    - cls-916rhpvg
    - cls-8degv76g
    - cls-kfh1rtvc
    - cls-dexchdfi
    - cls-inx2xb22
    - cls-aoj8bhq6
    - cls-ghq2luog
    - cls-k2xzvexe
    - cls-dyomi5ui

    # CLB 父集群 cluster 列表（测试环境）
    - cls-c4wxs2uu
    - cls-1imqg5fb

task:
  mergeStats:
    serviceVersion: 2.5.0
  merge:
    enable: true
    limit: 1
    skippedClusters:
      - "cls-oljaaoij"
      - "cls-jys2hjdh"
      - "cls-2iedl76h"
      - "cls-6cwf6f32"
      - "cls-o5w7nfg0"
      - "cls-krzgoaac"
      - "cls-dqk7bunm"
  liveness:
    interval: 1h
    appIDs:
      - "1251707795"
      - "1255429721"
