dashboard_db:
  cq:
    name: "dashboard"
    host: "*************"
    port: 3306
    user: "dash_dev_ro"
    pass: "UcpX3j68ZPdDB4hM"
  in:
    name: "dashboard"
    host: '*************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'Tp8MmkDRyUQjKPJZ'
  sg:
    name: "dashboard"
    host: '*************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'HzeZhK3R2476EyxQ'
  usw:
    name: "dashboard"
    host: '************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'M2eE74QgdkCPhAqW'
  use:
    name: "dashboard"
    host: '************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'JgqVm4NwRbDuFnTL'
  jp:
    name: "dashboard"
    host: '************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'X8qvucy2HA3UbpYR'
  kr:
    name: "dashboard"
    host: '*************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'HFetCEfVap298SZM'
  ru:
    name: "dashboard"
    host: '*************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'SgrUFuwDzGvhQ63T'
  th:
    name: "dashboard"
    host: '************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'WT94AP8SQvMpCNrB'
  hk:
    name: "dashboard"
    host: '***********'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'Hekn73NjvWqBMgLM'
  bj:
    name: "dashboard"
    host: '***********'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'FEqV4jDsC3zmuWaX'
  sh:
    name: "dashboard"
    host: '*************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'HzeZhK3R2476EyxQ'
  gz:
    name: "dashboard"
    host: "************"
    port: 3306
    user: "dash_dev_ro"
    pass: "UKeugyjs5WvrkYpT"
  cd:
    name: "dashboard"
    host: '***********'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'FEqV4jDsC3zmuWaX'
  ca:
    name: "dashboard"
    host: '*************'
    port: 3306
    user: "dash_dev_ro"
    pass: "VDWw6G94quXbpJeM"
  de:
    name: "dashboard"
    host: '*************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'PrqUzjEHD4TnAM5C'
  szjr:
    name: "dashboard"
    host: '*************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'R3pkMLCWj29xUGsM'
  shjr:
    name: "dashboard"
    host: '**************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'ZQH8apV6GgLcTwKV'
  nj:
    name: "dashboard"
    host: '************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'XWm35KezDfYGq4LA'
  tsn:
    name: "dashboard"
    host: '*************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'T9Lg6HYq3ySbnfUC'
  szx:
    name: "dashboard"
    host: '************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'QnjVXxs6LZHE5cFW'
  tpe:
    name: "dashboard"
    host: '*************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'QyJTgu9C5EUZbKkG'
  qy:
    name: "dashboard"
    host: "************"
    port: 3306
    user: "dash_dev_ro"
    pass: "Go54n61QO4ZZfJ0P"
  bjjr:
    name: "dashboard"
    host: '*************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'UKeugyjs5WvrkYpT'
  xbec:
    name: "dashboard"
    host: '************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'Hekn73NjvWqBMgLM'
  jkt:
    name: "dashboard"
    host: '*************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'TJ4RHfke8qvh2aBL'
  hzec:
    name: "dashboard"
    host: '***********'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'X6s2PKwq9pf8JZ5N'
  jnec:
    name: "dashboard"
    host: '**********'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'K34RtuzyxdRz3CjJ'
  fzec:
    name: "dashboard"
    host: '*************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'Hekn73NjvWqBMgLM'
  whec:
    name: "dashboard"
    host: '************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'FEqV4jDsC3zmuWaX'
  csec:
    name: "dashboard"
    host: '***********'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'HzeZhK3R2476EyxQ'
  sheec:
    name: "dashboard"
    host: '***********'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'UcpX3j68ZPdDB4hM'
  sjwec:
    name: "dashboard"
    host: '*************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'DBALFSH6eTxdgbyA'
  hfeec:
    name: "dashboard"
    host: '**************'
    port: 3306
    user: 'dash_dev_ro'
    pass: 'KNzH5hXsjb3xam4B'
  sao:
    name: "dashboard"
    host: "*************"
    port: 3306
    user: "dash_dev_ro"
    pass: "V8gSzyuqa2UkWnrP"
  xiyec:
    name: "dashboard"
    host: "*************"
    port: 3306
    user: "dash_dev_ro"
    pass: "GM2Xt4kQxRua3vZC"
  shadc:
    name: "dashboard"
    host: "************"
    port: 3306
    user: "dash_dev_ro"
    pass: "FXEsnQ8RbY7aw32R"
  shwxzf:
    name: "dashboard"
    host: "************"
    port: 3306
    user: "dash_dev_ro"
    pass: "36UjJqBz5q9gNUhX"
  gzwxzf:
    name: "dashboard"
    host: "************"
    port: 3306
    user: "dash_dev_ro"
    pass: "K8gTTtAsBFT7DxKG"
cauth:
  server:
    cq: "http://logical.server.console.tencentyun.com:8080/interface.php"
    jkt: "http://logical.server.console.tencentyun.com:8080/interface.php"
    jp: "http://logical.server.console.tencentyun.com:8080/interface.php"
    ca: "http://logical.server.console.tencentyun.com:8080/interface.php"
    use: "http://logical.server.console.tencentyun.com:8080/interface.php"
    th: "http://logical.server.console.tencentyun.com:8080/interface.php"
    in: "http://logical.server.console.tencentyun.com:8080/interface.php"
    ru: "http://logical.server.console.tencentyun.com:8080/interface.php"
    kr: "http://logical.server.console.tencentyun.com:8080/interface.php"
    de: "http://logical.server.console.tencentyun.com:8080/interface.php"
    sao: "http://logical.server.console.tencentyun.com:8080/interface.php"
    usw: "http://logical.server.console.tencentyun.com:8080/interface.php"
    cd: "http://logical.server.console.tencentyun.com:8080/interface.php"
    hk: "http://logical.server.console.tencentyun.com:8080/interface.php"
    sg: "http://logical.server.console.tencentyun.com:8080/interface.php"
    tsn: "http://logical.server.console.tencentyun.com:8080/interface.php"
    szx: "http://logical.server.console.tencentyun.com:8080/interface.php"
    gz: "http://logical.server.console.tencentyun.com:8080/interface.php"
    bj: "http://logical.server.console.tencentyun.com:8080/interface.php"
    nj: "http://logical.server.console.tencentyun.com:8080/interface.php"
    sh: "http://logical.server.console.tencentyun.com:8080/interface.php"
    shjr: "http://logical.server.console.tencentyun.com:8080/interface.php"
    szjr: "http://logical.server.console.tencentyun.com:8080/interface.php"
    bjjr: "http://logical.server.console.tencentyun.com:8080/interface.php"
    tpe: "http://logical.server.console.tencentyun.com:8080/interface.php"
    qy: "http://logical.server.console.tencentyun.com:8080/interface.php"
    xbec: "http://logical.server.console.tencentyun.com:8080/interface.php"
    hzec: "http://logical.server.console.tencentyun.com:8080/interface.php"
    jnec: "http://logical.server.console.tencentyun.com:8080/interface.php"
    fzec: "http://logical.server.console.tencentyun.com:8080/interface.php"
    whec: "http://logical.server.console.tencentyun.com:8080/interface.php"
    csec: "http://logical.server.console.tencentyun.com:8080/interface.php"
    sheec: "http://logical.server.console.tencentyun.com:8080/interface.php"
    sjwec: "http://logical.server.console.tencentyun.com:8080/interface.php"
    hfeec: "http://logical.server.console.tencentyun.com:8080/interface.php"
    xiyec: "http://logical.server.console.tencentyun.com:8080/interface.php"
    shadc: "http://logical.server.console.tencentyun.com:8080/interface.php"
vpc:
  vpcOss:
    url: "http://REGION.oss.vpc.tencentyun.com:8080/tvpc/api"
  apiV3:
    url: "http://REGION.vpcapiv3.tencentyun.com:8520"
domain:
  url: "http://api.gslb.tencentyun.com"
  sysId: 1923420120
roleCfg:
  tkeQcsRole:
    secretId: "AKIDXco1EUjCkIuZK5qfaXWbKQTWcq3JdyOq"
    secretKey: "Y6X2Z7kO6sGe2VXZXVQNOQUZYTXZdkaQ"
    rolePrefix: "roleName"
    roleName: "TKE_QCSRole"
tke:
  dashboard:
    url: "http://REGION.tke.caas.tencentyun.com/dashboard"
  tkeApiserver:
    url: "https://REGION.api.tke.caas.tencentyun.com"
    cert: "/etc/tops/conf/tke-api-cert/admin.pem"
    key: "/etc/tops/conf/tke-api-cert/admin-key.pem"
  tkePlatform:
    url: "https://REGION.tke.caas.tencentyun.com:9443"
    cert: "/etc/tops/conf/tke-platform-cert/platform.pem"
    key: "/etc/tops/conf/tke-platform-cert/platform-key.pem"
  tkeApplication:
    url: "https://REGION.tke.caas.tencentyun.com:9463"
    cert: "/etc/tops/conf/tke-platform-cert/platform.pem"
    key: "/etc/tops/conf/tke-platform-cert/platform-key.pem"
  defaultMetaCluster:
    gz: "cls-rbnichlc"
    szjr: "cls-6e4edn6y"
    sh: "cls-rb4l34r9"
    shjr: "cls-7we9vmys"
    bj: "cls-cnas00yb"
    cd: "cls-lizl7svv"
    cq: "cls-lr2pvld2"
    hk: "cls-ntwx40ak"
    sg: "cls-qbc3zefo"
    th: "cls-7wycntkc"
    in: "cls-kftyr0my"
    kr: "cls-4i52bq23"
    jp: "cls-nx3v4et0"
    usw: "cls-7xvp0tcw"
    use: "cls-52jdb4dl"
    de: "cls-3r01ruwo"
    ru: "cls-prfp27at"
    nj: "cls-3qs30g2e"
    tsn: "cls-jyi5zllr"
    szx: "cls-mh1d842k"
    tpe: "cls-qz9ifaxa"
    ca: "cls-nkxkhgvb"
    qy: "cls-h4629lhl"
    bjjr: "cls-e2d8nvih"
    xbec: "cls-4tqcs5rb"
    jkt: "cls-bydrsad1"
    jnec: "cls-46jrda2y"
    hzec: "cls-epm3yjlh"
    whec: "cls-epm3yjl0"
    csec: "cls-epm3ymyc"
    sheec: "cls-epm3x86t"
    sjwec: "cls-epm3x584"
    fzec: "cls-epm3yjl1"
    hfeec: "cls-epm3x57o"
    sao: "cls-3js0y19h"
    xiyec: "cls-9cgvijy6"
    shadc: "cls-3zcffj0n"
eks:
  cloudGwUrl: "http://REGION.tke.caas.tencentyun.com/tke-cloud-gw"
  eksServerUrl: "http://REGION.eks.caas.tencentyun.com/eks-server"
  eksPlatform:
    url: "https://REGION.eks.caas.tencentyun.com:30201"
    cert: "/etc/tops/conf/eks-platform-cert/eks-platform.pem"
    key: "/etc/tops/conf/eks-platform-cert/eks-platform-key.pem"
migration:
  global:
    cvmEtcdRootClientCertFile: "/etc/tops/conf/etcd-cert-cvm/etcd-client.crt"
    cvmEtcdRootClientKeyFile: "/etc/tops/conf/etcd-cert-cvm/etcd-client.key"
    kstoneEtcdRootClientCertFile: "/etc/tops/conf/etcd-cert-kstone/etcd-client.crt"
    kstoneEtcdRootClientKeyFile: "/etc/tops/conf/etcd-cert-kstone/etcd-client.key"
    oldCvmEtcdCACertFile: "/etc/tops/conf/etcd-root-ca/old-etcd-ca.crt"
    newCvmEtcdCACertFile: "/etc/tops/conf/etcd-root-ca/new-etcd-ca.crt"
    backupCluster:
      kubeconfig: "/etc/tops/conf/kube/config/backup-resource-kubeconfig-cd"
    backupEtcd:
      prefix: "/cls-bfpx4mje"
      endpoint: "https://**************:18336"
      cert: "/etc/tops/conf/etcd-cert-backup/etcd-client.crt"
      key: "/etc/tops/conf/etcd-cert-backup/etcd-client.key"
  metaclusters:
    cq:
      srcMetaCluster: "cls-lr2pvld2"
      dstMetaCluster: "cls-igj9pcga"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "/etc/tops/conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "/etc/tops/conf/key/ziyan_meta.key"
        host: "*********"
        port: 36000
    jkt:
      srcMetaCluster: "cls-bydrsad1"
      dstMetaCluster: "cls-afgk2s1j"
    jp:
      srcMetaCluster: "cls-nx3v4et0"
      dstMetaCluster: "cls-fd0y2nas"
    ca:
      srcMetaCluster: "cls-nkxkhgvb"
      dstMetaCluster: "cls-1zmp6uy3"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "/etc/tops/conf/key/meta.key"
        host: "*********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "/etc/tops/conf/key/ziyan_meta.key"
        host: "*********"
        port: 36000
    use:
      srcMetaCluster: "cls-52jdb4dl"
      dstMetaCluster: "cls-mq5b0wtf"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "/etc/tops/conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "/etc/tops/conf/key/ziyan_meta.key"
        host: "***********"
        port: 36000
    in:
      srcMetaCluster: "cls-kftyr0my"
      dstMetaCluster: "cls-5hnnf5ay"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "/etc/tops/conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "/etc/tops/conf/key/ziyan_meta.key"
        host: "***********"
        port: 36000
    th:
      srcMetaCluster: "cls-7wycntkc"
      dstMetaCluster: "cls-el7if67c"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "/etc/tops/conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "/etc/tops/conf/key/ziyan_meta.key"
        host: "**********"
        port: 36000
    kr:
      srcMetaCluster: "cls-4i52bq23"
      dstMetaCluster: "cls-p9g45e01"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "/etc/tops/conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "/etc/tops/conf/key/ziyan_meta.key"
        host: "**********"
        port: 36000
    ru:
      srcMetaCluster: "cls-prfp27at"
      dstMetaCluster: "cls-4623qke7"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "/etc/tops/conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "/etc/tops/conf/key/ziyan_meta.key"
        host: "*********"
        port: 36000
    de:
      srcMetaCluster: "cls-3r01ruwo"
      dstMetaCluster: "cls-1fl6rdoy"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "/etc/tops/conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "/etc/tops/conf/key/ziyan_meta.key"
        host: "**********"
        port: 36000
    usw:
      srcMetaCluster: "cls-7xvp0tcw"
      dstMetaCluster: "cls-jn7xp6cm"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "/etc/tops/conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "/etc/tops/conf/key/ziyan_meta.key"
        host: "**********"
        port: 36000
    cd:
      srcMetaCluster: "cls-lizl7svv"
      dstMetaCluster: "cls-3efjbva1"
      srcMetaTunnelConfig:
        user: "root"
        password: "TEG#@rpqd@4327"
        host: "**************"
        port: 22022
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "/etc/tops/conf/key/ziyan_meta.key"
        host: "***********"
        port: 36000
      srcMetaEtcd:
        endpoint: "http://*************:11706"
    sh:
      srcMetaCluster: "cls-rb4l34r9"
      dstMetaCluster: "cls-qvvptqc3"
      srcMetaTunnelConfig:
        user: "root"
        password: "TEG#@rpqd@4327"
        host: "**************"
        port: 22022
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "/etc/tops/conf/key/ziyan_meta.key"
        host: "*********"
        port: 36000
      srcMetaEtcd:
        endpoint: "http://**********:10565"
    gz:
      srcMetaCluster: "cls-rbnichlc"
      dstMetaCluster: "cls-pgl5mqfo"
      srcMetaTunnelConfig:
        user: "root"
        password: "TEG#@rpqd@4327"
        host: "**************"
        port: 22022
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "/etc/tops/conf/key/ziyan_meta.key"
        host: "**********"
        port: 36000
      srcMetaEtcd:
        endpoint: "http://**************:14800"
    hk:
      srcMetaCluster: "cls-ntwx40ak"
      dstMetaCluster: "cls-9o38eplq"
      srcMetaTunnelConfig:
        user: "root"
        password: "TEG#@rpqd@4327"
        host: "*************"
        port: 22022
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "/etc/tops/conf/key/ziyan_meta.key"
        host: "**********"
        port: 36000
      srcMetaEtcd:
        endpoint: "http://************:10726"
    sg:
      srcMetaCluster: "cls-qbc3zefo"
      dstMetaCluster: "cls-gcz1z1f6"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "/etc/tops/conf/key/meta.key"
        host: "*********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "/etc/tops/conf/key/ziyan_meta.key"
        host: "***********"
        port: 36000
    tsn:
      srcMetaCluster: "cls-jyi5zllr"
      dstMetaCluster: "cls-ky90hr3p"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "/etc/tops/conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "/etc/tops/conf/key/ziyan_meta.key"
        host: "***********"
        port: 36000
    szx:
      srcMetaCluster: "cls-mh1d842k"
      dstMetaCluster: "cls-8d0h5nyy"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "/etc/tops/conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "/etc/tops/conf/key/ziyan_meta.key"
        host: "************"
        port: 36000
    bj:
      srcMetaCluster: "cls-cnas00yb"
      dstMetaCluster: "cls-mlwo2v7b"
      srcMetaTunnelConfig:
        user: "root"
        password: "TEG#@rpqd@4327"
        host: "**************"
        port: 22022
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "/etc/tops/conf/key/ziyan_meta.key"
        host: "**********"
        port: 36000
      srcMetaEtcd:
        endpoint: "http://************:11353"
    sao:
      srcMetaCluster: "cls-3js0y19h"
      dstMetaCluster: "cls-oo4yn6y1"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "/etc/tops/conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "/etc/tops/conf/key/ziyan_meta.key"
        host: "********"
        port: 36000
    nj:
      srcMetaCluster: "cls-3qs30g2e"
      dstMetaCluster: "cls-rzasdlks"
      srcMetaTunnelConfig:
        user: "ubuntu"
        keyFile: "/etc/tops/conf/key/meta.key"
        host: "**********"
        port: 22
      dstMetaTunnelConfig:
        user: "root"
        keyFile: "/etc/tops/conf/key/ziyan_meta.key"
        host: "**********"
        port: 36000
#      srcMetaEtcd:
#        endpoint: "http://*******:2379"
