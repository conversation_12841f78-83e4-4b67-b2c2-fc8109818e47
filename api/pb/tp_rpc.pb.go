// Copyright 2015 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v4.25.1
// source: pb/tp_rpc.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 访问凭证
type TPToken struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"` // 用户名
	Token    string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`       // token
	Host     string `protobuf:"bytes,3,opt,name=host,proto3" json:"host,omitempty"`         // 访问地址
	Type     string `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`         // token类型
}

func (x *TPToken) Reset() {
	*x = TPToken{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_tp_rpc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TPToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TPToken) ProtoMessage() {}

func (x *TPToken) ProtoReflect() protoreflect.Message {
	mi := &file_pb_tp_rpc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TPToken.ProtoReflect.Descriptor instead.
func (*TPToken) Descriptor() ([]byte, []int) {
	return file_pb_tp_rpc_proto_rawDescGZIP(), []int{0}
}

func (x *TPToken) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *TPToken) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *TPToken) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *TPToken) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

// 创建任务请求
type TPCreateTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region      string   `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`                              // 地域信息
	ClusterId   string   `protobuf:"bytes,2,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`       // 集群id
	Workload    string   `protobuf:"bytes,3,opt,name=workload,proto3" json:"workload,omitempty"`                          // 工作负载
	ImageTag    string   `protobuf:"bytes,4,opt,name=image_tag,json=imageTag,proto3" json:"image_tag,omitempty"`          // 镜像tag
	Token       *TPToken `protobuf:"bytes,5,opt,name=token,proto3" json:"token,omitempty"`                                // 访问凭证: ianvs 获取其他
	Action      string   `protobuf:"bytes,6,opt,name=action,proto3" json:"action,omitempty"`                              // 任务类型：precheck/upgrade/postcheck
	Extender    string   `protobuf:"bytes,7,opt,name=extender,proto3" json:"extender,omitempty"`                          // 额外数据
	ClientToken string   `protobuf:"bytes,8,opt,name=client_token,json=clientToken,proto3" json:"client_token,omitempty"` // 幂等token: 同一个任务请求保持这里的字符串一致
	ReportId    int64    `protobuf:"varint,9,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`         // 用于主动上报task结果时的参数id
}

func (x *TPCreateTaskRequest) Reset() {
	*x = TPCreateTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_tp_rpc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TPCreateTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TPCreateTaskRequest) ProtoMessage() {}

func (x *TPCreateTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_tp_rpc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TPCreateTaskRequest.ProtoReflect.Descriptor instead.
func (*TPCreateTaskRequest) Descriptor() ([]byte, []int) {
	return file_pb_tp_rpc_proto_rawDescGZIP(), []int{1}
}

func (x *TPCreateTaskRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *TPCreateTaskRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *TPCreateTaskRequest) GetWorkload() string {
	if x != nil {
		return x.Workload
	}
	return ""
}

func (x *TPCreateTaskRequest) GetImageTag() string {
	if x != nil {
		return x.ImageTag
	}
	return ""
}

func (x *TPCreateTaskRequest) GetToken() *TPToken {
	if x != nil {
		return x.Token
	}
	return nil
}

func (x *TPCreateTaskRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *TPCreateTaskRequest) GetExtender() string {
	if x != nil {
		return x.Extender
	}
	return ""
}

func (x *TPCreateTaskRequest) GetClientToken() string {
	if x != nil {
		return x.ClientToken
	}
	return ""
}

func (x *TPCreateTaskRequest) GetReportId() int64 {
	if x != nil {
		return x.ReportId
	}
	return 0
}

// 创建任务返回体
type TPCreateTaskReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"` // 任务id
	Code   int32  `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`                  // 返回码
	Reason string `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`               // 如果返回码不为0表示失败，这里填充失败原因
}

func (x *TPCreateTaskReply) Reset() {
	*x = TPCreateTaskReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_tp_rpc_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TPCreateTaskReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TPCreateTaskReply) ProtoMessage() {}

func (x *TPCreateTaskReply) ProtoReflect() protoreflect.Message {
	mi := &file_pb_tp_rpc_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TPCreateTaskReply.ProtoReflect.Descriptor instead.
func (*TPCreateTaskReply) Descriptor() ([]byte, []int) {
	return file_pb_tp_rpc_proto_rawDescGZIP(), []int{2}
}

func (x *TPCreateTaskReply) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TPCreateTaskReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *TPCreateTaskReply) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// 获取任务执行情况请求
type TPDescribeTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region string `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"` // 地域信息
	TaskId string `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
}

func (x *TPDescribeTaskRequest) Reset() {
	*x = TPDescribeTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_tp_rpc_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TPDescribeTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TPDescribeTaskRequest) ProtoMessage() {}

func (x *TPDescribeTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_tp_rpc_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TPDescribeTaskRequest.ProtoReflect.Descriptor instead.
func (*TPDescribeTaskRequest) Descriptor() ([]byte, []int) {
	return file_pb_tp_rpc_proto_rawDescGZIP(), []int{3}
}

func (x *TPDescribeTaskRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *TPDescribeTaskRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

// 风险信息
type TPRisk struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppName  string `protobuf:"bytes,1,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"` // 组件名称
	Name     string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                      // 隐患名称
	Resrouce string `protobuf:"bytes,3,opt,name=resrouce,proto3" json:"resrouce,omitempty"`              // 【新增】资源信息  namespace + name + action
	Code     string `protobuf:"bytes,4,opt,name=code,proto3" json:"code,omitempty"`                      // 隐患错误码：PASS、FAILED、ERROR，对应检查通过、失败、错误
	Detail   string `protobuf:"bytes,5,opt,name=detail,proto3" json:"detail,omitempty"`                  // 隐患详情
	Level    string `protobuf:"bytes,6,opt,name=level,proto3" json:"level,omitempty"`                    // 隐患级别
	Solution string `protobuf:"bytes,7,opt,name=solution,proto3" json:"solution,omitempty"`              // 解决方案
}

func (x *TPRisk) Reset() {
	*x = TPRisk{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_tp_rpc_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TPRisk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TPRisk) ProtoMessage() {}

func (x *TPRisk) ProtoReflect() protoreflect.Message {
	mi := &file_pb_tp_rpc_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TPRisk.ProtoReflect.Descriptor instead.
func (*TPRisk) Descriptor() ([]byte, []int) {
	return file_pb_tp_rpc_proto_rawDescGZIP(), []int{4}
}

func (x *TPRisk) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *TPRisk) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TPRisk) GetResrouce() string {
	if x != nil {
		return x.Resrouce
	}
	return ""
}

func (x *TPRisk) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *TPRisk) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

func (x *TPRisk) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *TPRisk) GetSolution() string {
	if x != nil {
		return x.Solution
	}
	return ""
}

// 获取任务执行情况返回体
type TPDescribeTaskReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string    `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"` // 任务id
	Status string    `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	Code   int32     `protobuf:"varint,3,opt,name=code,proto3" json:"code,omitempty"`    // 返回码
	Reason string    `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"` // 如果返回码不为0表示失败，这里填充失败原因
	Risks  []*TPRisk `protobuf:"bytes,5,rep,name=risks,proto3" json:"risks,omitempty"`   // 风险信息：对于预检/后检需要暴露
}

func (x *TPDescribeTaskReply) Reset() {
	*x = TPDescribeTaskReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_tp_rpc_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TPDescribeTaskReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TPDescribeTaskReply) ProtoMessage() {}

func (x *TPDescribeTaskReply) ProtoReflect() protoreflect.Message {
	mi := &file_pb_tp_rpc_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TPDescribeTaskReply.ProtoReflect.Descriptor instead.
func (*TPDescribeTaskReply) Descriptor() ([]byte, []int) {
	return file_pb_tp_rpc_proto_rawDescGZIP(), []int{5}
}

func (x *TPDescribeTaskReply) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TPDescribeTaskReply) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *TPDescribeTaskReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *TPDescribeTaskReply) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *TPDescribeTaskReply) GetRisks() []*TPRisk {
	if x != nil {
		return x.Risks
	}
	return nil
}

type TPCancelTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region string `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	TaskId string `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
}

func (x *TPCancelTaskRequest) Reset() {
	*x = TPCancelTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_tp_rpc_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TPCancelTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TPCancelTaskRequest) ProtoMessage() {}

func (x *TPCancelTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_tp_rpc_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TPCancelTaskRequest.ProtoReflect.Descriptor instead.
func (*TPCancelTaskRequest) Descriptor() ([]byte, []int) {
	return file_pb_tp_rpc_proto_rawDescGZIP(), []int{6}
}

func (x *TPCancelTaskRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *TPCancelTaskRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type TPCancelTaskReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"` // 任务id
	Code   int32  `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`                  // 返回码
	Reason string `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`               // 如果返回码不为0表示失败，这里填充失败原因
}

func (x *TPCancelTaskReply) Reset() {
	*x = TPCancelTaskReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_tp_rpc_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TPCancelTaskReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TPCancelTaskReply) ProtoMessage() {}

func (x *TPCancelTaskReply) ProtoReflect() protoreflect.Message {
	mi := &file_pb_tp_rpc_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TPCancelTaskReply.ProtoReflect.Descriptor instead.
func (*TPCancelTaskReply) Descriptor() ([]byte, []int) {
	return file_pb_tp_rpc_proto_rawDescGZIP(), []int{7}
}

func (x *TPCancelTaskReply) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TPCancelTaskReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *TPCancelTaskReply) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// 上报任务执行结果请求
type TPReportTaskResultRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReportId int64     `protobuf:"varint,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"` // 上报id
	TaskId   string    `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`        // tp server的任务id
	Status   string    `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`                      // 任务状态
	Code     int32     `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`                         // 返回码
	Reason   string    `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"`                      // 失败原因
	Risks    []*TPRisk `protobuf:"bytes,6,rep,name=risks,proto3" json:"risks,omitempty"`                        // 风险信息
}

func (x *TPReportTaskResultRequest) Reset() {
	*x = TPReportTaskResultRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_tp_rpc_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TPReportTaskResultRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TPReportTaskResultRequest) ProtoMessage() {}

func (x *TPReportTaskResultRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_tp_rpc_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TPReportTaskResultRequest.ProtoReflect.Descriptor instead.
func (*TPReportTaskResultRequest) Descriptor() ([]byte, []int) {
	return file_pb_tp_rpc_proto_rawDescGZIP(), []int{8}
}

func (x *TPReportTaskResultRequest) GetReportId() int64 {
	if x != nil {
		return x.ReportId
	}
	return 0
}

func (x *TPReportTaskResultRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TPReportTaskResultRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *TPReportTaskResultRequest) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *TPReportTaskResultRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *TPReportTaskResultRequest) GetRisks() []*TPRisk {
	if x != nil {
		return x.Risks
	}
	return nil
}

// 上报任务执行结果返回体
type TPReportTaskResultReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code   int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`    // 返回码
	Reason string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"` // 失败原因
}

func (x *TPReportTaskResultReply) Reset() {
	*x = TPReportTaskResultReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_tp_rpc_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TPReportTaskResultReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TPReportTaskResultReply) ProtoMessage() {}

func (x *TPReportTaskResultReply) ProtoReflect() protoreflect.Message {
	mi := &file_pb_tp_rpc_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TPReportTaskResultReply.ProtoReflect.Descriptor instead.
func (*TPReportTaskResultReply) Descriptor() ([]byte, []int) {
	return file_pb_tp_rpc_proto_rawDescGZIP(), []int{9}
}

func (x *TPReportTaskResultReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *TPReportTaskResultReply) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

var File_pb_tp_rpc_proto protoreflect.FileDescriptor

var file_pb_tp_rpc_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x70, 0x62, 0x2f, 0x74, 0x70, 0x5f, 0x72, 0x70, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x02, 0x70, 0x62, 0x22, 0x63, 0x0a, 0x07, 0x54, 0x50, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x9c, 0x02, 0x0a, 0x13, 0x54,
	0x50, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x77, 0x6f, 0x72,
	0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x77, 0x6f, 0x72,
	0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74,
	0x61, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54,
	0x61, 0x67, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x62, 0x2e, 0x54, 0x50, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a,
	0x08, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1b, 0x0a, 0x09,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x22, 0x58, 0x0a, 0x11, 0x54, 0x50, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x17,
	0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x22, 0x48, 0x0a, 0x15, 0x54, 0x50, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0xb1, 0x01,
	0x0a, 0x06, 0x54, 0x50, 0x52, 0x69, 0x73, 0x6b, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x72, 0x6f,
	0x75, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x72, 0x6f,
	0x75, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x94, 0x01, 0x0a, 0x13, 0x54, 0x50, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x05, 0x72, 0x69, 0x73, 0x6b, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x70, 0x62, 0x2e, 0x54, 0x50, 0x52, 0x69, 0x73,
	0x6b, 0x52, 0x05, 0x72, 0x69, 0x73, 0x6b, 0x73, 0x22, 0x46, 0x0a, 0x13, 0x54, 0x50, 0x43, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64,
	0x22, 0x58, 0x0a, 0x11, 0x54, 0x50, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xb7, 0x01, 0x0a, 0x19, 0x54,
	0x50, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x05, 0x72, 0x69, 0x73, 0x6b, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0a, 0x2e, 0x70, 0x62, 0x2e, 0x54, 0x50, 0x52, 0x69, 0x73, 0x6b, 0x52, 0x05, 0x72,
	0x69, 0x73, 0x6b, 0x73, 0x22, 0x45, 0x0a, 0x17, 0x54, 0x50, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x32, 0xa8, 0x02, 0x0a, 0x0c,
	0x54, 0x50, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x12, 0x3e, 0x0a, 0x0a,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x17, 0x2e, 0x70, 0x62, 0x2e,
	0x54, 0x50, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x54, 0x50, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x44, 0x0a, 0x0c,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x19, 0x2e, 0x70,
	0x62, 0x2e, 0x54, 0x50, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x70, 0x62, 0x2e, 0x54, 0x50, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x00, 0x12, 0x3e, 0x0a, 0x0a, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x54, 0x61, 0x73, 0x6b,
	0x12, 0x17, 0x2e, 0x70, 0x62, 0x2e, 0x54, 0x50, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x54,
	0x50, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x00, 0x12, 0x52, 0x0a, 0x12, 0x54, 0x50, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1d, 0x2e, 0x70, 0x62, 0x2e, 0x54, 0x50,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x62, 0x2e, 0x54, 0x50, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x42, 0x40, 0x0a, 0x13, 0x69, 0x6f, 0x2e, 0x67, 0x72, 0x70,
	0x63, 0x2e, 0x73, 0x74, 0x61, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x70, 0x62, 0x42, 0x07, 0x50,
	0x62, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x1e, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f,
	0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6b, 0x6d, 0x65, 0x74, 0x69, 0x73, 0x2f, 0x73, 0x74, 0x61,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_tp_rpc_proto_rawDescOnce sync.Once
	file_pb_tp_rpc_proto_rawDescData = file_pb_tp_rpc_proto_rawDesc
)

func file_pb_tp_rpc_proto_rawDescGZIP() []byte {
	file_pb_tp_rpc_proto_rawDescOnce.Do(func() {
		file_pb_tp_rpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_tp_rpc_proto_rawDescData)
	})
	return file_pb_tp_rpc_proto_rawDescData
}

var file_pb_tp_rpc_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_pb_tp_rpc_proto_goTypes = []interface{}{
	(*TPToken)(nil),                   // 0: pb.TPToken
	(*TPCreateTaskRequest)(nil),       // 1: pb.TPCreateTaskRequest
	(*TPCreateTaskReply)(nil),         // 2: pb.TPCreateTaskReply
	(*TPDescribeTaskRequest)(nil),     // 3: pb.TPDescribeTaskRequest
	(*TPRisk)(nil),                    // 4: pb.TPRisk
	(*TPDescribeTaskReply)(nil),       // 5: pb.TPDescribeTaskReply
	(*TPCancelTaskRequest)(nil),       // 6: pb.TPCancelTaskRequest
	(*TPCancelTaskReply)(nil),         // 7: pb.TPCancelTaskReply
	(*TPReportTaskResultRequest)(nil), // 8: pb.TPReportTaskResultRequest
	(*TPReportTaskResultReply)(nil),   // 9: pb.TPReportTaskResultReply
}
var file_pb_tp_rpc_proto_depIdxs = []int32{
	0, // 0: pb.TPCreateTaskRequest.token:type_name -> pb.TPToken
	4, // 1: pb.TPDescribeTaskReply.risks:type_name -> pb.TPRisk
	4, // 2: pb.TPReportTaskResultRequest.risks:type_name -> pb.TPRisk
	1, // 3: pb.TPTaskEngine.CreateTask:input_type -> pb.TPCreateTaskRequest
	3, // 4: pb.TPTaskEngine.DescribeTask:input_type -> pb.TPDescribeTaskRequest
	6, // 5: pb.TPTaskEngine.CancelTask:input_type -> pb.TPCancelTaskRequest
	8, // 6: pb.TPTaskEngine.TPReportTaskResult:input_type -> pb.TPReportTaskResultRequest
	2, // 7: pb.TPTaskEngine.CreateTask:output_type -> pb.TPCreateTaskReply
	5, // 8: pb.TPTaskEngine.DescribeTask:output_type -> pb.TPDescribeTaskReply
	7, // 9: pb.TPTaskEngine.CancelTask:output_type -> pb.TPCancelTaskReply
	9, // 10: pb.TPTaskEngine.TPReportTaskResult:output_type -> pb.TPReportTaskResultReply
	7, // [7:11] is the sub-list for method output_type
	3, // [3:7] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_pb_tp_rpc_proto_init() }
func file_pb_tp_rpc_proto_init() {
	if File_pb_tp_rpc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_tp_rpc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TPToken); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_tp_rpc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TPCreateTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_tp_rpc_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TPCreateTaskReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_tp_rpc_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TPDescribeTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_tp_rpc_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TPRisk); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_tp_rpc_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TPDescribeTaskReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_tp_rpc_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TPCancelTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_tp_rpc_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TPCancelTaskReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_tp_rpc_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TPReportTaskResultRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_tp_rpc_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TPReportTaskResultReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_tp_rpc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_tp_rpc_proto_goTypes,
		DependencyIndexes: file_pb_tp_rpc_proto_depIdxs,
		MessageInfos:      file_pb_tp_rpc_proto_msgTypes,
	}.Build()
	File_pb_tp_rpc_proto = out.File
	file_pb_tp_rpc_proto_rawDesc = nil
	file_pb_tp_rpc_proto_goTypes = nil
	file_pb_tp_rpc_proto_depIdxs = nil
}
