// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v4.25.1
// source: pb/tp_rpc.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// TPTaskEngineClient is the client API for TPTaskEngine service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TPTaskEngineClient interface {
	// CreateTask Creates task, such as precheck, aftercheck, etc. for specified component
	CreateTask(ctx context.Context, in *TPCreateTaskRequest, opts ...grpc.CallOption) (*TPCreateTaskReply, error)
	// DescribeTask get the result of task
	DescribeTask(ctx context.Context, in *TPDescribeTaskRequest, opts ...grpc.CallOption) (*TPDescribeTaskReply, error)
	// CancelTask cancel task
	CancelTask(ctx context.Context, in *TPCancelTaskRequest, opts ...grpc.CallOption) (*TPCancelTaskReply, error)
	// TPReportTaskResult report the result of task
	TPReportTaskResult(ctx context.Context, in *TPReportTaskResultRequest, opts ...grpc.CallOption) (*TPReportTaskResultReply, error)
}

type tPTaskEngineClient struct {
	cc grpc.ClientConnInterface
}

func NewTPTaskEngineClient(cc grpc.ClientConnInterface) TPTaskEngineClient {
	return &tPTaskEngineClient{cc}
}

func (c *tPTaskEngineClient) CreateTask(ctx context.Context, in *TPCreateTaskRequest, opts ...grpc.CallOption) (*TPCreateTaskReply, error) {
	out := new(TPCreateTaskReply)
	err := c.cc.Invoke(ctx, "/pb.TPTaskEngine/CreateTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tPTaskEngineClient) DescribeTask(ctx context.Context, in *TPDescribeTaskRequest, opts ...grpc.CallOption) (*TPDescribeTaskReply, error) {
	out := new(TPDescribeTaskReply)
	err := c.cc.Invoke(ctx, "/pb.TPTaskEngine/DescribeTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tPTaskEngineClient) CancelTask(ctx context.Context, in *TPCancelTaskRequest, opts ...grpc.CallOption) (*TPCancelTaskReply, error) {
	out := new(TPCancelTaskReply)
	err := c.cc.Invoke(ctx, "/pb.TPTaskEngine/CancelTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tPTaskEngineClient) TPReportTaskResult(ctx context.Context, in *TPReportTaskResultRequest, opts ...grpc.CallOption) (*TPReportTaskResultReply, error) {
	out := new(TPReportTaskResultReply)
	err := c.cc.Invoke(ctx, "/pb.TPTaskEngine/TPReportTaskResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TPTaskEngineServer is the server API for TPTaskEngine service.
// All implementations must embed UnimplementedTPTaskEngineServer
// for forward compatibility
type TPTaskEngineServer interface {
	// CreateTask Creates task, such as precheck, aftercheck, etc. for specified component
	CreateTask(context.Context, *TPCreateTaskRequest) (*TPCreateTaskReply, error)
	// DescribeTask get the result of task
	DescribeTask(context.Context, *TPDescribeTaskRequest) (*TPDescribeTaskReply, error)
	// CancelTask cancel task
	CancelTask(context.Context, *TPCancelTaskRequest) (*TPCancelTaskReply, error)
	// TPReportTaskResult report the result of task
	TPReportTaskResult(context.Context, *TPReportTaskResultRequest) (*TPReportTaskResultReply, error)
	mustEmbedUnimplementedTPTaskEngineServer()
}

// UnimplementedTPTaskEngineServer must be embedded to have forward compatible implementations.
type UnimplementedTPTaskEngineServer struct {
}

func (UnimplementedTPTaskEngineServer) CreateTask(context.Context, *TPCreateTaskRequest) (*TPCreateTaskReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTask not implemented")
}
func (UnimplementedTPTaskEngineServer) DescribeTask(context.Context, *TPDescribeTaskRequest) (*TPDescribeTaskReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeTask not implemented")
}
func (UnimplementedTPTaskEngineServer) CancelTask(context.Context, *TPCancelTaskRequest) (*TPCancelTaskReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelTask not implemented")
}
func (UnimplementedTPTaskEngineServer) TPReportTaskResult(context.Context, *TPReportTaskResultRequest) (*TPReportTaskResultReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TPReportTaskResult not implemented")
}
func (UnimplementedTPTaskEngineServer) mustEmbedUnimplementedTPTaskEngineServer() {}

// UnsafeTPTaskEngineServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TPTaskEngineServer will
// result in compilation errors.
type UnsafeTPTaskEngineServer interface {
	mustEmbedUnimplementedTPTaskEngineServer()
}

func RegisterTPTaskEngineServer(s grpc.ServiceRegistrar, srv TPTaskEngineServer) {
	s.RegisterService(&TPTaskEngine_ServiceDesc, srv)
}

func _TPTaskEngine_CreateTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TPCreateTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TPTaskEngineServer).CreateTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.TPTaskEngine/CreateTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TPTaskEngineServer).CreateTask(ctx, req.(*TPCreateTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TPTaskEngine_DescribeTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TPDescribeTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TPTaskEngineServer).DescribeTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.TPTaskEngine/DescribeTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TPTaskEngineServer).DescribeTask(ctx, req.(*TPDescribeTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TPTaskEngine_CancelTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TPCancelTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TPTaskEngineServer).CancelTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.TPTaskEngine/CancelTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TPTaskEngineServer).CancelTask(ctx, req.(*TPCancelTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TPTaskEngine_TPReportTaskResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TPReportTaskResultRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TPTaskEngineServer).TPReportTaskResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.TPTaskEngine/TPReportTaskResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TPTaskEngineServer).TPReportTaskResult(ctx, req.(*TPReportTaskResultRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TPTaskEngine_ServiceDesc is the grpc.ServiceDesc for TPTaskEngine service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TPTaskEngine_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.TPTaskEngine",
	HandlerType: (*TPTaskEngineServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateTask",
			Handler:    _TPTaskEngine_CreateTask_Handler,
		},
		{
			MethodName: "DescribeTask",
			Handler:    _TPTaskEngine_DescribeTask_Handler,
		},
		{
			MethodName: "CancelTask",
			Handler:    _TPTaskEngine_CancelTask_Handler,
		},
		{
			MethodName: "TPReportTaskResult",
			Handler:    _TPTaskEngine_TPReportTaskResult_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/tp_rpc.proto",
}
