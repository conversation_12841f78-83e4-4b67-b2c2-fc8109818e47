syntax = "proto3";

option go_package = "./;kateway";

package tke;

service Kateway {
    rpc CheckUpgrade (CheckUpgradeRequest) returns (CheckUpgradeResponse) {}

    rpc GetUser (GetUserRequest) returns (GetUserResponse) {}
    rpc GetCluster (GetClusterRequest) returns (GetClusterResponse) {}
    rpc GetCLB (GetCLBRequest) returns (GetCLBResponse) {}
    rpc GetListeners (GetCLBRequest) returns (GetCLBResponse) {}
    rpc GetBackends (GetCLBRequest) returns (GetCLBResponse) {}
    rpc GetHealth (GetCLBRequest) returns (GetCLBResponse) {}
    rpc CheckImage (CheckImageRequest) returns (CheckImageResponse) {}

    rpc GetPod (GetPodRequest) returns (GetPodResponse) {}
    rpc GetDeploy (GetDeployRequest) returns (GetDeployResponse) {}
    rpc GetLeader (GetLeaderRequest) returns (GetLeaderResponse) {}
    rpc GetConfig (GetConfigRequest) returns (GetConfigResponse) {}

    rpc Logs (LogsRequest) returns (LogsResponse) {}
    rpc YunAPI (YunAPIRequest) returns (YunAPIResponse) {}

    rpc Admin (AdminRequest) returns (AdminResponse) {}

    rpc SetImage (SetImageRequest) returns (stream SetImageResponse) {}
    rpc Mock (MockRequest) returns (stream MockResponse) {}
    rpc Update (UpdateRequest) returns (stream UpdateResponse) {}

    rpc ListTask (ListTaskRequest) returns (ListTaskResponse) {}
    rpc GetTask (GetTaskRequest) returns (GetTaskResponse) {}
    rpc CreateTask (CreateTaskRequest) returns (stream CreateTaskResponse) {}
    
    // 统一启动参数更新接口（operation=set/unset）
    rpc UpdateArgs (ArgsRequest) returns (stream ArgsResponse) {}
}

message CheckUpgradeRequest {
    string version = 1;
    string os = 2;
}

message CheckUpgradeResponse {
    string version = 1;
    string url = 2;
}

message GetUserRequest {
    string query = 1;
}

message GetUserResponse {
    User user = 1;
}

message User {
    string uin = 1;
    string appid = 2;
    string name = 3;
}

message GetClusterRequest {
    string query = 1;
}

message GetClusterResponse {
    Cluster cluster = 1;
}

message Cluster {
    string id = 1;
    string type = 2;
    string version = 3;
    string name = 4;
    string region = 5;
    string state = 6;
    string vpc = 7;
    string created_at = 8;
    User user = 9;
    string metaCluster = 10;
}

message ClusterProperty {
    string NodeNameType = 1;
    string NetworkType = 2;
}

message GetCLBRequest {
    string query = 1;
}

message GetCLBResponse {
    string data = 1;
}

message GetPodRequest {
    Target target = 1;
}

message Target {
    string name = 1;
    string cluster = 2;
    string user = 3;
    string token = 4;
}

message GetPodResponse {
    string data = 1;
}

message GetDeployRequest {
    Target target = 1;
}

message GetDeployResponse {
    string data = 1;
}

message GetLeaderRequest {
    Target target = 1;
}

message GetLeaderResponse {
    string data = 1;
}

message GetConfigRequest {
    Target target = 1;
}

message GetConfigResponse {
    string data = 1;
}

message LogsRequest {
    Target target = 1;
    LogOptions log_options = 3;
}

message LogsResponse {
    string data = 1;
}

message LogOptions {
    string since_time = 1;
}

message YunAPIRequest {
    Target target = 1;
    string query = 2;
    LogOptions log_options = 3;
}

message YunAPIPodRequest {
}

message YunAPIResponse {
    string data = 1;
}

message AdminRequest {
    Target target = 1;
}

message AdminResponse {
    string data = 1;
}

message CheckImageRequest {
    string name = 1;
    string version = 2;
    string region = 3;
}

message CheckImageResponse {
}

message SetImageRequest {
    Target target = 1;
    string Image = 2;
}

message SetImageResponse {
    string data = 1;
}

message MockRequest {
    Target target = 1;
    string Image = 2;
}

message MockResponse {
    string data = 1;
}

message UpdateRequest {
    Target target = 1;
    string Image = 2;
    bool Force = 3;
}

message UpdateResponse {
    string data = 1;
}

message GetTaskRequest {
    string id = 1;
}

message GetTaskResponse {
    string data = 1;
}

message ListTaskRequest {
    uint64 limit = 1;
    uint64 offset = 2;
    repeated string filters = 3; 
}

message ListTaskResponse {
    string data = 1;
    uint64 total = 2;
}

message CreateTaskRequest {
    string type = 1;
    string input = 2;
    string creator = 3;
    bool   wait = 4;
}

message CreateTaskResponse {
    string data = 1;
}

// 参数管理请求（支持设置/取消参数）
message ArgsRequest {
    Target target = 1;           // 集群目标信息
    string operation = 2;        // 操作类型(set/unset)
    repeated string args = 3;    // 参数列表
    bool force = 4;              // 是否强制更新
}

// 参数管理响应
message ArgsResponse {
    string data = 1;
}
