// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: kateway.proto

package kateway

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Kateway_CheckUpgrade_FullMethodName = "/tke.Kateway/CheckUpgrade"
	Kateway_GetUser_FullMethodName      = "/tke.Kateway/GetUser"
	Kateway_GetCluster_FullMethodName   = "/tke.Kateway/GetCluster"
	Kateway_GetCLB_FullMethodName       = "/tke.Kateway/GetCLB"
	Kateway_GetListeners_FullMethodName = "/tke.Kateway/GetListeners"
	Kateway_GetBackends_FullMethodName  = "/tke.Kateway/GetBackends"
	Kateway_GetHealth_FullMethodName    = "/tke.Kateway/GetHealth"
	Kateway_CheckImage_FullMethodName   = "/tke.Kateway/CheckImage"
	Kateway_GetPod_FullMethodName       = "/tke.Kateway/GetPod"
	Kateway_GetDeploy_FullMethodName    = "/tke.Kateway/GetDeploy"
	Kateway_GetLeader_FullMethodName    = "/tke.Kateway/GetLeader"
	Kateway_GetConfig_FullMethodName    = "/tke.Kateway/GetConfig"
	Kateway_Logs_FullMethodName         = "/tke.Kateway/Logs"
	Kateway_YunAPI_FullMethodName       = "/tke.Kateway/YunAPI"
	Kateway_Admin_FullMethodName        = "/tke.Kateway/Admin"
	Kateway_SetImage_FullMethodName     = "/tke.Kateway/SetImage"
	Kateway_Mock_FullMethodName         = "/tke.Kateway/Mock"
	Kateway_Update_FullMethodName       = "/tke.Kateway/Update"
	Kateway_ListTask_FullMethodName     = "/tke.Kateway/ListTask"
	Kateway_GetTask_FullMethodName      = "/tke.Kateway/GetTask"
	Kateway_CreateTask_FullMethodName   = "/tke.Kateway/CreateTask"
	Kateway_UpdateArgs_FullMethodName   = "/tke.Kateway/UpdateArgs"
)

// KatewayClient is the client API for Kateway service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type KatewayClient interface {
	CheckUpgrade(ctx context.Context, in *CheckUpgradeRequest, opts ...grpc.CallOption) (*CheckUpgradeResponse, error)
	GetUser(ctx context.Context, in *GetUserRequest, opts ...grpc.CallOption) (*GetUserResponse, error)
	GetCluster(ctx context.Context, in *GetClusterRequest, opts ...grpc.CallOption) (*GetClusterResponse, error)
	GetCLB(ctx context.Context, in *GetCLBRequest, opts ...grpc.CallOption) (*GetCLBResponse, error)
	GetListeners(ctx context.Context, in *GetCLBRequest, opts ...grpc.CallOption) (*GetCLBResponse, error)
	GetBackends(ctx context.Context, in *GetCLBRequest, opts ...grpc.CallOption) (*GetCLBResponse, error)
	GetHealth(ctx context.Context, in *GetCLBRequest, opts ...grpc.CallOption) (*GetCLBResponse, error)
	CheckImage(ctx context.Context, in *CheckImageRequest, opts ...grpc.CallOption) (*CheckImageResponse, error)
	GetPod(ctx context.Context, in *GetPodRequest, opts ...grpc.CallOption) (*GetPodResponse, error)
	GetDeploy(ctx context.Context, in *GetDeployRequest, opts ...grpc.CallOption) (*GetDeployResponse, error)
	GetLeader(ctx context.Context, in *GetLeaderRequest, opts ...grpc.CallOption) (*GetLeaderResponse, error)
	GetConfig(ctx context.Context, in *GetConfigRequest, opts ...grpc.CallOption) (*GetConfigResponse, error)
	Logs(ctx context.Context, in *LogsRequest, opts ...grpc.CallOption) (*LogsResponse, error)
	YunAPI(ctx context.Context, in *YunAPIRequest, opts ...grpc.CallOption) (*YunAPIResponse, error)
	Admin(ctx context.Context, in *AdminRequest, opts ...grpc.CallOption) (*AdminResponse, error)
	SetImage(ctx context.Context, in *SetImageRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[SetImageResponse], error)
	Mock(ctx context.Context, in *MockRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[MockResponse], error)
	Update(ctx context.Context, in *UpdateRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[UpdateResponse], error)
	ListTask(ctx context.Context, in *ListTaskRequest, opts ...grpc.CallOption) (*ListTaskResponse, error)
	GetTask(ctx context.Context, in *GetTaskRequest, opts ...grpc.CallOption) (*GetTaskResponse, error)
	CreateTask(ctx context.Context, in *CreateTaskRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[CreateTaskResponse], error)
	// 统一启动参数更新接口（operation=set/unset）
	UpdateArgs(ctx context.Context, in *ArgsRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[ArgsResponse], error)
}

type katewayClient struct {
	cc grpc.ClientConnInterface
}

func NewKatewayClient(cc grpc.ClientConnInterface) KatewayClient {
	return &katewayClient{cc}
}

func (c *katewayClient) CheckUpgrade(ctx context.Context, in *CheckUpgradeRequest, opts ...grpc.CallOption) (*CheckUpgradeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckUpgradeResponse)
	err := c.cc.Invoke(ctx, Kateway_CheckUpgrade_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *katewayClient) GetUser(ctx context.Context, in *GetUserRequest, opts ...grpc.CallOption) (*GetUserResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserResponse)
	err := c.cc.Invoke(ctx, Kateway_GetUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *katewayClient) GetCluster(ctx context.Context, in *GetClusterRequest, opts ...grpc.CallOption) (*GetClusterResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetClusterResponse)
	err := c.cc.Invoke(ctx, Kateway_GetCluster_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *katewayClient) GetCLB(ctx context.Context, in *GetCLBRequest, opts ...grpc.CallOption) (*GetCLBResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCLBResponse)
	err := c.cc.Invoke(ctx, Kateway_GetCLB_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *katewayClient) GetListeners(ctx context.Context, in *GetCLBRequest, opts ...grpc.CallOption) (*GetCLBResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCLBResponse)
	err := c.cc.Invoke(ctx, Kateway_GetListeners_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *katewayClient) GetBackends(ctx context.Context, in *GetCLBRequest, opts ...grpc.CallOption) (*GetCLBResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCLBResponse)
	err := c.cc.Invoke(ctx, Kateway_GetBackends_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *katewayClient) GetHealth(ctx context.Context, in *GetCLBRequest, opts ...grpc.CallOption) (*GetCLBResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCLBResponse)
	err := c.cc.Invoke(ctx, Kateway_GetHealth_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *katewayClient) CheckImage(ctx context.Context, in *CheckImageRequest, opts ...grpc.CallOption) (*CheckImageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckImageResponse)
	err := c.cc.Invoke(ctx, Kateway_CheckImage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *katewayClient) GetPod(ctx context.Context, in *GetPodRequest, opts ...grpc.CallOption) (*GetPodResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPodResponse)
	err := c.cc.Invoke(ctx, Kateway_GetPod_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *katewayClient) GetDeploy(ctx context.Context, in *GetDeployRequest, opts ...grpc.CallOption) (*GetDeployResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDeployResponse)
	err := c.cc.Invoke(ctx, Kateway_GetDeploy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *katewayClient) GetLeader(ctx context.Context, in *GetLeaderRequest, opts ...grpc.CallOption) (*GetLeaderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLeaderResponse)
	err := c.cc.Invoke(ctx, Kateway_GetLeader_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *katewayClient) GetConfig(ctx context.Context, in *GetConfigRequest, opts ...grpc.CallOption) (*GetConfigResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetConfigResponse)
	err := c.cc.Invoke(ctx, Kateway_GetConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *katewayClient) Logs(ctx context.Context, in *LogsRequest, opts ...grpc.CallOption) (*LogsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LogsResponse)
	err := c.cc.Invoke(ctx, Kateway_Logs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *katewayClient) YunAPI(ctx context.Context, in *YunAPIRequest, opts ...grpc.CallOption) (*YunAPIResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(YunAPIResponse)
	err := c.cc.Invoke(ctx, Kateway_YunAPI_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *katewayClient) Admin(ctx context.Context, in *AdminRequest, opts ...grpc.CallOption) (*AdminResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AdminResponse)
	err := c.cc.Invoke(ctx, Kateway_Admin_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *katewayClient) SetImage(ctx context.Context, in *SetImageRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[SetImageResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &Kateway_ServiceDesc.Streams[0], Kateway_SetImage_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[SetImageRequest, SetImageResponse]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Kateway_SetImageClient = grpc.ServerStreamingClient[SetImageResponse]

func (c *katewayClient) Mock(ctx context.Context, in *MockRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[MockResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &Kateway_ServiceDesc.Streams[1], Kateway_Mock_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[MockRequest, MockResponse]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Kateway_MockClient = grpc.ServerStreamingClient[MockResponse]

func (c *katewayClient) Update(ctx context.Context, in *UpdateRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[UpdateResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &Kateway_ServiceDesc.Streams[2], Kateway_Update_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[UpdateRequest, UpdateResponse]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Kateway_UpdateClient = grpc.ServerStreamingClient[UpdateResponse]

func (c *katewayClient) ListTask(ctx context.Context, in *ListTaskRequest, opts ...grpc.CallOption) (*ListTaskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListTaskResponse)
	err := c.cc.Invoke(ctx, Kateway_ListTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *katewayClient) GetTask(ctx context.Context, in *GetTaskRequest, opts ...grpc.CallOption) (*GetTaskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTaskResponse)
	err := c.cc.Invoke(ctx, Kateway_GetTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *katewayClient) CreateTask(ctx context.Context, in *CreateTaskRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[CreateTaskResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &Kateway_ServiceDesc.Streams[3], Kateway_CreateTask_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[CreateTaskRequest, CreateTaskResponse]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Kateway_CreateTaskClient = grpc.ServerStreamingClient[CreateTaskResponse]

func (c *katewayClient) UpdateArgs(ctx context.Context, in *ArgsRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[ArgsResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &Kateway_ServiceDesc.Streams[4], Kateway_UpdateArgs_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[ArgsRequest, ArgsResponse]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Kateway_UpdateArgsClient = grpc.ServerStreamingClient[ArgsResponse]

// KatewayServer is the server API for Kateway service.
// All implementations must embed UnimplementedKatewayServer
// for forward compatibility.
type KatewayServer interface {
	CheckUpgrade(context.Context, *CheckUpgradeRequest) (*CheckUpgradeResponse, error)
	GetUser(context.Context, *GetUserRequest) (*GetUserResponse, error)
	GetCluster(context.Context, *GetClusterRequest) (*GetClusterResponse, error)
	GetCLB(context.Context, *GetCLBRequest) (*GetCLBResponse, error)
	GetListeners(context.Context, *GetCLBRequest) (*GetCLBResponse, error)
	GetBackends(context.Context, *GetCLBRequest) (*GetCLBResponse, error)
	GetHealth(context.Context, *GetCLBRequest) (*GetCLBResponse, error)
	CheckImage(context.Context, *CheckImageRequest) (*CheckImageResponse, error)
	GetPod(context.Context, *GetPodRequest) (*GetPodResponse, error)
	GetDeploy(context.Context, *GetDeployRequest) (*GetDeployResponse, error)
	GetLeader(context.Context, *GetLeaderRequest) (*GetLeaderResponse, error)
	GetConfig(context.Context, *GetConfigRequest) (*GetConfigResponse, error)
	Logs(context.Context, *LogsRequest) (*LogsResponse, error)
	YunAPI(context.Context, *YunAPIRequest) (*YunAPIResponse, error)
	Admin(context.Context, *AdminRequest) (*AdminResponse, error)
	SetImage(*SetImageRequest, grpc.ServerStreamingServer[SetImageResponse]) error
	Mock(*MockRequest, grpc.ServerStreamingServer[MockResponse]) error
	Update(*UpdateRequest, grpc.ServerStreamingServer[UpdateResponse]) error
	ListTask(context.Context, *ListTaskRequest) (*ListTaskResponse, error)
	GetTask(context.Context, *GetTaskRequest) (*GetTaskResponse, error)
	CreateTask(*CreateTaskRequest, grpc.ServerStreamingServer[CreateTaskResponse]) error
	// 统一启动参数更新接口（operation=set/unset）
	UpdateArgs(*ArgsRequest, grpc.ServerStreamingServer[ArgsResponse]) error
	mustEmbedUnimplementedKatewayServer()
}

// UnimplementedKatewayServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedKatewayServer struct{}

func (UnimplementedKatewayServer) CheckUpgrade(context.Context, *CheckUpgradeRequest) (*CheckUpgradeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUpgrade not implemented")
}
func (UnimplementedKatewayServer) GetUser(context.Context, *GetUserRequest) (*GetUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUser not implemented")
}
func (UnimplementedKatewayServer) GetCluster(context.Context, *GetClusterRequest) (*GetClusterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCluster not implemented")
}
func (UnimplementedKatewayServer) GetCLB(context.Context, *GetCLBRequest) (*GetCLBResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCLB not implemented")
}
func (UnimplementedKatewayServer) GetListeners(context.Context, *GetCLBRequest) (*GetCLBResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetListeners not implemented")
}
func (UnimplementedKatewayServer) GetBackends(context.Context, *GetCLBRequest) (*GetCLBResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBackends not implemented")
}
func (UnimplementedKatewayServer) GetHealth(context.Context, *GetCLBRequest) (*GetCLBResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHealth not implemented")
}
func (UnimplementedKatewayServer) CheckImage(context.Context, *CheckImageRequest) (*CheckImageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckImage not implemented")
}
func (UnimplementedKatewayServer) GetPod(context.Context, *GetPodRequest) (*GetPodResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPod not implemented")
}
func (UnimplementedKatewayServer) GetDeploy(context.Context, *GetDeployRequest) (*GetDeployResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeploy not implemented")
}
func (UnimplementedKatewayServer) GetLeader(context.Context, *GetLeaderRequest) (*GetLeaderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLeader not implemented")
}
func (UnimplementedKatewayServer) GetConfig(context.Context, *GetConfigRequest) (*GetConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConfig not implemented")
}
func (UnimplementedKatewayServer) Logs(context.Context, *LogsRequest) (*LogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Logs not implemented")
}
func (UnimplementedKatewayServer) YunAPI(context.Context, *YunAPIRequest) (*YunAPIResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method YunAPI not implemented")
}
func (UnimplementedKatewayServer) Admin(context.Context, *AdminRequest) (*AdminResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Admin not implemented")
}
func (UnimplementedKatewayServer) SetImage(*SetImageRequest, grpc.ServerStreamingServer[SetImageResponse]) error {
	return status.Errorf(codes.Unimplemented, "method SetImage not implemented")
}
func (UnimplementedKatewayServer) Mock(*MockRequest, grpc.ServerStreamingServer[MockResponse]) error {
	return status.Errorf(codes.Unimplemented, "method Mock not implemented")
}
func (UnimplementedKatewayServer) Update(*UpdateRequest, grpc.ServerStreamingServer[UpdateResponse]) error {
	return status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (UnimplementedKatewayServer) ListTask(context.Context, *ListTaskRequest) (*ListTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTask not implemented")
}
func (UnimplementedKatewayServer) GetTask(context.Context, *GetTaskRequest) (*GetTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTask not implemented")
}
func (UnimplementedKatewayServer) CreateTask(*CreateTaskRequest, grpc.ServerStreamingServer[CreateTaskResponse]) error {
	return status.Errorf(codes.Unimplemented, "method CreateTask not implemented")
}
func (UnimplementedKatewayServer) UpdateArgs(*ArgsRequest, grpc.ServerStreamingServer[ArgsResponse]) error {
	return status.Errorf(codes.Unimplemented, "method UpdateArgs not implemented")
}
func (UnimplementedKatewayServer) mustEmbedUnimplementedKatewayServer() {}
func (UnimplementedKatewayServer) testEmbeddedByValue()                 {}

// UnsafeKatewayServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to KatewayServer will
// result in compilation errors.
type UnsafeKatewayServer interface {
	mustEmbedUnimplementedKatewayServer()
}

func RegisterKatewayServer(s grpc.ServiceRegistrar, srv KatewayServer) {
	// If the following call pancis, it indicates UnimplementedKatewayServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Kateway_ServiceDesc, srv)
}

func _Kateway_CheckUpgrade_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUpgradeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KatewayServer).CheckUpgrade(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kateway_CheckUpgrade_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KatewayServer).CheckUpgrade(ctx, req.(*CheckUpgradeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kateway_GetUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KatewayServer).GetUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kateway_GetUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KatewayServer).GetUser(ctx, req.(*GetUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kateway_GetCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KatewayServer).GetCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kateway_GetCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KatewayServer).GetCluster(ctx, req.(*GetClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kateway_GetCLB_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCLBRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KatewayServer).GetCLB(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kateway_GetCLB_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KatewayServer).GetCLB(ctx, req.(*GetCLBRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kateway_GetListeners_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCLBRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KatewayServer).GetListeners(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kateway_GetListeners_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KatewayServer).GetListeners(ctx, req.(*GetCLBRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kateway_GetBackends_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCLBRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KatewayServer).GetBackends(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kateway_GetBackends_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KatewayServer).GetBackends(ctx, req.(*GetCLBRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kateway_GetHealth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCLBRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KatewayServer).GetHealth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kateway_GetHealth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KatewayServer).GetHealth(ctx, req.(*GetCLBRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kateway_CheckImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckImageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KatewayServer).CheckImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kateway_CheckImage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KatewayServer).CheckImage(ctx, req.(*CheckImageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kateway_GetPod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KatewayServer).GetPod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kateway_GetPod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KatewayServer).GetPod(ctx, req.(*GetPodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kateway_GetDeploy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeployRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KatewayServer).GetDeploy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kateway_GetDeploy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KatewayServer).GetDeploy(ctx, req.(*GetDeployRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kateway_GetLeader_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLeaderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KatewayServer).GetLeader(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kateway_GetLeader_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KatewayServer).GetLeader(ctx, req.(*GetLeaderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kateway_GetConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KatewayServer).GetConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kateway_GetConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KatewayServer).GetConfig(ctx, req.(*GetConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kateway_Logs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KatewayServer).Logs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kateway_Logs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KatewayServer).Logs(ctx, req.(*LogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kateway_YunAPI_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(YunAPIRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KatewayServer).YunAPI(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kateway_YunAPI_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KatewayServer).YunAPI(ctx, req.(*YunAPIRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kateway_Admin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KatewayServer).Admin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kateway_Admin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KatewayServer).Admin(ctx, req.(*AdminRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kateway_SetImage_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(SetImageRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(KatewayServer).SetImage(m, &grpc.GenericServerStream[SetImageRequest, SetImageResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Kateway_SetImageServer = grpc.ServerStreamingServer[SetImageResponse]

func _Kateway_Mock_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(MockRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(KatewayServer).Mock(m, &grpc.GenericServerStream[MockRequest, MockResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Kateway_MockServer = grpc.ServerStreamingServer[MockResponse]

func _Kateway_Update_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(UpdateRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(KatewayServer).Update(m, &grpc.GenericServerStream[UpdateRequest, UpdateResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Kateway_UpdateServer = grpc.ServerStreamingServer[UpdateResponse]

func _Kateway_ListTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KatewayServer).ListTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kateway_ListTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KatewayServer).ListTask(ctx, req.(*ListTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kateway_GetTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KatewayServer).GetTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kateway_GetTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KatewayServer).GetTask(ctx, req.(*GetTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kateway_CreateTask_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(CreateTaskRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(KatewayServer).CreateTask(m, &grpc.GenericServerStream[CreateTaskRequest, CreateTaskResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Kateway_CreateTaskServer = grpc.ServerStreamingServer[CreateTaskResponse]

func _Kateway_UpdateArgs_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(ArgsRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(KatewayServer).UpdateArgs(m, &grpc.GenericServerStream[ArgsRequest, ArgsResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Kateway_UpdateArgsServer = grpc.ServerStreamingServer[ArgsResponse]

// Kateway_ServiceDesc is the grpc.ServiceDesc for Kateway service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Kateway_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "tke.Kateway",
	HandlerType: (*KatewayServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckUpgrade",
			Handler:    _Kateway_CheckUpgrade_Handler,
		},
		{
			MethodName: "GetUser",
			Handler:    _Kateway_GetUser_Handler,
		},
		{
			MethodName: "GetCluster",
			Handler:    _Kateway_GetCluster_Handler,
		},
		{
			MethodName: "GetCLB",
			Handler:    _Kateway_GetCLB_Handler,
		},
		{
			MethodName: "GetListeners",
			Handler:    _Kateway_GetListeners_Handler,
		},
		{
			MethodName: "GetBackends",
			Handler:    _Kateway_GetBackends_Handler,
		},
		{
			MethodName: "GetHealth",
			Handler:    _Kateway_GetHealth_Handler,
		},
		{
			MethodName: "CheckImage",
			Handler:    _Kateway_CheckImage_Handler,
		},
		{
			MethodName: "GetPod",
			Handler:    _Kateway_GetPod_Handler,
		},
		{
			MethodName: "GetDeploy",
			Handler:    _Kateway_GetDeploy_Handler,
		},
		{
			MethodName: "GetLeader",
			Handler:    _Kateway_GetLeader_Handler,
		},
		{
			MethodName: "GetConfig",
			Handler:    _Kateway_GetConfig_Handler,
		},
		{
			MethodName: "Logs",
			Handler:    _Kateway_Logs_Handler,
		},
		{
			MethodName: "YunAPI",
			Handler:    _Kateway_YunAPI_Handler,
		},
		{
			MethodName: "Admin",
			Handler:    _Kateway_Admin_Handler,
		},
		{
			MethodName: "ListTask",
			Handler:    _Kateway_ListTask_Handler,
		},
		{
			MethodName: "GetTask",
			Handler:    _Kateway_GetTask_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SetImage",
			Handler:       _Kateway_SetImage_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Mock",
			Handler:       _Kateway_Mock_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Update",
			Handler:       _Kateway_Update_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "CreateTask",
			Handler:       _Kateway_CreateTask_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "UpdateArgs",
			Handler:       _Kateway_UpdateArgs_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "kateway.proto",
}
