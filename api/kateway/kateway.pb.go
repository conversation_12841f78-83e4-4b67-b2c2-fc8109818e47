// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.2
// 	protoc        v5.29.3
// source: kateway.proto

package kateway

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CheckUpgradeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Version       string                 `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	Os            string                 `protobuf:"bytes,2,opt,name=os,proto3" json:"os,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckUpgradeRequest) Reset() {
	*x = CheckUpgradeRequest{}
	mi := &file_kateway_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUpgradeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUpgradeRequest) ProtoMessage() {}

func (x *CheckUpgradeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUpgradeRequest.ProtoReflect.Descriptor instead.
func (*CheckUpgradeRequest) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{0}
}

func (x *CheckUpgradeRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CheckUpgradeRequest) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

type CheckUpgradeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Version       string                 `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	Url           string                 `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckUpgradeResponse) Reset() {
	*x = CheckUpgradeResponse{}
	mi := &file_kateway_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUpgradeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUpgradeResponse) ProtoMessage() {}

func (x *CheckUpgradeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUpgradeResponse.ProtoReflect.Descriptor instead.
func (*CheckUpgradeResponse) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{1}
}

func (x *CheckUpgradeResponse) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CheckUpgradeResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type GetUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Query         string                 `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserRequest) Reset() {
	*x = GetUserRequest{}
	mi := &file_kateway_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserRequest) ProtoMessage() {}

func (x *GetUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserRequest.ProtoReflect.Descriptor instead.
func (*GetUserRequest) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{2}
}

func (x *GetUserRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

type GetUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *User                  `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserResponse) Reset() {
	*x = GetUserResponse{}
	mi := &file_kateway_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserResponse) ProtoMessage() {}

func (x *GetUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserResponse.ProtoReflect.Descriptor instead.
func (*GetUserResponse) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{3}
}

func (x *GetUserResponse) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

type User struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Uin           string                 `protobuf:"bytes,1,opt,name=uin,proto3" json:"uin,omitempty"`
	Appid         string                 `protobuf:"bytes,2,opt,name=appid,proto3" json:"appid,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *User) Reset() {
	*x = User{}
	mi := &file_kateway_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{4}
}

func (x *User) GetUin() string {
	if x != nil {
		return x.Uin
	}
	return ""
}

func (x *User) GetAppid() string {
	if x != nil {
		return x.Appid
	}
	return ""
}

func (x *User) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetClusterRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Query         string                 `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClusterRequest) Reset() {
	*x = GetClusterRequest{}
	mi := &file_kateway_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClusterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClusterRequest) ProtoMessage() {}

func (x *GetClusterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClusterRequest.ProtoReflect.Descriptor instead.
func (*GetClusterRequest) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{5}
}

func (x *GetClusterRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

type GetClusterResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Cluster       *Cluster               `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClusterResponse) Reset() {
	*x = GetClusterResponse{}
	mi := &file_kateway_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClusterResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClusterResponse) ProtoMessage() {}

func (x *GetClusterResponse) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClusterResponse.ProtoReflect.Descriptor instead.
func (*GetClusterResponse) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{6}
}

func (x *GetClusterResponse) GetCluster() *Cluster {
	if x != nil {
		return x.Cluster
	}
	return nil
}

type Cluster struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Type          string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Version       string                 `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Region        string                 `protobuf:"bytes,5,opt,name=region,proto3" json:"region,omitempty"`
	State         string                 `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`
	Vpc           string                 `protobuf:"bytes,7,opt,name=vpc,proto3" json:"vpc,omitempty"`
	CreatedAt     string                 `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	User          *User                  `protobuf:"bytes,9,opt,name=user,proto3" json:"user,omitempty"`
	MetaCluster   string                 `protobuf:"bytes,10,opt,name=metaCluster,proto3" json:"metaCluster,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Cluster) Reset() {
	*x = Cluster{}
	mi := &file_kateway_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Cluster) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cluster) ProtoMessage() {}

func (x *Cluster) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cluster.ProtoReflect.Descriptor instead.
func (*Cluster) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{7}
}

func (x *Cluster) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Cluster) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Cluster) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Cluster) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Cluster) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *Cluster) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *Cluster) GetVpc() string {
	if x != nil {
		return x.Vpc
	}
	return ""
}

func (x *Cluster) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *Cluster) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *Cluster) GetMetaCluster() string {
	if x != nil {
		return x.MetaCluster
	}
	return ""
}

type ClusterProperty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NodeNameType  string                 `protobuf:"bytes,1,opt,name=NodeNameType,proto3" json:"NodeNameType,omitempty"`
	NetworkType   string                 `protobuf:"bytes,2,opt,name=NetworkType,proto3" json:"NetworkType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClusterProperty) Reset() {
	*x = ClusterProperty{}
	mi := &file_kateway_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClusterProperty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClusterProperty) ProtoMessage() {}

func (x *ClusterProperty) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClusterProperty.ProtoReflect.Descriptor instead.
func (*ClusterProperty) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{8}
}

func (x *ClusterProperty) GetNodeNameType() string {
	if x != nil {
		return x.NodeNameType
	}
	return ""
}

func (x *ClusterProperty) GetNetworkType() string {
	if x != nil {
		return x.NetworkType
	}
	return ""
}

type GetCLBRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Query         string                 `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCLBRequest) Reset() {
	*x = GetCLBRequest{}
	mi := &file_kateway_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCLBRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCLBRequest) ProtoMessage() {}

func (x *GetCLBRequest) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCLBRequest.ProtoReflect.Descriptor instead.
func (*GetCLBRequest) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{9}
}

func (x *GetCLBRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

type GetCLBResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          string                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCLBResponse) Reset() {
	*x = GetCLBResponse{}
	mi := &file_kateway_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCLBResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCLBResponse) ProtoMessage() {}

func (x *GetCLBResponse) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCLBResponse.ProtoReflect.Descriptor instead.
func (*GetCLBResponse) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{10}
}

func (x *GetCLBResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type GetPodRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Target        *Target                `protobuf:"bytes,1,opt,name=target,proto3" json:"target,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPodRequest) Reset() {
	*x = GetPodRequest{}
	mi := &file_kateway_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPodRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPodRequest) ProtoMessage() {}

func (x *GetPodRequest) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPodRequest.ProtoReflect.Descriptor instead.
func (*GetPodRequest) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{11}
}

func (x *GetPodRequest) GetTarget() *Target {
	if x != nil {
		return x.Target
	}
	return nil
}

type Target struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Cluster       string                 `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	User          string                 `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	Token         string                 `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Target) Reset() {
	*x = Target{}
	mi := &file_kateway_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Target) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Target) ProtoMessage() {}

func (x *Target) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Target.ProtoReflect.Descriptor instead.
func (*Target) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{12}
}

func (x *Target) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Target) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *Target) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *Target) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type GetPodResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          string                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPodResponse) Reset() {
	*x = GetPodResponse{}
	mi := &file_kateway_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPodResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPodResponse) ProtoMessage() {}

func (x *GetPodResponse) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPodResponse.ProtoReflect.Descriptor instead.
func (*GetPodResponse) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{13}
}

func (x *GetPodResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type GetDeployRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Target        *Target                `protobuf:"bytes,1,opt,name=target,proto3" json:"target,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDeployRequest) Reset() {
	*x = GetDeployRequest{}
	mi := &file_kateway_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDeployRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeployRequest) ProtoMessage() {}

func (x *GetDeployRequest) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeployRequest.ProtoReflect.Descriptor instead.
func (*GetDeployRequest) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{14}
}

func (x *GetDeployRequest) GetTarget() *Target {
	if x != nil {
		return x.Target
	}
	return nil
}

type GetDeployResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          string                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDeployResponse) Reset() {
	*x = GetDeployResponse{}
	mi := &file_kateway_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDeployResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeployResponse) ProtoMessage() {}

func (x *GetDeployResponse) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeployResponse.ProtoReflect.Descriptor instead.
func (*GetDeployResponse) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{15}
}

func (x *GetDeployResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type GetLeaderRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Target        *Target                `protobuf:"bytes,1,opt,name=target,proto3" json:"target,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLeaderRequest) Reset() {
	*x = GetLeaderRequest{}
	mi := &file_kateway_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLeaderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLeaderRequest) ProtoMessage() {}

func (x *GetLeaderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLeaderRequest.ProtoReflect.Descriptor instead.
func (*GetLeaderRequest) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{16}
}

func (x *GetLeaderRequest) GetTarget() *Target {
	if x != nil {
		return x.Target
	}
	return nil
}

type GetLeaderResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          string                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLeaderResponse) Reset() {
	*x = GetLeaderResponse{}
	mi := &file_kateway_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLeaderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLeaderResponse) ProtoMessage() {}

func (x *GetLeaderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLeaderResponse.ProtoReflect.Descriptor instead.
func (*GetLeaderResponse) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{17}
}

func (x *GetLeaderResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type GetConfigRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Target        *Target                `protobuf:"bytes,1,opt,name=target,proto3" json:"target,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetConfigRequest) Reset() {
	*x = GetConfigRequest{}
	mi := &file_kateway_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfigRequest) ProtoMessage() {}

func (x *GetConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfigRequest.ProtoReflect.Descriptor instead.
func (*GetConfigRequest) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{18}
}

func (x *GetConfigRequest) GetTarget() *Target {
	if x != nil {
		return x.Target
	}
	return nil
}

type GetConfigResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          string                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetConfigResponse) Reset() {
	*x = GetConfigResponse{}
	mi := &file_kateway_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfigResponse) ProtoMessage() {}

func (x *GetConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfigResponse.ProtoReflect.Descriptor instead.
func (*GetConfigResponse) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{19}
}

func (x *GetConfigResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type LogsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Target        *Target                `protobuf:"bytes,1,opt,name=target,proto3" json:"target,omitempty"`
	LogOptions    *LogOptions            `protobuf:"bytes,3,opt,name=log_options,json=logOptions,proto3" json:"log_options,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogsRequest) Reset() {
	*x = LogsRequest{}
	mi := &file_kateway_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogsRequest) ProtoMessage() {}

func (x *LogsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogsRequest.ProtoReflect.Descriptor instead.
func (*LogsRequest) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{20}
}

func (x *LogsRequest) GetTarget() *Target {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *LogsRequest) GetLogOptions() *LogOptions {
	if x != nil {
		return x.LogOptions
	}
	return nil
}

type LogsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          string                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogsResponse) Reset() {
	*x = LogsResponse{}
	mi := &file_kateway_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogsResponse) ProtoMessage() {}

func (x *LogsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogsResponse.ProtoReflect.Descriptor instead.
func (*LogsResponse) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{21}
}

func (x *LogsResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type LogOptions struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SinceTime     string                 `protobuf:"bytes,1,opt,name=since_time,json=sinceTime,proto3" json:"since_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogOptions) Reset() {
	*x = LogOptions{}
	mi := &file_kateway_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogOptions) ProtoMessage() {}

func (x *LogOptions) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogOptions.ProtoReflect.Descriptor instead.
func (*LogOptions) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{22}
}

func (x *LogOptions) GetSinceTime() string {
	if x != nil {
		return x.SinceTime
	}
	return ""
}

type YunAPIRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Target        *Target                `protobuf:"bytes,1,opt,name=target,proto3" json:"target,omitempty"`
	Query         string                 `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"`
	LogOptions    *LogOptions            `protobuf:"bytes,3,opt,name=log_options,json=logOptions,proto3" json:"log_options,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *YunAPIRequest) Reset() {
	*x = YunAPIRequest{}
	mi := &file_kateway_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *YunAPIRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*YunAPIRequest) ProtoMessage() {}

func (x *YunAPIRequest) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use YunAPIRequest.ProtoReflect.Descriptor instead.
func (*YunAPIRequest) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{23}
}

func (x *YunAPIRequest) GetTarget() *Target {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *YunAPIRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *YunAPIRequest) GetLogOptions() *LogOptions {
	if x != nil {
		return x.LogOptions
	}
	return nil
}

type YunAPIPodRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *YunAPIPodRequest) Reset() {
	*x = YunAPIPodRequest{}
	mi := &file_kateway_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *YunAPIPodRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*YunAPIPodRequest) ProtoMessage() {}

func (x *YunAPIPodRequest) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use YunAPIPodRequest.ProtoReflect.Descriptor instead.
func (*YunAPIPodRequest) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{24}
}

type YunAPIResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          string                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *YunAPIResponse) Reset() {
	*x = YunAPIResponse{}
	mi := &file_kateway_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *YunAPIResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*YunAPIResponse) ProtoMessage() {}

func (x *YunAPIResponse) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use YunAPIResponse.ProtoReflect.Descriptor instead.
func (*YunAPIResponse) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{25}
}

func (x *YunAPIResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type AdminRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Target        *Target                `protobuf:"bytes,1,opt,name=target,proto3" json:"target,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AdminRequest) Reset() {
	*x = AdminRequest{}
	mi := &file_kateway_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdminRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminRequest) ProtoMessage() {}

func (x *AdminRequest) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminRequest.ProtoReflect.Descriptor instead.
func (*AdminRequest) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{26}
}

func (x *AdminRequest) GetTarget() *Target {
	if x != nil {
		return x.Target
	}
	return nil
}

type AdminResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          string                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AdminResponse) Reset() {
	*x = AdminResponse{}
	mi := &file_kateway_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdminResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminResponse) ProtoMessage() {}

func (x *AdminResponse) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminResponse.ProtoReflect.Descriptor instead.
func (*AdminResponse) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{27}
}

func (x *AdminResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type CheckImageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Version       string                 `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Region        string                 `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckImageRequest) Reset() {
	*x = CheckImageRequest{}
	mi := &file_kateway_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckImageRequest) ProtoMessage() {}

func (x *CheckImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckImageRequest.ProtoReflect.Descriptor instead.
func (*CheckImageRequest) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{28}
}

func (x *CheckImageRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CheckImageRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CheckImageRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type CheckImageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckImageResponse) Reset() {
	*x = CheckImageResponse{}
	mi := &file_kateway_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckImageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckImageResponse) ProtoMessage() {}

func (x *CheckImageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckImageResponse.ProtoReflect.Descriptor instead.
func (*CheckImageResponse) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{29}
}

type SetImageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Target        *Target                `protobuf:"bytes,1,opt,name=target,proto3" json:"target,omitempty"`
	Image         string                 `protobuf:"bytes,2,opt,name=Image,proto3" json:"Image,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetImageRequest) Reset() {
	*x = SetImageRequest{}
	mi := &file_kateway_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetImageRequest) ProtoMessage() {}

func (x *SetImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetImageRequest.ProtoReflect.Descriptor instead.
func (*SetImageRequest) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{30}
}

func (x *SetImageRequest) GetTarget() *Target {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *SetImageRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

type SetImageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          string                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetImageResponse) Reset() {
	*x = SetImageResponse{}
	mi := &file_kateway_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetImageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetImageResponse) ProtoMessage() {}

func (x *SetImageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetImageResponse.ProtoReflect.Descriptor instead.
func (*SetImageResponse) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{31}
}

func (x *SetImageResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type MockRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Target        *Target                `protobuf:"bytes,1,opt,name=target,proto3" json:"target,omitempty"`
	Image         string                 `protobuf:"bytes,2,opt,name=Image,proto3" json:"Image,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MockRequest) Reset() {
	*x = MockRequest{}
	mi := &file_kateway_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MockRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MockRequest) ProtoMessage() {}

func (x *MockRequest) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MockRequest.ProtoReflect.Descriptor instead.
func (*MockRequest) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{32}
}

func (x *MockRequest) GetTarget() *Target {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *MockRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

type MockResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          string                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MockResponse) Reset() {
	*x = MockResponse{}
	mi := &file_kateway_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MockResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MockResponse) ProtoMessage() {}

func (x *MockResponse) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MockResponse.ProtoReflect.Descriptor instead.
func (*MockResponse) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{33}
}

func (x *MockResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type UpdateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Target        *Target                `protobuf:"bytes,1,opt,name=target,proto3" json:"target,omitempty"`
	Image         string                 `protobuf:"bytes,2,opt,name=Image,proto3" json:"Image,omitempty"`
	Force         bool                   `protobuf:"varint,3,opt,name=Force,proto3" json:"Force,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRequest) Reset() {
	*x = UpdateRequest{}
	mi := &file_kateway_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRequest) ProtoMessage() {}

func (x *UpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRequest.ProtoReflect.Descriptor instead.
func (*UpdateRequest) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{34}
}

func (x *UpdateRequest) GetTarget() *Target {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *UpdateRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *UpdateRequest) GetForce() bool {
	if x != nil {
		return x.Force
	}
	return false
}

type UpdateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          string                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateResponse) Reset() {
	*x = UpdateResponse{}
	mi := &file_kateway_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateResponse) ProtoMessage() {}

func (x *UpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateResponse.ProtoReflect.Descriptor instead.
func (*UpdateResponse) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{35}
}

func (x *UpdateResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type GetTaskRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTaskRequest) Reset() {
	*x = GetTaskRequest{}
	mi := &file_kateway_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskRequest) ProtoMessage() {}

func (x *GetTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskRequest.ProtoReflect.Descriptor instead.
func (*GetTaskRequest) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{36}
}

func (x *GetTaskRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetTaskResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          string                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTaskResponse) Reset() {
	*x = GetTaskResponse{}
	mi := &file_kateway_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskResponse) ProtoMessage() {}

func (x *GetTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskResponse.ProtoReflect.Descriptor instead.
func (*GetTaskResponse) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{37}
}

func (x *GetTaskResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type ListTaskRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Limit         uint64                 `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset        uint64                 `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Filters       []string               `protobuf:"bytes,3,rep,name=filters,proto3" json:"filters,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTaskRequest) Reset() {
	*x = ListTaskRequest{}
	mi := &file_kateway_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTaskRequest) ProtoMessage() {}

func (x *ListTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTaskRequest.ProtoReflect.Descriptor instead.
func (*ListTaskRequest) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{38}
}

func (x *ListTaskRequest) GetLimit() uint64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListTaskRequest) GetOffset() uint64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ListTaskRequest) GetFilters() []string {
	if x != nil {
		return x.Filters
	}
	return nil
}

type ListTaskResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          string                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	Total         uint64                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTaskResponse) Reset() {
	*x = ListTaskResponse{}
	mi := &file_kateway_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTaskResponse) ProtoMessage() {}

func (x *ListTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTaskResponse.ProtoReflect.Descriptor instead.
func (*ListTaskResponse) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{39}
}

func (x *ListTaskResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *ListTaskResponse) GetTotal() uint64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type CreateTaskRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Input         string                 `protobuf:"bytes,2,opt,name=input,proto3" json:"input,omitempty"`
	Creator       string                 `protobuf:"bytes,3,opt,name=creator,proto3" json:"creator,omitempty"`
	Wait          bool                   `protobuf:"varint,4,opt,name=wait,proto3" json:"wait,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTaskRequest) Reset() {
	*x = CreateTaskRequest{}
	mi := &file_kateway_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTaskRequest) ProtoMessage() {}

func (x *CreateTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTaskRequest.ProtoReflect.Descriptor instead.
func (*CreateTaskRequest) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{40}
}

func (x *CreateTaskRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CreateTaskRequest) GetInput() string {
	if x != nil {
		return x.Input
	}
	return ""
}

func (x *CreateTaskRequest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *CreateTaskRequest) GetWait() bool {
	if x != nil {
		return x.Wait
	}
	return false
}

type CreateTaskResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          string                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTaskResponse) Reset() {
	*x = CreateTaskResponse{}
	mi := &file_kateway_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTaskResponse) ProtoMessage() {}

func (x *CreateTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTaskResponse.ProtoReflect.Descriptor instead.
func (*CreateTaskResponse) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{41}
}

func (x *CreateTaskResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

// 参数管理请求（支持设置/取消参数）
type ArgsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Target        *Target                `protobuf:"bytes,1,opt,name=target,proto3" json:"target,omitempty"`       // 集群目标信息
	Operation     string                 `protobuf:"bytes,2,opt,name=operation,proto3" json:"operation,omitempty"` // 操作类型(set/unset)
	Args          []string               `protobuf:"bytes,3,rep,name=args,proto3" json:"args,omitempty"`           // 参数列表
	Force         bool                   `protobuf:"varint,4,opt,name=force,proto3" json:"force,omitempty"`        // 是否强制更新
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ArgsRequest) Reset() {
	*x = ArgsRequest{}
	mi := &file_kateway_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArgsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArgsRequest) ProtoMessage() {}

func (x *ArgsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArgsRequest.ProtoReflect.Descriptor instead.
func (*ArgsRequest) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{42}
}

func (x *ArgsRequest) GetTarget() *Target {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *ArgsRequest) GetOperation() string {
	if x != nil {
		return x.Operation
	}
	return ""
}

func (x *ArgsRequest) GetArgs() []string {
	if x != nil {
		return x.Args
	}
	return nil
}

func (x *ArgsRequest) GetForce() bool {
	if x != nil {
		return x.Force
	}
	return false
}

// 参数管理响应
type ArgsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          string                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ArgsResponse) Reset() {
	*x = ArgsResponse{}
	mi := &file_kateway_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArgsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArgsResponse) ProtoMessage() {}

func (x *ArgsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_kateway_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArgsResponse.ProtoReflect.Descriptor instead.
func (*ArgsResponse) Descriptor() ([]byte, []int) {
	return file_kateway_proto_rawDescGZIP(), []int{43}
}

func (x *ArgsResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

var File_kateway_proto protoreflect.FileDescriptor

var file_kateway_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x6b, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x03, 0x74, 0x6b, 0x65, 0x22, 0x3f, 0x0a, 0x13, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x70, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x6f, 0x73, 0x22, 0x42, 0x0a, 0x14, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x70,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x26, 0x0a, 0x0e, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x71,
	0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x22, 0x30, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x09, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75,
	0x73, 0x65, 0x72, 0x22, 0x42, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x6e, 0x12, 0x14, 0x0a,
	0x05, 0x61, 0x70, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70,
	0x70, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x29, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65,
	0x72, 0x79, 0x22, 0x3c, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x26, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x74, 0x6b, 0x65, 0x2e,
	0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x22, 0xfb, 0x01, 0x0a, 0x07, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x76, 0x70, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x70, 0x63, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x74, 0x6b,
	0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b,
	0x6d, 0x65, 0x74, 0x61, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x6d, 0x65, 0x74, 0x61, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x22, 0x57,
	0x0a, 0x0f, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x12, 0x22, 0x0a, 0x0c, 0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x4e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x22, 0x25, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x43, 0x4c,
	0x42, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x22, 0x24,
	0x0a, 0x0e, 0x47, 0x65, 0x74, 0x43, 0x4c, 0x42, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x34, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x22, 0x60, 0x0a, 0x06, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x24, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x37, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x22, 0x27, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x37, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4c, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x22, 0x27, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x37, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x06, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x6b, 0x65,
	0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x22,
	0x27, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x64, 0x0a, 0x0b, 0x4c, 0x6f, 0x67, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x30, 0x0a, 0x0b,
	0x6c, 0x6f, 0x67, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x4c, 0x6f, 0x67, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x0a, 0x6c, 0x6f, 0x67, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x22,
	0x0a, 0x0c, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x2b, 0x0a, 0x0a, 0x4c, 0x6f, 0x67, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0x7c, 0x0a, 0x0d, 0x59, 0x75, 0x6e, 0x41, 0x50, 0x49, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x23, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x06, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x30, 0x0a, 0x0b, 0x6c,
	0x6f, 0x67, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x4c, 0x6f, 0x67, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x0a, 0x6c, 0x6f, 0x67, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x12, 0x0a,
	0x10, 0x59, 0x75, 0x6e, 0x41, 0x50, 0x49, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x22, 0x24, 0x0a, 0x0e, 0x59, 0x75, 0x6e, 0x41, 0x50, 0x49, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x33, 0x0a, 0x0c, 0x41, 0x64, 0x6d, 0x69, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x22, 0x23, 0x0a, 0x0d,
	0x41, 0x64, 0x6d, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x22, 0x59, 0x0a, 0x11, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x14, 0x0a, 0x12,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x4c, 0x0a, 0x0f, 0x53, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x22, 0x26, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x48, 0x0a, 0x0b, 0x4d, 0x6f, 0x63, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x22, 0x22, 0x0a, 0x0c, 0x4d, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x60, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x46, 0x6f, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x05, 0x46, 0x6f, 0x72, 0x63, 0x65, 0x22, 0x24, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x20,
	0x0a, 0x0e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x25, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x59, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x73, 0x22, 0x3c, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x22, 0x6b, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x70,
	0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x77, 0x61, 0x69,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x77, 0x61, 0x69, 0x74, 0x22, 0x28, 0x0a,
	0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x7a, 0x0a, 0x0b, 0x41, 0x72, 0x67, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x67,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x61, 0x72, 0x67, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x66, 0x6f,
	0x72, 0x63, 0x65, 0x22, 0x22, 0x0a, 0x0c, 0x41, 0x72, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x32, 0x83, 0x0a, 0x0a, 0x07, 0x4b, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x12, 0x45, 0x0a, 0x0c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x70, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x12, 0x18, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55,
	0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e,
	0x74, 0x6b, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x36, 0x0a, 0x07, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x12, 0x13, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x74, 0x6b, 0x65,
	0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x3f, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x12, 0x16, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x33, 0x0a, 0x06, 0x47, 0x65, 0x74, 0x43, 0x4c, 0x42, 0x12, 0x12, 0x2e,
	0x74, 0x6b, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x4c, 0x42, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x13, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x4c, 0x42, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x39, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x12, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x4c, 0x42, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x74,
	0x6b, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x4c, 0x42, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x38, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e,
	0x64, 0x73, 0x12, 0x12, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x4c, 0x42, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x4c, 0x42, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x36, 0x0a,
	0x09, 0x47, 0x65, 0x74, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x12, 0x12, 0x2e, 0x74, 0x6b, 0x65,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x4c, 0x42, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13,
	0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x4c, 0x42, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3f, 0x0a, 0x0a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x12, 0x16, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x74, 0x6b,
	0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x33, 0x0a, 0x06, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64,
	0x12, 0x12, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3c, 0x0a, 0x09, 0x47,
	0x65, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x12, 0x15, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x47,
	0x65, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3c, 0x0a, 0x09, 0x47, 0x65, 0x74,
	0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x15, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x47, 0x65, 0x74,
	0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x74, 0x6b, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3c, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x15, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x74, 0x6b,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x2d, 0x0a, 0x04, 0x4c, 0x6f, 0x67, 0x73, 0x12, 0x10, 0x2e,
	0x74, 0x6b, 0x65, 0x2e, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x11, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x33, 0x0a, 0x06, 0x59, 0x75, 0x6e, 0x41, 0x50, 0x49, 0x12, 0x12,
	0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x59, 0x75, 0x6e, 0x41, 0x50, 0x49, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x13, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x59, 0x75, 0x6e, 0x41, 0x50, 0x49, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x30, 0x0a, 0x05, 0x41, 0x64, 0x6d,
	0x69, 0x6e, 0x12, 0x11, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x41, 0x64, 0x6d, 0x69,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3b, 0x0a, 0x08, 0x53,
	0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x14, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x53, 0x65,
	0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e,
	0x74, 0x6b, 0x65, 0x2e, 0x53, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x30, 0x01, 0x12, 0x2f, 0x0a, 0x04, 0x4d, 0x6f, 0x63, 0x6b,
	0x12, 0x10, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x4d, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x11, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x4d, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x30, 0x01, 0x12, 0x35, 0x0a, 0x06, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x12, 0x12, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x30, 0x01,
	0x12, 0x39, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x14, 0x2e, 0x74,
	0x6b, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x15, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x36, 0x0a, 0x07, 0x47,
	0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x13, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x47, 0x65, 0x74,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x74, 0x6b,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x41, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x12, 0x16, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x74, 0x6b, 0x65, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x30, 0x01, 0x12, 0x35, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x41, 0x72, 0x67, 0x73, 0x12, 0x10, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x41, 0x72, 0x67, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x11, 0x2e, 0x74, 0x6b, 0x65, 0x2e, 0x41, 0x72, 0x67,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x30, 0x01, 0x42, 0x0c, 0x5a,
	0x0a, 0x2e, 0x2f, 0x3b, 0x6b, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_kateway_proto_rawDescOnce sync.Once
	file_kateway_proto_rawDescData = file_kateway_proto_rawDesc
)

func file_kateway_proto_rawDescGZIP() []byte {
	file_kateway_proto_rawDescOnce.Do(func() {
		file_kateway_proto_rawDescData = protoimpl.X.CompressGZIP(file_kateway_proto_rawDescData)
	})
	return file_kateway_proto_rawDescData
}

var file_kateway_proto_msgTypes = make([]protoimpl.MessageInfo, 44)
var file_kateway_proto_goTypes = []any{
	(*CheckUpgradeRequest)(nil),  // 0: tke.CheckUpgradeRequest
	(*CheckUpgradeResponse)(nil), // 1: tke.CheckUpgradeResponse
	(*GetUserRequest)(nil),       // 2: tke.GetUserRequest
	(*GetUserResponse)(nil),      // 3: tke.GetUserResponse
	(*User)(nil),                 // 4: tke.User
	(*GetClusterRequest)(nil),    // 5: tke.GetClusterRequest
	(*GetClusterResponse)(nil),   // 6: tke.GetClusterResponse
	(*Cluster)(nil),              // 7: tke.Cluster
	(*ClusterProperty)(nil),      // 8: tke.ClusterProperty
	(*GetCLBRequest)(nil),        // 9: tke.GetCLBRequest
	(*GetCLBResponse)(nil),       // 10: tke.GetCLBResponse
	(*GetPodRequest)(nil),        // 11: tke.GetPodRequest
	(*Target)(nil),               // 12: tke.Target
	(*GetPodResponse)(nil),       // 13: tke.GetPodResponse
	(*GetDeployRequest)(nil),     // 14: tke.GetDeployRequest
	(*GetDeployResponse)(nil),    // 15: tke.GetDeployResponse
	(*GetLeaderRequest)(nil),     // 16: tke.GetLeaderRequest
	(*GetLeaderResponse)(nil),    // 17: tke.GetLeaderResponse
	(*GetConfigRequest)(nil),     // 18: tke.GetConfigRequest
	(*GetConfigResponse)(nil),    // 19: tke.GetConfigResponse
	(*LogsRequest)(nil),          // 20: tke.LogsRequest
	(*LogsResponse)(nil),         // 21: tke.LogsResponse
	(*LogOptions)(nil),           // 22: tke.LogOptions
	(*YunAPIRequest)(nil),        // 23: tke.YunAPIRequest
	(*YunAPIPodRequest)(nil),     // 24: tke.YunAPIPodRequest
	(*YunAPIResponse)(nil),       // 25: tke.YunAPIResponse
	(*AdminRequest)(nil),         // 26: tke.AdminRequest
	(*AdminResponse)(nil),        // 27: tke.AdminResponse
	(*CheckImageRequest)(nil),    // 28: tke.CheckImageRequest
	(*CheckImageResponse)(nil),   // 29: tke.CheckImageResponse
	(*SetImageRequest)(nil),      // 30: tke.SetImageRequest
	(*SetImageResponse)(nil),     // 31: tke.SetImageResponse
	(*MockRequest)(nil),          // 32: tke.MockRequest
	(*MockResponse)(nil),         // 33: tke.MockResponse
	(*UpdateRequest)(nil),        // 34: tke.UpdateRequest
	(*UpdateResponse)(nil),       // 35: tke.UpdateResponse
	(*GetTaskRequest)(nil),       // 36: tke.GetTaskRequest
	(*GetTaskResponse)(nil),      // 37: tke.GetTaskResponse
	(*ListTaskRequest)(nil),      // 38: tke.ListTaskRequest
	(*ListTaskResponse)(nil),     // 39: tke.ListTaskResponse
	(*CreateTaskRequest)(nil),    // 40: tke.CreateTaskRequest
	(*CreateTaskResponse)(nil),   // 41: tke.CreateTaskResponse
	(*ArgsRequest)(nil),          // 42: tke.ArgsRequest
	(*ArgsResponse)(nil),         // 43: tke.ArgsResponse
}
var file_kateway_proto_depIdxs = []int32{
	4,  // 0: tke.GetUserResponse.user:type_name -> tke.User
	7,  // 1: tke.GetClusterResponse.cluster:type_name -> tke.Cluster
	4,  // 2: tke.Cluster.user:type_name -> tke.User
	12, // 3: tke.GetPodRequest.target:type_name -> tke.Target
	12, // 4: tke.GetDeployRequest.target:type_name -> tke.Target
	12, // 5: tke.GetLeaderRequest.target:type_name -> tke.Target
	12, // 6: tke.GetConfigRequest.target:type_name -> tke.Target
	12, // 7: tke.LogsRequest.target:type_name -> tke.Target
	22, // 8: tke.LogsRequest.log_options:type_name -> tke.LogOptions
	12, // 9: tke.YunAPIRequest.target:type_name -> tke.Target
	22, // 10: tke.YunAPIRequest.log_options:type_name -> tke.LogOptions
	12, // 11: tke.AdminRequest.target:type_name -> tke.Target
	12, // 12: tke.SetImageRequest.target:type_name -> tke.Target
	12, // 13: tke.MockRequest.target:type_name -> tke.Target
	12, // 14: tke.UpdateRequest.target:type_name -> tke.Target
	12, // 15: tke.ArgsRequest.target:type_name -> tke.Target
	0,  // 16: tke.Kateway.CheckUpgrade:input_type -> tke.CheckUpgradeRequest
	2,  // 17: tke.Kateway.GetUser:input_type -> tke.GetUserRequest
	5,  // 18: tke.Kateway.GetCluster:input_type -> tke.GetClusterRequest
	9,  // 19: tke.Kateway.GetCLB:input_type -> tke.GetCLBRequest
	9,  // 20: tke.Kateway.GetListeners:input_type -> tke.GetCLBRequest
	9,  // 21: tke.Kateway.GetBackends:input_type -> tke.GetCLBRequest
	9,  // 22: tke.Kateway.GetHealth:input_type -> tke.GetCLBRequest
	28, // 23: tke.Kateway.CheckImage:input_type -> tke.CheckImageRequest
	11, // 24: tke.Kateway.GetPod:input_type -> tke.GetPodRequest
	14, // 25: tke.Kateway.GetDeploy:input_type -> tke.GetDeployRequest
	16, // 26: tke.Kateway.GetLeader:input_type -> tke.GetLeaderRequest
	18, // 27: tke.Kateway.GetConfig:input_type -> tke.GetConfigRequest
	20, // 28: tke.Kateway.Logs:input_type -> tke.LogsRequest
	23, // 29: tke.Kateway.YunAPI:input_type -> tke.YunAPIRequest
	26, // 30: tke.Kateway.Admin:input_type -> tke.AdminRequest
	30, // 31: tke.Kateway.SetImage:input_type -> tke.SetImageRequest
	32, // 32: tke.Kateway.Mock:input_type -> tke.MockRequest
	34, // 33: tke.Kateway.Update:input_type -> tke.UpdateRequest
	38, // 34: tke.Kateway.ListTask:input_type -> tke.ListTaskRequest
	36, // 35: tke.Kateway.GetTask:input_type -> tke.GetTaskRequest
	40, // 36: tke.Kateway.CreateTask:input_type -> tke.CreateTaskRequest
	42, // 37: tke.Kateway.UpdateArgs:input_type -> tke.ArgsRequest
	1,  // 38: tke.Kateway.CheckUpgrade:output_type -> tke.CheckUpgradeResponse
	3,  // 39: tke.Kateway.GetUser:output_type -> tke.GetUserResponse
	6,  // 40: tke.Kateway.GetCluster:output_type -> tke.GetClusterResponse
	10, // 41: tke.Kateway.GetCLB:output_type -> tke.GetCLBResponse
	10, // 42: tke.Kateway.GetListeners:output_type -> tke.GetCLBResponse
	10, // 43: tke.Kateway.GetBackends:output_type -> tke.GetCLBResponse
	10, // 44: tke.Kateway.GetHealth:output_type -> tke.GetCLBResponse
	29, // 45: tke.Kateway.CheckImage:output_type -> tke.CheckImageResponse
	13, // 46: tke.Kateway.GetPod:output_type -> tke.GetPodResponse
	15, // 47: tke.Kateway.GetDeploy:output_type -> tke.GetDeployResponse
	17, // 48: tke.Kateway.GetLeader:output_type -> tke.GetLeaderResponse
	19, // 49: tke.Kateway.GetConfig:output_type -> tke.GetConfigResponse
	21, // 50: tke.Kateway.Logs:output_type -> tke.LogsResponse
	25, // 51: tke.Kateway.YunAPI:output_type -> tke.YunAPIResponse
	27, // 52: tke.Kateway.Admin:output_type -> tke.AdminResponse
	31, // 53: tke.Kateway.SetImage:output_type -> tke.SetImageResponse
	33, // 54: tke.Kateway.Mock:output_type -> tke.MockResponse
	35, // 55: tke.Kateway.Update:output_type -> tke.UpdateResponse
	39, // 56: tke.Kateway.ListTask:output_type -> tke.ListTaskResponse
	37, // 57: tke.Kateway.GetTask:output_type -> tke.GetTaskResponse
	41, // 58: tke.Kateway.CreateTask:output_type -> tke.CreateTaskResponse
	43, // 59: tke.Kateway.UpdateArgs:output_type -> tke.ArgsResponse
	38, // [38:60] is the sub-list for method output_type
	16, // [16:38] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_kateway_proto_init() }
func file_kateway_proto_init() {
	if File_kateway_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_kateway_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   44,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_kateway_proto_goTypes,
		DependencyIndexes: file_kateway_proto_depIdxs,
		MessageInfos:      file_kateway_proto_msgTypes,
	}.Build()
	File_kateway_proto = out.File
	file_kateway_proto_rawDesc = nil
	file_kateway_proto_goTypes = nil
	file_kateway_proto_depIdxs = nil
}
