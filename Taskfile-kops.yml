version: 3

includes:
  tools:
    taskfile: https://raw.githubusercontent.com/QianChenglong/taskfile/main/tools/Taskfile.yml
    internal: true
  go:
    taskfile: https://raw.githubusercontent.com/QianChenglong/taskfile/main/go/Taskfile.yml
    internal: true
    vars:
      VERSION_PACKAGE: "git.woa.com/kateway/pkg/app/version"
  tag:
    taskfile: https://raw.githubusercontent.com/QianChenglong/taskfile/main/git/tag.yml
    internal: true
  docker:
    taskfile: https://raw.githubusercontent.com/QianChenglong/taskfile/main/docker/Taskfile.yml
    internal: true
    vars:
      REGISTRY_PREFIX: mirrors.tencent.com/kateway
  kubectl:
    taskfile: https://raw.githubusercontent.com/QianChenglong/taskfile/main/kubectl/Taskfile.yml
    internal: true
    vars:
      CONTEXT: '{{default "kateway.dev" .CONTEXT }}'
      NAMESPACE: '{{default "kube-system" .NAMESPACE }}'
      WORKLOAD: "deploy/{{.NAME}}"
      CONTAINER: "{{.NAME}}"

set:
  - nounset
  - errexit
  - pipefail

env:
  ENV:
    sh: echo ${ENV:-dev}

dotenv: [".env", "{{.ENV}}.env.", "{{.HOME}}/.env"]

vars:
  NAME: "kops"
  PACKAGE: "./cmd/kops/{{.NAME}}"

  BASE: mirrors.tencent.com/kateway/base:v1.0.0

  ARCHS: "amd64 arm64"
tasks:
  default:
    desc: "默认"
    cmds:
      - task: check
      - task: build

  init:
    desc: "项目初始化，安装依赖工具，初始化相关配置"
    cmds:
      - task: tools:init

  check:
    desc: "检查"
    cmds:
      - task: go:check

  build:
    desc: "编译"
    cmds:
      - task: go:build
        vars:
          OS: '{{default "linux" .OS}}'
          ARCH: '{{default "amd64" .ARCH}}'
          PACKAGE: "{{.PACKAGE}}"

  test:
    desc: "go:test"
    cmds:
      - task: go:test

  image:
    desc: "构建并推送镜像"
    cmds:
      - task: build
        vars:
          OS: '{{default "linux" .OS}}'
          ARCH: '{{default "amd64" .ARCH}}'
      - task: docker:build
        vars:
          NAME: "{{.NAME}}"
          TAG: "{{.VERSION}}"
          BASE: "{{.BASE}}"
          OS: "{{.OS}}"
          ARCH: "{{.ARCH}}"

  release:
    desc: "制作发布包"
    cmds:
      - for: {var: ARCHS, as: ARCH}
        task: image
        vars:
          OS: "{{.OS}}"
          ARCH: "{{.ARCH}}"

      - task: docker:manifest
        vars:
          NAME: "{{.NAME}}"
          TAG: "{{.VERSION}}"

  clean:
    desc: "go:clean"
    cmds:
      - task: go:clean
      - task: docker:clean
        vars:
          PATTERN: "{{.NAME}}"

  update:
    desc: "更新工作负载镜像"
    cmds:
      - task: kubectl:set-image
        vars:
          VERSION: "{{.VERSION}}"

  undo:
    desc: "回滚工作负载"
    cmds:
      - task: kubectl:undo

  tag:
    desc: "打tag"
    cmds:
      - task: tag:date

  untag:
    desc: "撤销最近版本"
    cmds:
      - task: tag:untag

  changelog:
    desc: "生成changelog"
    cmds:
      - task: tools:changelog
