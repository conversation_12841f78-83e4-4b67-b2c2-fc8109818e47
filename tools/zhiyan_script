#!/bin/bash

nowtime=$(date +'%Y-%m-%d %H:%M:%S')
OPS_LOCAP_IP=$(ifconfig eth1 | grep 'inet ' | awk '{print $2}')
echo [${nowtime}][PID:$$]@${OPS_LOCAP_IP}

###### 成功和失败的标准取决于最后一条执行语句的返回值，0为成功如exit 0，非0为失败如exit 1
###### 外部参数占位符形式：$var（建议三个字符以上），内部参数占位符形式：${var}
###### 可在此处开始编写您的脚本逻辑代码

set -o xtrace

if [[ "$kops_component" == "all" ]]; then
	kops service $kops_action --cluster=$kops_cluster --image=$kops_image --token=$kops_token --user=$kops_user
	kops ingress $kops_action --cluster=$kops_cluster --image=$kops_image --token=$kops_token --user=$kops_user
else
	kops $kops_component $kops_action --cluster=$kops_cluster --image=$kops_image --token=$kops_token --user=$kops_user
fi
