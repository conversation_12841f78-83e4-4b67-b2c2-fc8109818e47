package v1alpha1

import (
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type Master struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   MasterSpec   `json:"spec"`
	Status MasterStatus `json:"status"`
}

type MasterSpec struct {
	Version string `json:"version"`

	Etcd EtcdConfig `json:"etcd"`

	ImageRepository string `json:"imageRepository,omitempty"`

	ApiServer         ApiServerConfig         `json:"apiServer"`
	ControllerManager ControllerManagerConfig `json:"controllerManager"`
	Scheduler         SchedulerConfig         `json:"scheduler"`

	Network NetworkConfig `json:"network"`
}

type ApiServerConfig struct {
	TLS TLSConfig `json:"tls,omitempty"`

	AdvertiseAddress string `json:"advertiseAddress,omitempty"`

	SecurePort   int `json:"securePort"`
	InsecurePort int `json:"insecurePort"`

	AuditEnabled      bool                     `json:"auditEnabled,omitempty"`
	LogAgentImage     string                   `json:"logAgentImage,omitempty"`
	LogAgentResources *v1.ResourceRequirements `json:"logAgentResources,omitempty"`
	ComponentExtraConfig
}

type ControllerManagerConfig struct {
	ComponentExtraConfig
}

type SchedulerConfig struct {
	ComponentExtraConfig
}

type TLSConfig struct {
	ExternalCerts bool     `json:"externalCerts,omitempty"`
	ExtraSANs     []string `json:"apiserverExtraSans,omitempty"`
}

type NetworkConfig struct {
	ServiceCIDR      string `json:"serviceCidr"`
	PodCIDR          string `json:"podCidr"`
	NodeCIDRMaskSize int    `json:"nodeCidrMaskSize"`
	ClusterDomain    string `json:"clusterDomain"`
}

type EtcdConfig struct {
	Servers           []string `json:"servers"`
	ClientCertsSecret string   `json:"clientCertsSecrets,omitempty"`
}

type PodConfig struct {
	ImagePullSecrets []v1.LocalObjectReference `json:"imagePullSecrets,omitempty"`

	Labels      map[string]string `json:"labels,omitempty"`
	Annotations map[string]string `json:"annotations,omitempty"`

	InitContainers []v1.Container `json:"initContainers,omitempty"`
	Containers     []v1.Container `json:"containers,omitempty"`

	Affinity     *v1.Affinity      `json:"affinity,omitempty"`
	NodeSelector map[string]string `json:"nodeSelector,omitempty"`
	Tolerations  []v1.Toleration   `json:"tolerations,omitempty"`

	HostAliases []v1.HostAlias `json:"hostAliases,omitempty"`

	MinReadySeconds int32 `json:"minReadySeconds,omitempty"`
}

type ComponentExtraConfig struct {
	HyperKube      bool `json:"hyperKube,omitempty"`
	DisableGenArgs bool `json:"disableGenArgs"`

	ExtraArgs       []string         `json:"extraArgs,omitempty"`
	TrimArgs        []string         `json:"trimArgs,omitempty"`
	ExtraConfigMaps []ExtraConfigMap `json:"extraConfigMaps,omitempty"`
	ExtraSecrets    []ExtraSecret    `json:"secrets,omitempty"`

	Env []v1.EnvVar `json:"env,omitempty"`

	Replicas *int32 `json:"replicas,omitempty"`

	Image string `json:"image,omitempty"`

	Resources v1.ResourceRequirements `json:"resources,omitempty"`

	ReadinessProbe *v1.Probe `json:"readinessProbe,omitempty"`
	LivenessProbe  *v1.Probe `json:"livenessProbe,omitempty"`

	Lifecycle *v1.Lifecycle `json:"lifecycle,omitempty"`

	Pod PodConfig `json:"pod,omitempty"`
}

type ExtraConfigMap struct {
	Name    string `json:"name"`
	MountAt string `json:"mountAt"`
}

type ExtraSecret struct {
	Name    string `json:"name"`
	MountAt string `json:"mountAt"`
}

type MasterStatus struct {
	Phase           string   `json:"phase"`
	PhaseFinalizers []string `json:"phaseFinalizers"`
}

const (
	PhaseMasterCreating    = "creating"
	PhaseMasterRunning     = "running"
	PhaseMasterUpdating    = "updating"
	PhaseMasterUpgrading   = "upgrading"
	PhaseMasterRollbacking = "rollbacking"
)

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type MasterList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata"`

	Items []Master `json:"items"`
}

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type MasterSnapshot struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec MasterSpec `json:"spec,omitempty"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type MasterSnapshotList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`

	Items []MasterSnapshot `json:"items" protobuf:"bytes,2,rep,name=items"`
}
