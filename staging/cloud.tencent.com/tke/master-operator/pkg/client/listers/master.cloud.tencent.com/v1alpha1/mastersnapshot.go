/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by lister-gen. DO NOT EDIT.

package v1alpha1

import (
	v1alpha1 "cloud.tencent.com/tke/master-operator/pkg/apis/master.cloud.tencent.com/v1alpha1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
)

// MasterSnapshotLister helps list MasterSnapshots.
// All objects returned here must be treated as read-only.
type MasterSnapshotLister interface {
	// List lists all MasterSnapshots in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1alpha1.MasterSnapshot, err error)
	// MasterSnapshots returns an object that can list and get MasterSnapshots.
	MasterSnapshots(namespace string) MasterSnapshotNamespaceLister
	MasterSnapshotListerExpansion
}

// masterSnapshotLister implements the MasterSnapshotLister interface.
type masterSnapshotLister struct {
	indexer cache.Indexer
}

// NewMasterSnapshotLister returns a new MasterSnapshotLister.
func NewMasterSnapshotLister(indexer cache.Indexer) MasterSnapshotLister {
	return &masterSnapshotLister{indexer: indexer}
}

// List lists all MasterSnapshots in the indexer.
func (s *masterSnapshotLister) List(selector labels.Selector) (ret []*v1alpha1.MasterSnapshot, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1alpha1.MasterSnapshot))
	})
	return ret, err
}

// MasterSnapshots returns an object that can list and get MasterSnapshots.
func (s *masterSnapshotLister) MasterSnapshots(namespace string) MasterSnapshotNamespaceLister {
	return masterSnapshotNamespaceLister{indexer: s.indexer, namespace: namespace}
}

// MasterSnapshotNamespaceLister helps list and get MasterSnapshots.
// All objects returned here must be treated as read-only.
type MasterSnapshotNamespaceLister interface {
	// List lists all MasterSnapshots in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1alpha1.MasterSnapshot, err error)
	// Get retrieves the MasterSnapshot from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*v1alpha1.MasterSnapshot, error)
	MasterSnapshotNamespaceListerExpansion
}

// masterSnapshotNamespaceLister implements the MasterSnapshotNamespaceLister
// interface.
type masterSnapshotNamespaceLister struct {
	indexer   cache.Indexer
	namespace string
}

// List lists all MasterSnapshots in the indexer for a given namespace.
func (s masterSnapshotNamespaceLister) List(selector labels.Selector) (ret []*v1alpha1.MasterSnapshot, err error) {
	err = cache.ListAllByNamespace(s.indexer, s.namespace, selector, func(m interface{}) {
		ret = append(ret, m.(*v1alpha1.MasterSnapshot))
	})
	return ret, err
}

// Get retrieves the MasterSnapshot from the indexer for a given namespace and name.
func (s masterSnapshotNamespaceLister) Get(name string) (*v1alpha1.MasterSnapshot, error) {
	obj, exists, err := s.indexer.GetByKey(s.namespace + "/" + name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1alpha1.Resource("mastersnapshot"), name)
	}
	return obj.(*v1alpha1.MasterSnapshot), nil
}
