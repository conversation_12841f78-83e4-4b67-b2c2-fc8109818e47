/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	v1alpha1 "cloud.tencent.com/tke/master-operator/pkg/apis/master.cloud.tencent.com/v1alpha1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeMasterSnapshots implements MasterSnapshotInterface
type FakeMasterSnapshots struct {
	Fake *FakeMasterV1alpha1
	ns   string
}

var mastersnapshotsResource = schema.GroupVersionResource{Group: "master.cloud.tencent.com", Version: "v1alpha1", Resource: "mastersnapshots"}

var mastersnapshotsKind = schema.GroupVersionKind{Group: "master.cloud.tencent.com", Version: "v1alpha1", Kind: "MasterSnapshot"}

// Get takes name of the masterSnapshot, and returns the corresponding masterSnapshot object, and an error if there is any.
func (c *FakeMasterSnapshots) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1alpha1.MasterSnapshot, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewGetAction(mastersnapshotsResource, c.ns, name), &v1alpha1.MasterSnapshot{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha1.MasterSnapshot), err
}

// List takes label and field selectors, and returns the list of MasterSnapshots that match those selectors.
func (c *FakeMasterSnapshots) List(ctx context.Context, opts v1.ListOptions) (result *v1alpha1.MasterSnapshotList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewListAction(mastersnapshotsResource, mastersnapshotsKind, c.ns, opts), &v1alpha1.MasterSnapshotList{})

	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &v1alpha1.MasterSnapshotList{ListMeta: obj.(*v1alpha1.MasterSnapshotList).ListMeta}
	for _, item := range obj.(*v1alpha1.MasterSnapshotList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested masterSnapshots.
func (c *FakeMasterSnapshots) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchAction(mastersnapshotsResource, c.ns, opts))

}

// Create takes the representation of a masterSnapshot and creates it.  Returns the server's representation of the masterSnapshot, and an error, if there is any.
func (c *FakeMasterSnapshots) Create(ctx context.Context, masterSnapshot *v1alpha1.MasterSnapshot, opts v1.CreateOptions) (result *v1alpha1.MasterSnapshot, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewCreateAction(mastersnapshotsResource, c.ns, masterSnapshot), &v1alpha1.MasterSnapshot{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha1.MasterSnapshot), err
}

// Update takes the representation of a masterSnapshot and updates it. Returns the server's representation of the masterSnapshot, and an error, if there is any.
func (c *FakeMasterSnapshots) Update(ctx context.Context, masterSnapshot *v1alpha1.MasterSnapshot, opts v1.UpdateOptions) (result *v1alpha1.MasterSnapshot, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateAction(mastersnapshotsResource, c.ns, masterSnapshot), &v1alpha1.MasterSnapshot{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha1.MasterSnapshot), err
}

// Delete takes name of the masterSnapshot and deletes it. Returns an error if one occurs.
func (c *FakeMasterSnapshots) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteAction(mastersnapshotsResource, c.ns, name), &v1alpha1.MasterSnapshot{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeMasterSnapshots) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	action := testing.NewDeleteCollectionAction(mastersnapshotsResource, c.ns, listOpts)

	_, err := c.Fake.Invokes(action, &v1alpha1.MasterSnapshotList{})
	return err
}

// Patch applies the patch and returns the patched masterSnapshot.
func (c *FakeMasterSnapshots) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.MasterSnapshot, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceAction(mastersnapshotsResource, c.ns, name, pt, data, subresources...), &v1alpha1.MasterSnapshot{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha1.MasterSnapshot), err
}
