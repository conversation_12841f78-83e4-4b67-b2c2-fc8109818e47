/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	v1alpha1 "cloud.tencent.com/tke/master-operator/pkg/apis/master.cloud.tencent.com/v1alpha1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeMasters implements MasterInterface
type FakeMasters struct {
	Fake *FakeMasterV1alpha1
	ns   string
}

var mastersResource = schema.GroupVersionResource{Group: "master.cloud.tencent.com", Version: "v1alpha1", Resource: "masters"}

var mastersKind = schema.GroupVersionKind{Group: "master.cloud.tencent.com", Version: "v1alpha1", Kind: "Master"}

// Get takes name of the master, and returns the corresponding master object, and an error if there is any.
func (c *FakeMasters) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1alpha1.Master, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewGetAction(mastersResource, c.ns, name), &v1alpha1.Master{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha1.Master), err
}

// List takes label and field selectors, and returns the list of Masters that match those selectors.
func (c *FakeMasters) List(ctx context.Context, opts v1.ListOptions) (result *v1alpha1.MasterList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewListAction(mastersResource, mastersKind, c.ns, opts), &v1alpha1.MasterList{})

	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &v1alpha1.MasterList{ListMeta: obj.(*v1alpha1.MasterList).ListMeta}
	for _, item := range obj.(*v1alpha1.MasterList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested masters.
func (c *FakeMasters) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchAction(mastersResource, c.ns, opts))

}

// Create takes the representation of a master and creates it.  Returns the server's representation of the master, and an error, if there is any.
func (c *FakeMasters) Create(ctx context.Context, master *v1alpha1.Master, opts v1.CreateOptions) (result *v1alpha1.Master, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewCreateAction(mastersResource, c.ns, master), &v1alpha1.Master{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha1.Master), err
}

// Update takes the representation of a master and updates it. Returns the server's representation of the master, and an error, if there is any.
func (c *FakeMasters) Update(ctx context.Context, master *v1alpha1.Master, opts v1.UpdateOptions) (result *v1alpha1.Master, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateAction(mastersResource, c.ns, master), &v1alpha1.Master{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha1.Master), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeMasters) UpdateStatus(ctx context.Context, master *v1alpha1.Master, opts v1.UpdateOptions) (*v1alpha1.Master, error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateSubresourceAction(mastersResource, "status", c.ns, master), &v1alpha1.Master{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha1.Master), err
}

// Delete takes name of the master and deletes it. Returns an error if one occurs.
func (c *FakeMasters) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteAction(mastersResource, c.ns, name), &v1alpha1.Master{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeMasters) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	action := testing.NewDeleteCollectionAction(mastersResource, c.ns, listOpts)

	_, err := c.Fake.Invokes(action, &v1alpha1.MasterList{})
	return err
}

// Patch applies the patch and returns the patched master.
func (c *FakeMasters) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.Master, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceAction(mastersResource, c.ns, name, pt, data, subresources...), &v1alpha1.Master{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha1.Master), err
}
