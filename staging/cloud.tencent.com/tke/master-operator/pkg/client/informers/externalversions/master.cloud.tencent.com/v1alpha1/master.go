/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by informer-gen. DO NOT EDIT.

package v1alpha1

import (
	"context"
	time "time"

	mastercloudtencentcomv1alpha1 "cloud.tencent.com/tke/master-operator/pkg/apis/master.cloud.tencent.com/v1alpha1"
	versioned "cloud.tencent.com/tke/master-operator/pkg/client/clientset/versioned"
	internalinterfaces "cloud.tencent.com/tke/master-operator/pkg/client/informers/externalversions/internalinterfaces"
	v1alpha1 "cloud.tencent.com/tke/master-operator/pkg/client/listers/master.cloud.tencent.com/v1alpha1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
	watch "k8s.io/apimachinery/pkg/watch"
	cache "k8s.io/client-go/tools/cache"
)

// MasterInformer provides access to a shared informer and lister for
// Masters.
type MasterInformer interface {
	Informer() cache.SharedIndexInformer
	Lister() v1alpha1.MasterLister
}

type masterInformer struct {
	factory          internalinterfaces.SharedInformerFactory
	tweakListOptions internalinterfaces.TweakListOptionsFunc
	namespace        string
}

// NewMasterInformer constructs a new informer for Master type.
// Always prefer using an informer factory to get a shared informer instead of getting an independent
// one. This reduces memory footprint and number of connections to the server.
func NewMasterInformer(client versioned.Interface, namespace string, resyncPeriod time.Duration, indexers cache.Indexers) cache.SharedIndexInformer {
	return NewFilteredMasterInformer(client, namespace, resyncPeriod, indexers, nil)
}

// NewFilteredMasterInformer constructs a new informer for Master type.
// Always prefer using an informer factory to get a shared informer instead of getting an independent
// one. This reduces memory footprint and number of connections to the server.
func NewFilteredMasterInformer(client versioned.Interface, namespace string, resyncPeriod time.Duration, indexers cache.Indexers, tweakListOptions internalinterfaces.TweakListOptionsFunc) cache.SharedIndexInformer {
	return cache.NewSharedIndexInformer(
		&cache.ListWatch{
			ListFunc: func(options v1.ListOptions) (runtime.Object, error) {
				if tweakListOptions != nil {
					tweakListOptions(&options)
				}
				return client.MasterV1alpha1().Masters(namespace).List(context.TODO(), options)
			},
			WatchFunc: func(options v1.ListOptions) (watch.Interface, error) {
				if tweakListOptions != nil {
					tweakListOptions(&options)
				}
				return client.MasterV1alpha1().Masters(namespace).Watch(context.TODO(), options)
			},
		},
		&mastercloudtencentcomv1alpha1.Master{},
		resyncPeriod,
		indexers,
	)
}

func (f *masterInformer) defaultInformer(client versioned.Interface, resyncPeriod time.Duration) cache.SharedIndexInformer {
	return NewFilteredMasterInformer(client, f.namespace, resyncPeriod, cache.Indexers{cache.NamespaceIndex: cache.MetaNamespaceIndexFunc}, f.tweakListOptions)
}

func (f *masterInformer) Informer() cache.SharedIndexInformer {
	return f.factory.InformerFor(&mastercloudtencentcomv1alpha1.Master{}, f.defaultInformer)
}

func (f *masterInformer) Lister() v1alpha1.MasterLister {
	return v1alpha1.NewMasterLister(f.Informer().GetIndexer())
}
