/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by lister-gen. DO NOT EDIT.

package v1

// AddonTypeListerExpansion allows custom methods to be added to
// AddonTypeLister.
type AddonTypeListerExpansion interface{}

// CBSListerExpansion allows custom methods to be added to
// CBSLister.
type CBSListerExpansion interface{}

// CFSListerExpansion allows custom methods to be added to
// CFSLister.
type CFSListerExpansion interface{}

// COSListerExpansion allows custom methods to be added to
// COSLister.
type COSListerExpansion interface{}

// ClusterListerExpansion allows custom methods to be added to
// ClusterLister.
type ClusterListerExpansion interface{}

// ClusterAPIResourceListerExpansion allows custom methods to be added to
// ClusterAPIResourceLister.
type ClusterAPIResourceListerExpansion interface{}

// ClusterAddonListerExpansion allows custom methods to be added to
// ClusterAddonLister.
type ClusterAddonListerExpansion interface{}

// ClusterAddonKindListerExpansion allows custom methods to be added to
// ClusterAddonKindLister.
type ClusterAddonKindListerExpansion interface{}

// ClusterAuthenticationListerExpansion allows custom methods to be added to
// ClusterAuthenticationLister.
type ClusterAuthenticationListerExpansion interface{}

// ClusterAuthenticationNamespaceListerExpansion allows custom methods to be added to
// ClusterAuthenticationNamespaceLister.
type ClusterAuthenticationNamespaceListerExpansion interface{}

// ConfigMapListerExpansion allows custom methods to be added to
// ConfigMapLister.
type ConfigMapListerExpansion interface{}

// DNSAutoscalerListerExpansion allows custom methods to be added to
// DNSAutoscalerLister.
type DNSAutoscalerListerExpansion interface{}

// DeSchedulerListerExpansion allows custom methods to be added to
// DeSchedulerLister.
type DeSchedulerListerExpansion interface{}

// DynamicSchedulerListerExpansion allows custom methods to be added to
// DynamicSchedulerLister.
type DynamicSchedulerListerExpansion interface{}

// EniIpamdListerExpansion allows custom methods to be added to
// EniIpamdLister.
type EniIpamdListerExpansion interface{}

// EnvironmentListerExpansion allows custom methods to be added to
// EnvironmentLister.
type EnvironmentListerExpansion interface{}

// GPUManagerListerExpansion allows custom methods to be added to
// GPUManagerLister.
type GPUManagerListerExpansion interface{}

// GameAppListerExpansion allows custom methods to be added to
// GameAppLister.
type GameAppListerExpansion interface{}

// HPCListerExpansion allows custom methods to be added to
// HPCLister.
type HPCListerExpansion interface{}

// HelmListerExpansion allows custom methods to be added to
// HelmLister.
type HelmListerExpansion interface{}

// ImageP2PListerExpansion allows custom methods to be added to
// ImageP2PLister.
type ImageP2PListerExpansion interface{}

// LBCFListerExpansion allows custom methods to be added to
// LBCFLister.
type LBCFListerExpansion interface{}

// LogCollectorListerExpansion allows custom methods to be added to
// LogCollectorLister.
type LogCollectorListerExpansion interface{}

// NamespaceSetListerExpansion allows custom methods to be added to
// NamespaceSetLister.
type NamespaceSetListerExpansion interface{}

// NetworkPolicyListerExpansion allows custom methods to be added to
// NetworkPolicyLister.
type NetworkPolicyListerExpansion interface{}

// NginxIngressListerExpansion allows custom methods to be added to
// NginxIngressLister.
type NginxIngressListerExpansion interface{}

// NodeLocalDNSCacheListerExpansion allows custom methods to be added to
// NodeLocalDNSCacheLister.
type NodeLocalDNSCacheListerExpansion interface{}

// NodeProblemDetectorListerExpansion allows custom methods to be added to
// NodeProblemDetectorLister.
type NodeProblemDetectorListerExpansion interface{}

// OLMListerExpansion allows custom methods to be added to
// OLMLister.
type OLMListerExpansion interface{}

// OOMGuardListerExpansion allows custom methods to be added to
// OOMGuardLister.
type OOMGuardListerExpansion interface{}

// PersistentEventListerExpansion allows custom methods to be added to
// PersistentEventLister.
type PersistentEventListerExpansion interface{}

// ProjectListerExpansion allows custom methods to be added to
// ProjectLister.
type ProjectListerExpansion interface{}

// QGPUListerExpansion allows custom methods to be added to
// QGPULister.
type QGPUListerExpansion interface{}

// RegistryListerExpansion allows custom methods to be added to
// RegistryLister.
type RegistryListerExpansion interface{}

// TcrListerExpansion allows custom methods to be added to
// TcrLister.
type TcrListerExpansion interface{}
