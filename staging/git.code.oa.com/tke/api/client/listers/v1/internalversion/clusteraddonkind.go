/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by lister-gen. DO NOT EDIT.

package internalversion

import (
	v1 "git.code.oa.com/tke/api/platform/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
)

// ClusterAddonKindLister helps list ClusterAddonKinds.
// All objects returned here must be treated as read-only.
type ClusterAddonKindLister interface {
	// List lists all ClusterAddonKinds in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1.ClusterAddonKind, err error)
	// Get retrieves the ClusterAddonKind from the index for a given name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*v1.ClusterAddonKind, error)
	ClusterAddonKindListerExpansion
}

// clusterAddonKindLister implements the ClusterAddonKindLister interface.
type clusterAddonKindLister struct {
	indexer cache.Indexer
}

// NewClusterAddonKindLister returns a new ClusterAddonKindLister.
func NewClusterAddonKindLister(indexer cache.Indexer) ClusterAddonKindLister {
	return &clusterAddonKindLister{indexer: indexer}
}

// List lists all ClusterAddonKinds in the indexer.
func (s *clusterAddonKindLister) List(selector labels.Selector) (ret []*v1.ClusterAddonKind, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1.ClusterAddonKind))
	})
	return ret, err
}

// Get retrieves the ClusterAddonKind from the index for a given name.
func (s *clusterAddonKindLister) Get(name string) (*v1.ClusterAddonKind, error) {
	obj, exists, err := s.indexer.GetByKey(name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1.Resource("clusteraddonkind"), name)
	}
	return obj.(*v1.ClusterAddonKind), nil
}
