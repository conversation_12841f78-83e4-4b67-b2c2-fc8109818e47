/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by lister-gen. DO NOT EDIT.

package internalversion

import (
	v1 "git.code.oa.com/tke/api/platform/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
)

// OOMGuardLister helps list OOMGuards.
// All objects returned here must be treated as read-only.
type OOMGuardLister interface {
	// List lists all OOMGuards in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1.OOMGuard, err error)
	// Get retrieves the OOMGuard from the index for a given name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*v1.OOMGuard, error)
	OOMGuardListerExpansion
}

// oOMGuardLister implements the OOMGuardLister interface.
type oOMGuardLister struct {
	indexer cache.Indexer
}

// NewOOMGuardLister returns a new OOMGuardLister.
func NewOOMGuardLister(indexer cache.Indexer) OOMGuardLister {
	return &oOMGuardLister{indexer: indexer}
}

// List lists all OOMGuards in the indexer.
func (s *oOMGuardLister) List(selector labels.Selector) (ret []*v1.OOMGuard, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1.OOMGuard))
	})
	return ret, err
}

// Get retrieves the OOMGuard from the index for a given name.
func (s *oOMGuardLister) Get(name string) (*v1.OOMGuard, error) {
	obj, exists, err := s.indexer.GetByKey(name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1.Resource("oomguard"), name)
	}
	return obj.(*v1.OOMGuard), nil
}
