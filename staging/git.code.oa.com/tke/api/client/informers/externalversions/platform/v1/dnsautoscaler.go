/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by informer-gen. DO NOT EDIT.

package v1

import (
	"context"
	time "time"

	versioned "git.code.oa.com/tke/api/client/clientset/versioned"
	internalinterfaces "git.code.oa.com/tke/api/client/informers/externalversions/internalinterfaces"
	v1 "git.code.oa.com/tke/api/client/listers/platform/v1"
	platformv1 "git.code.oa.com/tke/api/platform/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
	watch "k8s.io/apimachinery/pkg/watch"
	cache "k8s.io/client-go/tools/cache"
)

// DNSAutoscalerInformer provides access to a shared informer and lister for
// DNSAutoscalers.
type DNSAutoscalerInformer interface {
	Informer() cache.SharedIndexInformer
	Lister() v1.DNSAutoscalerLister
}

type dNSAutoscalerInformer struct {
	factory          internalinterfaces.SharedInformerFactory
	tweakListOptions internalinterfaces.TweakListOptionsFunc
}

// NewDNSAutoscalerInformer constructs a new informer for DNSAutoscaler type.
// Always prefer using an informer factory to get a shared informer instead of getting an independent
// one. This reduces memory footprint and number of connections to the server.
func NewDNSAutoscalerInformer(client versioned.Interface, resyncPeriod time.Duration, indexers cache.Indexers) cache.SharedIndexInformer {
	return NewFilteredDNSAutoscalerInformer(client, resyncPeriod, indexers, nil)
}

// NewFilteredDNSAutoscalerInformer constructs a new informer for DNSAutoscaler type.
// Always prefer using an informer factory to get a shared informer instead of getting an independent
// one. This reduces memory footprint and number of connections to the server.
func NewFilteredDNSAutoscalerInformer(client versioned.Interface, resyncPeriod time.Duration, indexers cache.Indexers, tweakListOptions internalinterfaces.TweakListOptionsFunc) cache.SharedIndexInformer {
	return cache.NewSharedIndexInformer(
		&cache.ListWatch{
			ListFunc: func(options metav1.ListOptions) (runtime.Object, error) {
				if tweakListOptions != nil {
					tweakListOptions(&options)
				}
				return client.PlatformV1().DNSAutoscalers().List(context.TODO(), options)
			},
			WatchFunc: func(options metav1.ListOptions) (watch.Interface, error) {
				if tweakListOptions != nil {
					tweakListOptions(&options)
				}
				return client.PlatformV1().DNSAutoscalers().Watch(context.TODO(), options)
			},
		},
		&platformv1.DNSAutoscaler{},
		resyncPeriod,
		indexers,
	)
}

func (f *dNSAutoscalerInformer) defaultInformer(client versioned.Interface, resyncPeriod time.Duration) cache.SharedIndexInformer {
	return NewFilteredDNSAutoscalerInformer(client, resyncPeriod, cache.Indexers{cache.NamespaceIndex: cache.MetaNamespaceIndexFunc}, f.tweakListOptions)
}

func (f *dNSAutoscalerInformer) Informer() cache.SharedIndexInformer {
	return f.factory.InformerFor(&platformv1.DNSAutoscaler{}, f.defaultInformer)
}

func (f *dNSAutoscalerInformer) Lister() v1.DNSAutoscalerLister {
	return v1.NewDNSAutoscalerLister(f.Informer().GetIndexer())
}
