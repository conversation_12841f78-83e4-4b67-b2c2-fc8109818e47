/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	scheme "git.code.oa.com/tke/api/client/clientset/versioned/scheme"
	v1 "git.code.oa.com/tke/api/platform/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// QGPUsGetter has a method to return a QGPUInterface.
// A group's client should implement this interface.
type QGPUsGetter interface {
	QGPUs() QGPUInterface
}

// QGPUInterface has methods to work with QGPU resources.
type QGPUInterface interface {
	Create(ctx context.Context, qGPU *v1.QGPU, opts metav1.CreateOptions) (*v1.QGPU, error)
	Update(ctx context.Context, qGPU *v1.QGPU, opts metav1.UpdateOptions) (*v1.QGPU, error)
	UpdateStatus(ctx context.Context, qGPU *v1.QGPU, opts metav1.UpdateOptions) (*v1.QGPU, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*v1.QGPU, error)
	List(ctx context.Context, opts metav1.ListOptions) (*v1.QGPUList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.QGPU, err error)
	QGPUExpansion
}

// qGPUs implements QGPUInterface
type qGPUs struct {
	client rest.Interface
}

// newQGPUs returns a QGPUs
func newQGPUs(c *PlatformV1Client) *qGPUs {
	return &qGPUs{
		client: c.RESTClient(),
	}
}

// Get takes name of the qGPU, and returns the corresponding qGPU object, and an error if there is any.
func (c *qGPUs) Get(ctx context.Context, name string, options metav1.GetOptions) (result *v1.QGPU, err error) {
	result = &v1.QGPU{}
	err = c.client.Get().
		Resource("qgpus").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of QGPUs that match those selectors.
func (c *qGPUs) List(ctx context.Context, opts metav1.ListOptions) (result *v1.QGPUList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1.QGPUList{}
	err = c.client.Get().
		Resource("qgpus").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested qGPUs.
func (c *qGPUs) Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Resource("qgpus").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a qGPU and creates it.  Returns the server's representation of the qGPU, and an error, if there is any.
func (c *qGPUs) Create(ctx context.Context, qGPU *v1.QGPU, opts metav1.CreateOptions) (result *v1.QGPU, err error) {
	result = &v1.QGPU{}
	err = c.client.Post().
		Resource("qgpus").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(qGPU).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a qGPU and updates it. Returns the server's representation of the qGPU, and an error, if there is any.
func (c *qGPUs) Update(ctx context.Context, qGPU *v1.QGPU, opts metav1.UpdateOptions) (result *v1.QGPU, err error) {
	result = &v1.QGPU{}
	err = c.client.Put().
		Resource("qgpus").
		Name(qGPU.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(qGPU).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *qGPUs) UpdateStatus(ctx context.Context, qGPU *v1.QGPU, opts metav1.UpdateOptions) (result *v1.QGPU, err error) {
	result = &v1.QGPU{}
	err = c.client.Put().
		Resource("qgpus").
		Name(qGPU.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(qGPU).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the qGPU and deletes it. Returns an error if one occurs.
func (c *qGPUs) Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error {
	return c.client.Delete().
		Resource("qgpus").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched qGPU.
func (c *qGPUs) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.QGPU, err error) {
	result = &v1.QGPU{}
	err = c.client.Patch(pt).
		Resource("qgpus").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
