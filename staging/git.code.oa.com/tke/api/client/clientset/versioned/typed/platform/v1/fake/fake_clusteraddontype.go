/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	testing "k8s.io/client-go/testing"
)

// FakeClusterAddonTypes implements ClusterAddonTypeInterface
type FakeClusterAddonTypes struct {
	Fake *FakePlatformV1
}

var clusteraddontypesResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "clusteraddontypes"}

var clusteraddontypesKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "ClusterAddonType"}

// List takes label and field selectors, and returns the list of ClusterAddonTypes that match those selectors.
func (c *FakeClusterAddonTypes) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.ClusterAddonTypeList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(clusteraddontypesResource, clusteraddontypesKind, opts), &platformv1.ClusterAddonTypeList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.ClusterAddonTypeList{ListMeta: obj.(*platformv1.ClusterAddonTypeList).ListMeta}
	for _, item := range obj.(*platformv1.ClusterAddonTypeList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}
