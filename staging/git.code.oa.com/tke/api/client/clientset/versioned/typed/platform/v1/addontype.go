/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	scheme "git.code.oa.com/tke/api/client/clientset/versioned/scheme"
	v1 "git.code.oa.com/tke/api/platform/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// AddonTypesGetter has a method to return a AddonTypeInterface.
// A group's client should implement this interface.
type AddonTypesGetter interface {
	AddonTypes() AddonTypeInterface
}

// AddonTypeInterface has methods to work with AddonType resources.
type AddonTypeInterface interface {
	Create(ctx context.Context, addonType *v1.AddonType, opts metav1.CreateOptions) (*v1.AddonType, error)
	Update(ctx context.Context, addonType *v1.AddonType, opts metav1.UpdateOptions) (*v1.AddonType, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*v1.AddonType, error)
	List(ctx context.Context, opts metav1.ListOptions) (*v1.AddonTypeList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.AddonType, err error)
	AddonTypeExpansion
}

// addonTypes implements AddonTypeInterface
type addonTypes struct {
	client rest.Interface
}

// newAddonTypes returns a AddonTypes
func newAddonTypes(c *PlatformV1Client) *addonTypes {
	return &addonTypes{
		client: c.RESTClient(),
	}
}

// Get takes name of the addonType, and returns the corresponding addonType object, and an error if there is any.
func (c *addonTypes) Get(ctx context.Context, name string, options metav1.GetOptions) (result *v1.AddonType, err error) {
	result = &v1.AddonType{}
	err = c.client.Get().
		Resource("addontypes").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of AddonTypes that match those selectors.
func (c *addonTypes) List(ctx context.Context, opts metav1.ListOptions) (result *v1.AddonTypeList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1.AddonTypeList{}
	err = c.client.Get().
		Resource("addontypes").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested addonTypes.
func (c *addonTypes) Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Resource("addontypes").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a addonType and creates it.  Returns the server's representation of the addonType, and an error, if there is any.
func (c *addonTypes) Create(ctx context.Context, addonType *v1.AddonType, opts metav1.CreateOptions) (result *v1.AddonType, err error) {
	result = &v1.AddonType{}
	err = c.client.Post().
		Resource("addontypes").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(addonType).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a addonType and updates it. Returns the server's representation of the addonType, and an error, if there is any.
func (c *addonTypes) Update(ctx context.Context, addonType *v1.AddonType, opts metav1.UpdateOptions) (result *v1.AddonType, err error) {
	result = &v1.AddonType{}
	err = c.client.Put().
		Resource("addontypes").
		Name(addonType.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(addonType).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the addonType and deletes it. Returns an error if one occurs.
func (c *addonTypes) Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error {
	return c.client.Delete().
		Resource("addontypes").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched addonType.
func (c *addonTypes) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.AddonType, err error) {
	result = &v1.AddonType{}
	err = c.client.Patch(pt).
		Resource("addontypes").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
