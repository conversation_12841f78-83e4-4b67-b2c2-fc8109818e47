/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeClusterAddonKinds implements ClusterAddonKindInterface
type FakeClusterAddonKinds struct {
	Fake *FakePlatformV1
}

var clusteraddonkindsResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "clusteraddonkinds"}

var clusteraddonkindsKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "ClusterAddonKind"}

// Get takes name of the clusterAddonKind, and returns the corresponding clusterAddonKind object, and an error if there is any.
func (c *FakeClusterAddonKinds) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.ClusterAddonKind, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(clusteraddonkindsResource, name), &platformv1.ClusterAddonKind{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.ClusterAddonKind), err
}

// List takes label and field selectors, and returns the list of ClusterAddonKinds that match those selectors.
func (c *FakeClusterAddonKinds) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.ClusterAddonKindList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(clusteraddonkindsResource, clusteraddonkindsKind, opts), &platformv1.ClusterAddonKindList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.ClusterAddonKindList{ListMeta: obj.(*platformv1.ClusterAddonKindList).ListMeta}
	for _, item := range obj.(*platformv1.ClusterAddonKindList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested clusterAddonKinds.
func (c *FakeClusterAddonKinds) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(clusteraddonkindsResource, opts))
}

// Create takes the representation of a clusterAddonKind and creates it.  Returns the server's representation of the clusterAddonKind, and an error, if there is any.
func (c *FakeClusterAddonKinds) Create(ctx context.Context, clusterAddonKind *platformv1.ClusterAddonKind, opts v1.CreateOptions) (result *platformv1.ClusterAddonKind, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(clusteraddonkindsResource, clusterAddonKind), &platformv1.ClusterAddonKind{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.ClusterAddonKind), err
}

// Update takes the representation of a clusterAddonKind and updates it. Returns the server's representation of the clusterAddonKind, and an error, if there is any.
func (c *FakeClusterAddonKinds) Update(ctx context.Context, clusterAddonKind *platformv1.ClusterAddonKind, opts v1.UpdateOptions) (result *platformv1.ClusterAddonKind, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(clusteraddonkindsResource, clusterAddonKind), &platformv1.ClusterAddonKind{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.ClusterAddonKind), err
}

// Delete takes name of the clusterAddonKind and deletes it. Returns an error if one occurs.
func (c *FakeClusterAddonKinds) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(clusteraddonkindsResource, name), &platformv1.ClusterAddonKind{})
	return err
}

// Patch applies the patch and returns the patched clusterAddonKind.
func (c *FakeClusterAddonKinds) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.ClusterAddonKind, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(clusteraddonkindsResource, name, pt, data, subresources...), &platformv1.ClusterAddonKind{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.ClusterAddonKind), err
}
