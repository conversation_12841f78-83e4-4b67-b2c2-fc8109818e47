/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeQGPUs implements QGPUInterface
type FakeQGPUs struct {
	Fake *FakePlatformV1
}

var qgpusResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "qgpus"}

var qgpusKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "QGPU"}

// Get takes name of the qGPU, and returns the corresponding qGPU object, and an error if there is any.
func (c *FakeQGPUs) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.QGPU, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(qgpusResource, name), &platformv1.QGPU{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.QGPU), err
}

// List takes label and field selectors, and returns the list of QGPUs that match those selectors.
func (c *FakeQGPUs) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.QGPUList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(qgpusResource, qgpusKind, opts), &platformv1.QGPUList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.QGPUList{ListMeta: obj.(*platformv1.QGPUList).ListMeta}
	for _, item := range obj.(*platformv1.QGPUList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested qGPUs.
func (c *FakeQGPUs) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(qgpusResource, opts))
}

// Create takes the representation of a qGPU and creates it.  Returns the server's representation of the qGPU, and an error, if there is any.
func (c *FakeQGPUs) Create(ctx context.Context, qGPU *platformv1.QGPU, opts v1.CreateOptions) (result *platformv1.QGPU, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(qgpusResource, qGPU), &platformv1.QGPU{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.QGPU), err
}

// Update takes the representation of a qGPU and updates it. Returns the server's representation of the qGPU, and an error, if there is any.
func (c *FakeQGPUs) Update(ctx context.Context, qGPU *platformv1.QGPU, opts v1.UpdateOptions) (result *platformv1.QGPU, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(qgpusResource, qGPU), &platformv1.QGPU{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.QGPU), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeQGPUs) UpdateStatus(ctx context.Context, qGPU *platformv1.QGPU, opts v1.UpdateOptions) (*platformv1.QGPU, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(qgpusResource, "status", qGPU), &platformv1.QGPU{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.QGPU), err
}

// Delete takes name of the qGPU and deletes it. Returns an error if one occurs.
func (c *FakeQGPUs) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(qgpusResource, name), &platformv1.QGPU{})
	return err
}

// Patch applies the patch and returns the patched qGPU.
func (c *FakeQGPUs) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.QGPU, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(qgpusResource, name, pt, data, subresources...), &platformv1.QGPU{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.QGPU), err
}
