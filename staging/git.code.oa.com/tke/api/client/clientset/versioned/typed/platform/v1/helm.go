/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	scheme "git.code.oa.com/tke/api/client/clientset/versioned/scheme"
	v1 "git.code.oa.com/tke/api/platform/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// HelmsGetter has a method to return a HelmInterface.
// A group's client should implement this interface.
type HelmsGetter interface {
	Helms() HelmInterface
}

// HelmInterface has methods to work with Helm resources.
type HelmInterface interface {
	Create(ctx context.Context, helm *v1.Helm, opts metav1.CreateOptions) (*v1.Helm, error)
	Update(ctx context.Context, helm *v1.Helm, opts metav1.UpdateOptions) (*v1.Helm, error)
	UpdateStatus(ctx context.Context, helm *v1.Helm, opts metav1.UpdateOptions) (*v1.Helm, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*v1.Helm, error)
	List(ctx context.Context, opts metav1.ListOptions) (*v1.HelmList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.Helm, err error)
	HelmExpansion
}

// helms implements HelmInterface
type helms struct {
	client rest.Interface
}

// newHelms returns a Helms
func newHelms(c *PlatformV1Client) *helms {
	return &helms{
		client: c.RESTClient(),
	}
}

// Get takes name of the helm, and returns the corresponding helm object, and an error if there is any.
func (c *helms) Get(ctx context.Context, name string, options metav1.GetOptions) (result *v1.Helm, err error) {
	result = &v1.Helm{}
	err = c.client.Get().
		Resource("helms").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of Helms that match those selectors.
func (c *helms) List(ctx context.Context, opts metav1.ListOptions) (result *v1.HelmList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1.HelmList{}
	err = c.client.Get().
		Resource("helms").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested helms.
func (c *helms) Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Resource("helms").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a helm and creates it.  Returns the server's representation of the helm, and an error, if there is any.
func (c *helms) Create(ctx context.Context, helm *v1.Helm, opts metav1.CreateOptions) (result *v1.Helm, err error) {
	result = &v1.Helm{}
	err = c.client.Post().
		Resource("helms").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(helm).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a helm and updates it. Returns the server's representation of the helm, and an error, if there is any.
func (c *helms) Update(ctx context.Context, helm *v1.Helm, opts metav1.UpdateOptions) (result *v1.Helm, err error) {
	result = &v1.Helm{}
	err = c.client.Put().
		Resource("helms").
		Name(helm.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(helm).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *helms) UpdateStatus(ctx context.Context, helm *v1.Helm, opts metav1.UpdateOptions) (result *v1.Helm, err error) {
	result = &v1.Helm{}
	err = c.client.Put().
		Resource("helms").
		Name(helm.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(helm).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the helm and deletes it. Returns an error if one occurs.
func (c *helms) Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error {
	return c.client.Delete().
		Resource("helms").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched helm.
func (c *helms) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.Helm, err error) {
	result = &v1.Helm{}
	err = c.client.Patch(pt).
		Resource("helms").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
