/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeNodeProblemDetectors implements NodeProblemDetectorInterface
type FakeNodeProblemDetectors struct {
	Fake *FakePlatformV1
}

var nodeproblemdetectorsResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "nodeproblemdetectors"}

var nodeproblemdetectorsKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "NodeProblemDetector"}

// Get takes name of the nodeProblemDetector, and returns the corresponding nodeProblemDetector object, and an error if there is any.
func (c *FakeNodeProblemDetectors) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.NodeProblemDetector, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(nodeproblemdetectorsResource, name), &platformv1.NodeProblemDetector{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NodeProblemDetector), err
}

// List takes label and field selectors, and returns the list of NodeProblemDetectors that match those selectors.
func (c *FakeNodeProblemDetectors) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.NodeProblemDetectorList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(nodeproblemdetectorsResource, nodeproblemdetectorsKind, opts), &platformv1.NodeProblemDetectorList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.NodeProblemDetectorList{ListMeta: obj.(*platformv1.NodeProblemDetectorList).ListMeta}
	for _, item := range obj.(*platformv1.NodeProblemDetectorList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested nodeProblemDetectors.
func (c *FakeNodeProblemDetectors) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(nodeproblemdetectorsResource, opts))
}

// Create takes the representation of a nodeProblemDetector and creates it.  Returns the server's representation of the nodeProblemDetector, and an error, if there is any.
func (c *FakeNodeProblemDetectors) Create(ctx context.Context, nodeProblemDetector *platformv1.NodeProblemDetector, opts v1.CreateOptions) (result *platformv1.NodeProblemDetector, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(nodeproblemdetectorsResource, nodeProblemDetector), &platformv1.NodeProblemDetector{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NodeProblemDetector), err
}

// Update takes the representation of a nodeProblemDetector and updates it. Returns the server's representation of the nodeProblemDetector, and an error, if there is any.
func (c *FakeNodeProblemDetectors) Update(ctx context.Context, nodeProblemDetector *platformv1.NodeProblemDetector, opts v1.UpdateOptions) (result *platformv1.NodeProblemDetector, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(nodeproblemdetectorsResource, nodeProblemDetector), &platformv1.NodeProblemDetector{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NodeProblemDetector), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeNodeProblemDetectors) UpdateStatus(ctx context.Context, nodeProblemDetector *platformv1.NodeProblemDetector, opts v1.UpdateOptions) (*platformv1.NodeProblemDetector, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(nodeproblemdetectorsResource, "status", nodeProblemDetector), &platformv1.NodeProblemDetector{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NodeProblemDetector), err
}

// Delete takes name of the nodeProblemDetector and deletes it. Returns an error if one occurs.
func (c *FakeNodeProblemDetectors) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(nodeproblemdetectorsResource, name), &platformv1.NodeProblemDetector{})
	return err
}

// Patch applies the patch and returns the patched nodeProblemDetector.
func (c *FakeNodeProblemDetectors) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.NodeProblemDetector, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(nodeproblemdetectorsResource, name, pt, data, subresources...), &platformv1.NodeProblemDetector{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NodeProblemDetector), err
}
