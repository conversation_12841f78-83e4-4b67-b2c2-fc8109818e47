/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeTcrs implements TcrInterface
type FakeTcrs struct {
	Fake *FakePlatformV1
}

var tcrsResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "tcrs"}

var tcrsKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "Tcr"}

// Get takes name of the tcr, and returns the corresponding tcr object, and an error if there is any.
func (c *FakeTcrs) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.Tcr, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(tcrsResource, name), &platformv1.Tcr{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.Tcr), err
}

// List takes label and field selectors, and returns the list of Tcrs that match those selectors.
func (c *FakeTcrs) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.TcrList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(tcrsResource, tcrsKind, opts), &platformv1.TcrList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.TcrList{ListMeta: obj.(*platformv1.TcrList).ListMeta}
	for _, item := range obj.(*platformv1.TcrList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested tcrs.
func (c *FakeTcrs) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(tcrsResource, opts))
}

// Create takes the representation of a tcr and creates it.  Returns the server's representation of the tcr, and an error, if there is any.
func (c *FakeTcrs) Create(ctx context.Context, tcr *platformv1.Tcr, opts v1.CreateOptions) (result *platformv1.Tcr, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(tcrsResource, tcr), &platformv1.Tcr{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.Tcr), err
}

// Update takes the representation of a tcr and updates it. Returns the server's representation of the tcr, and an error, if there is any.
func (c *FakeTcrs) Update(ctx context.Context, tcr *platformv1.Tcr, opts v1.UpdateOptions) (result *platformv1.Tcr, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(tcrsResource, tcr), &platformv1.Tcr{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.Tcr), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeTcrs) UpdateStatus(ctx context.Context, tcr *platformv1.Tcr, opts v1.UpdateOptions) (*platformv1.Tcr, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(tcrsResource, "status", tcr), &platformv1.Tcr{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.Tcr), err
}

// Delete takes name of the tcr and deletes it. Returns an error if one occurs.
func (c *FakeTcrs) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(tcrsResource, name), &platformv1.Tcr{})
	return err
}

// Patch applies the patch and returns the patched tcr.
func (c *FakeTcrs) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.Tcr, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(tcrsResource, name, pt, data, subresources...), &platformv1.Tcr{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.Tcr), err
}
