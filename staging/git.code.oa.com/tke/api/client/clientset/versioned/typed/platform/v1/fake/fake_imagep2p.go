/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeImageP2Ps implements ImageP2PInterface
type FakeImageP2Ps struct {
	Fake *FakePlatformV1
}

var imagep2psResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "imagep2ps"}

var imagep2psKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "ImageP2P"}

// Get takes name of the imageP2P, and returns the corresponding imageP2P object, and an error if there is any.
func (c *FakeImageP2Ps) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.ImageP2P, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(imagep2psResource, name), &platformv1.ImageP2P{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.ImageP2P), err
}

// List takes label and field selectors, and returns the list of ImageP2Ps that match those selectors.
func (c *FakeImageP2Ps) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.ImageP2PList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(imagep2psResource, imagep2psKind, opts), &platformv1.ImageP2PList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.ImageP2PList{ListMeta: obj.(*platformv1.ImageP2PList).ListMeta}
	for _, item := range obj.(*platformv1.ImageP2PList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested imageP2Ps.
func (c *FakeImageP2Ps) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(imagep2psResource, opts))
}

// Create takes the representation of a imageP2P and creates it.  Returns the server's representation of the imageP2P, and an error, if there is any.
func (c *FakeImageP2Ps) Create(ctx context.Context, imageP2P *platformv1.ImageP2P, opts v1.CreateOptions) (result *platformv1.ImageP2P, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(imagep2psResource, imageP2P), &platformv1.ImageP2P{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.ImageP2P), err
}

// Update takes the representation of a imageP2P and updates it. Returns the server's representation of the imageP2P, and an error, if there is any.
func (c *FakeImageP2Ps) Update(ctx context.Context, imageP2P *platformv1.ImageP2P, opts v1.UpdateOptions) (result *platformv1.ImageP2P, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(imagep2psResource, imageP2P), &platformv1.ImageP2P{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.ImageP2P), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeImageP2Ps) UpdateStatus(ctx context.Context, imageP2P *platformv1.ImageP2P, opts v1.UpdateOptions) (*platformv1.ImageP2P, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(imagep2psResource, "status", imageP2P), &platformv1.ImageP2P{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.ImageP2P), err
}

// Delete takes name of the imageP2P and deletes it. Returns an error if one occurs.
func (c *FakeImageP2Ps) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(imagep2psResource, name), &platformv1.ImageP2P{})
	return err
}

// Patch applies the patch and returns the patched imageP2P.
func (c *FakeImageP2Ps) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.ImageP2P, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(imagep2psResource, name, pt, data, subresources...), &platformv1.ImageP2P{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.ImageP2P), err
}
