/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeEnvironments implements EnvironmentInterface
type FakeEnvironments struct {
	Fake *FakePlatformV1
}

var environmentsResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "environments"}

var environmentsKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "Environment"}

// Get takes name of the environment, and returns the corresponding environment object, and an error if there is any.
func (c *FakeEnvironments) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.Environment, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(environmentsResource, name), &platformv1.Environment{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.Environment), err
}

// List takes label and field selectors, and returns the list of Environments that match those selectors.
func (c *FakeEnvironments) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.EnvironmentList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(environmentsResource, environmentsKind, opts), &platformv1.EnvironmentList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.EnvironmentList{ListMeta: obj.(*platformv1.EnvironmentList).ListMeta}
	for _, item := range obj.(*platformv1.EnvironmentList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested environments.
func (c *FakeEnvironments) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(environmentsResource, opts))
}

// Create takes the representation of a environment and creates it.  Returns the server's representation of the environment, and an error, if there is any.
func (c *FakeEnvironments) Create(ctx context.Context, environment *platformv1.Environment, opts v1.CreateOptions) (result *platformv1.Environment, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(environmentsResource, environment), &platformv1.Environment{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.Environment), err
}

// Update takes the representation of a environment and updates it. Returns the server's representation of the environment, and an error, if there is any.
func (c *FakeEnvironments) Update(ctx context.Context, environment *platformv1.Environment, opts v1.UpdateOptions) (result *platformv1.Environment, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(environmentsResource, environment), &platformv1.Environment{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.Environment), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeEnvironments) UpdateStatus(ctx context.Context, environment *platformv1.Environment, opts v1.UpdateOptions) (*platformv1.Environment, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(environmentsResource, "status", environment), &platformv1.Environment{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.Environment), err
}

// Delete takes name of the environment and deletes it. Returns an error if one occurs.
func (c *FakeEnvironments) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(environmentsResource, name), &platformv1.Environment{})
	return err
}

// Patch applies the patch and returns the patched environment.
func (c *FakeEnvironments) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.Environment, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(environmentsResource, name, pt, data, subresources...), &platformv1.Environment{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.Environment), err
}
