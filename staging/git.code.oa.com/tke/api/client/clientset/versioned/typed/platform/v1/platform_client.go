/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"git.code.oa.com/tke/api/client/clientset/versioned/scheme"
	v1 "git.code.oa.com/tke/api/platform/v1"
	rest "k8s.io/client-go/rest"
)

type PlatformV1Interface interface {
	RESTClient() rest.Interface
	AddonTypesGetter
	CBSsGetter
	CFSsGetter
	COSsGetter
	ClustersGetter
	ClusterAPIResourcesGetter
	ClusterAddonsGetter
	ClusterAddonKindsGetter
	ClusterAddonTypesGetter
	ClusterAuthenticationsGetter
	ConfigMapsGetter
	DNSAutoscalersGetter
	DeSchedulersGetter
	DynamicSchedulersGetter
	EniIpamdsGetter
	EnvironmentsGetter
	GPUManagersGetter
	GameAppsGetter
	HPCsGetter
	HelmsGetter
	ImageP2PsGetter
	LBCFsGetter
	LogCollectorsGetter
	NamespaceSetsGetter
	NetworkPoliciesGetter
	NginxIngressesGetter
	NodeLocalDNSCachesGetter
	NodeProblemDetectorsGetter
	OLMsGetter
	OOMGuardsGetter
	PersistentEventsGetter
	ProjectsGetter
	QGPUsGetter
	RegistriesGetter
	TcrsGetter
}

// PlatformV1Client is used to interact with features provided by the platform.tke group.
type PlatformV1Client struct {
	restClient rest.Interface
}

func (c *PlatformV1Client) AddonTypes() AddonTypeInterface {
	return newAddonTypes(c)
}

func (c *PlatformV1Client) CBSs() CBSInterface {
	return newCBSs(c)
}

func (c *PlatformV1Client) CFSs() CFSInterface {
	return newCFSs(c)
}

func (c *PlatformV1Client) COSs() COSInterface {
	return newCOSs(c)
}

func (c *PlatformV1Client) Clusters() ClusterInterface {
	return newClusters(c)
}

func (c *PlatformV1Client) ClusterAPIResources() ClusterAPIResourceInterface {
	return newClusterAPIResources(c)
}

func (c *PlatformV1Client) ClusterAddons() ClusterAddonInterface {
	return newClusterAddons(c)
}

func (c *PlatformV1Client) ClusterAddonKinds() ClusterAddonKindInterface {
	return newClusterAddonKinds(c)
}

func (c *PlatformV1Client) ClusterAddonTypes() ClusterAddonTypeInterface {
	return newClusterAddonTypes(c)
}

func (c *PlatformV1Client) ClusterAuthentications(namespace string) ClusterAuthenticationInterface {
	return newClusterAuthentications(c, namespace)
}

func (c *PlatformV1Client) ConfigMaps() ConfigMapInterface {
	return newConfigMaps(c)
}

func (c *PlatformV1Client) DNSAutoscalers() DNSAutoscalerInterface {
	return newDNSAutoscalers(c)
}

func (c *PlatformV1Client) DeSchedulers() DeSchedulerInterface {
	return newDeSchedulers(c)
}

func (c *PlatformV1Client) DynamicSchedulers() DynamicSchedulerInterface {
	return newDynamicSchedulers(c)
}

func (c *PlatformV1Client) EniIpamds() EniIpamdInterface {
	return newEniIpamds(c)
}

func (c *PlatformV1Client) Environments() EnvironmentInterface {
	return newEnvironments(c)
}

func (c *PlatformV1Client) GPUManagers() GPUManagerInterface {
	return newGPUManagers(c)
}

func (c *PlatformV1Client) GameApps() GameAppInterface {
	return newGameApps(c)
}

func (c *PlatformV1Client) HPCs() HPCInterface {
	return newHPCs(c)
}

func (c *PlatformV1Client) Helms() HelmInterface {
	return newHelms(c)
}

func (c *PlatformV1Client) ImageP2Ps() ImageP2PInterface {
	return newImageP2Ps(c)
}

func (c *PlatformV1Client) LBCFs() LBCFInterface {
	return newLBCFs(c)
}

func (c *PlatformV1Client) LogCollectors() LogCollectorInterface {
	return newLogCollectors(c)
}

func (c *PlatformV1Client) NamespaceSets() NamespaceSetInterface {
	return newNamespaceSets(c)
}

func (c *PlatformV1Client) NetworkPolicies() NetworkPolicyInterface {
	return newNetworkPolicies(c)
}

func (c *PlatformV1Client) NginxIngresses() NginxIngressInterface {
	return newNginxIngresses(c)
}

func (c *PlatformV1Client) NodeLocalDNSCaches() NodeLocalDNSCacheInterface {
	return newNodeLocalDNSCaches(c)
}

func (c *PlatformV1Client) NodeProblemDetectors() NodeProblemDetectorInterface {
	return newNodeProblemDetectors(c)
}

func (c *PlatformV1Client) OLMs() OLMInterface {
	return newOLMs(c)
}

func (c *PlatformV1Client) OOMGuards() OOMGuardInterface {
	return newOOMGuards(c)
}

func (c *PlatformV1Client) PersistentEvents() PersistentEventInterface {
	return newPersistentEvents(c)
}

func (c *PlatformV1Client) Projects() ProjectInterface {
	return newProjects(c)
}

func (c *PlatformV1Client) QGPUs() QGPUInterface {
	return newQGPUs(c)
}

func (c *PlatformV1Client) Registries() RegistryInterface {
	return newRegistries(c)
}

func (c *PlatformV1Client) Tcrs() TcrInterface {
	return newTcrs(c)
}

// NewForConfig creates a new PlatformV1Client for the given config.
func NewForConfig(c *rest.Config) (*PlatformV1Client, error) {
	config := *c
	if err := setConfigDefaults(&config); err != nil {
		return nil, err
	}
	client, err := rest.RESTClientFor(&config)
	if err != nil {
		return nil, err
	}
	return &PlatformV1Client{client}, nil
}

// NewForConfigOrDie creates a new PlatformV1Client for the given config and
// panics if there is an error in the config.
func NewForConfigOrDie(c *rest.Config) *PlatformV1Client {
	client, err := NewForConfig(c)
	if err != nil {
		panic(err)
	}
	return client
}

// New creates a new PlatformV1Client for the given RESTClient.
func New(c rest.Interface) *PlatformV1Client {
	return &PlatformV1Client{c}
}

func setConfigDefaults(config *rest.Config) error {
	gv := v1.SchemeGroupVersion
	config.GroupVersion = &gv
	config.APIPath = "/apis"
	config.NegotiatedSerializer = scheme.Codecs.WithoutConversion()

	if config.UserAgent == "" {
		config.UserAgent = rest.DefaultKubernetesUserAgent()
	}

	return nil
}

// RESTClient returns a RESTClient that is used to communicate
// with API server by this client implementation.
func (c *PlatformV1Client) RESTClient() rest.Interface {
	if c == nil {
		return nil
	}
	return c.restClient
}
