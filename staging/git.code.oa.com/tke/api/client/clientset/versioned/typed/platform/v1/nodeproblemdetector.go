/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	scheme "git.code.oa.com/tke/api/client/clientset/versioned/scheme"
	v1 "git.code.oa.com/tke/api/platform/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// NodeProblemDetectorsGetter has a method to return a NodeProblemDetectorInterface.
// A group's client should implement this interface.
type NodeProblemDetectorsGetter interface {
	NodeProblemDetectors() NodeProblemDetectorInterface
}

// NodeProblemDetectorInterface has methods to work with NodeProblemDetector resources.
type NodeProblemDetectorInterface interface {
	Create(ctx context.Context, nodeProblemDetector *v1.NodeProblemDetector, opts metav1.CreateOptions) (*v1.NodeProblemDetector, error)
	Update(ctx context.Context, nodeProblemDetector *v1.NodeProblemDetector, opts metav1.UpdateOptions) (*v1.NodeProblemDetector, error)
	UpdateStatus(ctx context.Context, nodeProblemDetector *v1.NodeProblemDetector, opts metav1.UpdateOptions) (*v1.NodeProblemDetector, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*v1.NodeProblemDetector, error)
	List(ctx context.Context, opts metav1.ListOptions) (*v1.NodeProblemDetectorList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.NodeProblemDetector, err error)
	NodeProblemDetectorExpansion
}

// nodeProblemDetectors implements NodeProblemDetectorInterface
type nodeProblemDetectors struct {
	client rest.Interface
}

// newNodeProblemDetectors returns a NodeProblemDetectors
func newNodeProblemDetectors(c *PlatformV1Client) *nodeProblemDetectors {
	return &nodeProblemDetectors{
		client: c.RESTClient(),
	}
}

// Get takes name of the nodeProblemDetector, and returns the corresponding nodeProblemDetector object, and an error if there is any.
func (c *nodeProblemDetectors) Get(ctx context.Context, name string, options metav1.GetOptions) (result *v1.NodeProblemDetector, err error) {
	result = &v1.NodeProblemDetector{}
	err = c.client.Get().
		Resource("nodeproblemdetectors").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of NodeProblemDetectors that match those selectors.
func (c *nodeProblemDetectors) List(ctx context.Context, opts metav1.ListOptions) (result *v1.NodeProblemDetectorList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1.NodeProblemDetectorList{}
	err = c.client.Get().
		Resource("nodeproblemdetectors").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested nodeProblemDetectors.
func (c *nodeProblemDetectors) Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Resource("nodeproblemdetectors").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a nodeProblemDetector and creates it.  Returns the server's representation of the nodeProblemDetector, and an error, if there is any.
func (c *nodeProblemDetectors) Create(ctx context.Context, nodeProblemDetector *v1.NodeProblemDetector, opts metav1.CreateOptions) (result *v1.NodeProblemDetector, err error) {
	result = &v1.NodeProblemDetector{}
	err = c.client.Post().
		Resource("nodeproblemdetectors").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(nodeProblemDetector).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a nodeProblemDetector and updates it. Returns the server's representation of the nodeProblemDetector, and an error, if there is any.
func (c *nodeProblemDetectors) Update(ctx context.Context, nodeProblemDetector *v1.NodeProblemDetector, opts metav1.UpdateOptions) (result *v1.NodeProblemDetector, err error) {
	result = &v1.NodeProblemDetector{}
	err = c.client.Put().
		Resource("nodeproblemdetectors").
		Name(nodeProblemDetector.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(nodeProblemDetector).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *nodeProblemDetectors) UpdateStatus(ctx context.Context, nodeProblemDetector *v1.NodeProblemDetector, opts metav1.UpdateOptions) (result *v1.NodeProblemDetector, err error) {
	result = &v1.NodeProblemDetector{}
	err = c.client.Put().
		Resource("nodeproblemdetectors").
		Name(nodeProblemDetector.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(nodeProblemDetector).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the nodeProblemDetector and deletes it. Returns an error if one occurs.
func (c *nodeProblemDetectors) Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error {
	return c.client.Delete().
		Resource("nodeproblemdetectors").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched nodeProblemDetector.
func (c *nodeProblemDetectors) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.NodeProblemDetector, err error) {
	result = &v1.NodeProblemDetector{}
	err = c.client.Patch(pt).
		Resource("nodeproblemdetectors").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
