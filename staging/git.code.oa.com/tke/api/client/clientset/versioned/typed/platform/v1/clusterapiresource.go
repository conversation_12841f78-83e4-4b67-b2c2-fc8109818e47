/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	scheme "git.code.oa.com/tke/api/client/clientset/versioned/scheme"
	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	rest "k8s.io/client-go/rest"
)

// ClusterAPIResourcesGetter has a method to return a ClusterAPIResourceInterface.
// A group's client should implement this interface.
type ClusterAPIResourcesGetter interface {
	ClusterAPIResources() ClusterAPIResourceInterface
}

// ClusterAPIResourceInterface has methods to work with ClusterAPIResource resources.
type ClusterAPIResourceInterface interface {
	Get(ctx context.Context, name string, opts v1.GetOptions) (*platformv1.ClusterAPIResource, error)
	List(ctx context.Context, opts v1.ListOptions) (*platformv1.ClusterAPIResourceList, error)
	ClusterAPIResourceExpansion
}

// clusterAPIResources implements ClusterAPIResourceInterface
type clusterAPIResources struct {
	client rest.Interface
}

// newClusterAPIResources returns a ClusterAPIResources
func newClusterAPIResources(c *PlatformV1Client) *clusterAPIResources {
	return &clusterAPIResources{
		client: c.RESTClient(),
	}
}

// Get takes name of the clusterAPIResource, and returns the corresponding clusterAPIResource object, and an error if there is any.
func (c *clusterAPIResources) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.ClusterAPIResource, err error) {
	result = &platformv1.ClusterAPIResource{}
	err = c.client.Get().
		Resource("clusterapiresources").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of ClusterAPIResources that match those selectors.
func (c *clusterAPIResources) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.ClusterAPIResourceList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &platformv1.ClusterAPIResourceList{}
	err = c.client.Get().
		Resource("clusterapiresources").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}
