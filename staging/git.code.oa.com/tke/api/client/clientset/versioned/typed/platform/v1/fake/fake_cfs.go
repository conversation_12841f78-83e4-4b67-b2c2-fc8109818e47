/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeCFSs implements CFSInterface
type FakeCFSs struct {
	Fake *FakePlatformV1
}

var cfssResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "cfss"}

var cfssKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "CFS"}

// Get takes name of the cFS, and returns the corresponding cFS object, and an error if there is any.
func (c *FakeCFSs) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.CFS, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(cfssResource, name), &platformv1.CFS{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.CFS), err
}

// List takes label and field selectors, and returns the list of CFSs that match those selectors.
func (c *FakeCFSs) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.CFSList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(cfssResource, cfssKind, opts), &platformv1.CFSList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.CFSList{ListMeta: obj.(*platformv1.CFSList).ListMeta}
	for _, item := range obj.(*platformv1.CFSList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested cFSs.
func (c *FakeCFSs) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(cfssResource, opts))
}

// Create takes the representation of a cFS and creates it.  Returns the server's representation of the cFS, and an error, if there is any.
func (c *FakeCFSs) Create(ctx context.Context, cFS *platformv1.CFS, opts v1.CreateOptions) (result *platformv1.CFS, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(cfssResource, cFS), &platformv1.CFS{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.CFS), err
}

// Update takes the representation of a cFS and updates it. Returns the server's representation of the cFS, and an error, if there is any.
func (c *FakeCFSs) Update(ctx context.Context, cFS *platformv1.CFS, opts v1.UpdateOptions) (result *platformv1.CFS, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(cfssResource, cFS), &platformv1.CFS{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.CFS), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeCFSs) UpdateStatus(ctx context.Context, cFS *platformv1.CFS, opts v1.UpdateOptions) (*platformv1.CFS, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(cfssResource, "status", cFS), &platformv1.CFS{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.CFS), err
}

// Delete takes name of the cFS and deletes it. Returns an error if one occurs.
func (c *FakeCFSs) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(cfssResource, name), &platformv1.CFS{})
	return err
}

// Patch applies the patch and returns the patched cFS.
func (c *FakeCFSs) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.CFS, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(cfssResource, name, pt, data, subresources...), &platformv1.CFS{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.CFS), err
}
