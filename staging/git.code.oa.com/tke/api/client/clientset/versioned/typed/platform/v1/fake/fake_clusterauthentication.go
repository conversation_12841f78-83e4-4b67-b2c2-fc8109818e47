/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeClusterAuthentications implements ClusterAuthenticationInterface
type FakeClusterAuthentications struct {
	Fake *FakePlatformV1
	ns   string
}

var clusterauthenticationsResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "clusterauthentications"}

var clusterauthenticationsKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "ClusterAuthentication"}

// Get takes name of the clusterAuthentication, and returns the corresponding clusterAuthentication object, and an error if there is any.
func (c *FakeClusterAuthentications) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.ClusterAuthentication, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewGetAction(clusterauthenticationsResource, c.ns, name), &platformv1.ClusterAuthentication{})

	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.ClusterAuthentication), err
}

// List takes label and field selectors, and returns the list of ClusterAuthentications that match those selectors.
func (c *FakeClusterAuthentications) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.ClusterAuthenticationList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewListAction(clusterauthenticationsResource, clusterauthenticationsKind, c.ns, opts), &platformv1.ClusterAuthenticationList{})

	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.ClusterAuthenticationList{ListMeta: obj.(*platformv1.ClusterAuthenticationList).ListMeta}
	for _, item := range obj.(*platformv1.ClusterAuthenticationList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested clusterAuthentications.
func (c *FakeClusterAuthentications) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchAction(clusterauthenticationsResource, c.ns, opts))

}

// Create takes the representation of a clusterAuthentication and creates it.  Returns the server's representation of the clusterAuthentication, and an error, if there is any.
func (c *FakeClusterAuthentications) Create(ctx context.Context, clusterAuthentication *platformv1.ClusterAuthentication, opts v1.CreateOptions) (result *platformv1.ClusterAuthentication, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewCreateAction(clusterauthenticationsResource, c.ns, clusterAuthentication), &platformv1.ClusterAuthentication{})

	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.ClusterAuthentication), err
}

// Update takes the representation of a clusterAuthentication and updates it. Returns the server's representation of the clusterAuthentication, and an error, if there is any.
func (c *FakeClusterAuthentications) Update(ctx context.Context, clusterAuthentication *platformv1.ClusterAuthentication, opts v1.UpdateOptions) (result *platformv1.ClusterAuthentication, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateAction(clusterauthenticationsResource, c.ns, clusterAuthentication), &platformv1.ClusterAuthentication{})

	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.ClusterAuthentication), err
}

// Delete takes name of the clusterAuthentication and deletes it. Returns an error if one occurs.
func (c *FakeClusterAuthentications) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteAction(clusterauthenticationsResource, c.ns, name), &platformv1.ClusterAuthentication{})

	return err
}

// Patch applies the patch and returns the patched clusterAuthentication.
func (c *FakeClusterAuthentications) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.ClusterAuthentication, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceAction(clusterauthenticationsResource, c.ns, name, pt, data, subresources...), &platformv1.ClusterAuthentication{})

	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.ClusterAuthentication), err
}
