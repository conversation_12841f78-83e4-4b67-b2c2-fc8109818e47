/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeLogCollectors implements LogCollectorInterface
type FakeLogCollectors struct {
	Fake *FakePlatformV1
}

var logcollectorsResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "logcollectors"}

var logcollectorsKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "LogCollector"}

// Get takes name of the logCollector, and returns the corresponding logCollector object, and an error if there is any.
func (c *FakeLogCollectors) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.LogCollector, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(logcollectorsResource, name), &platformv1.LogCollector{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.LogCollector), err
}

// List takes label and field selectors, and returns the list of LogCollectors that match those selectors.
func (c *FakeLogCollectors) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.LogCollectorList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(logcollectorsResource, logcollectorsKind, opts), &platformv1.LogCollectorList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.LogCollectorList{ListMeta: obj.(*platformv1.LogCollectorList).ListMeta}
	for _, item := range obj.(*platformv1.LogCollectorList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested logCollectors.
func (c *FakeLogCollectors) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(logcollectorsResource, opts))
}

// Create takes the representation of a logCollector and creates it.  Returns the server's representation of the logCollector, and an error, if there is any.
func (c *FakeLogCollectors) Create(ctx context.Context, logCollector *platformv1.LogCollector, opts v1.CreateOptions) (result *platformv1.LogCollector, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(logcollectorsResource, logCollector), &platformv1.LogCollector{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.LogCollector), err
}

// Update takes the representation of a logCollector and updates it. Returns the server's representation of the logCollector, and an error, if there is any.
func (c *FakeLogCollectors) Update(ctx context.Context, logCollector *platformv1.LogCollector, opts v1.UpdateOptions) (result *platformv1.LogCollector, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(logcollectorsResource, logCollector), &platformv1.LogCollector{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.LogCollector), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeLogCollectors) UpdateStatus(ctx context.Context, logCollector *platformv1.LogCollector, opts v1.UpdateOptions) (*platformv1.LogCollector, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(logcollectorsResource, "status", logCollector), &platformv1.LogCollector{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.LogCollector), err
}

// Delete takes name of the logCollector and deletes it. Returns an error if one occurs.
func (c *FakeLogCollectors) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(logcollectorsResource, name), &platformv1.LogCollector{})
	return err
}

// Patch applies the patch and returns the patched logCollector.
func (c *FakeLogCollectors) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.LogCollector, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(logcollectorsResource, name, pt, data, subresources...), &platformv1.LogCollector{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.LogCollector), err
}
