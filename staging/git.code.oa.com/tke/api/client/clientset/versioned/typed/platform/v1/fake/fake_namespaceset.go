/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeNamespaceSets implements NamespaceSetInterface
type FakeNamespaceSets struct {
	Fake *FakePlatformV1
}

var namespacesetsResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "namespacesets"}

var namespacesetsKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "NamespaceSet"}

// Get takes name of the namespaceSet, and returns the corresponding namespaceSet object, and an error if there is any.
func (c *FakeNamespaceSets) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.NamespaceSet, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(namespacesetsResource, name), &platformv1.NamespaceSet{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NamespaceSet), err
}

// List takes label and field selectors, and returns the list of NamespaceSets that match those selectors.
func (c *FakeNamespaceSets) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.NamespaceSetList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(namespacesetsResource, namespacesetsKind, opts), &platformv1.NamespaceSetList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.NamespaceSetList{ListMeta: obj.(*platformv1.NamespaceSetList).ListMeta}
	for _, item := range obj.(*platformv1.NamespaceSetList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested namespaceSets.
func (c *FakeNamespaceSets) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(namespacesetsResource, opts))
}

// Create takes the representation of a namespaceSet and creates it.  Returns the server's representation of the namespaceSet, and an error, if there is any.
func (c *FakeNamespaceSets) Create(ctx context.Context, namespaceSet *platformv1.NamespaceSet, opts v1.CreateOptions) (result *platformv1.NamespaceSet, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(namespacesetsResource, namespaceSet), &platformv1.NamespaceSet{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NamespaceSet), err
}

// Update takes the representation of a namespaceSet and updates it. Returns the server's representation of the namespaceSet, and an error, if there is any.
func (c *FakeNamespaceSets) Update(ctx context.Context, namespaceSet *platformv1.NamespaceSet, opts v1.UpdateOptions) (result *platformv1.NamespaceSet, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(namespacesetsResource, namespaceSet), &platformv1.NamespaceSet{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NamespaceSet), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeNamespaceSets) UpdateStatus(ctx context.Context, namespaceSet *platformv1.NamespaceSet, opts v1.UpdateOptions) (*platformv1.NamespaceSet, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(namespacesetsResource, "status", namespaceSet), &platformv1.NamespaceSet{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NamespaceSet), err
}

// Delete takes name of the namespaceSet and deletes it. Returns an error if one occurs.
func (c *FakeNamespaceSets) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(namespacesetsResource, name), &platformv1.NamespaceSet{})
	return err
}

// Patch applies the patch and returns the patched namespaceSet.
func (c *FakeNamespaceSets) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.NamespaceSet, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(namespacesetsResource, name, pt, data, subresources...), &platformv1.NamespaceSet{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NamespaceSet), err
}
