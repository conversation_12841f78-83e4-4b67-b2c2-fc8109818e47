/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeAddonTypes implements AddonTypeInterface
type FakeAddonTypes struct {
	Fake *FakePlatformV1
}

var addontypesResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "addontypes"}

var addontypesKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "AddonType"}

// Get takes name of the addonType, and returns the corresponding addonType object, and an error if there is any.
func (c *FakeAddonTypes) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.AddonType, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(addontypesResource, name), &platformv1.AddonType{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.AddonType), err
}

// List takes label and field selectors, and returns the list of AddonTypes that match those selectors.
func (c *FakeAddonTypes) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.AddonTypeList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(addontypesResource, addontypesKind, opts), &platformv1.AddonTypeList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.AddonTypeList{ListMeta: obj.(*platformv1.AddonTypeList).ListMeta}
	for _, item := range obj.(*platformv1.AddonTypeList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested addonTypes.
func (c *FakeAddonTypes) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(addontypesResource, opts))
}

// Create takes the representation of a addonType and creates it.  Returns the server's representation of the addonType, and an error, if there is any.
func (c *FakeAddonTypes) Create(ctx context.Context, addonType *platformv1.AddonType, opts v1.CreateOptions) (result *platformv1.AddonType, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(addontypesResource, addonType), &platformv1.AddonType{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.AddonType), err
}

// Update takes the representation of a addonType and updates it. Returns the server's representation of the addonType, and an error, if there is any.
func (c *FakeAddonTypes) Update(ctx context.Context, addonType *platformv1.AddonType, opts v1.UpdateOptions) (result *platformv1.AddonType, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(addontypesResource, addonType), &platformv1.AddonType{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.AddonType), err
}

// Delete takes name of the addonType and deletes it. Returns an error if one occurs.
func (c *FakeAddonTypes) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(addontypesResource, name), &platformv1.AddonType{})
	return err
}

// Patch applies the patch and returns the patched addonType.
func (c *FakeAddonTypes) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.AddonType, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(addontypesResource, name, pt, data, subresources...), &platformv1.AddonType{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.AddonType), err
}
