/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeDynamicSchedulers implements DynamicSchedulerInterface
type FakeDynamicSchedulers struct {
	Fake *FakePlatformV1
}

var dynamicschedulersResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "dynamicschedulers"}

var dynamicschedulersKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "DynamicScheduler"}

// Get takes name of the dynamicScheduler, and returns the corresponding dynamicScheduler object, and an error if there is any.
func (c *FakeDynamicSchedulers) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.DynamicScheduler, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(dynamicschedulersResource, name), &platformv1.DynamicScheduler{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.DynamicScheduler), err
}

// List takes label and field selectors, and returns the list of DynamicSchedulers that match those selectors.
func (c *FakeDynamicSchedulers) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.DynamicSchedulerList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(dynamicschedulersResource, dynamicschedulersKind, opts), &platformv1.DynamicSchedulerList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.DynamicSchedulerList{ListMeta: obj.(*platformv1.DynamicSchedulerList).ListMeta}
	for _, item := range obj.(*platformv1.DynamicSchedulerList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested dynamicSchedulers.
func (c *FakeDynamicSchedulers) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(dynamicschedulersResource, opts))
}

// Create takes the representation of a dynamicScheduler and creates it.  Returns the server's representation of the dynamicScheduler, and an error, if there is any.
func (c *FakeDynamicSchedulers) Create(ctx context.Context, dynamicScheduler *platformv1.DynamicScheduler, opts v1.CreateOptions) (result *platformv1.DynamicScheduler, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(dynamicschedulersResource, dynamicScheduler), &platformv1.DynamicScheduler{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.DynamicScheduler), err
}

// Update takes the representation of a dynamicScheduler and updates it. Returns the server's representation of the dynamicScheduler, and an error, if there is any.
func (c *FakeDynamicSchedulers) Update(ctx context.Context, dynamicScheduler *platformv1.DynamicScheduler, opts v1.UpdateOptions) (result *platformv1.DynamicScheduler, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(dynamicschedulersResource, dynamicScheduler), &platformv1.DynamicScheduler{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.DynamicScheduler), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeDynamicSchedulers) UpdateStatus(ctx context.Context, dynamicScheduler *platformv1.DynamicScheduler, opts v1.UpdateOptions) (*platformv1.DynamicScheduler, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(dynamicschedulersResource, "status", dynamicScheduler), &platformv1.DynamicScheduler{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.DynamicScheduler), err
}

// Delete takes name of the dynamicScheduler and deletes it. Returns an error if one occurs.
func (c *FakeDynamicSchedulers) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(dynamicschedulersResource, name), &platformv1.DynamicScheduler{})
	return err
}

// Patch applies the patch and returns the patched dynamicScheduler.
func (c *FakeDynamicSchedulers) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.DynamicScheduler, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(dynamicschedulersResource, name, pt, data, subresources...), &platformv1.DynamicScheduler{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.DynamicScheduler), err
}
