/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeNginxIngresses implements NginxIngressInterface
type FakeNginxIngresses struct {
	Fake *FakePlatformV1
}

var nginxingressesResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "nginxingresses"}

var nginxingressesKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "NginxIngress"}

// Get takes name of the nginxIngress, and returns the corresponding nginxIngress object, and an error if there is any.
func (c *FakeNginxIngresses) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.NginxIngress, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(nginxingressesResource, name), &platformv1.NginxIngress{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NginxIngress), err
}

// List takes label and field selectors, and returns the list of NginxIngresses that match those selectors.
func (c *FakeNginxIngresses) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.NginxIngressList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(nginxingressesResource, nginxingressesKind, opts), &platformv1.NginxIngressList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.NginxIngressList{ListMeta: obj.(*platformv1.NginxIngressList).ListMeta}
	for _, item := range obj.(*platformv1.NginxIngressList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested nginxIngresses.
func (c *FakeNginxIngresses) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(nginxingressesResource, opts))
}

// Create takes the representation of a nginxIngress and creates it.  Returns the server's representation of the nginxIngress, and an error, if there is any.
func (c *FakeNginxIngresses) Create(ctx context.Context, nginxIngress *platformv1.NginxIngress, opts v1.CreateOptions) (result *platformv1.NginxIngress, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(nginxingressesResource, nginxIngress), &platformv1.NginxIngress{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NginxIngress), err
}

// Update takes the representation of a nginxIngress and updates it. Returns the server's representation of the nginxIngress, and an error, if there is any.
func (c *FakeNginxIngresses) Update(ctx context.Context, nginxIngress *platformv1.NginxIngress, opts v1.UpdateOptions) (result *platformv1.NginxIngress, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(nginxingressesResource, nginxIngress), &platformv1.NginxIngress{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NginxIngress), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeNginxIngresses) UpdateStatus(ctx context.Context, nginxIngress *platformv1.NginxIngress, opts v1.UpdateOptions) (*platformv1.NginxIngress, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(nginxingressesResource, "status", nginxIngress), &platformv1.NginxIngress{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NginxIngress), err
}

// Delete takes name of the nginxIngress and deletes it. Returns an error if one occurs.
func (c *FakeNginxIngresses) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(nginxingressesResource, name), &platformv1.NginxIngress{})
	return err
}

// Patch applies the patch and returns the patched nginxIngress.
func (c *FakeNginxIngresses) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.NginxIngress, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(nginxingressesResource, name, pt, data, subresources...), &platformv1.NginxIngress{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NginxIngress), err
}
