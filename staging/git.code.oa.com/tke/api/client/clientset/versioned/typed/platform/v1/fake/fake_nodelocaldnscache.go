/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeNodeLocalDNSCaches implements NodeLocalDNSCacheInterface
type FakeNodeLocalDNSCaches struct {
	Fake *FakePlatformV1
}

var nodelocaldnscachesResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "nodelocaldnscaches"}

var nodelocaldnscachesKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "NodeLocalDNSCache"}

// Get takes name of the nodeLocalDNSCache, and returns the corresponding nodeLocalDNSCache object, and an error if there is any.
func (c *FakeNodeLocalDNSCaches) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.NodeLocalDNSCache, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(nodelocaldnscachesResource, name), &platformv1.NodeLocalDNSCache{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NodeLocalDNSCache), err
}

// List takes label and field selectors, and returns the list of NodeLocalDNSCaches that match those selectors.
func (c *FakeNodeLocalDNSCaches) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.NodeLocalDNSCacheList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(nodelocaldnscachesResource, nodelocaldnscachesKind, opts), &platformv1.NodeLocalDNSCacheList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.NodeLocalDNSCacheList{ListMeta: obj.(*platformv1.NodeLocalDNSCacheList).ListMeta}
	for _, item := range obj.(*platformv1.NodeLocalDNSCacheList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested nodeLocalDNSCaches.
func (c *FakeNodeLocalDNSCaches) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(nodelocaldnscachesResource, opts))
}

// Create takes the representation of a nodeLocalDNSCache and creates it.  Returns the server's representation of the nodeLocalDNSCache, and an error, if there is any.
func (c *FakeNodeLocalDNSCaches) Create(ctx context.Context, nodeLocalDNSCache *platformv1.NodeLocalDNSCache, opts v1.CreateOptions) (result *platformv1.NodeLocalDNSCache, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(nodelocaldnscachesResource, nodeLocalDNSCache), &platformv1.NodeLocalDNSCache{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NodeLocalDNSCache), err
}

// Update takes the representation of a nodeLocalDNSCache and updates it. Returns the server's representation of the nodeLocalDNSCache, and an error, if there is any.
func (c *FakeNodeLocalDNSCaches) Update(ctx context.Context, nodeLocalDNSCache *platformv1.NodeLocalDNSCache, opts v1.UpdateOptions) (result *platformv1.NodeLocalDNSCache, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(nodelocaldnscachesResource, nodeLocalDNSCache), &platformv1.NodeLocalDNSCache{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NodeLocalDNSCache), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeNodeLocalDNSCaches) UpdateStatus(ctx context.Context, nodeLocalDNSCache *platformv1.NodeLocalDNSCache, opts v1.UpdateOptions) (*platformv1.NodeLocalDNSCache, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(nodelocaldnscachesResource, "status", nodeLocalDNSCache), &platformv1.NodeLocalDNSCache{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NodeLocalDNSCache), err
}

// Delete takes name of the nodeLocalDNSCache and deletes it. Returns an error if one occurs.
func (c *FakeNodeLocalDNSCaches) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(nodelocaldnscachesResource, name), &platformv1.NodeLocalDNSCache{})
	return err
}

// Patch applies the patch and returns the patched nodeLocalDNSCache.
func (c *FakeNodeLocalDNSCaches) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.NodeLocalDNSCache, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(nodelocaldnscachesResource, name, pt, data, subresources...), &platformv1.NodeLocalDNSCache{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NodeLocalDNSCache), err
}
