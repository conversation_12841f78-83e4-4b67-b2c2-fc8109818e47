/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	scheme "git.code.oa.com/tke/api/client/clientset/versioned/scheme"
	v1 "git.code.oa.com/tke/api/platform/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// TcrsGetter has a method to return a TcrInterface.
// A group's client should implement this interface.
type TcrsGetter interface {
	Tcrs() TcrInterface
}

// TcrInterface has methods to work with Tcr resources.
type TcrInterface interface {
	Create(ctx context.Context, tcr *v1.Tcr, opts metav1.CreateOptions) (*v1.Tcr, error)
	Update(ctx context.Context, tcr *v1.Tcr, opts metav1.UpdateOptions) (*v1.Tcr, error)
	UpdateStatus(ctx context.Context, tcr *v1.Tcr, opts metav1.UpdateOptions) (*v1.Tcr, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*v1.Tcr, error)
	List(ctx context.Context, opts metav1.ListOptions) (*v1.TcrList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.Tcr, err error)
	TcrExpansion
}

// tcrs implements TcrInterface
type tcrs struct {
	client rest.Interface
}

// newTcrs returns a Tcrs
func newTcrs(c *PlatformV1Client) *tcrs {
	return &tcrs{
		client: c.RESTClient(),
	}
}

// Get takes name of the tcr, and returns the corresponding tcr object, and an error if there is any.
func (c *tcrs) Get(ctx context.Context, name string, options metav1.GetOptions) (result *v1.Tcr, err error) {
	result = &v1.Tcr{}
	err = c.client.Get().
		Resource("tcrs").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of Tcrs that match those selectors.
func (c *tcrs) List(ctx context.Context, opts metav1.ListOptions) (result *v1.TcrList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1.TcrList{}
	err = c.client.Get().
		Resource("tcrs").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested tcrs.
func (c *tcrs) Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Resource("tcrs").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a tcr and creates it.  Returns the server's representation of the tcr, and an error, if there is any.
func (c *tcrs) Create(ctx context.Context, tcr *v1.Tcr, opts metav1.CreateOptions) (result *v1.Tcr, err error) {
	result = &v1.Tcr{}
	err = c.client.Post().
		Resource("tcrs").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(tcr).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a tcr and updates it. Returns the server's representation of the tcr, and an error, if there is any.
func (c *tcrs) Update(ctx context.Context, tcr *v1.Tcr, opts metav1.UpdateOptions) (result *v1.Tcr, err error) {
	result = &v1.Tcr{}
	err = c.client.Put().
		Resource("tcrs").
		Name(tcr.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(tcr).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *tcrs) UpdateStatus(ctx context.Context, tcr *v1.Tcr, opts metav1.UpdateOptions) (result *v1.Tcr, err error) {
	result = &v1.Tcr{}
	err = c.client.Put().
		Resource("tcrs").
		Name(tcr.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(tcr).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the tcr and deletes it. Returns an error if one occurs.
func (c *tcrs) Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error {
	return c.client.Delete().
		Resource("tcrs").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched tcr.
func (c *tcrs) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.Tcr, err error) {
	result = &v1.Tcr{}
	err = c.client.Patch(pt).
		Resource("tcrs").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
