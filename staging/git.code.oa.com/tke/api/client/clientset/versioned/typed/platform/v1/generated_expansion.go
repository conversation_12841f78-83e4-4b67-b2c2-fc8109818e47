/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package v1

type AddonTypeExpansion interface{}

type CBSExpansion interface{}

type CFSExpansion interface{}

type COSExpansion interface{}

type ClusterExpansion interface{}

type ClusterAPIResourceExpansion interface{}

type ClusterAddonExpansion interface{}

type ClusterAddonKindExpansion interface{}

type ClusterAddonTypeExpansion interface{}

type ClusterAuthenticationExpansion interface{}

type ConfigMapExpansion interface{}

type DNSAutoscalerExpansion interface{}

type DeSchedulerExpansion interface{}

type DynamicSchedulerExpansion interface{}

type EniIpamdExpansion interface{}

type EnvironmentExpansion interface{}

type GPUManagerExpansion interface{}

type GameAppExpansion interface{}

type HPCExpansion interface{}

type HelmExpansion interface{}

type ImageP2PExpansion interface{}

type LBCFExpansion interface{}

type LogCollectorExpansion interface{}

type NamespaceSetExpansion interface{}

type NetworkPolicyExpansion interface{}

type NginxIngressExpansion interface{}

type NodeLocalDNSCacheExpansion interface{}

type NodeProblemDetectorExpansion interface{}

type OLMExpansion interface{}

type OOMGuardExpansion interface{}

type PersistentEventExpansion interface{}

type ProjectExpansion interface{}

type QGPUExpansion interface{}

type RegistryExpansion interface{}

type TcrExpansion interface{}
