/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	scheme "git.code.oa.com/tke/api/client/clientset/versioned/scheme"
	v1 "git.code.oa.com/tke/api/platform/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// GPUManagersGetter has a method to return a GPUManagerInterface.
// A group's client should implement this interface.
type GPUManagersGetter interface {
	GPUManagers() GPUManagerInterface
}

// GPUManagerInterface has methods to work with GPUManager resources.
type GPUManagerInterface interface {
	Create(ctx context.Context, gPUManager *v1.GPUManager, opts metav1.CreateOptions) (*v1.GPUManager, error)
	Update(ctx context.Context, gPUManager *v1.GPUManager, opts metav1.UpdateOptions) (*v1.GPUManager, error)
	UpdateStatus(ctx context.Context, gPUManager *v1.GPUManager, opts metav1.UpdateOptions) (*v1.GPUManager, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*v1.GPUManager, error)
	List(ctx context.Context, opts metav1.ListOptions) (*v1.GPUManagerList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.GPUManager, err error)
	GPUManagerExpansion
}

// gPUManagers implements GPUManagerInterface
type gPUManagers struct {
	client rest.Interface
}

// newGPUManagers returns a GPUManagers
func newGPUManagers(c *PlatformV1Client) *gPUManagers {
	return &gPUManagers{
		client: c.RESTClient(),
	}
}

// Get takes name of the gPUManager, and returns the corresponding gPUManager object, and an error if there is any.
func (c *gPUManagers) Get(ctx context.Context, name string, options metav1.GetOptions) (result *v1.GPUManager, err error) {
	result = &v1.GPUManager{}
	err = c.client.Get().
		Resource("gpumanagers").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of GPUManagers that match those selectors.
func (c *gPUManagers) List(ctx context.Context, opts metav1.ListOptions) (result *v1.GPUManagerList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1.GPUManagerList{}
	err = c.client.Get().
		Resource("gpumanagers").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested gPUManagers.
func (c *gPUManagers) Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Resource("gpumanagers").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a gPUManager and creates it.  Returns the server's representation of the gPUManager, and an error, if there is any.
func (c *gPUManagers) Create(ctx context.Context, gPUManager *v1.GPUManager, opts metav1.CreateOptions) (result *v1.GPUManager, err error) {
	result = &v1.GPUManager{}
	err = c.client.Post().
		Resource("gpumanagers").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(gPUManager).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a gPUManager and updates it. Returns the server's representation of the gPUManager, and an error, if there is any.
func (c *gPUManagers) Update(ctx context.Context, gPUManager *v1.GPUManager, opts metav1.UpdateOptions) (result *v1.GPUManager, err error) {
	result = &v1.GPUManager{}
	err = c.client.Put().
		Resource("gpumanagers").
		Name(gPUManager.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(gPUManager).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *gPUManagers) UpdateStatus(ctx context.Context, gPUManager *v1.GPUManager, opts metav1.UpdateOptions) (result *v1.GPUManager, err error) {
	result = &v1.GPUManager{}
	err = c.client.Put().
		Resource("gpumanagers").
		Name(gPUManager.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(gPUManager).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the gPUManager and deletes it. Returns an error if one occurs.
func (c *gPUManagers) Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error {
	return c.client.Delete().
		Resource("gpumanagers").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched gPUManager.
func (c *gPUManagers) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.GPUManager, err error) {
	result = &v1.GPUManager{}
	err = c.client.Patch(pt).
		Resource("gpumanagers").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
