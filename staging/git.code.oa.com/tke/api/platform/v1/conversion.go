/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKE
 * available.
 *
 * Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License"); you may not use this
 * file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

package v1

import (
	"fmt"

	"k8s.io/apimachinery/pkg/runtime"
)

func addConversionFuncs(scheme *runtime.Scheme) error {
	if err := AddFieldLabelConversionsForCluster(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForProject(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForPersistentEvent(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForHelm(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForGameApp(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForEniIpamd(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForGPUManager(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForLogCollector(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForLBCF(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForCFS(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForCOS(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForClusterAuthentication(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForNodeProblemDetector(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForAddonType(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForImageP2P(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForDNSAutoscaler(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForNodeLocalDNSCache(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForOOMGuard(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForTcr(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForNginxIngress(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForDynamicScheduler(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForCBS(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForHPC(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForNetworkPolicy(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForDeScheduler(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForOLM(scheme); err != nil {
		return err
	}

	if err := AddFieldLabelConversionsForQGPU(scheme); err != nil {
		return err
	}

	return AddFieldLabelConversionsForNamespaceSet(scheme)
}

// AddFieldLabelConversionsForLogCollector adds a conversion function to convert
// field selectors of VolumeDecorator from the given version to internal version
// representation.
func AddFieldLabelConversionsForLogCollector(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("LogCollector"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"status.phase",
				"status.version",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForGPUManager adds a conversion function to convert
// field selectors of GPUManager from the given version to internal version
// representation.
func AddFieldLabelConversionsForGPUManager(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("GPUManager"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"status.phase",
				"status.version",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForCluster adds a conversion function to convert
// field selectors of Cluster from the given version to internal version
// representation.
func AddFieldLabelConversionsForCluster(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("Cluster"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.version",
				"spec.type",
				"status.locked",
				"status.phase",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForProject adds a conversion function to convert
// field selectors of Project from the given version to internal version
// representation.
func AddFieldLabelConversionsForProject(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("Project"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForProject adds a conversion function to convert
// field selectors of Project from the given version to internal version
// representation.
func AddFieldLabelConversionsForNamespaceSet(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("NamespaceSet"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.projectName",
				"spec.namespaceName",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForPersistentEvent adds a conversion function to convert
// field selectors of Project from the given version to internal version
// representation.
func AddFieldLabelConversionsForPersistentEvent(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("PersistentEvent"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"status.version",
				"status.phase",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForHelm adds a conversion function to convert
// field selectors of Project from the given version to internal version
// representation.
func AddFieldLabelConversionsForHelm(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("Helm"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"status.version",
				"status.phase",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForGameApp adds a conversion function to convert
// field selectors of Project from the given version to internal version
// representation.
func AddFieldLabelConversionsForGameApp(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("GameApp"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"status.version",
				"status.phase",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForEniIpamd adds a conversion function to convert
// field selectors of Project from the given version to internal version
// representation.
func AddFieldLabelConversionsForEniIpamd(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("EniIpamd"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"status.version",
				"status.phase",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForLBCF adds a conversion function to convert
// field selectors of LBCF from the given version to internal version
// representation.
func AddFieldLabelConversionsForLBCF(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("LBCF"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"status.phase",
				"status.version",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForCFS adds a conversion function to convert
// field selectors of Project from the given version to internal version
// representation.
func AddFieldLabelConversionsForCFS(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("CFS"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"spec.rootdir",
				"status.version",
				"status.phase",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForCOS adds a conversion function to convert
// field selectors of Project from the given version to internal version
// representation.
func AddFieldLabelConversionsForCOS(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("COS"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"spec.rootdir",
				"status.version",
				"status.phase",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForNodeProblemDetector adds a conversion function to convert
// field selectors of Project from the given version to internal version
// representation.
func AddFieldLabelConversionsForNodeProblemDetector(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("NodeProblemDetector"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"status.version",
				"status.phase",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForAddonType adds a conversion function to convert
// field selectors of AddonType from the given version to internal version
// representation.
func AddFieldLabelConversionsForAddonType(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("AddonType"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantIDs",
				"spec.type",
				"spec.latestVersion",
				"spec.level",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForClusterAuthentication adds a conversion
// function to convert field selectors of Project from the given version to internal
// version representation.
func AddFieldLabelConversionsForClusterAuthentication(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("ClusterAuthentication"),
		func(label, value string) (string, string, error) {
			switch label {
			case "tenantID",
				"clusterName",
				"ownerUIN",
				"subAccountsUIN",
				"authenticationInfo.commonName",
				"metadata.name",
				"metadata.namespace":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForImageP2P adds a conversion function to convert
// field selectors of Project from the given version to internal version
// representation.
func AddFieldLabelConversionsForImageP2P(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("ImageP2P"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"status.version",
				"status.phase",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForDNSAutoscaler adds a conversion function to convert
// field selectors of Project from the given version to internal version
// representation.
func AddFieldLabelConversionsForDNSAutoscaler(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("DNSAutoscaler"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"status.version",
				"status.phase",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForNodeLocalDNSCache adds a conversion function to convert
// field selectors of Project from the given version to internal version
// representation.
func AddFieldLabelConversionsForNodeLocalDNSCache(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("NodeLocalDNSCache"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"status.version",
				"status.phase",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForOOMGuard adds a conversion function to convert
// field selectors of Project from the given version to internal version
// representation.
func AddFieldLabelConversionsForOOMGuard(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("OOMGuard"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"status.version",
				"status.phase",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForTcr adds a conversion function to convert
// field selectors of Project from the given version to internal version
// representation.
func AddFieldLabelConversionsForTcr(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("Tcr"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"status.version",
				"status.phase",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForHPC adds a conversion function to convert
// field selectors of Project from the given version to internal version
// representation.
func AddFieldLabelConversionsForHPC(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("HPC"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"status.version",
				"status.phase",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForNginxIngress adds a conversion function to convert
// field selectors of Project from the given version to internal version
// representation.
func AddFieldLabelConversionsForNginxIngress(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("NginxIngress"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"status.version",
				"status.phase",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForDynamicScheduler adds a conversion function to convert
// field selectors of Project from the given version to internal version
// representation.
func AddFieldLabelConversionsForDynamicScheduler(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("DynamicScheduler"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"status.version",
				"status.phase",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForCBS adds a conversion function to convert
// field selectors of Project from the given version to internal version
// representation.
func AddFieldLabelConversionsForCBS(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("CBS"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"spec.rootdir",
				"status.version",
				"status.phase",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForNetworkPolicy adds a conversion function to convert
// field selectors of Project from the given version to internal version
// representation.
func AddFieldLabelConversionsForNetworkPolicy(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("NetworkPolicy"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"status.version",
				"status.phase",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForDeScheduler adds a conversion function to convert
// field selectors of Project from the given version to internal version
// representation.
func AddFieldLabelConversionsForDeScheduler(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("DeScheduler"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"status.version",
				"status.phase",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

// AddFieldLabelConversionsForOLM adds a conversion function to convert
// field selectors of Project from the given version to internal version
// representation.
func AddFieldLabelConversionsForOLM(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("OLM"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"status.version",
				"status.phase",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}

func AddFieldLabelConversionsForQGPU(scheme *runtime.Scheme) error {
	return scheme.AddFieldLabelConversionFunc(SchemeGroupVersion.WithKind("QGPU"),
		func(label, value string) (string, string, error) {
			switch label {
			case "spec.tenantID",
				"spec.clusterName",
				"spec.version",
				"status.version",
				"status.phase",
				"metadata.name":
				return label, value, nil
			default:
				return "", "", fmt.Errorf("field label not supported: %s", label)
			}
		})
}
