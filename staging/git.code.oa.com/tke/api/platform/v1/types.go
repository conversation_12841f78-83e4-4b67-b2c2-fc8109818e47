/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKE
 * available.
 *
 * Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License"); you may not use this
 * file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

package v1

import (
	"time"

	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// Cluster is a Kubernetes cluster in TKE.
type Cluster struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec ClusterSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status ClusterStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterList is the whole list of all clusters which owned by a tenant.
type ClusterList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// List of clusters
	Items []Cluster `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// ClusterSpec is a description of a cluster.
type ClusterSpec struct {
	// Finalizers is an opaque list of values that must be empty to permanently remove object from storage.
	// +optional
	Finalizers  []FinalizerName `json:"finalizers,omitempty" protobuf:"bytes,1,rep,name=finalizers,casttype=FinalizerName"`
	TenantID    string          `json:"tenantID" protobuf:"bytes,2,opt,name=tenantID"`
	DisplayName string          `json:"displayName" protobuf:"bytes,3,opt,name=displayName"`
	Type        ClusterType     `json:"type" protobuf:"bytes,4,opt,name=type,casttype=ClusterType"`
	// +optional
	ClusterCIDR string `json:"clusterCIDR,omitempty" protobuf:"bytes,5,opt,name=clusterCIDR"`
	// +optional
	PublicAlternativeNames []string `json:"publicAlternativeNames,omitempty" protobuf:"bytes,6,opt,name=publicAlternativeNames"`
	// +optional
	Features ClusterFeature `json:"features,omitempty" protobuf:"bytes,7,opt,name=features,casttype=ClusterFeature"`
	// +optional
	Properties ClusterProperty `json:"properties,omitempty" protobuf:"bytes,8,opt,name=properties,casttype=ClusterProperty"`
	// +optional
	CreatorUIN string `json:"creatorUIN" protobuf:"bytes,9,opt,name=creatorUIN"`
	// +optional
	UseRBAC *bool `json:"useRBAC" protobuf:"bytes,10,opt,name=useRBAC"`
}

// ClusterStatus represents information about the status of a cluster.
type ClusterStatus struct {
	// +optional
	Locked *bool `json:"locked,omitempty" protobuf:"varint,1,opt,name=locked"`
	// +optional
	Version string `json:"version" protobuf:"bytes,2,opt,name=version"`
	// +optional
	Phase ClusterPhase `json:"phase,omitempty" protobuf:"bytes,3,opt,name=phase,casttype=ClusterPhase"`
	// +optional
	// +patchMergeKey=type
	// +patchStrategy=merge
	Conditions []ClusterCondition `json:"conditions,omitempty" patchStrategy:"merge" patchMergeKey:"type" protobuf:"bytes,4,rep,name=conditions"`
	// A human readable message indicating details about why the cluster is in this condition.
	// +optional
	Message string `json:"message,omitempty" protobuf:"bytes,5,opt,name=message"`
	// A brief CamelCase message indicating details about why the cluster is in this state.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,6,opt,name=reason"`
	// List of addresses reachable to the cluster.
	// +optional
	// +patchMergeKey=type
	// +patchStrategy=merge
	Addresses []ClusterAddress `json:"addresses,omitempty" patchStrategy:"merge" patchMergeKey:"type" protobuf:"bytes,7,rep,name=addresses"`
	// +optional
	Credential ClusterCredential `json:"credential,omitempty" protobuf:"bytes,8,opt,name=credential,casttype=ClusterCredential"`
	// The addon component of the cluster
	// Deprecated
	// +optional
	AddOns ClusterAddOn `json:"addOns,omitempty" protobuf:"bytes,9,opt,name=addOns,casttype=ClusterAddOn"`
	// +optional
	Resource ClusterResource `json:"resource,omitempty" protobuf:"bytes,10,opt,name=resource,casttype=ClusterResource"`
	// +optional
	// +patchMergeKey=type
	// +patchStrategy=merge
	Components []ClusterComponent `json:"components,omitempty" patchStrategy:"merge" patchMergeKey:"type" protobuf:"bytes,11,rep,name=components"`
}

// Deprecated
// ClusterAddOn contains the AddOn component for the current kubernetes cluster
type ClusterAddOn struct {
}

// FinalizerName is the name identifying a finalizer during cluster lifecycle.
type FinalizerName string

const (
	ClusterFinalize FinalizerName = "cluster"
)

// ClusterType defines the type of cluster
type ClusterType string

const (
	ClusterImported ClusterType = "imported"
)

// AddonPhase defines the phase of helm constructor.
type AddonPhase string

const (
	// AddonPhaseInitializing means is wait initializing.
	AddonPhaseInitializing AddonPhase = "initializing"
	// AddonPhaseReinitializing means is reinitializing.
	AddonPhaseReinitializing AddonPhase = "reinitializing"
	// AddonPhaseChecking means is wait checking.
	AddonPhaseChecking AddonPhase = "checking"
	// AddonPhaseRunning means is running.
	AddonPhaseRunning AddonPhase = "running"
	// AddonPhaseUpgrading means is upgrading.
	AddonPhaseUpgrading AddonPhase = "upgrading"
	// AddonPhaseFailed means has been failed.
	AddonPhaseFailed      AddonPhase = "failed"
	AddonPhaseTerminating AddonPhase = "terminating"
	AddonPhaseUnknown     AddonPhase = "unknown"
	AddonPhaseUnhealthy   AddonPhase = "unhealthy"
	AddonPhasePending     AddonPhase = "pending"
)

// ClusterPhase defines the phase of cluster constructor
type ClusterPhase string

const (
	// ClusterRunning is the normal running phase
	ClusterRunning ClusterPhase = "running"
	// ClusterInitializing is the initialize phase
	ClusterInitializing ClusterPhase = "initializing"
	// ClusterFailed is the failed phase
	ClusterFailed ClusterPhase = "failed"
	// ClusterTerminating means the cluster is undergoing graceful termination
	ClusterTerminating ClusterPhase = "terminating"
)

// ClusterCondition contains details for the current condition of this cluster.
type ClusterCondition struct {
	// Type is the type of the condition.
	Type string `json:"type" protobuf:"bytes,1,opt,name=type"`
	// Status is the status of the condition.
	// Can be True, False, Unknown.
	Status ConditionStatus `json:"status" protobuf:"bytes,2,opt,name=status,casttype=ConditionStatus"`
	// Last time we probed the condition.
	// +optional
	LastProbeTime metav1.Time `json:"lastProbeTime,omitempty" protobuf:"bytes,3,opt,name=lastProbeTime"`
	// Last time the condition transitioned from one status to another.
	// +optional
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty" protobuf:"bytes,4,opt,name=lastTransitionTime"`
	// Unique, one-word, CamelCase reason for the condition's last transition.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,5,opt,name=reason"`
	// Human-readable message indicating details about last transition.
	// +optional
	Message string `json:"message,omitempty" protobuf:"bytes,6,opt,name=message"`
}

type ClusterAddressType string

// These are valid address type of node.
const (
	ClusterPublic    ClusterAddressType = "public"
	ClusterAdvertise ClusterAddressType = "advertise"
	ClusterInternal  ClusterAddressType = "internal"
)

// ClusterAddress contains information for the cluster's address.
type ClusterAddress struct {
	// Cluster address type, one of Public, ExternalIP or InternalIP.
	Type ClusterAddressType `json:"type" protobuf:"bytes,1,opt,name=type,casttype=ClusterAddressType"`
	// The cluster address.
	IP    string `json:"ip" protobuf:"bytes,2,opt,name=ip"`
	Port  int32  `json:"port" protobuf:"varint,3,name=port"`
	Proxy string `json:"proxy" protobuf:"bytes,4,opt,name=proxy"`
}

type ClusterCredential struct {
	// +optional
	CACert []byte `json:"caCert,omitempty" protobuf:"bytes,1,opt,name=caCert"`
	// +optional
	CAKey []byte `json:"caKey,omitempty" protobuf:"bytes,2,opt,name=caKey"`
	// +optional
	APIServerCert []byte `json:"apiServerCert,omitempty" protobuf:"bytes,3,opt,name=apiServerCert"`
	// +optional
	APIServerKey []byte `json:"apiServerKey,omitempty" protobuf:"bytes,4,opt,name=apiServerKey"`
	// +optional
	ClientCert []byte `json:"clientCert,omitempty" protobuf:"bytes,5,opt,name=clientCert"`
	// +optional
	ClientKey []byte `json:"clientKey,omitempty" protobuf:"bytes,6,opt,name=clientKey"`
	// +optional
	Privileged bool `json:"privileged,omitempty" protobuf:"varint,7,opt,name=privileged"`
	// +optional
	Token *string `json:"token,omitempty" protobuf:"bytes,8,rep,name=token"`
	// +optional
	KubeletToken *string `json:"kubeletToken,omitempty" protobuf:"bytes,9,rep,name=kubeletToken"`
	// +optional
	KubeProxyToken *string `json:"kubeProxyToken,omitempty" protobuf:"bytes,10,rep,name=kubeProxyToken"`
}

type ClusterFeature struct {
	// +optional
	IPVS *bool `json:"ipvs,omitempty" protobuf:"varint,1,opt,name=ipvs"`
	// +optional
	Public *bool `json:"public,omitempty" protobuf:"varint,2,opt,name=public"`
}

type ClusterProperty struct {
	// +optional
	MaxClusterServiceNum *int32 `json:"maxClusterServiceNum,omitempty" protobuf:"bytes,1,opt,name=maxClusterServiceNum"`
	// +optional
	MaxNodePodNum *int32 `json:"maxNodePodNum,omitempty" protobuf:"bytes,2,opt,name=maxNodePodNum"`
}

// ResourceName is the name identifying various resources in a ResourceList.
type ResourceName string

// ResourceList is a set of (resource name, quantity) pairs.
type ResourceList map[ResourceName]resource.Quantity

type ClusterResource struct {
	// Capacity represents the total resources of a cluster.
	// +optional
	Capacity ResourceList `json:"capacity,omitempty" protobuf:"bytes,1,rep,name=capacity,casttype=ResourceList,castkey=ResourceName"`
	// Allocatable represents the resources of a cluster that are available for scheduling.
	// Defaults to Capacity.
	// +optional
	Allocatable ResourceList `json:"allocatable,omitempty" protobuf:"bytes,2,rep,name=allocatable,casttype=ResourceList,castkey=ResourceName"`
	// +optional
	Allocated ResourceList `json:"allocated,omitempty" protobuf:"bytes,3,rep,name=allocated,casttype=ResourceList,castkey=ResourceName"`
}

type ClusterComponent struct {
	Type     string                   `json:"type" protobuf:"bytes,1,opt,name=type"`
	Replicas ClusterComponentReplicas `json:"replicas" protobuf:"bytes,2,opt,name=replicas,casttype=ClusterComponentReplicas"`
}

type ClusterComponentReplicas struct {
	Desired   int32 `json:"desired" protobuf:"varint,1,name=desired"`
	Current   int32 `json:"current" protobuf:"varint,2,name=current"`
	Available int32 `json:"available" protobuf:"varint,3,name=available"`
	Updated   int32 `json:"updated" protobuf:"varint,4,name=updated"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterApplyOptions is the query options to a kube-apiserver proxy call for cluster object.
type ClusterApplyOptions struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	NotUpdate               bool `json:"notUpdate,omitempty" protobuf:"varint,1,opt,name=notUpdate"`
	IsMultiClusterResources bool `json:"isMultiClusterResources,omitempty" protobuf:"varint,2,opt,name=isMultiClusterResources"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// Project is a logical top-level container for a set of kubernetes resources.
type Project struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Spec defines the desired identities of projects in this set.
	// +optional
	Spec ProjectSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status ProjectStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ProjectList is the whole list of all projects which owned by a tenant.
type ProjectList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// List of projects/namespaces.
	Items []Project `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// ProjectSpec is a description of a project.
type ProjectSpec struct {
	TenantID    string `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	DisplayName string `json:"displayName" protobuf:"bytes,2,opt,name=displayName"`
	Description string `json:"description,omitempty" protobuf:"bytes,3,opt,name=description"`
	// +optional
	// +patchMergeKey=name
	// +patchStrategy=merge
	Managers []Manager `json:"managers,omitempty" patchStrategy:"merge" patchMergeKey:"name" protobuf:"bytes,4,rep,name=managers"`
}

// Manager includes the username.
type Manager struct {
	Name        string `json:"name" protobuf:"bytes,1,opt,name=name"`
	DisplayName string `json:"displayName" protobuf:"bytes,2,opt,name=displayName"`
}

// ProjectStatus represents information about the status of a project.
type ProjectStatus struct {
	// +optional
	Locked *bool `json:"locked,omitempty" protobuf:"varint,1,opt,name=locked"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type Registry struct {
	metav1.TypeMeta `json:",inline"`
	// Standard object's metadata.
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// +optional
	Spec RegistrySpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// RegistryList is a resource containing a list of Registry objects.
type RegistryList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// +optional
	Items []Registry `json:"items,omitempty" protobuf:"bytes,2,opt,name=items"`
}

type RegistrySpec struct {
	// +optional
	TenantID string `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	// +optional
	DisplayName string `json:"displayName,omitempty" protobuf:"bytes,2,opt,name=displayName"`
	// +optional
	ClusterName string `json:"clusterName,omitempty" protobuf:"bytes,3,opt,name=clusterName"`
	// +optional
	URL string `json:"url,omitempty" protobuf:"bytes,4,opt,name=url"`
	// +optional
	UserName *string `json:"userName,omitempty" protobuf:"bytes,5,opt,name=userName"`
	// +optional
	Password *string `json:"password,omitempty" protobuf:"bytes,6,opt,name=password"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NamespaceSet is a namespace in cluster for project.
type NamespaceSet struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Spec defines the desired identities of projects in this set.
	// +optional
	Spec NamespaceSetSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status NamespaceSetStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// NamespaceSetSpec is a description of a namespace set.
type NamespaceSetSpec struct {
	TenantID      string `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ProjectName   string `json:"projectName" protobuf:"bytes,2,opt,name=projectName"`
	ClusterName   string `json:"clusterName" protobuf:"bytes,3,opt,name=clusterName"`
	NamespaceName string `json:"namespaceName" protobuf:"bytes,4,opt,name=namespaceName"`
}

// NamespaceSetPhase defines the phase of namespace set constructor
type NamespaceSetPhase string

const (
	// NamespaceSetPending means the namespace is wait initializing
	NamespaceSetPending NamespaceSetPhase = "pending"
	// NamespaceSetActive means the namespace is available for use in the system
	NamespaceSetActive NamespaceSetPhase = "active"
	// NamespaceSetFailed means the namespace is failed
	NamespaceSetFailed NamespaceSetPhase = "failed"
)

// NamespaceSetStatus represents information about the status of a namespace set.
type NamespaceSetStatus struct {
	// Phase is the current lifecycle phase of the namespace of cluster.
	// +optional
	Phase NamespaceSetPhase `json:"phase,omitempty" protobuf:"bytes,1,opt,name=phase,casttype=NamespaceSetPhase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,2,opt,name=reason"`
	// +optional
	Locked *bool `json:"locked,omitempty" protobuf:"varint,3,opt,name=locked"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NamespaceSetList is the whole list of all namespaces in clusters which
// owned by a project.
type NamespaceSetList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// List of namespaces in clusters.
	Items []NamespaceSet `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// Persistent event is a recorder of kubernetes event.
type PersistentEvent struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec PersistentEventSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status PersistentEventStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// PersistentEvent is the whole list of all clusters which owned by a tenant.
type PersistentEventList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// List of PersistentEvents
	Items []PersistentEvent `json:"items" protobuf:"bytes,2,rep,name=items"`
}

type PersistentEventSpec struct {
	TenantID          string            `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName       string            `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	PersistentBackEnd PersistentBackEnd `json:"persistentBackEnd,omitempty" protobuf:"bytes,3,opt,name=persistentBackEnd"`
	Version           string            `json:"version,omitempty" protobuf:"bytes,4,opt,name=version"`
}

type PersistentEventStatus struct {
	// Phase is the current lifecycle phase of the persistent event of cluster.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,1,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,2,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,3,name=retryCount"`
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time `json:"lastReInitializingTimestamp" protobuf:"bytes,4,name=lastReInitializingTimestamp"`
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,5,opt,name=version"`
}

type PersistentBackEnd struct {
	CLS *StorageBackEndCLS `json:"cls,omitempty" protobuf:"bytes,1,opt,name=cls"`
	ES  *StorageBackEndES  `json:"es,omitempty" protobuf:"bytes,2,opt,name=es"`
}

type StorageBackEndCLS struct {
	LogSetID string `json:"logSetId,omitempty" protobuf:"bytes,1,opt,name=logSetID"`
	TopicID  string `json:"topicId,omitempty" protobuf:"bytes,2,opt,name=topicID"`
}

type StorageBackEndES struct {
	IP        string `json:"ip,omitempty" protobuf:"bytes,1,opt,name=ip"`
	Port      int32  `json:"port,omitempty" protobuf:"varint,2,opt,name=port"`
	Scheme    string `json:"scheme,omitempty" protobuf:"bytes,3,opt,name=scheme"`
	IndexName string `json:"indexName,omitempty" protobuf:"bytes,4,opt,name=indexName"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// HelmProxyOptions is the query options to a Helm-api proxy call.
type HelmProxyOptions struct {
	metav1.TypeMeta `json:",inline"`

	// Path is the URL path to use for the current proxy request to helm-api.
	// +optional
	Path string `json:"path,omitempty" protobuf:"bytes,1,opt,name=path"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// Helm is a kubernetes package manager.
type Helm struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec HelmSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status HelmStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// HelmList is the whole list of all helms which owned by a tenant.
type HelmList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// List of Helms
	Items []Helm `json:"items" protobuf:"bytes,2,rep,name=items"`
}

type HelmSpec struct {
	TenantID          string      `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName       string      `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	VersionDeprecated HelmVersion `json:"version,omitempty" protobuf:"bytes,3,opt,name=version"`
	Version           string      `json:"version2,omitempty" protobuf:"bytes,4,opt,name=version2"`
}

type HelmVersion struct {
	Tiller  string `json:"tiller,omitempty" protobuf:"bytes,1,opt,name=tiller"`
	Swift   string `json:"swift,omitempty" protobuf:"bytes,2,opt,name=swift"`
	HelmAPI string `json:"helmAPI,omitempty" protobuf:"bytes,3,opt,name=helmAPI"`
}

type HelmStatus struct {
	// Phase is the current lifecycle phase of the helm of cluster.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,1,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,2,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,3,name=retryCount"`
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time `json:"lastReInitializingTimestamp" protobuf:"bytes,4,name=lastReInitializingTimestamp"`
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,5,opt,name=version"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// GameAppProxyOptions is the query options to a gameapp proxy call.
type GameAppProxyOptions struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	Namespace string `json:"namespace,omitempty" protobuf:"bytes,1,opt,name=namespace"`
	// +optional
	Name string `json:"name,omitempty" protobuf:"bytes,2,opt,name=name"`
	// +optional
	Action string `json:"action,omitempty" protobuf:"bytes,3,opt,name=action"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// GameApp is a kubernetes package manager.
type GameApp struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec GameAppSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status GameAppStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// GameAppList is the whole list of all GameApps which owned by a tenant.
type GameAppList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// List of GameApps
	Items []GameApp `json:"items" protobuf:"bytes,2,rep,name=items"`
}

type GameAppSpec struct {
	TenantID          string         `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName       string         `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	VersionDeprecated GameAppVersion `json:"version,omitempty" protobuf:"bytes,3,opt,name=version"`
	Version           string         `json:"version2,omitempty" protobuf:"bytes,4,opt,name=version2"`
}

type GameAppVersion struct {
	GameAppOperator string `json:"gameAppOperator,omitempty" protobuf:"bytes,1,opt,name=gameAppOperator"`
}

type GameAppStatus struct {
	// Phase is the current lifecycle phase of the GameApp of cluster.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,1,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,2,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,3,name=retryCount"`
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time `json:"lastReInitializingTimestamp" protobuf:"bytes,4,name=lastReInitializingTimestamp"`
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,5,opt,name=version"`
}

// +genclient
// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ConfigMap holds configuration data for tke to consume.
type ConfigMap struct {
	metav1.TypeMeta `json:",inline"`
	// Standard object's metadata.
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Data contains the configuration data.
	// Each key must consist of alphanumeric characters, '-', '_' or '.'.
	// Values with non-UTF-8 byte sequences must use the BinaryData field.
	// The keys stored in Data must not overlap with the keys in
	// the BinaryData field, this is enforced during validation process.
	// +optional
	Data map[string]string `json:"data,omitempty" protobuf:"bytes,2,rep,name=data"`

	// BinaryData contains the binary data.
	// Each key must consist of alphanumeric characters, '-', '_' or '.'.
	// BinaryData can contain byte sequences that are not in the UTF-8 range.
	// The keys stored in BinaryData must not overlap with the ones in
	// the Data field, this is enforced during validation process.
	// +optional
	BinaryData map[string][]byte `json:"binaryData,omitempty" protobuf:"bytes,3,rep,name=binaryData"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ConfigMapList is a resource containing a list of ConfigMap objects.
type ConfigMapList struct {
	metav1.TypeMeta `json:",inline"`

	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Items is the list of ConfigMaps.
	Items []ConfigMap `json:"items" protobuf:"bytes,2,rep,name=items"`
}

type ConditionStatus string

// These are valid condition statuses.
// "ConditionTrue" means a resource is in the condition.
// "ConditionFalse" means a resource is not in the condition.
// "ConditionUnknown" means server can't decide if a resource is in the condition
// or not.
const (
	ConditionTrue    ConditionStatus = "True"
	ConditionFalse   ConditionStatus = "False"
	ConditionUnknown ConditionStatus = "Unknown"
)

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// EniIpamd is a kubernetes eni ipamd.
type EniIpamd struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec EniIpamdSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status EniIpamdStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// EniIpamdList is the whole list of all eni ipamd which owned by a tenant.
type EniIpamdList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// List of eni ipamd
	Items []EniIpamd `json:"items" protobuf:"bytes,2,rep,name=items"`
}

type EniIpamdSpec struct {
	TenantID            string `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName         string `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	VpcID               string `json:"vpcID" protobuf:"bytes,3,opt,name=vpcID"`
	SubnetID            string `json:"subnetID" protobuf:"bytes,4,opt,name=subnetID"`
	SubnetCidr          string `json:"subnetCidr" protobuf:"bytes,5,opt,name=subnetCidr"`
	Zone                string `json:"zone" protobuf:"bytes,6,opt,name=zone"`
	VirtualScale        int32  `json:"virtualScale,omitempty" protobuf:"varint,7,opt,name=virtualScale"`
	Version             string `json:"version,omitempty" protobuf:"bytes,8,opt,name=version"`
	ClaimExpiredSeconds int32  `json:"claimExpiredSeconds,omitempty" protobuf:"varint,9,opt,name=claimExpiredSeconds"`
}

// EniIpamdCondition contains details for the current condition of this eniipamd.
type EniIpamdCondition struct {
	// Type is the type of the condition.
	Type string `json:"type" protobuf:"bytes,1,opt,name=type"`
	// Status is the status of the condition.
	// Can be True, False, Unknown.
	Status ConditionStatus `json:"status" protobuf:"bytes,2,opt,name=status,casttype=ConditionStatus"`
	// Last time we probed the condition.
	// +optional
	LastProbeTime metav1.Time `json:"lastProbeTime,omitempty" protobuf:"bytes,3,opt,name=lastProbeTime"`
	// Last time the condition transitioned from one status to another.
	// +optional
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty" protobuf:"bytes,4,opt,name=lastTransitionTime"`
	// Unique, one-word, CamelCase reason for the condition's last transition.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,5,opt,name=reason"`
	// Human-readable message indicating details about last transition.
	// +optional
	Message string `json:"message,omitempty" protobuf:"bytes,6,opt,name=message"`
}

type EniIpamdStatus struct {
	// Phase is the current lifecycle phase of the eni ipamd of cluster.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,1,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,2,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,3,name=retryCount"`
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time `json:"lastReInitializingTimestamp" protobuf:"bytes,4,name=lastReInitializingTimestamp"`
	// +optional
	// +patchMergeKey=type
	// +patchStrategy=merge
	Conditions []EniIpamdCondition `json:"conditions,omitempty" patchStrategy:"merge" patchMergeKey:"type" protobuf:"bytes,5,rep,name=conditions"`
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,6,opt,name=version"`
}

// These are internal finalizer values to tke, must be qualified name unless defined here.
const (
	FinalizerEniIpamd FinalizerName = "tke.cloud.tencent.com/eni-ipamd"
)

// +genclient
// +genclient:nonNamespaced
// +genclient:onlyVerbs=list,get
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAPIResource contains the GKV for the current kubernetes cluster
type ClusterAPIResource struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// groupVersion is the group and version this APIResourceList is for.
	GroupVersion string `json:"groupVersion" protobuf:"bytes,2,opt,name=groupVersion"`
	// resources contains the name of the resources and if they are namespaced.
	APIResources []ClusterGroupAPIResource `json:"resources" protobuf:"bytes,3,rep,name=resources"`
}

// APIResource specifies the name of a resource and whether it is namespaced.
type ClusterGroupAPIResource struct {
	// name is the plural name of the resource.
	Name string `json:"name" protobuf:"bytes,1,opt,name=name"`
	// singularName is the singular name of the resource.  This allows clients to handle plural and singular opaquely.
	// The singularName is more correct for reporting status on a single item and both singular and plural are allowed
	// from the kubectl CLI interface.
	SingularName string `json:"singularName" protobuf:"bytes,6,opt,name=singularName"`
	// namespaced indicates if a resource is namespaced or not.
	Namespaced bool `json:"namespaced" protobuf:"varint,2,opt,name=namespaced"`
	// group is the preferred group of the resource.  Empty implies the group of the containing resource list.
	// For subresources, this may have a different value, for example: Scale".
	Group string `json:"group,omitempty" protobuf:"bytes,8,opt,name=group"`
	// version is the preferred version of the resource.  Empty implies the version of the containing resource list
	// For subresources, this may have a different value, for example: v1 (while inside a v1beta1 version of the core resource's group)".
	Version string `json:"version,omitempty" protobuf:"bytes,9,opt,name=version"`
	// kind is the kind for the resource (e.g. 'Foo' is the kind for a resource 'foo')
	Kind string `json:"kind" protobuf:"bytes,3,opt,name=kind"`
	// verbs is a list of supported kube verbs (this includes get, list, watch, create,
	// update, patch, delete, deletecollection, and proxy)
	Verbs []string `json:"verbs" protobuf:"bytes,4,rep,name=verbs"`
	// shortNames is a list of suggested short names of the resource.
	ShortNames []string `json:"shortNames,omitempty" protobuf:"bytes,5,rep,name=shortNames"`
	// categories is a list of the grouped resources this resource belongs to (e.g. 'all')
	Categories []string `json:"categories,omitempty" protobuf:"bytes,7,rep,name=categories"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAPIResourceList is the whole list of all ClusterAPIResource.
type ClusterAPIResourceList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// List of ClusterAPIResource
	Items []ClusterAPIResource `json:"items" protobuf:"bytes,2,rep,name=items"`
	// Failed Group Error
	FailedGroupError string `json:"failedGroupError" protobuf:"bytes,3,rep,name=failedGroupError"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAPIResourceOptions is the query options.
type ClusterAPIResourceOptions struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	OnlySecure bool `json:"onlySecure,omitempty" protobuf:"varint,1,opt,name=onlySecure"`
}

// AddonLevel indicates the level of cluster addon.
type AddonLevel string

// These are valid level of addon.
const (
	// LevelBasic is level for basic of cluster.
	LevelBasic AddonLevel = "Basic"
	// LevelEnhance is level for enhance of cluster.
	LevelEnhance AddonLevel = "Enhance"
)

// +genclient
// +genclient:nonNamespaced
// +genclient:onlyVerbs=list,get
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAddon contains the Addon component for the current kubernetes cluster
type ClusterAddon struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// Spec defines the desired identities of addons in this set.
	// +optional
	Spec ClusterAddonSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status ClusterAddonStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAddonList is the whole list of all ClusterAddon.
type ClusterAddonList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// List of ClusterAddon
	Items []ClusterAddon `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// ClusterAddonSpec indicates the specifications of the ClusterAddon.
type ClusterAddonSpec struct {
	// Addon type, one of Helm, PersistentEvent or LogCollector etc.
	Type string `json:"type" protobuf:"bytes,1,opt,name=type"`
	// AddonLevel is level of cluster addon.
	Level AddonLevel `json:"level" protobuf:"bytes,2,opt,name=level,casttype=AddonLevel"`
	// Version
	Version string `json:"version" protobuf:"bytes,3,opt,name=version"`
}

// ClusterAddonStatus is information about the current status of a ClusterAddon.
type ClusterAddonStatus struct {
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	// Phase is the current lifecycle phase of the addon of cluster.
	// +optional
	Phase string `json:"phase,omitempty" protobuf:"bytes,2,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,3,opt,name=reason"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// AddonType records the all addons of platform available.
type AddonType struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// +optional
	Spec AddonTypeSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// AddonTypeList is a resource containing a list of AddonType objects.
type AddonTypeList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// +optional
	Items []AddonType `json:"items,omitempty" protobuf:"bytes,2,opt,name=items"`
}

// AddonTypeSpec describes the attributes of a AddonType.
type AddonTypeSpec struct {
	// Addon type, one of Helm, PersistentEvent or LogCollector etc.
	Type string `json:"type" protobuf:"bytes,1,opt,name=type"`
	// TenantIDs are whitelist of the addonType
	TenantIDs     []string     `json:"tenantIDs" protobuf:"bytes,2,opt,name=tenantids"`
	Level         AddonLevel   `json:"level" protobuf:"bytes,3,opt,name=level,casttype=AddonLevel"`
	LatestVersion string       `json:"latestVersion" protobuf:"bytes,4,opt,name=latestVersion"`
	Description   string       `json:"description,omitempty" protobuf:"bytes,5,opt,name=description"`
	VersionSpec   []VersionMap `json:"versionSpec,omitempty" protobuf:"bytes,6,opt,name=versionSpec"`
}

// VersionMap indicates the specifications of every componet version in the addon.
type VersionMap struct {
	Name string `json:"name,omitempty" protobuf:"bytes,1,opt,name=name"`
	Tag  string `json:"tag,omitempty" protobuf:"bytes,2,opt,name=version"`
	// FullImagePath can be used for overwrite addon componet .Spec.Image
	FullImagePath string `json:"fullImagePath,omitempty" protobuf:"bytes,3,opt,name=fullImagePath"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:onlyVerbs=list
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAddonType records the all addons of cluster available.
type ClusterAddonType struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// Addon type, one of Helm, PersistentEvent or LogCollector etc.
	Type string `json:"type" protobuf:"bytes,2,opt,name=type"`
	// AddonLevel is level of cluster addon.
	Level AddonLevel `json:"level" protobuf:"bytes,3,opt,name=level,casttype=AddonLevel"`
	// LatestVersion is latest version of the addon.
	LatestVersion string `json:"latestVersion" protobuf:"bytes,4,opt,name=latestVersion"`
	Description   string `json:"description,omitempty" protobuf:"bytes,5,opt,name=description"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAddonTypeList is a resource containing a list of ClusterAddonType objects.
type ClusterAddonTypeList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// +optional
	Items []ClusterAddonType `json:"items,omitempty" protobuf:"bytes,2,opt,name=items"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// GPUManager is a kind of device plugin for kubelet to help manage GPUs.
type GPUManager struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec GPUManagerSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status GPUManagerStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// GPUManagerList is the whole list of all GPUManager which owned by a tenant.
type GPUManagerList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// List of GPUManagers
	Items []GPUManager `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// GPUManagerSpec describes the attributes of a GPUManager.
type GPUManagerSpec struct {
	TenantID    string `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName string `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	Version     string `json:"version,omitempty" protobuf:"bytes,3,opt,name=version"`
}

// GPUManagerStatus is information about the current status of a GPUManager.
type GPUManagerStatus struct {
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	// Phase is the current lifecycle phase of the GPUManager of cluster.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,2,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,3,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,4,name=retryCount"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// CLSLogConfigProxyOptions is the query options to a kube-apiserver proxy call for CLS LogConfig crd object.
type CLSLogConfigProxyOptions struct {
	metav1.TypeMeta `json:",inline"`

	Name string `json:"name,omitempty" protobuf:"bytes,2,opt,name=name"`

	// +optional
	Action string `json:"action,omitempty" protobuf:"bytes,3,opt,name=action"`

	// A selector to restrict the list of returned objects by their fields.
	// Defaults to everything.
	// +optional
	FieldSelector string `json:"fieldSelector,omitempty" protobuf:"bytes,4,opt,name=fieldSelector"`

	// Limit specifies the maximum number of results to return from the server. The server may
	// not support this field on all resource types, but if it does and more results remain it
	// will set the continue field on the returned list object.
	// +optional
	Limit int64 `json:"limit,omitempty" protobuf:"varint,5,opt,name=limit"`

	// Continue is a token returned by the server that lets a client retrieve chunks of results
	// from the server by specifying limit. The server may reject requests for continuation tokens
	// it does not recognize and will return a 410 error if the token can no longer be used because
	// it has expired.
	// +optional
	Continue string `json:"continue,omitempty" protobuf:"bytes,6,opt,name=continue"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// LogCollectorProxyOptions is the query options to a kube-apiserver proxy call for LogCollector crd object.
type LogCollectorProxyOptions struct {
	metav1.TypeMeta `json:",inline"`

	Namespace string `json:"namespace,omitempty" protobuf:"bytes,1,opt,name=namespace"`
	Name      string `json:"name,omitempty" protobuf:"bytes,2,opt,name=name"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// RecommendationProxyOptions is the query options to a kube-apiserver proxy call for Recommendation crd object.
type RecommendationProxyOptions struct {
	metav1.TypeMeta `json:",inline"`

	Path string `json:"path,omitempty" protobuf:"bytes,1,opt,name=namespace"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// LogCollector is a manager to collect logs of workload.
type LogCollector struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Spec defines the desired identities of LogCollector.
	// +optional
	Spec LogCollectorSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status LogCollectorStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// LogCollectorList is the whole list of all LogCollector which owned by a tenant.
type LogCollectorList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// List of volume decorators.
	Items []LogCollector `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// LogCollectorSpec describes the attributes of a LogCollector.
type LogCollectorSpec struct {
	TenantID    string `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName string `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	Version     string `json:"version,omitempty" protobuf:"bytes,3,opt,name=version"`
}

// LogCollectorStatus is information about the current status of a LogCollector.
type LogCollectorStatus struct {
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	// Phase is the current lifecycle phase of the LogCollector of cluster.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,2,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,3,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,4,name=retryCount"`
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time `json:"lastReInitializingTimestamp" protobuf:"bytes,5,name=lastReInitializingTimestamp"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// LBCFProxyOptions is the query options to a kube-apiserver proxy call.
type LBCFProxyOptions struct {
	metav1.TypeMeta `json:",inline"`

	Namespace string `json:"namespace,omitempty" protobuf:"bytes,1,opt,name=namespace"`
	Name      string `json:"name,omitempty" protobuf:"bytes,2,opt,name=name"`
	Action    string `json:"action,omitempty" protobuf:"bytes,3,opt,name=action"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// LBCF is a kubernetes load balancer manager.
type LBCF struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec LBCFSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status LBCFStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// LBCFList is the whole list of all helms which owned by a tenant.
type LBCFList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// List of LBCFs
	Items []LBCF `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// LBCFSpec describes the attributes on a Helm.
type LBCFSpec struct {
	TenantID    string `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName string `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	Version     string `json:"version,omitempty" protobuf:"bytes,3,opt,name=version"`
}

// LBCFStatus is information about the current status of a Helm.
type LBCFStatus struct {
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	// Phase is the current lifecycle phase of the helm of cluster.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,2,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,3,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,4,name=retryCount"`
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time `json:"lastReInitializingTimestamp" protobuf:"bytes,5,name=lastReInitializingTimestamp"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// CFSProxyOptions is the query options to a cfs-api proxy call.
type CFSProxyOptions struct {
	metav1.TypeMeta `json:",inline"`

	// Path is the URL path to use for the current proxy request to cfs-api.
	// +optional
	Path string `json:"path,omitempty" protobuf:"bytes,1,opt,name=path"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// CFS is a kubernetes csi for using CFS.
type CFS struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec CFSSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status CFSStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// CFSList is the whole list of all CFSs.
type CFSList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// List of CFSs
	Items []CFS `json:"cfss" protobuf:"bytes,2,rep,name=items"`
}

// CFSSpec describes the attributes on a CFS.
type CFSSpec struct {
	TenantID    string `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName string `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	Version     string `json:"version,omitempty" protobuf:"bytes,5,opt,name=version"`
	Rootdir     string `json:"rootdir,omitempty" protobuf:"bytes,6,opt,name=rootdir"`
}

// CFSStatus is information about the current status of a CFS.
type CFSStatus struct {
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	// Phase is the current lifecycle phase of cfs.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,2,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,3,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,4,name=retryCount"`
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time `json:"lastReInitializingTimestamp" protobuf:"bytes,5,name=lastReInitializingTimestamp"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// COSProxyOptions is the query options to a cos-api proxy call.
type COSProxyOptions struct {
	metav1.TypeMeta `json:",inline"`

	// Path is the URL path to use for the current proxy request to cos-api.
	// +optional
	Path string `json:"path,omitempty" protobuf:"bytes,1,opt,name=path"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// COS is a kubernetes csi for using COS.
type COS struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec COSSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status COSStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// COSList is the whole list of all COSs.
type COSList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// List of COSs
	Items []COS `json:"coss" protobuf:"bytes,2,rep,name=items"`
}

// COSSpec describes the attributes on a COS.
type COSSpec struct {
	TenantID    string `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName string `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	Version     string `json:"version,omitempty" protobuf:"bytes,5,opt,name=version"`
	Rootdir     string `json:"rootdir,omitempty" protobuf:"bytes,6,opt,name=rootdir"`
}

// COSStatus is information about the current status of a COS.
type COSStatus struct {
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	// Phase is the current lifecycle phase of cos.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,2,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,3,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,4,name=retryCount"`
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time `json:"lastReInitializingTimestamp" protobuf:"bytes,5,name=lastReInitializingTimestamp"`
}

// +genclient
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAuthentication is the resource that record the users tencentcloud accounts.
type ClusterAuthentication struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// +optional
	TenantID string `json:"tenantID" protobuf:"bytes,2,opt,name=tenantID"`
	// +optional
	ClusterName string `json:"clusterName" protobuf:"bytes,3,opt,name=clusterName"`
	// +optional
	OwnerUIN string `json:"ownerUIN" protobuf:"bytes,4,opt,name=ownerUIN"`
	// +optional
	SubAccountUIN string `json:"subAccountUIN" protobuf:"bytes,5,opt,name=subAccountUIN"`
	// +optional
	ServiceRole bool `json:"serviceRole" protobuf:"bytes,6,opt,name=serviceRole"`
	// +optional
	AuthenticationInfo AuthenticationInfo `json:"authenticationInfo" protobuf:"bytes,7,opt,name=authenticationInfo"`
}

type AuthenticationInfo struct {
	ClientCertificate []byte `json:"clientCertificate" protobuf:"bytes,1,opt,name=clientCertificate"`
	ClientKey         []byte `json:"clientKey" protobuf:"bytes,2,opt,name=clientKey"`
	CommonName        string `json:"commonName" protobuf:"bytes,3,opt,name=commonName"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAuthenticationList is the whole list of all ClusterAuthentications.
type ClusterAuthenticationList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// List of ClusterAuthentications
	Items []ClusterAuthentication `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NodeProblemDetector is the whole list of all NPD which owned by a tenant.
type NodeProblemDetector struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec NodeProblemDetectorSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status NodeProblemDetectorStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NodeProblemDetectorList is the whole list of all NodeProblemDetector.
type NodeProblemDetectorList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// List of NPDs.
	Items []NodeProblemDetector `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// NodeProblemDetectorStatus is information about the current status of a NodeProblemDetector.
type NodeProblemDetectorStatus struct {
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	// Phase is the current lifecycle phase of npd.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,2,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,3,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,4,name=retryCount"`
}

// NodeProblemDetectorSpec describes the attributes on a NodeProblemDetector.
type NodeProblemDetectorSpec struct {
	TenantID    string   `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName string   `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	Version     string   `json:"version,omitempty" protobuf:"bytes,3,opt,name=version"`
	SelfCure    bool     `json:"selfCure" protobuf:"bytes,4,opt,name=selfcure"`
	UIN         string   `json:"uin" protobuf:"bytes,5,opt,name=uin"`
	SubUin      string   `json:"subUin" protobuf:"bytes,6,opt,name=subuin"`
	Policys     []Policy `json:"policys" protobuf:"bytes,7,opt,name=policys"`
}

type Policy struct {
	ConditionType string  `json:"conditionType" protobuf:"bytes,1,opt,name=conditionType"`
	Actions       Actions `json:"actions" protobuf:"bytes,2,opt,name=actions"`
}

type Actions struct {
	Runtime RunTime `json:"runtime" protobuf:"bytes,1,opt,name=runtime"`
	NodePod NodePod `json:"nodePod" protobuf:"bytes,2,opt,name=nodePod"`
	CVM     CVM     `json:"CVM" protobuf:"bytes,3,opt,name=CVM"`
}

type NodePod struct {
	RetryCounts int32 `json:"retryCounts" protobuf:"bytes,1,opt,name=retryCounts"`
	Evict       bool  `json:"evict" protobuf:"bytes,2,opt,name=evict"`
}

type RunTime struct {
	RetryCounts    int32 `json:"retryCounts" protobuf:"bytes,1,opt,name=retryCounts"`
	ReStartDokcer  bool  `json:"reStartDokcer" protobuf:"bytes,2,opt,name=reStartDokcer"`
	ReStartKubelet bool  `json:"reStartKubelet" protobuf:"bytes,3,opt,name=reStartKubelet"`
}

type CVM struct {
	RetryCounts int32 `json:"retryCounts" protobuf:"bytes,1,opt,name=retryCounts"`
	ReBootCVM   bool  `json:"reBootCVM" protobuf:"bytes,2,opt,name=reBootCVM"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// ImageP2PProxyOptions is the query options to a p2p-api proxy call.
type ImageP2PProxyOptions struct {
	metav1.TypeMeta `json:",inline"`
	// Path is the URL path to use for the current proxy request to p2p-api.
	// +optional
	Path string `json:"path,omitempty" protobuf:"bytes,1,opt,name=path"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ImageP2P is a P2P based distribution image system for Kubernetes.
type ImageP2P struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec ImageP2PSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status ImageP2PStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ImageP2PList is the whole list of all ImageP2Ps.
type ImageP2PList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// List of P2Ps
	Items []ImageP2P `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// ImageP2PSpec describes the attributes on a ImageP2Ps.
type ImageP2PSpec struct {
	TenantID          string `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName       string `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	RegistryAddress   string `json:"registryAddress" protobuf:"bytes,3,opt,name=registryAddress"`
	AgentDownloadRate int64  `json:"agentDownloadRate" protobuf:"bytes,4,opt,name=agentDownloadRate"`
	Version           string `json:"version,omitempty" protobuf:"bytes,5,opt,name=version"`
	ProxyReplicas     int64  `json:"proxyReplicas" protobuf:"bytes,6,opt,name=proxyReplicas"`
	TrackerReplicas   int64  `json:"trackerReplicas" protobuf:"bytes,7,opt,name=trackerReplicas"`
	P2PAgentWorkDir   string `json:"p2pAgentWorkDir,omitempty" protobuf:"bytes,8,opt,name=p2pAgentWorkDir"`
	P2PProxyWorkDir   string `json:"p2pProxyWorkDir,omitempty" protobuf:"bytes,9,opt,name=p2pProxyWorkDir"`
}

// ImageP2PStatus is information about the current status of a P2P.
type ImageP2PStatus struct {
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	// Phase is the current lifecycle phase of p2p.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,2,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,3,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,4,name=retryCount"`
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time `json:"lastReInitializingTimestamp" protobuf:"bytes,5,name=lastReInitializingTimestamp"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// DNSAutoscaler is a kubernetes Horizontal cluster-proportional-autoscaler
type DNSAutoscaler struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec DNSAutoscalerSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status DNSAutoscalerStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// DNSAutoscalerSpec defines the desired identities of DNSAutoscaler.
type DNSAutoscalerSpec struct {
	TenantID    string `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName string `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	Version     string `json:"version,omitempty" protobuf:"bytes,3,opt,name=version"`
	Target      string `json:"target,omitempty" protobuf:"bytes,4,opt,name=target"`
}

// DNSAutoscaler Status is information about the current status of a DNSAutoscaler.
type DNSAutoscalerStatus struct {
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	// Phase is the current lifecycle phase of cos.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,2,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,3,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,4,name=retryCount"`
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time `json:"lastReInitializingTimestamp" protobuf:"bytes,5,name=lastReInitializingTimestamp"`

	// Components is a generic status holder for the top level resource
	// +optional
	Components []ComponentStatus `json:"components,omitempty" protobuf:"bytes,6,name=components"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// DNSAutoscalerList is the whole list of all DNSAutoscaler which owned by a tenant.
type DNSAutoscalerList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// List of DNSAutoscalers
	Items []DNSAutoscaler `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// ComponentStatus is a generic status holder for objects
type ComponentStatus struct {
	// Link to object
	// +optional
	Link string `json:"link,omitempty" protobuf:"bytes,1,opt,name=link"`
	// Name of object
	Name string `json:"name,omitempty" protobuf:"bytes,2,opt,name=name"`
	// Kind of object
	Kind string `json:"kind,omitempty" protobuf:"bytes,3,opt,name=kind"`
	// Object group
	// +optional
	Group string `json:"group,omitempty" protobuf:"bytes,4,opt,name=group"`
	// Status. Values: InProgress, Ready, Unknown
	Status string `json:"status,omitempty" protobuf:"bytes,5,opt,name=status"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NodeLocalDNSCache is a kubernetes node local dns cache
type NodeLocalDNSCache struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec NodeLocalDNSCacheSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status NodeLocalDNSCacheStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// NodeLocalDNSCacheSpec defines the desired identities of NodeLocalDNSCache.
type NodeLocalDNSCacheSpec struct {
	TenantID    string `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName string `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	Version     string `json:"version,omitempty" protobuf:"bytes,3,opt,name=version"`
	Target      string `json:"target,omitempty" protobuf:"bytes,4,opt,name=target"`
}

// NodeLocalDNSCache Status is information about the current status of a NodeLocalDNSCache.
type NodeLocalDNSCacheStatus struct {
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	// Phase is the current lifecycle phase of cos.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,2,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,3,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,4,name=retryCount"`
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time `json:"lastReInitializingTimestamp" protobuf:"bytes,5,name=lastReInitializingTimestamp"`

	// Components is a generic status holder for the top level resource
	// +optional
	Components []ComponentStatus `json:"components,omitempty" protobuf:"bytes,6,name=components"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NodeLocalDNSCacheList is the whole list of all NodeLocalDNSCache which owned by a tenant.
type NodeLocalDNSCacheList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// List of NodeLocalDNSCaches
	Items []NodeLocalDNSCache `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// OOMGuard is a addon to avoid frequently cgroup OOM for Kubernetes.
type OOMGuard struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec OOMGuardSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status OOMGuardStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// OOMGuardList is the whole list of all OOMGuardList.
type OOMGuardList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// List of OOMGuard
	Items []OOMGuard `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// OOMGuardSpec describes the attributes on a OOMGuard.
type OOMGuardSpec struct {
	TenantID    string `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName string `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	Version     string `json:"version,omitempty" protobuf:"bytes,5,opt,name=version"`
}

// OOMGuardStatus is information about the current status of a OOMGuard.
type OOMGuardStatus struct {
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	// Phase is the current lifecycle phase of p2p.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,2,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,3,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,4,name=retryCount"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// Tcr is a enterprise private docker registry.
type Tcr struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec TcrSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status TcrStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// TcrList is the whole list of all TCRs.
type TcrList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// List of TCRs
	Items []Tcr `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// HostItem ...
type HostItem struct {
	Domain   string `json:"domain" protobuf:"bytes,1,opt,name=domain"`
	IP       string `json:"ip" protobuf:"bytes,2,opt,name=ip"`
	Disabled bool   `json:"disabled" protobuf:"bytes,3,opt,name=disabled"`
	V6       bool   `json:"v6" protobuf:"bytes,4,opt,name=v6"`
}

// TcrItem ...
type TcrItem struct {
	Name                  string `json:"name" protobuf:"bytes,1,opt,name=name"`
	RegistrySource        string `json:"registrySource" protobuf:"bytes,2,opt,name=registrySource"`
	Username              string `json:"username" protobuf:"bytes,3,opt,name=username"`
	Password              string `json:"password" protobuf:"bytes,4,opt,name=password"`
	Server                string `json:"server" protobuf:"bytes,5,opt,name=server"`
	NamespacesString      string `json:"namespaces" protobuf:"bytes,6,opt,name=namespaces"`
	ServiceAccountsString string `json:"serviceAccounts" protobuf:"bytes,7,opt,name=serviceAccounts"`
}

// TcrSpec describes the attributes of the Tcr.
type TcrSpec struct {
	TenantID    string `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName string `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	Version     string `json:"version" protobuf:"bytes,3,opt,name=version"`

	Hosts []HostItem `json:"hosts" protobuf:"bytes,4,rep,name=hosts"`
	Tcrs  []TcrItem  `json:"tcrs" protobuf:"bytes,5,rep,name=tcrss"`

	Accelerated           bool   `json:"accelerated" protobuf:"varint,6,opt,name=accelerated"`
	AcceleratedNamespaces string `json:"acceleratedNamespaces" protobuf:"bytes,7,opt,name=acceleratedNamespaces"`
}

// TcrStatus is information about the current status of a TCR.
type TcrStatus struct {
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	// Phase is the current lifecycle phase of TCR.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,2,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,3,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,4,name=retryCount"`
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time `json:"lastReInitializingTimestamp" protobuf:"bytes,5,name=lastReInitializingTimestamp"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAddonKind returns a kind name
type ClusterAddonKind struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta `protobuf:"bytes,1,opt,name=objectMeta"`
}

// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAddonKindList saves the addons grouped by kind
type ClusterAddonKindList struct {
	metav1.TypeMeta

	// +optional
	metav1.ListMeta `protobuf:"bytes,1,opt,name=listMeta"`

	// +optional
	Items []ClusterAddonKind `protobuf:"bytes,2,rep,name=items"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// HPCProxyOptions is the query options to a hpc proxy call.
type HPCProxyOptions struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	Namespace string `json:"namespace,omitempty" protobuf:"bytes,1,opt,name=namespace"`
	// +optional
	Name string `json:"name,omitempty" protobuf:"bytes,2,opt,name=name"`
	// +optional
	Action string `json:"action,omitempty" protobuf:"bytes,3,opt,name=action"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// HPC is a kubernetes Horizontal pod cron autoscaler
type HPC struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec HPCSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status HPCStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// HPCSpec defines the desired identities of HPC.
type HPCSpec struct {
	TenantID    string    `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName string    `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	Version     string    `json:"version,omitempty" protobuf:"bytes,3,opt,name=version"`
	ChartInfo   ChartSpec `json:"chartInfo,omitempty" protobuf:"bytes,4,opt,name=chartInfo"`
	UIN         string    `json:"uin" protobuf:"bytes,5,opt,name=uin"`
	SubUin      string    `json:"subUin" protobuf:"bytes,6,opt,name=subuin"`
}

// ChartSpec defines the desired identities of Chart.
type ChartSpec struct {
	Chart           string `json:"chart" protobuf:"bytes,1,opt,name=chart"`
	ChartFrom       string `json:"chartFrom" protobuf:"bytes,2,opt,name=chartFrom"`
	ChartVersion    string `json:"chartVersion" protobuf:"bytes,3,opt,name=chartVersion"`
	ChartNamespace  string `json:"chartNamespace" protobuf:"bytes,4,opt,name=chartNamespace"`
	ChartInstanceID string `json:"chartInstanceID" protobuf:"bytes,5,opt,name=chartInstanceID"`
}

// HPCStatus is information about the current status of a HPC.
type HPCStatus struct {
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	// Phase is the current lifecycle phase of cos.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,2,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,3,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,4,name=retryCount"`
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time `json:"lastReInitializingTimestamp" protobuf:"bytes,5,name=lastReInitializingTimestamp"`

	// Components is a generic status holder for the top level resource
	// +optional
	Components []ComponentStatus `json:"components,omitempty" protobuf:"bytes,6,name=components"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// HPCList is the whole list of all HPC which owned by a tenant.
type HPCList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// List of HPCs
	Items []HPC `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NginxIngressProxyOptions is the query options to a kube-apiserver proxy call for LogCollector crd object.
type NginxIngressProxyOptions struct {
	metav1.TypeMeta `json:",inline"`

	Name string `json:"name,omitempty" protobuf:"bytes,2,opt,name=name"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NginxIngress is a set of kubernetes nginx ingress controller.
type NginxIngress struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// +optional
	Spec NginxIngressSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`

	// +optional
	Status NginxIngressStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

type NginxIngressSpec struct {
	TenantID          string                 `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName       string                 `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	Version           string                 `json:"version,omitempty" protobuf:"bytes,3,opt,name=version"`
	VersionDeprecated NginxControllerVersion `json:"versionDeprecated,omitempty" protobuf:"bytes,4,opt,name=VersionDeprecated"`
}

type NginxControllerVersion struct {
	Controller string `json:"controller,omitempty" protobuf:"bytes,1,opt,name=tiller"`
}

type NginxIngressStatus struct {
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	// Phase is the current lifecycle phase of the LogCollector of cluster.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,2,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,3,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,4,name=retryCount"`
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time `json:"lastReInitializingTimestamp" protobuf:"bytes,5,name=lastReInitializingTimestamp"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type NginxIngressList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta `protobuf:"bytes,1,opt,name=listMeta"`

	// List of NginxIngress
	// +optional
	Items []NginxIngress `json:"items" protobuf:"bytes,2,rep,name=items"`
}

type FilterThreshold struct {
	CpuAvgUsage5M float32 `json:"CpuAvgUsage5M,string" protobuf:"varint,1,name=CpuAvgUsage5M"`
	CpuMaxUsage1H float32 `json:"CpuMaxUsage1H,string" protobuf:"varint,2,name=CpuMaxUsage1H"`
	MemAvgUsage5M float32 `json:"MemAvgUsage5M,string" protobuf:"varint,3,name=MemAvgUsage5M"`
	MemMaxUsage1H float32 `json:"MemMaxUsage1H,string" protobuf:"varint,4,name=MemMaxUsage1H"`
}

type ScoreThreshold struct {
	CpuAvgUsage5M float32 `json:"CpuAvgUsage5M,string" protobuf:"varint,1,name=CpuAvgUsage5M"`
	CpuMaxUsage1H float32 `json:"CpuMaxUsage1H,string" protobuf:"varint,2,name=CpuMaxUsage1H"`
	CpuMaxUsage1D float32 `json:"CpuMaxUsage1D,string" protobuf:"varint,3,name=CpuMaxUsage1D"`
	MemAvgUsage5M float32 `json:"MemAvgUsage5M,string" protobuf:"varint,4,name=MemAvgUsage5M"`
	MemMaxUsage1H float32 `json:"MemMaxUsage1H,string" protobuf:"varint,5,name=MemMaxUsage1H"`
	MemMaxUsage1D float32 `json:"MemMaxUsage1D,string" protobuf:"varint,6,name=MemMaxUsage1D"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// DynamicScheduler is a addon to extend native kube-scheduler functions
type DynamicScheduler struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec DynamicSchedulerSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status DynamicSchedulerStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// DynamicSchedulerList is the whole list of all DynamicScheduler.
type DynamicSchedulerList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// List of Scheduler
	Items []DynamicScheduler `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// DynamicSchedulerSpec describes the attributes on a DynamicScheduler addon.
type DynamicSchedulerSpec struct {
	TenantID          string          `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName       string          `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	Version           string          `json:"version,omitempty" protobuf:"bytes,3,opt,name=version"`
	PrometheusBackEnd string          `json:"prometheusBackEnd,omitempty" protobuf:"bytes,4,opt,name=prometheusBackEnd"`
	FilterThreshold   FilterThreshold `json:"filterThreshold,omitempty" protobuf:"bytes,5,opt,name=filterThreshold"`
	ScoreThreshold    ScoreThreshold  `json:"scoreThreshold,omitempty" protobuf:"bytes,6,opt,name=scoreThreshold"`
}

// DynamicSchedulerStatus is information about the current status of a DynamicScheduler addon.
type DynamicSchedulerStatus struct {
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	// Phase is the current lifecycle phase of DynamicScheduler addon.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,2,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,3,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,4,name=retryCount"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// SchedulerPolicy describes a struct for a policy resource used in api.
type SchedulerPolicy struct {
	metav1.TypeMeta `json:",inline"`
	// Holds the information to configure the fit predicate functions
	Predicates []PredicatePolicy `json:"predicates" protobuf:"bytes,1,rep,name=predicates"`
	// Holds the information to configure the priority functions
	Priorities []PriorityPolicy `json:"priorities" protobuf:"bytes,2,rep,name=priorities"`
	// Holds the information to communicate with the extender(s)
	Extenders []Extender `json:"extenders" protobuf:"bytes,3,rep,name=extenders"`
	// RequiredDuringScheduling affinity is not symmetric, but there is an implicit PreferredDuringScheduling affinity rule
	// corresponding to every RequiredDuringScheduling affinity rule.
	// HardPodAffinitySymmetricWeight represents the weight of implicit PreferredDuringScheduling affinity rule, in the range 1-100.
	HardPodAffinitySymmetricWeight int32 `json:"hardPodAffinitySymmetricWeight" protobuf:"varint,4,opt,name=hardPodAffinitySymmetricWeight"`

	// When AlwaysCheckAllPredicates is set to true, scheduler checks all
	// the configured predicates even after one or more of them fails.
	// When the flag is set to false, scheduler skips checking the rest
	// of the predicates after it finds one predicate that failed.
	AlwaysCheckAllPredicates bool `json:"alwaysCheckAllPredicates" protobuf:"varint,5,opt,name=alwaysCheckAllPredicates"`
}

// PredicatePolicy describes a struct of a predicate policy.
type PredicatePolicy struct {
	// Identifier of the predicate policy
	// For a custom predicate, the name can be user-defined
	// For the Kubernetes provided predicates, the name is the identifier of the pre-defined predicate
	Name string `json:"name" protobuf:"bytes,1,opt,name=name"`
	// Holds the parameters to configure the given predicate
	Argument *PredicateArgument `json:"argument" protobuf:"bytes,2,opt,name=argument"`
}

// PriorityPolicy describes a struct of a priority policy.
type PriorityPolicy struct {
	// Identifier of the priority policy
	// For a custom priority, the name can be user-defined
	// For the Kubernetes provided priority functions, the name is the identifier of the pre-defined priority function
	Name string `json:"name" protobuf:"bytes,1,opt,name=name"`
	// The numeric multiplier for the node scores that the priority function generates
	// The weight should be non-zero and can be a positive or a negative integer
	Weight int64 `json:"weight" protobuf:"varint,2,opt,name=weight"`
	// Holds the parameters to configure the given priority function
	Argument *PriorityArgument `json:"argument" protobuf:"bytes,3,opt,name=argument"`
}

// PredicateArgument represents the arguments to configure predicate functions in scheduler policy configuration.
// Only one of its members may be specified
type PredicateArgument struct {
	// The predicate that provides affinity for pods belonging to a service
	// It uses a label to identify nodes that belong to the same "group"
	ServiceAffinity *ServiceAffinity `json:"serviceAffinity" protobuf:"bytes,1,opt,name=serviceAffinity"`
	// The predicate that checks whether a particular node has a certain label
	// defined or not, regardless of value
	LabelsPresence *LabelsPresence `json:"labelsPresence" protobuf:"bytes,2,opt,name=labelsPresence"`
}

// PriorityArgument represents the arguments to configure priority functions in scheduler policy configuration.
// Only one of its members may be specified
type PriorityArgument struct {
	// The priority function that ensures a good spread (anti-affinity) for pods belonging to a service
	// It uses a label to identify nodes that belong to the same "group"
	ServiceAntiAffinity *ServiceAntiAffinity `json:"serviceAntiAffinity" protobuf:"bytes,1,opt,name=serviceAntiAffinity"`
	// The priority function that checks whether a particular node has a certain label
	// defined or not, regardless of value
	LabelPreference *LabelPreference `json:"labelPreference" protobuf:"bytes,2,opt,name=labelPreference"`
	// The RequestedToCapacityRatio priority function is parametrized with function shape.
	RequestedToCapacityRatioArguments *RequestedToCapacityRatioArguments `json:"requestedToCapacityRatioArguments" protobuf:"bytes,3,opt,name=requestedToCapacityRatioArguments"`
}

// ServiceAffinity holds the parameters that are used to configure the corresponding predicate in scheduler policy configuration.
type ServiceAffinity struct {
	// The list of labels that identify node "groups"
	// All of the labels should match for the node to be considered a fit for hosting the pod
	Labels []string `json:"labels" protobuf:"bytes,1,rep,name=labels"`
}

// LabelsPresence holds the parameters that are used to configure the corresponding predicate in scheduler policy configuration.
type LabelsPresence struct {
	// The list of labels that identify node "groups"
	// All of the labels should be either present (or absent) for the node to be considered a fit for hosting the pod
	Labels []string `json:"labels" protobuf:"bytes,1,rep,name=labels"`
	// The boolean flag that indicates whether the labels should be present or absent from the node
	Presence bool `json:"presence" protobuf:"varint,2,opt,name=presence"`
}

// ServiceAntiAffinity holds the parameters that are used to configure the corresponding priority function
type ServiceAntiAffinity struct {
	// Used to identify node "groups"
	Label string `json:"label" protobuf:"bytes,1,opt,name=label"`
}

// LabelPreference holds the parameters that are used to configure the corresponding priority function
type LabelPreference struct {
	// Used to identify node "groups"
	Label string `json:"label" protobuf:"bytes,1,opt,name=label"`
	// This is a boolean flag
	// If true, higher priority is given to nodes that have the label
	// If false, higher priority is given to nodes that do not have the label
	Presence bool `json:"presence" protobuf:"varint,2,opt,name=presence"`
}

// RequestedToCapacityRatioArguments holds arguments specific to RequestedToCapacityRatio priority function.
type RequestedToCapacityRatioArguments struct {
	// Array of point defining priority function shape.
	Shape     []UtilizationShapePoint `json:"shape" protobuf:"bytes,1,rep,name=shape"`
	Resources []ResourceSpec          `json:"resources,omitempty" protobuf:"bytes,2,rep,name=resources"`
}

// UtilizationShapePoint represents single point of priority function shape.
type UtilizationShapePoint struct {
	// Utilization (x axis). Valid values are 0 to 100. Fully utilized node maps to 100.
	Utilization int32 `json:"utilization" protobuf:"varint,1,opt,name=utilization"`
	// Score assigned to given utilization (y axis). Valid values are 0 to 10.
	Score int32 `json:"score" protobuf:"varint,2,opt,name=score"`
}

// ResourceSpec represents single resource and weight for bin packing of priority RequestedToCapacityRatioArguments.
type ResourceSpec struct {
	// Name of the resource to be managed by RequestedToCapacityRatio function.
	Name string `json:"name" protobuf:"bytes,1,opt,name=name"`
	// Weight of the resource.
	Weight int64 `json:"weight,omitempty" protobuf:"varint,2,opt,name=weight"`
}

// ExtenderManagedResource describes the arguments of extended resources
// managed by an extender.
type ExtenderManagedResource struct {
	// Name is the extended resource name.
	Name string `json:"name" protobuf:"bytes,1,opt,name=name"`
	// IgnoredByScheduler indicates whether kube-scheduler should ignore this
	// resource when applying predicates.
	IgnoredByScheduler bool `json:"ignoredByScheduler,omitempty" protobuf:"varint,2,opt,name=ignoredByScheduler"`
}

// ExtenderTLSConfig contains settings to enable TLS with extender
type ExtenderTLSConfig struct {
	// Server should be accessed without verifying the TLS certificate. For testing only.
	Insecure bool `json:"insecure,omitempty" protobuf:"varint,1,opt,name=insecure"`
	// ServerName is passed to the server for SNI and is used in the client to check server
	// certificates against. If ServerName is empty, the hostname used to contact the
	// server is used.
	ServerName string `json:"serverName,omitempty" protobuf:"bytes,2,opt,name=serverName"`

	// Server requires TLS client certificate authentication
	CertFile string `json:"certFile,omitempty" protobuf:"bytes,3,opt,name=certFile"`
	// Server requires TLS client certificate authentication
	KeyFile string `json:"keyFile,omitempty" protobuf:"bytes,4,opt,name=keyFile"`
	// Trusted root certificates for server
	CAFile string `json:"caFile,omitempty" protobuf:"bytes,5,opt,name=caFile"`

	// CertData holds PEM-encoded bytes (typically read from a client certificate file).
	// CertData takes precedence over CertFile
	CertData []byte `json:"certData,omitempty" protobuf:"bytes,6,opt,name=certData"`
	// KeyData holds PEM-encoded bytes (typically read from a client certificate key file).
	// KeyData takes precedence over KeyFile
	KeyData []byte `json:"keyData,omitempty" protobuf:"bytes,7,opt,name=keyData"`
	// CAData holds PEM-encoded bytes (typically read from a root certificates bundle).
	// CAData takes precedence over CAFile
	CAData []byte `json:"caData,omitempty" protobuf:"bytes,8,opt,name=caData"`
}

// Extender holds the parameters used to communicate with the extender. If a verb is unspecified/empty,
// it is assumed that the extender chose not to provide that extension.
type Extender struct {
	// URLPrefix at which the extender is available
	URLPrefix string `json:"urlPrefix" protobuf:"bytes,1,opt,name=urlPrefix"`
	// Verb for the filter call, empty if not supported. This verb is appended to the URLPrefix when issuing the filter call to extender.
	FilterVerb string `json:"filterVerb,omitempty" protobuf:"bytes,2,opt,name=filterVerb"`
	// Verb for the preempt call, empty if not supported. This verb is appended to the URLPrefix when issuing the preempt call to extender.
	PreemptVerb string `json:"preemptVerb,omitempty" protobuf:"bytes,3,opt,name=preemptVerb"`
	// Verb for the prioritize call, empty if not supported. This verb is appended to the URLPrefix when issuing the prioritize call to extender.
	PrioritizeVerb string `json:"prioritizeVerb,omitempty" protobuf:"bytes,4,opt,name=prioritizeVerb"`
	// The numeric multiplier for the node scores that the prioritize call generates.
	// The weight should be a positive integer
	Weight int64 `json:"weight,omitempty" protobuf:"varint,5,opt,name=weight"`
	// Verb for the bind call, empty if not supported. This verb is appended to the URLPrefix when issuing the bind call to extender.
	// If this method is implemented by the extender, it is the extender's responsibility to bind the pod to apiserver. Only one extender
	// can implement this function.
	BindVerb string `json:"bindVerb,omitempty" protobuf:"bytes,6,opt,name=bindVerb"`
	// EnableHTTPS specifies whether https should be used to communicate with the extender
	EnableHTTPS bool `json:"enableHttps,omitempty" protobuf:"varint,7,opt,name=enableHttps"`
	// TLSConfig specifies the transport layer security config
	TLSConfig *ExtenderTLSConfig `json:"tlsConfig,omitempty" protobuf:"bytes,8,opt,name=tlsConfig"`
	// HTTPTimeout specifies the timeout duration for a call to the extender. Filter timeout fails the scheduling of the pod. Prioritize
	// timeout is ignored, k8s/other extenders priorities are used to select the node.
	HTTPTimeout time.Duration `json:"httpTimeout,omitempty" protobuf:"varint,9,opt,name=httpTimeout,casttype=time.Duration"`
	// NodeCacheCapable specifies that the extender is capable of caching node information,
	// so the scheduler should only send minimal information about the eligible nodes
	// assuming that the extender already cached full details of all nodes in the cluster
	NodeCacheCapable bool `json:"nodeCacheCapable,omitempty" protobuf:"varint,10,opt,name=nodeCacheCapable"`
	// ManagedResources is a list of extended resources that are managed by
	// this extender.
	// - A pod will be sent to the extender on the Filter, Prioritize and Bind
	//   (if the extender is the binder) phases iff the pod requests at least
	//   one of the extended resources in this list. If empty or unspecified,
	//   all pods will be sent to this extender.
	// - If IgnoredByScheduler is set to true for a resource, kube-scheduler
	//   will skip checking the resource in predicates.
	// +optional
	ManagedResources []ExtenderManagedResource `json:"managedResources,omitempty" protobuf:"bytes,11,rep,name=managedResources"`
	// Ignorable specifies if the extender is ignorable, i.e. scheduling should not
	// fail when the extender returns an error or is not reachable.
	Ignorable bool `json:"ignorable,omitempty" protobuf:"varint,12,opt,name=ignorable"`
	// Verb for the prebind call, empty if not supported. This verb is appended to the URLPrefix when issuing the prebind call to extender.
	PrebindVerb string `json:"prebindVerb,omitempty" protobuf:"bytes,13,opt,name=prebindVerb"`
	// Verb for the unreserve call, empty if not supported. This verb is appended to the URLPrefix when issuing the bind call to extender.
	UnreserveVerb string `json:"unreserveVerb,omitempty" protobuf:"bytes,14,opt,name=unreserveVerb"`
}

type CBSProxyOptions struct {
	metav1.TypeMeta `json:",inline"`

	// Path is the URL path to use for the current proxy request to cbs-api.
	// +optional
	Path string `json:"path,omitempty" protobuf:"bytes,1,opt,name=path"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// CBS is a kubernetes csi for using CBS.
type CBS struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec CBSSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status CBSStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// CBSList is the whole list of all CBSs.
type CBSList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// List of CBSs
	Items []CBS `json:"cbss" protobuf:"bytes,2,rep,name=items"`
}

// CBSSpec describes the attributes on a CBS.
type CBSSpec struct {
	TenantID    string `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName string `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	Version     string `json:"version,omitempty" protobuf:"bytes,5,opt,name=version"`
	Rootdir     string `json:"rootdir,omitempty" protobuf:"bytes,6,opt,name=rootdir"`
}

// CBSStatus is information about the current status of a CBS.
type CBSStatus struct {
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	// Phase is the current lifecycle phase of cbs.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,2,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,3,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,4,name=retryCount"`
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time `json:"lastReInitializingTimestamp" protobuf:"bytes,5,name=lastReInitializingTimestamp"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NetworkPolicy is a addon to set network policy for Kubernetes.
type NetworkPolicy struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec NetworkPolicySpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status NetworkPolicyStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NetworkPolicyList is the whole list of all NetworkPolicyList.
type NetworkPolicyList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// List of NetworkPolicy
	Items []NetworkPolicy `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// NetworkPolicySpec describes the attributes on a NetworkPolicy.
type NetworkPolicySpec struct {
	TenantID    string `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName string `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	Version     string `json:"version,omitempty" protobuf:"bytes,5,opt,name=version"`
}

// NetworkPolicyStatus is information about the current status of a NetworkPolicy.
type NetworkPolicyStatus struct {
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	// Phase is the current lifecycle phase of p2p.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,2,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,3,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,4,name=retryCount"`
}

type LoadThreshold struct {
	CpuAvgUsage5M  int32 `json:"CpuAvgUsage5M" protobuf:"varint,1,name=CpuAvgUsage5M"`
	CpuTargetUsage int32 `json:"CpuTargetUsage" protobuf:"varint,2,name=CpuTargetUsage"`
	MemAvgUsage5M  int32 `json:"MemAvgUsage5M" protobuf:"varint,3,name=MemAvgUsage5M"`
	MemTargetUsage int32 `json:"MemTargetUsage" protobuf:"varint,4,name=MemTargetUsage"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// DeScheduler is a addon to extend native kube-scheduler functions
type DeScheduler struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec DeSchedulerSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status DeSchedulerStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// DeSchedulerList is the whole list of all DeScheduler.
type DeSchedulerList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// List of Scheduler
	Items []DeScheduler `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// DeSchedulerSpec describes the attributes on a DeScheduler addon.
type DeSchedulerSpec struct {
	TenantID          string        `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName       string        `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	Version           string        `json:"version,omitempty" protobuf:"bytes,3,opt,name=version"`
	PrometheusBackEnd string        `json:"prometheusBackEnd,omitempty" protobuf:"bytes,4,opt,name=prometheusBackEnd"`
	LoadThreshold     LoadThreshold `json:"loadThreshold,omitempty" protobuf:"bytes,5,opt,name=loadThreshold"`
}

// DeSchedulerStatus is information about the current status of a DeScheduler addon.
type DeSchedulerStatus struct {
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	// Phase is the current lifecycle phase of DeScheduler addon.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,2,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,3,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,4,name=retryCount"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// OLMProxyOptions is the query options to a hpc proxy call.
type OLMProxyOptions struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	Namespace string `json:"namespace,omitempty" protobuf:"bytes,1,opt,name=namespace"`
	// +optional
	Name string `json:"name,omitempty" protobuf:"bytes,2,opt,name=name"`
	// +optional
	Action string `json:"action,omitempty" protobuf:"bytes,3,opt,name=action"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// OLM is a tool for operator lifecycle manager on kubernetes
type OLM struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec OLMSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status OLMStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// OLMSpec defines the desired identities of OLM.
type OLMSpec struct {
	TenantID    string    `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName string    `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	Version     string    `json:"version,omitempty" protobuf:"bytes,3,opt,name=version"`
	ChartInfo   ChartSpec `json:"chartInfo,omitempty" protobuf:"bytes,4,opt,name=chartInfo"`
	UIN         string    `json:"uin" protobuf:"bytes,5,opt,name=uin"`
	SubUin      string    `json:"subUin" protobuf:"bytes,6,opt,name=subuin"`
}

// OLMStatus is information about the current status of a OLM.
type OLMStatus struct {
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	// Phase is the current lifecycle phase of cos.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,2,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,3,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,4,name=retryCount"`
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time `json:"lastReInitializingTimestamp" protobuf:"bytes,5,name=lastReInitializingTimestamp"`

	// Components is a generic status holder for the top level resource
	// +optional
	Components []ComponentStatus `json:"components,omitempty" protobuf:"bytes,6,name=components"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// OLMList is the whole list of all OLM which owned by a tenant.
type OLMList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// List of OLMs
	Items []OLM `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// Environment defines a context associating cluster and a bundle of selected apps to provide
// a environment for specific tasks, such like a.i. jobs, big data jobs, etc.
type Environment struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	Spec   EnvironmentSpec   `json:"spec" protobuf:"bytes,2,name=spec"`
	Status EnvironmentStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type EnvironmentList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	Items []Environment `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// EnvironmentType is the type of environment, which at this moment we only support AI environment
type EnvironmentType string

const (
	AIEnvironment EnvironmentType = "ai"
)

type AppSpec struct {
	Chart          string `json:"chart" protobuf:"bytes,1,name=chart"`
	ChartFrom      string `json:"chartFrom" protobuf:"bytes,2,name=chartFrom"`
	ChartNamespace string `json:"chartNamespace" protobuf:"bytes,3,name=chartNamespace"`
	ReleaseName    string `json:"releaseName,omitempty" protobuf:"bytes,4,name=releaseName"`
}

type EnvironmentSpec struct {
	// EnvType defines what kind of environment is defined
	EnvType EnvironmentType `json:"envType" protobuf:"bytes,1,name=envType"`

	// TenantID and ClusterName are used to locate the cluster that is bound to this Environment
	TenantID      string `json:"tenantID" protobuf:"bytes,2,name=tenantID"`
	ClusterName   string `json:"clusterName" protobuf:"bytes,3,name=clusterName"`
	UIN           string `json:"uin" protobuf:"bytes,4,name=uin"`
	SubAccountUin string `json:"subAccountUin" protobuf:"bytes,5,name=subAccountUin"`

	// Users customized the environment setting by customized what Apps shall be installed
	Apps []AppSpec `json:"apps,omitempty" protobuf:"bytes,6,rep,name=apps"`
}

type EnvironmentStatus struct {
	// Supports maps the name of Apps to the kind of resources the environment
	// is now supporting due to the installation of the Apps
	Supports map[string]metav1.GroupVersionKind `json:"supports" protobuf:"bytes,1,rep,name=supports"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// QGPU is a better GPU manager for GPU sharing and isolation.
type QGPU struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec QGPUSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	// +optional
	Status QGPUStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// QGPUList is the whole list of all QGPU.
type QGPUList struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	// List of QGPU
	Items []QGPU `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// QGPUSpec describes the attributes on a QGPUSpec.
type QGPUSpec struct {
	TenantID    string `json:"tenantID" protobuf:"bytes,1,opt,name=tenantID"`
	ClusterName string `json:"clusterName" protobuf:"bytes,2,opt,name=clusterName"`
	Priority    string `json:"priority" protobuf:"bytes,3,opt,name=priority"`
	Version     string `json:"version,omitempty" protobuf:"bytes,5,opt,name=version"`
}

// QGPUStatus is information about the current status of a QGPUStatus.
type QGPUStatus struct {
	// +optional
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	// Phase is the current lifecycle phase of p2p.
	// +optional
	Phase AddonPhase `json:"phase,omitempty" protobuf:"bytes,2,opt,name=phase"`
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,3,opt,name=reason"`
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32 `json:"retryCount" protobuf:"varint,4,name=retryCount"`
}
