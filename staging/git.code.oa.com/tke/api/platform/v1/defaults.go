/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKE
 * available.
 *
 * Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License"); you may not use this
 * file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

package v1

import (
	"k8s.io/apimachinery/pkg/runtime"
)

// revive:disable:var-naming

func addDefaultingFuncs(scheme *runtime.Scheme) error {
	return RegisterDefaults(scheme)
}

func SetDefaults_ClusterStatus(obj *ClusterStatus) {
	if obj.Phase == "" {
		obj.Phase = ClusterInitializing
	}
}

// SetDefaults_NamespaceSetStatus sets additional defaults namespace status.
func SetDefaults_NamespaceSetStatus(obj *NamespaceSetStatus) {
	if obj.Phase == "" {
		obj.Phase = NamespaceSetPending
	}
}

// SetDefaults_PersistentEventStatus sets additional defaults persistent event status.
func SetDefaults_PersistentEventStatus(obj *PersistentEventStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

func SetDefaults_HelmStatus(obj *HelmStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

func SetDefaults_GameAppStatus(obj *GameAppStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

func SetDefaults_ConfigMap(obj *ConfigMap) {
	if obj.Data == nil {
		obj.Data = make(map[string]string)
	}
}

func SetDefaults_EniIpamdSpec(obj *EniIpamdSpec) {
	if obj.VirtualScale == 0 {
		obj.VirtualScale = 10
	}
}

func SetDefaults_EniIpamdStatus(obj *EniIpamdStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

func SetDefaults_GPUManagerStatus(obj *GPUManagerStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

func SetDefaults_LogCollectorStatus(obj *LogCollectorStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

func SetDefaults_LBCFStatus(obj *LBCFStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

func SetDefaults_CFSStatus(obj *CFSStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

func SetDefaults_COSStatus(obj *COSStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

func SetDefaults_NodeProblemDetectorStatus(obj *NodeProblemDetectorStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

func SetDefaults_ImageP2PStatus(obj *ImageP2PStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

func SetDefaults_OOMGuardStatus(obj *OOMGuardStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

func SetDefaults_DNSAutoscalerStatus(obj *DNSAutoscalerStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

func SetDefaults_NodeLocalDNSCacheStatus(obj *NodeLocalDNSCacheStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

// SetDefaults_TcrStatus init TCR status
func SetDefaults_TcrStatus(obj *TcrStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

func SetDefaults_HPCStatus(obj *HPCStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

// SetDefaults_NginxIngressStatus init NginxIngress status
func SetDefaults_NginxIngressStatus(obj *NginxIngressStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

func SetDefaults_DynamicSchedulerStatus(obj *DynamicSchedulerStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

func SetDefaults_CBSStatus(obj *CBSStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

func SetDefaults_NetworkPolicyStatus(obj *NetworkPolicyStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

func SetDefaults_DeSchedulerStatus(obj *DeSchedulerStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

func SetDefaults_OLMStatus(obj *OLMStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

func SetDefaults_QGPUStatus(obj *QGPUStatus) {
	if obj.Phase == "" {
		obj.Phase = AddonPhaseInitializing
	}
}

// revive:enable:var-naming
