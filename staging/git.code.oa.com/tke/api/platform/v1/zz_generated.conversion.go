// +build !ignore_autogenerated

/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by conversion-gen. DO NOT EDIT.

package v1

import (
	unsafe "unsafe"

	platform "git.code.oa.com/tke/api/platform"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	conversion "k8s.io/apimachinery/pkg/conversion"
	runtime "k8s.io/apimachinery/pkg/runtime"
)

func init() {
	localSchemeBuilder.Register(RegisterConversions)
}

// RegisterConversions adds conversion functions to the given scheme.
// Public to allow building arbitrary schemes.
func RegisterConversions(s *runtime.Scheme) error {
	if err := s.AddGeneratedConversionFunc((*Actions)(nil), (*platform.Actions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Actions_To_platform_Actions(a.(*Actions), b.(*platform.Actions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.Actions)(nil), (*Actions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_Actions_To_v1_Actions(a.(*platform.Actions), b.(*Actions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*AddonType)(nil), (*platform.AddonType)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_AddonType_To_platform_AddonType(a.(*AddonType), b.(*platform.AddonType), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.AddonType)(nil), (*AddonType)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_AddonType_To_v1_AddonType(a.(*platform.AddonType), b.(*AddonType), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*AddonTypeList)(nil), (*platform.AddonTypeList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_AddonTypeList_To_platform_AddonTypeList(a.(*AddonTypeList), b.(*platform.AddonTypeList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.AddonTypeList)(nil), (*AddonTypeList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_AddonTypeList_To_v1_AddonTypeList(a.(*platform.AddonTypeList), b.(*AddonTypeList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*AddonTypeSpec)(nil), (*platform.AddonTypeSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_AddonTypeSpec_To_platform_AddonTypeSpec(a.(*AddonTypeSpec), b.(*platform.AddonTypeSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.AddonTypeSpec)(nil), (*AddonTypeSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_AddonTypeSpec_To_v1_AddonTypeSpec(a.(*platform.AddonTypeSpec), b.(*AddonTypeSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*AppSpec)(nil), (*platform.AppSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_AppSpec_To_platform_AppSpec(a.(*AppSpec), b.(*platform.AppSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.AppSpec)(nil), (*AppSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_AppSpec_To_v1_AppSpec(a.(*platform.AppSpec), b.(*AppSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*AuthenticationInfo)(nil), (*platform.AuthenticationInfo)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_AuthenticationInfo_To_platform_AuthenticationInfo(a.(*AuthenticationInfo), b.(*platform.AuthenticationInfo), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.AuthenticationInfo)(nil), (*AuthenticationInfo)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_AuthenticationInfo_To_v1_AuthenticationInfo(a.(*platform.AuthenticationInfo), b.(*AuthenticationInfo), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CBS)(nil), (*platform.CBS)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CBS_To_platform_CBS(a.(*CBS), b.(*platform.CBS), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.CBS)(nil), (*CBS)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_CBS_To_v1_CBS(a.(*platform.CBS), b.(*CBS), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CBSList)(nil), (*platform.CBSList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CBSList_To_platform_CBSList(a.(*CBSList), b.(*platform.CBSList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.CBSList)(nil), (*CBSList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_CBSList_To_v1_CBSList(a.(*platform.CBSList), b.(*CBSList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CBSSpec)(nil), (*platform.CBSSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CBSSpec_To_platform_CBSSpec(a.(*CBSSpec), b.(*platform.CBSSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.CBSSpec)(nil), (*CBSSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_CBSSpec_To_v1_CBSSpec(a.(*platform.CBSSpec), b.(*CBSSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CBSStatus)(nil), (*platform.CBSStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CBSStatus_To_platform_CBSStatus(a.(*CBSStatus), b.(*platform.CBSStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.CBSStatus)(nil), (*CBSStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_CBSStatus_To_v1_CBSStatus(a.(*platform.CBSStatus), b.(*CBSStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CFS)(nil), (*platform.CFS)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CFS_To_platform_CFS(a.(*CFS), b.(*platform.CFS), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.CFS)(nil), (*CFS)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_CFS_To_v1_CFS(a.(*platform.CFS), b.(*CFS), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CFSList)(nil), (*platform.CFSList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CFSList_To_platform_CFSList(a.(*CFSList), b.(*platform.CFSList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.CFSList)(nil), (*CFSList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_CFSList_To_v1_CFSList(a.(*platform.CFSList), b.(*CFSList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CFSSpec)(nil), (*platform.CFSSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CFSSpec_To_platform_CFSSpec(a.(*CFSSpec), b.(*platform.CFSSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.CFSSpec)(nil), (*CFSSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_CFSSpec_To_v1_CFSSpec(a.(*platform.CFSSpec), b.(*CFSSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CFSStatus)(nil), (*platform.CFSStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CFSStatus_To_platform_CFSStatus(a.(*CFSStatus), b.(*platform.CFSStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.CFSStatus)(nil), (*CFSStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_CFSStatus_To_v1_CFSStatus(a.(*platform.CFSStatus), b.(*CFSStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CLSLogConfigProxyOptions)(nil), (*platform.CLSLogConfigProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CLSLogConfigProxyOptions_To_platform_CLSLogConfigProxyOptions(a.(*CLSLogConfigProxyOptions), b.(*platform.CLSLogConfigProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.CLSLogConfigProxyOptions)(nil), (*CLSLogConfigProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_CLSLogConfigProxyOptions_To_v1_CLSLogConfigProxyOptions(a.(*platform.CLSLogConfigProxyOptions), b.(*CLSLogConfigProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*COS)(nil), (*platform.COS)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_COS_To_platform_COS(a.(*COS), b.(*platform.COS), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.COS)(nil), (*COS)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_COS_To_v1_COS(a.(*platform.COS), b.(*COS), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*COSList)(nil), (*platform.COSList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_COSList_To_platform_COSList(a.(*COSList), b.(*platform.COSList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.COSList)(nil), (*COSList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_COSList_To_v1_COSList(a.(*platform.COSList), b.(*COSList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*COSSpec)(nil), (*platform.COSSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_COSSpec_To_platform_COSSpec(a.(*COSSpec), b.(*platform.COSSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.COSSpec)(nil), (*COSSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_COSSpec_To_v1_COSSpec(a.(*platform.COSSpec), b.(*COSSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*COSStatus)(nil), (*platform.COSStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_COSStatus_To_platform_COSStatus(a.(*COSStatus), b.(*platform.COSStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.COSStatus)(nil), (*COSStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_COSStatus_To_v1_COSStatus(a.(*platform.COSStatus), b.(*COSStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CVM)(nil), (*platform.CVM)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CVM_To_platform_CVM(a.(*CVM), b.(*platform.CVM), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.CVM)(nil), (*CVM)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_CVM_To_v1_CVM(a.(*platform.CVM), b.(*CVM), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ChartSpec)(nil), (*platform.ChartSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ChartSpec_To_platform_ChartSpec(a.(*ChartSpec), b.(*platform.ChartSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ChartSpec)(nil), (*ChartSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ChartSpec_To_v1_ChartSpec(a.(*platform.ChartSpec), b.(*ChartSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*Cluster)(nil), (*platform.Cluster)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Cluster_To_platform_Cluster(a.(*Cluster), b.(*platform.Cluster), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.Cluster)(nil), (*Cluster)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_Cluster_To_v1_Cluster(a.(*platform.Cluster), b.(*Cluster), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAPIResource)(nil), (*platform.ClusterAPIResource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAPIResource_To_platform_ClusterAPIResource(a.(*ClusterAPIResource), b.(*platform.ClusterAPIResource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAPIResource)(nil), (*ClusterAPIResource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAPIResource_To_v1_ClusterAPIResource(a.(*platform.ClusterAPIResource), b.(*ClusterAPIResource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAPIResourceList)(nil), (*platform.ClusterAPIResourceList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAPIResourceList_To_platform_ClusterAPIResourceList(a.(*ClusterAPIResourceList), b.(*platform.ClusterAPIResourceList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAPIResourceList)(nil), (*ClusterAPIResourceList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAPIResourceList_To_v1_ClusterAPIResourceList(a.(*platform.ClusterAPIResourceList), b.(*ClusterAPIResourceList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAPIResourceOptions)(nil), (*platform.ClusterAPIResourceOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAPIResourceOptions_To_platform_ClusterAPIResourceOptions(a.(*ClusterAPIResourceOptions), b.(*platform.ClusterAPIResourceOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAPIResourceOptions)(nil), (*ClusterAPIResourceOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAPIResourceOptions_To_v1_ClusterAPIResourceOptions(a.(*platform.ClusterAPIResourceOptions), b.(*ClusterAPIResourceOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAddOn)(nil), (*platform.ClusterAddOn)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAddOn_To_platform_ClusterAddOn(a.(*ClusterAddOn), b.(*platform.ClusterAddOn), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAddOn)(nil), (*ClusterAddOn)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAddOn_To_v1_ClusterAddOn(a.(*platform.ClusterAddOn), b.(*ClusterAddOn), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAddon)(nil), (*platform.ClusterAddon)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAddon_To_platform_ClusterAddon(a.(*ClusterAddon), b.(*platform.ClusterAddon), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAddon)(nil), (*ClusterAddon)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAddon_To_v1_ClusterAddon(a.(*platform.ClusterAddon), b.(*ClusterAddon), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAddonKind)(nil), (*platform.ClusterAddonKind)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAddonKind_To_platform_ClusterAddonKind(a.(*ClusterAddonKind), b.(*platform.ClusterAddonKind), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAddonKind)(nil), (*ClusterAddonKind)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAddonKind_To_v1_ClusterAddonKind(a.(*platform.ClusterAddonKind), b.(*ClusterAddonKind), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAddonKindList)(nil), (*platform.ClusterAddonKindList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAddonKindList_To_platform_ClusterAddonKindList(a.(*ClusterAddonKindList), b.(*platform.ClusterAddonKindList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAddonKindList)(nil), (*ClusterAddonKindList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAddonKindList_To_v1_ClusterAddonKindList(a.(*platform.ClusterAddonKindList), b.(*ClusterAddonKindList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAddonList)(nil), (*platform.ClusterAddonList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAddonList_To_platform_ClusterAddonList(a.(*ClusterAddonList), b.(*platform.ClusterAddonList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAddonList)(nil), (*ClusterAddonList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAddonList_To_v1_ClusterAddonList(a.(*platform.ClusterAddonList), b.(*ClusterAddonList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAddonSpec)(nil), (*platform.ClusterAddonSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAddonSpec_To_platform_ClusterAddonSpec(a.(*ClusterAddonSpec), b.(*platform.ClusterAddonSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAddonSpec)(nil), (*ClusterAddonSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAddonSpec_To_v1_ClusterAddonSpec(a.(*platform.ClusterAddonSpec), b.(*ClusterAddonSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAddonStatus)(nil), (*platform.ClusterAddonStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAddonStatus_To_platform_ClusterAddonStatus(a.(*ClusterAddonStatus), b.(*platform.ClusterAddonStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAddonStatus)(nil), (*ClusterAddonStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAddonStatus_To_v1_ClusterAddonStatus(a.(*platform.ClusterAddonStatus), b.(*ClusterAddonStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAddonType)(nil), (*platform.ClusterAddonType)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAddonType_To_platform_ClusterAddonType(a.(*ClusterAddonType), b.(*platform.ClusterAddonType), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAddonType)(nil), (*ClusterAddonType)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAddonType_To_v1_ClusterAddonType(a.(*platform.ClusterAddonType), b.(*ClusterAddonType), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAddonTypeList)(nil), (*platform.ClusterAddonTypeList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAddonTypeList_To_platform_ClusterAddonTypeList(a.(*ClusterAddonTypeList), b.(*platform.ClusterAddonTypeList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAddonTypeList)(nil), (*ClusterAddonTypeList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAddonTypeList_To_v1_ClusterAddonTypeList(a.(*platform.ClusterAddonTypeList), b.(*ClusterAddonTypeList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAddress)(nil), (*platform.ClusterAddress)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAddress_To_platform_ClusterAddress(a.(*ClusterAddress), b.(*platform.ClusterAddress), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAddress)(nil), (*ClusterAddress)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAddress_To_v1_ClusterAddress(a.(*platform.ClusterAddress), b.(*ClusterAddress), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterApplyOptions)(nil), (*platform.ClusterApplyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterApplyOptions_To_platform_ClusterApplyOptions(a.(*ClusterApplyOptions), b.(*platform.ClusterApplyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterApplyOptions)(nil), (*ClusterApplyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterApplyOptions_To_v1_ClusterApplyOptions(a.(*platform.ClusterApplyOptions), b.(*ClusterApplyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAuthentication)(nil), (*platform.ClusterAuthentication)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAuthentication_To_platform_ClusterAuthentication(a.(*ClusterAuthentication), b.(*platform.ClusterAuthentication), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAuthentication)(nil), (*ClusterAuthentication)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAuthentication_To_v1_ClusterAuthentication(a.(*platform.ClusterAuthentication), b.(*ClusterAuthentication), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAuthenticationList)(nil), (*platform.ClusterAuthenticationList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAuthenticationList_To_platform_ClusterAuthenticationList(a.(*ClusterAuthenticationList), b.(*platform.ClusterAuthenticationList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAuthenticationList)(nil), (*ClusterAuthenticationList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAuthenticationList_To_v1_ClusterAuthenticationList(a.(*platform.ClusterAuthenticationList), b.(*ClusterAuthenticationList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterComponent)(nil), (*platform.ClusterComponent)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterComponent_To_platform_ClusterComponent(a.(*ClusterComponent), b.(*platform.ClusterComponent), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterComponent)(nil), (*ClusterComponent)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterComponent_To_v1_ClusterComponent(a.(*platform.ClusterComponent), b.(*ClusterComponent), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterComponentReplicas)(nil), (*platform.ClusterComponentReplicas)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterComponentReplicas_To_platform_ClusterComponentReplicas(a.(*ClusterComponentReplicas), b.(*platform.ClusterComponentReplicas), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterComponentReplicas)(nil), (*ClusterComponentReplicas)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterComponentReplicas_To_v1_ClusterComponentReplicas(a.(*platform.ClusterComponentReplicas), b.(*ClusterComponentReplicas), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterCondition)(nil), (*platform.ClusterCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterCondition_To_platform_ClusterCondition(a.(*ClusterCondition), b.(*platform.ClusterCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterCondition)(nil), (*ClusterCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterCondition_To_v1_ClusterCondition(a.(*platform.ClusterCondition), b.(*ClusterCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterCredential)(nil), (*platform.ClusterCredential)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterCredential_To_platform_ClusterCredential(a.(*ClusterCredential), b.(*platform.ClusterCredential), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterCredential)(nil), (*ClusterCredential)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterCredential_To_v1_ClusterCredential(a.(*platform.ClusterCredential), b.(*ClusterCredential), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterFeature)(nil), (*platform.ClusterFeature)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterFeature_To_platform_ClusterFeature(a.(*ClusterFeature), b.(*platform.ClusterFeature), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterFeature)(nil), (*ClusterFeature)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterFeature_To_v1_ClusterFeature(a.(*platform.ClusterFeature), b.(*ClusterFeature), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterGroupAPIResource)(nil), (*platform.ClusterGroupAPIResource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterGroupAPIResource_To_platform_ClusterGroupAPIResource(a.(*ClusterGroupAPIResource), b.(*platform.ClusterGroupAPIResource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterGroupAPIResource)(nil), (*ClusterGroupAPIResource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterGroupAPIResource_To_v1_ClusterGroupAPIResource(a.(*platform.ClusterGroupAPIResource), b.(*ClusterGroupAPIResource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterList)(nil), (*platform.ClusterList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterList_To_platform_ClusterList(a.(*ClusterList), b.(*platform.ClusterList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterList)(nil), (*ClusterList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterList_To_v1_ClusterList(a.(*platform.ClusterList), b.(*ClusterList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterProperty)(nil), (*platform.ClusterProperty)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterProperty_To_platform_ClusterProperty(a.(*ClusterProperty), b.(*platform.ClusterProperty), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterProperty)(nil), (*ClusterProperty)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterProperty_To_v1_ClusterProperty(a.(*platform.ClusterProperty), b.(*ClusterProperty), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterResource)(nil), (*platform.ClusterResource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterResource_To_platform_ClusterResource(a.(*ClusterResource), b.(*platform.ClusterResource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterResource)(nil), (*ClusterResource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterResource_To_v1_ClusterResource(a.(*platform.ClusterResource), b.(*ClusterResource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterSpec)(nil), (*platform.ClusterSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterSpec_To_platform_ClusterSpec(a.(*ClusterSpec), b.(*platform.ClusterSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterSpec)(nil), (*ClusterSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterSpec_To_v1_ClusterSpec(a.(*platform.ClusterSpec), b.(*ClusterSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterStatus)(nil), (*platform.ClusterStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterStatus_To_platform_ClusterStatus(a.(*ClusterStatus), b.(*platform.ClusterStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterStatus)(nil), (*ClusterStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterStatus_To_v1_ClusterStatus(a.(*platform.ClusterStatus), b.(*ClusterStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ComponentStatus)(nil), (*platform.ComponentStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ComponentStatus_To_platform_ComponentStatus(a.(*ComponentStatus), b.(*platform.ComponentStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ComponentStatus)(nil), (*ComponentStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ComponentStatus_To_v1_ComponentStatus(a.(*platform.ComponentStatus), b.(*ComponentStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ConfigMap)(nil), (*platform.ConfigMap)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ConfigMap_To_platform_ConfigMap(a.(*ConfigMap), b.(*platform.ConfigMap), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ConfigMap)(nil), (*ConfigMap)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ConfigMap_To_v1_ConfigMap(a.(*platform.ConfigMap), b.(*ConfigMap), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ConfigMapList)(nil), (*platform.ConfigMapList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ConfigMapList_To_platform_ConfigMapList(a.(*ConfigMapList), b.(*platform.ConfigMapList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ConfigMapList)(nil), (*ConfigMapList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ConfigMapList_To_v1_ConfigMapList(a.(*platform.ConfigMapList), b.(*ConfigMapList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*DNSAutoscaler)(nil), (*platform.DNSAutoscaler)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_DNSAutoscaler_To_platform_DNSAutoscaler(a.(*DNSAutoscaler), b.(*platform.DNSAutoscaler), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.DNSAutoscaler)(nil), (*DNSAutoscaler)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_DNSAutoscaler_To_v1_DNSAutoscaler(a.(*platform.DNSAutoscaler), b.(*DNSAutoscaler), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*DNSAutoscalerList)(nil), (*platform.DNSAutoscalerList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_DNSAutoscalerList_To_platform_DNSAutoscalerList(a.(*DNSAutoscalerList), b.(*platform.DNSAutoscalerList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.DNSAutoscalerList)(nil), (*DNSAutoscalerList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_DNSAutoscalerList_To_v1_DNSAutoscalerList(a.(*platform.DNSAutoscalerList), b.(*DNSAutoscalerList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*DNSAutoscalerSpec)(nil), (*platform.DNSAutoscalerSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_DNSAutoscalerSpec_To_platform_DNSAutoscalerSpec(a.(*DNSAutoscalerSpec), b.(*platform.DNSAutoscalerSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.DNSAutoscalerSpec)(nil), (*DNSAutoscalerSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_DNSAutoscalerSpec_To_v1_DNSAutoscalerSpec(a.(*platform.DNSAutoscalerSpec), b.(*DNSAutoscalerSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*DNSAutoscalerStatus)(nil), (*platform.DNSAutoscalerStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_DNSAutoscalerStatus_To_platform_DNSAutoscalerStatus(a.(*DNSAutoscalerStatus), b.(*platform.DNSAutoscalerStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.DNSAutoscalerStatus)(nil), (*DNSAutoscalerStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_DNSAutoscalerStatus_To_v1_DNSAutoscalerStatus(a.(*platform.DNSAutoscalerStatus), b.(*DNSAutoscalerStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*DeScheduler)(nil), (*platform.DeScheduler)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_DeScheduler_To_platform_DeScheduler(a.(*DeScheduler), b.(*platform.DeScheduler), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.DeScheduler)(nil), (*DeScheduler)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_DeScheduler_To_v1_DeScheduler(a.(*platform.DeScheduler), b.(*DeScheduler), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*DeSchedulerList)(nil), (*platform.DeSchedulerList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_DeSchedulerList_To_platform_DeSchedulerList(a.(*DeSchedulerList), b.(*platform.DeSchedulerList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.DeSchedulerList)(nil), (*DeSchedulerList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_DeSchedulerList_To_v1_DeSchedulerList(a.(*platform.DeSchedulerList), b.(*DeSchedulerList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*DeSchedulerSpec)(nil), (*platform.DeSchedulerSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_DeSchedulerSpec_To_platform_DeSchedulerSpec(a.(*DeSchedulerSpec), b.(*platform.DeSchedulerSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.DeSchedulerSpec)(nil), (*DeSchedulerSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_DeSchedulerSpec_To_v1_DeSchedulerSpec(a.(*platform.DeSchedulerSpec), b.(*DeSchedulerSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*DeSchedulerStatus)(nil), (*platform.DeSchedulerStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_DeSchedulerStatus_To_platform_DeSchedulerStatus(a.(*DeSchedulerStatus), b.(*platform.DeSchedulerStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.DeSchedulerStatus)(nil), (*DeSchedulerStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_DeSchedulerStatus_To_v1_DeSchedulerStatus(a.(*platform.DeSchedulerStatus), b.(*DeSchedulerStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*DynamicScheduler)(nil), (*platform.DynamicScheduler)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_DynamicScheduler_To_platform_DynamicScheduler(a.(*DynamicScheduler), b.(*platform.DynamicScheduler), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.DynamicScheduler)(nil), (*DynamicScheduler)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_DynamicScheduler_To_v1_DynamicScheduler(a.(*platform.DynamicScheduler), b.(*DynamicScheduler), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*DynamicSchedulerList)(nil), (*platform.DynamicSchedulerList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_DynamicSchedulerList_To_platform_DynamicSchedulerList(a.(*DynamicSchedulerList), b.(*platform.DynamicSchedulerList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.DynamicSchedulerList)(nil), (*DynamicSchedulerList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_DynamicSchedulerList_To_v1_DynamicSchedulerList(a.(*platform.DynamicSchedulerList), b.(*DynamicSchedulerList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*DynamicSchedulerSpec)(nil), (*platform.DynamicSchedulerSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_DynamicSchedulerSpec_To_platform_DynamicSchedulerSpec(a.(*DynamicSchedulerSpec), b.(*platform.DynamicSchedulerSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.DynamicSchedulerSpec)(nil), (*DynamicSchedulerSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_DynamicSchedulerSpec_To_v1_DynamicSchedulerSpec(a.(*platform.DynamicSchedulerSpec), b.(*DynamicSchedulerSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*DynamicSchedulerStatus)(nil), (*platform.DynamicSchedulerStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_DynamicSchedulerStatus_To_platform_DynamicSchedulerStatus(a.(*DynamicSchedulerStatus), b.(*platform.DynamicSchedulerStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.DynamicSchedulerStatus)(nil), (*DynamicSchedulerStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_DynamicSchedulerStatus_To_v1_DynamicSchedulerStatus(a.(*platform.DynamicSchedulerStatus), b.(*DynamicSchedulerStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*EniIpamd)(nil), (*platform.EniIpamd)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EniIpamd_To_platform_EniIpamd(a.(*EniIpamd), b.(*platform.EniIpamd), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.EniIpamd)(nil), (*EniIpamd)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_EniIpamd_To_v1_EniIpamd(a.(*platform.EniIpamd), b.(*EniIpamd), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*EniIpamdCondition)(nil), (*platform.EniIpamdCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EniIpamdCondition_To_platform_EniIpamdCondition(a.(*EniIpamdCondition), b.(*platform.EniIpamdCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.EniIpamdCondition)(nil), (*EniIpamdCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_EniIpamdCondition_To_v1_EniIpamdCondition(a.(*platform.EniIpamdCondition), b.(*EniIpamdCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*EniIpamdList)(nil), (*platform.EniIpamdList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EniIpamdList_To_platform_EniIpamdList(a.(*EniIpamdList), b.(*platform.EniIpamdList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.EniIpamdList)(nil), (*EniIpamdList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_EniIpamdList_To_v1_EniIpamdList(a.(*platform.EniIpamdList), b.(*EniIpamdList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*EniIpamdSpec)(nil), (*platform.EniIpamdSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EniIpamdSpec_To_platform_EniIpamdSpec(a.(*EniIpamdSpec), b.(*platform.EniIpamdSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.EniIpamdSpec)(nil), (*EniIpamdSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_EniIpamdSpec_To_v1_EniIpamdSpec(a.(*platform.EniIpamdSpec), b.(*EniIpamdSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*EniIpamdStatus)(nil), (*platform.EniIpamdStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EniIpamdStatus_To_platform_EniIpamdStatus(a.(*EniIpamdStatus), b.(*platform.EniIpamdStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.EniIpamdStatus)(nil), (*EniIpamdStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_EniIpamdStatus_To_v1_EniIpamdStatus(a.(*platform.EniIpamdStatus), b.(*EniIpamdStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*Environment)(nil), (*platform.Environment)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Environment_To_platform_Environment(a.(*Environment), b.(*platform.Environment), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.Environment)(nil), (*Environment)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_Environment_To_v1_Environment(a.(*platform.Environment), b.(*Environment), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*EnvironmentList)(nil), (*platform.EnvironmentList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EnvironmentList_To_platform_EnvironmentList(a.(*EnvironmentList), b.(*platform.EnvironmentList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.EnvironmentList)(nil), (*EnvironmentList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_EnvironmentList_To_v1_EnvironmentList(a.(*platform.EnvironmentList), b.(*EnvironmentList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*EnvironmentSpec)(nil), (*platform.EnvironmentSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EnvironmentSpec_To_platform_EnvironmentSpec(a.(*EnvironmentSpec), b.(*platform.EnvironmentSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.EnvironmentSpec)(nil), (*EnvironmentSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_EnvironmentSpec_To_v1_EnvironmentSpec(a.(*platform.EnvironmentSpec), b.(*EnvironmentSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*EnvironmentStatus)(nil), (*platform.EnvironmentStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EnvironmentStatus_To_platform_EnvironmentStatus(a.(*EnvironmentStatus), b.(*platform.EnvironmentStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.EnvironmentStatus)(nil), (*EnvironmentStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_EnvironmentStatus_To_v1_EnvironmentStatus(a.(*platform.EnvironmentStatus), b.(*EnvironmentStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*FilterThreshold)(nil), (*platform.FilterThreshold)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_FilterThreshold_To_platform_FilterThreshold(a.(*FilterThreshold), b.(*platform.FilterThreshold), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.FilterThreshold)(nil), (*FilterThreshold)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_FilterThreshold_To_v1_FilterThreshold(a.(*platform.FilterThreshold), b.(*FilterThreshold), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*GPUManager)(nil), (*platform.GPUManager)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_GPUManager_To_platform_GPUManager(a.(*GPUManager), b.(*platform.GPUManager), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.GPUManager)(nil), (*GPUManager)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_GPUManager_To_v1_GPUManager(a.(*platform.GPUManager), b.(*GPUManager), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*GPUManagerList)(nil), (*platform.GPUManagerList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_GPUManagerList_To_platform_GPUManagerList(a.(*GPUManagerList), b.(*platform.GPUManagerList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.GPUManagerList)(nil), (*GPUManagerList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_GPUManagerList_To_v1_GPUManagerList(a.(*platform.GPUManagerList), b.(*GPUManagerList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*GPUManagerSpec)(nil), (*platform.GPUManagerSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_GPUManagerSpec_To_platform_GPUManagerSpec(a.(*GPUManagerSpec), b.(*platform.GPUManagerSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.GPUManagerSpec)(nil), (*GPUManagerSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_GPUManagerSpec_To_v1_GPUManagerSpec(a.(*platform.GPUManagerSpec), b.(*GPUManagerSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*GPUManagerStatus)(nil), (*platform.GPUManagerStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_GPUManagerStatus_To_platform_GPUManagerStatus(a.(*GPUManagerStatus), b.(*platform.GPUManagerStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.GPUManagerStatus)(nil), (*GPUManagerStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_GPUManagerStatus_To_v1_GPUManagerStatus(a.(*platform.GPUManagerStatus), b.(*GPUManagerStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*GameApp)(nil), (*platform.GameApp)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_GameApp_To_platform_GameApp(a.(*GameApp), b.(*platform.GameApp), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.GameApp)(nil), (*GameApp)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_GameApp_To_v1_GameApp(a.(*platform.GameApp), b.(*GameApp), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*GameAppList)(nil), (*platform.GameAppList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_GameAppList_To_platform_GameAppList(a.(*GameAppList), b.(*platform.GameAppList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.GameAppList)(nil), (*GameAppList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_GameAppList_To_v1_GameAppList(a.(*platform.GameAppList), b.(*GameAppList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*GameAppProxyOptions)(nil), (*platform.GameAppProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_GameAppProxyOptions_To_platform_GameAppProxyOptions(a.(*GameAppProxyOptions), b.(*platform.GameAppProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.GameAppProxyOptions)(nil), (*GameAppProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_GameAppProxyOptions_To_v1_GameAppProxyOptions(a.(*platform.GameAppProxyOptions), b.(*GameAppProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*GameAppSpec)(nil), (*platform.GameAppSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_GameAppSpec_To_platform_GameAppSpec(a.(*GameAppSpec), b.(*platform.GameAppSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.GameAppSpec)(nil), (*GameAppSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_GameAppSpec_To_v1_GameAppSpec(a.(*platform.GameAppSpec), b.(*GameAppSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*GameAppStatus)(nil), (*platform.GameAppStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_GameAppStatus_To_platform_GameAppStatus(a.(*GameAppStatus), b.(*platform.GameAppStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.GameAppStatus)(nil), (*GameAppStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_GameAppStatus_To_v1_GameAppStatus(a.(*platform.GameAppStatus), b.(*GameAppStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*GameAppVersion)(nil), (*platform.GameAppVersion)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_GameAppVersion_To_platform_GameAppVersion(a.(*GameAppVersion), b.(*platform.GameAppVersion), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.GameAppVersion)(nil), (*GameAppVersion)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_GameAppVersion_To_v1_GameAppVersion(a.(*platform.GameAppVersion), b.(*GameAppVersion), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*HPC)(nil), (*platform.HPC)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_HPC_To_platform_HPC(a.(*HPC), b.(*platform.HPC), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.HPC)(nil), (*HPC)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_HPC_To_v1_HPC(a.(*platform.HPC), b.(*HPC), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*HPCList)(nil), (*platform.HPCList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_HPCList_To_platform_HPCList(a.(*HPCList), b.(*platform.HPCList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.HPCList)(nil), (*HPCList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_HPCList_To_v1_HPCList(a.(*platform.HPCList), b.(*HPCList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*HPCProxyOptions)(nil), (*platform.HPCProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_HPCProxyOptions_To_platform_HPCProxyOptions(a.(*HPCProxyOptions), b.(*platform.HPCProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.HPCProxyOptions)(nil), (*HPCProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_HPCProxyOptions_To_v1_HPCProxyOptions(a.(*platform.HPCProxyOptions), b.(*HPCProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*HPCSpec)(nil), (*platform.HPCSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_HPCSpec_To_platform_HPCSpec(a.(*HPCSpec), b.(*platform.HPCSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.HPCSpec)(nil), (*HPCSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_HPCSpec_To_v1_HPCSpec(a.(*platform.HPCSpec), b.(*HPCSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*HPCStatus)(nil), (*platform.HPCStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_HPCStatus_To_platform_HPCStatus(a.(*HPCStatus), b.(*platform.HPCStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.HPCStatus)(nil), (*HPCStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_HPCStatus_To_v1_HPCStatus(a.(*platform.HPCStatus), b.(*HPCStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*Helm)(nil), (*platform.Helm)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Helm_To_platform_Helm(a.(*Helm), b.(*platform.Helm), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.Helm)(nil), (*Helm)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_Helm_To_v1_Helm(a.(*platform.Helm), b.(*Helm), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*HelmList)(nil), (*platform.HelmList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_HelmList_To_platform_HelmList(a.(*HelmList), b.(*platform.HelmList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.HelmList)(nil), (*HelmList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_HelmList_To_v1_HelmList(a.(*platform.HelmList), b.(*HelmList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*HelmProxyOptions)(nil), (*platform.HelmProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_HelmProxyOptions_To_platform_HelmProxyOptions(a.(*HelmProxyOptions), b.(*platform.HelmProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.HelmProxyOptions)(nil), (*HelmProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_HelmProxyOptions_To_v1_HelmProxyOptions(a.(*platform.HelmProxyOptions), b.(*HelmProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*HelmSpec)(nil), (*platform.HelmSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_HelmSpec_To_platform_HelmSpec(a.(*HelmSpec), b.(*platform.HelmSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.HelmSpec)(nil), (*HelmSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_HelmSpec_To_v1_HelmSpec(a.(*platform.HelmSpec), b.(*HelmSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*HelmStatus)(nil), (*platform.HelmStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_HelmStatus_To_platform_HelmStatus(a.(*HelmStatus), b.(*platform.HelmStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.HelmStatus)(nil), (*HelmStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_HelmStatus_To_v1_HelmStatus(a.(*platform.HelmStatus), b.(*HelmStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*HelmVersion)(nil), (*platform.HelmVersion)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_HelmVersion_To_platform_HelmVersion(a.(*HelmVersion), b.(*platform.HelmVersion), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.HelmVersion)(nil), (*HelmVersion)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_HelmVersion_To_v1_HelmVersion(a.(*platform.HelmVersion), b.(*HelmVersion), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*HostItem)(nil), (*platform.HostItem)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_HostItem_To_platform_HostItem(a.(*HostItem), b.(*platform.HostItem), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.HostItem)(nil), (*HostItem)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_HostItem_To_v1_HostItem(a.(*platform.HostItem), b.(*HostItem), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ImageP2P)(nil), (*platform.ImageP2P)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ImageP2P_To_platform_ImageP2P(a.(*ImageP2P), b.(*platform.ImageP2P), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ImageP2P)(nil), (*ImageP2P)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ImageP2P_To_v1_ImageP2P(a.(*platform.ImageP2P), b.(*ImageP2P), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ImageP2PList)(nil), (*platform.ImageP2PList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ImageP2PList_To_platform_ImageP2PList(a.(*ImageP2PList), b.(*platform.ImageP2PList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ImageP2PList)(nil), (*ImageP2PList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ImageP2PList_To_v1_ImageP2PList(a.(*platform.ImageP2PList), b.(*ImageP2PList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ImageP2PSpec)(nil), (*platform.ImageP2PSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ImageP2PSpec_To_platform_ImageP2PSpec(a.(*ImageP2PSpec), b.(*platform.ImageP2PSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ImageP2PSpec)(nil), (*ImageP2PSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ImageP2PSpec_To_v1_ImageP2PSpec(a.(*platform.ImageP2PSpec), b.(*ImageP2PSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ImageP2PStatus)(nil), (*platform.ImageP2PStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ImageP2PStatus_To_platform_ImageP2PStatus(a.(*ImageP2PStatus), b.(*platform.ImageP2PStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ImageP2PStatus)(nil), (*ImageP2PStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ImageP2PStatus_To_v1_ImageP2PStatus(a.(*platform.ImageP2PStatus), b.(*ImageP2PStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*LBCF)(nil), (*platform.LBCF)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LBCF_To_platform_LBCF(a.(*LBCF), b.(*platform.LBCF), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.LBCF)(nil), (*LBCF)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_LBCF_To_v1_LBCF(a.(*platform.LBCF), b.(*LBCF), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*LBCFList)(nil), (*platform.LBCFList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LBCFList_To_platform_LBCFList(a.(*LBCFList), b.(*platform.LBCFList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.LBCFList)(nil), (*LBCFList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_LBCFList_To_v1_LBCFList(a.(*platform.LBCFList), b.(*LBCFList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*LBCFProxyOptions)(nil), (*platform.LBCFProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LBCFProxyOptions_To_platform_LBCFProxyOptions(a.(*LBCFProxyOptions), b.(*platform.LBCFProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.LBCFProxyOptions)(nil), (*LBCFProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_LBCFProxyOptions_To_v1_LBCFProxyOptions(a.(*platform.LBCFProxyOptions), b.(*LBCFProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*LBCFSpec)(nil), (*platform.LBCFSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LBCFSpec_To_platform_LBCFSpec(a.(*LBCFSpec), b.(*platform.LBCFSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.LBCFSpec)(nil), (*LBCFSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_LBCFSpec_To_v1_LBCFSpec(a.(*platform.LBCFSpec), b.(*LBCFSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*LBCFStatus)(nil), (*platform.LBCFStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LBCFStatus_To_platform_LBCFStatus(a.(*LBCFStatus), b.(*platform.LBCFStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.LBCFStatus)(nil), (*LBCFStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_LBCFStatus_To_v1_LBCFStatus(a.(*platform.LBCFStatus), b.(*LBCFStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*LoadThreshold)(nil), (*platform.LoadThreshold)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LoadThreshold_To_platform_LoadThreshold(a.(*LoadThreshold), b.(*platform.LoadThreshold), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.LoadThreshold)(nil), (*LoadThreshold)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_LoadThreshold_To_v1_LoadThreshold(a.(*platform.LoadThreshold), b.(*LoadThreshold), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*LogCollector)(nil), (*platform.LogCollector)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LogCollector_To_platform_LogCollector(a.(*LogCollector), b.(*platform.LogCollector), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.LogCollector)(nil), (*LogCollector)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_LogCollector_To_v1_LogCollector(a.(*platform.LogCollector), b.(*LogCollector), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*LogCollectorList)(nil), (*platform.LogCollectorList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LogCollectorList_To_platform_LogCollectorList(a.(*LogCollectorList), b.(*platform.LogCollectorList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.LogCollectorList)(nil), (*LogCollectorList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_LogCollectorList_To_v1_LogCollectorList(a.(*platform.LogCollectorList), b.(*LogCollectorList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*LogCollectorProxyOptions)(nil), (*platform.LogCollectorProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LogCollectorProxyOptions_To_platform_LogCollectorProxyOptions(a.(*LogCollectorProxyOptions), b.(*platform.LogCollectorProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.LogCollectorProxyOptions)(nil), (*LogCollectorProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_LogCollectorProxyOptions_To_v1_LogCollectorProxyOptions(a.(*platform.LogCollectorProxyOptions), b.(*LogCollectorProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*LogCollectorSpec)(nil), (*platform.LogCollectorSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LogCollectorSpec_To_platform_LogCollectorSpec(a.(*LogCollectorSpec), b.(*platform.LogCollectorSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.LogCollectorSpec)(nil), (*LogCollectorSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_LogCollectorSpec_To_v1_LogCollectorSpec(a.(*platform.LogCollectorSpec), b.(*LogCollectorSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*LogCollectorStatus)(nil), (*platform.LogCollectorStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LogCollectorStatus_To_platform_LogCollectorStatus(a.(*LogCollectorStatus), b.(*platform.LogCollectorStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.LogCollectorStatus)(nil), (*LogCollectorStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_LogCollectorStatus_To_v1_LogCollectorStatus(a.(*platform.LogCollectorStatus), b.(*LogCollectorStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*Manager)(nil), (*platform.Manager)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Manager_To_platform_Manager(a.(*Manager), b.(*platform.Manager), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.Manager)(nil), (*Manager)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_Manager_To_v1_Manager(a.(*platform.Manager), b.(*Manager), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NamespaceSet)(nil), (*platform.NamespaceSet)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NamespaceSet_To_platform_NamespaceSet(a.(*NamespaceSet), b.(*platform.NamespaceSet), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NamespaceSet)(nil), (*NamespaceSet)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NamespaceSet_To_v1_NamespaceSet(a.(*platform.NamespaceSet), b.(*NamespaceSet), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NamespaceSetList)(nil), (*platform.NamespaceSetList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NamespaceSetList_To_platform_NamespaceSetList(a.(*NamespaceSetList), b.(*platform.NamespaceSetList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NamespaceSetList)(nil), (*NamespaceSetList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NamespaceSetList_To_v1_NamespaceSetList(a.(*platform.NamespaceSetList), b.(*NamespaceSetList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NamespaceSetSpec)(nil), (*platform.NamespaceSetSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NamespaceSetSpec_To_platform_NamespaceSetSpec(a.(*NamespaceSetSpec), b.(*platform.NamespaceSetSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NamespaceSetSpec)(nil), (*NamespaceSetSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NamespaceSetSpec_To_v1_NamespaceSetSpec(a.(*platform.NamespaceSetSpec), b.(*NamespaceSetSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NamespaceSetStatus)(nil), (*platform.NamespaceSetStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NamespaceSetStatus_To_platform_NamespaceSetStatus(a.(*NamespaceSetStatus), b.(*platform.NamespaceSetStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NamespaceSetStatus)(nil), (*NamespaceSetStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NamespaceSetStatus_To_v1_NamespaceSetStatus(a.(*platform.NamespaceSetStatus), b.(*NamespaceSetStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NetworkPolicy)(nil), (*platform.NetworkPolicy)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NetworkPolicy_To_platform_NetworkPolicy(a.(*NetworkPolicy), b.(*platform.NetworkPolicy), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NetworkPolicy)(nil), (*NetworkPolicy)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NetworkPolicy_To_v1_NetworkPolicy(a.(*platform.NetworkPolicy), b.(*NetworkPolicy), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NetworkPolicyList)(nil), (*platform.NetworkPolicyList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NetworkPolicyList_To_platform_NetworkPolicyList(a.(*NetworkPolicyList), b.(*platform.NetworkPolicyList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NetworkPolicyList)(nil), (*NetworkPolicyList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NetworkPolicyList_To_v1_NetworkPolicyList(a.(*platform.NetworkPolicyList), b.(*NetworkPolicyList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NetworkPolicySpec)(nil), (*platform.NetworkPolicySpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NetworkPolicySpec_To_platform_NetworkPolicySpec(a.(*NetworkPolicySpec), b.(*platform.NetworkPolicySpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NetworkPolicySpec)(nil), (*NetworkPolicySpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NetworkPolicySpec_To_v1_NetworkPolicySpec(a.(*platform.NetworkPolicySpec), b.(*NetworkPolicySpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NetworkPolicyStatus)(nil), (*platform.NetworkPolicyStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NetworkPolicyStatus_To_platform_NetworkPolicyStatus(a.(*NetworkPolicyStatus), b.(*platform.NetworkPolicyStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NetworkPolicyStatus)(nil), (*NetworkPolicyStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NetworkPolicyStatus_To_v1_NetworkPolicyStatus(a.(*platform.NetworkPolicyStatus), b.(*NetworkPolicyStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NginxControllerVersion)(nil), (*platform.NginxControllerVersion)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NginxControllerVersion_To_platform_NginxControllerVersion(a.(*NginxControllerVersion), b.(*platform.NginxControllerVersion), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NginxControllerVersion)(nil), (*NginxControllerVersion)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NginxControllerVersion_To_v1_NginxControllerVersion(a.(*platform.NginxControllerVersion), b.(*NginxControllerVersion), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NginxIngress)(nil), (*platform.NginxIngress)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NginxIngress_To_platform_NginxIngress(a.(*NginxIngress), b.(*platform.NginxIngress), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NginxIngress)(nil), (*NginxIngress)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NginxIngress_To_v1_NginxIngress(a.(*platform.NginxIngress), b.(*NginxIngress), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NginxIngressList)(nil), (*platform.NginxIngressList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NginxIngressList_To_platform_NginxIngressList(a.(*NginxIngressList), b.(*platform.NginxIngressList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NginxIngressList)(nil), (*NginxIngressList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NginxIngressList_To_v1_NginxIngressList(a.(*platform.NginxIngressList), b.(*NginxIngressList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NginxIngressProxyOptions)(nil), (*platform.NginxIngressProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NginxIngressProxyOptions_To_platform_NginxIngressProxyOptions(a.(*NginxIngressProxyOptions), b.(*platform.NginxIngressProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NginxIngressProxyOptions)(nil), (*NginxIngressProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NginxIngressProxyOptions_To_v1_NginxIngressProxyOptions(a.(*platform.NginxIngressProxyOptions), b.(*NginxIngressProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NginxIngressSpec)(nil), (*platform.NginxIngressSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NginxIngressSpec_To_platform_NginxIngressSpec(a.(*NginxIngressSpec), b.(*platform.NginxIngressSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NginxIngressSpec)(nil), (*NginxIngressSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NginxIngressSpec_To_v1_NginxIngressSpec(a.(*platform.NginxIngressSpec), b.(*NginxIngressSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NginxIngressStatus)(nil), (*platform.NginxIngressStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NginxIngressStatus_To_platform_NginxIngressStatus(a.(*NginxIngressStatus), b.(*platform.NginxIngressStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NginxIngressStatus)(nil), (*NginxIngressStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NginxIngressStatus_To_v1_NginxIngressStatus(a.(*platform.NginxIngressStatus), b.(*NginxIngressStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NodeLocalDNSCache)(nil), (*platform.NodeLocalDNSCache)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeLocalDNSCache_To_platform_NodeLocalDNSCache(a.(*NodeLocalDNSCache), b.(*platform.NodeLocalDNSCache), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NodeLocalDNSCache)(nil), (*NodeLocalDNSCache)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NodeLocalDNSCache_To_v1_NodeLocalDNSCache(a.(*platform.NodeLocalDNSCache), b.(*NodeLocalDNSCache), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NodeLocalDNSCacheList)(nil), (*platform.NodeLocalDNSCacheList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeLocalDNSCacheList_To_platform_NodeLocalDNSCacheList(a.(*NodeLocalDNSCacheList), b.(*platform.NodeLocalDNSCacheList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NodeLocalDNSCacheList)(nil), (*NodeLocalDNSCacheList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NodeLocalDNSCacheList_To_v1_NodeLocalDNSCacheList(a.(*platform.NodeLocalDNSCacheList), b.(*NodeLocalDNSCacheList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NodeLocalDNSCacheSpec)(nil), (*platform.NodeLocalDNSCacheSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeLocalDNSCacheSpec_To_platform_NodeLocalDNSCacheSpec(a.(*NodeLocalDNSCacheSpec), b.(*platform.NodeLocalDNSCacheSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NodeLocalDNSCacheSpec)(nil), (*NodeLocalDNSCacheSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NodeLocalDNSCacheSpec_To_v1_NodeLocalDNSCacheSpec(a.(*platform.NodeLocalDNSCacheSpec), b.(*NodeLocalDNSCacheSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NodeLocalDNSCacheStatus)(nil), (*platform.NodeLocalDNSCacheStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeLocalDNSCacheStatus_To_platform_NodeLocalDNSCacheStatus(a.(*NodeLocalDNSCacheStatus), b.(*platform.NodeLocalDNSCacheStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NodeLocalDNSCacheStatus)(nil), (*NodeLocalDNSCacheStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NodeLocalDNSCacheStatus_To_v1_NodeLocalDNSCacheStatus(a.(*platform.NodeLocalDNSCacheStatus), b.(*NodeLocalDNSCacheStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NodePod)(nil), (*platform.NodePod)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodePod_To_platform_NodePod(a.(*NodePod), b.(*platform.NodePod), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NodePod)(nil), (*NodePod)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NodePod_To_v1_NodePod(a.(*platform.NodePod), b.(*NodePod), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NodeProblemDetector)(nil), (*platform.NodeProblemDetector)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeProblemDetector_To_platform_NodeProblemDetector(a.(*NodeProblemDetector), b.(*platform.NodeProblemDetector), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NodeProblemDetector)(nil), (*NodeProblemDetector)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NodeProblemDetector_To_v1_NodeProblemDetector(a.(*platform.NodeProblemDetector), b.(*NodeProblemDetector), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NodeProblemDetectorList)(nil), (*platform.NodeProblemDetectorList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeProblemDetectorList_To_platform_NodeProblemDetectorList(a.(*NodeProblemDetectorList), b.(*platform.NodeProblemDetectorList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NodeProblemDetectorList)(nil), (*NodeProblemDetectorList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NodeProblemDetectorList_To_v1_NodeProblemDetectorList(a.(*platform.NodeProblemDetectorList), b.(*NodeProblemDetectorList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NodeProblemDetectorSpec)(nil), (*platform.NodeProblemDetectorSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeProblemDetectorSpec_To_platform_NodeProblemDetectorSpec(a.(*NodeProblemDetectorSpec), b.(*platform.NodeProblemDetectorSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NodeProblemDetectorSpec)(nil), (*NodeProblemDetectorSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NodeProblemDetectorSpec_To_v1_NodeProblemDetectorSpec(a.(*platform.NodeProblemDetectorSpec), b.(*NodeProblemDetectorSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NodeProblemDetectorStatus)(nil), (*platform.NodeProblemDetectorStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeProblemDetectorStatus_To_platform_NodeProblemDetectorStatus(a.(*NodeProblemDetectorStatus), b.(*platform.NodeProblemDetectorStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.NodeProblemDetectorStatus)(nil), (*NodeProblemDetectorStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_NodeProblemDetectorStatus_To_v1_NodeProblemDetectorStatus(a.(*platform.NodeProblemDetectorStatus), b.(*NodeProblemDetectorStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*OLM)(nil), (*platform.OLM)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_OLM_To_platform_OLM(a.(*OLM), b.(*platform.OLM), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.OLM)(nil), (*OLM)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_OLM_To_v1_OLM(a.(*platform.OLM), b.(*OLM), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*OLMList)(nil), (*platform.OLMList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_OLMList_To_platform_OLMList(a.(*OLMList), b.(*platform.OLMList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.OLMList)(nil), (*OLMList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_OLMList_To_v1_OLMList(a.(*platform.OLMList), b.(*OLMList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*OLMProxyOptions)(nil), (*platform.OLMProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_OLMProxyOptions_To_platform_OLMProxyOptions(a.(*OLMProxyOptions), b.(*platform.OLMProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.OLMProxyOptions)(nil), (*OLMProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_OLMProxyOptions_To_v1_OLMProxyOptions(a.(*platform.OLMProxyOptions), b.(*OLMProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*OLMSpec)(nil), (*platform.OLMSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_OLMSpec_To_platform_OLMSpec(a.(*OLMSpec), b.(*platform.OLMSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.OLMSpec)(nil), (*OLMSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_OLMSpec_To_v1_OLMSpec(a.(*platform.OLMSpec), b.(*OLMSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*OLMStatus)(nil), (*platform.OLMStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_OLMStatus_To_platform_OLMStatus(a.(*OLMStatus), b.(*platform.OLMStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.OLMStatus)(nil), (*OLMStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_OLMStatus_To_v1_OLMStatus(a.(*platform.OLMStatus), b.(*OLMStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*OOMGuard)(nil), (*platform.OOMGuard)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_OOMGuard_To_platform_OOMGuard(a.(*OOMGuard), b.(*platform.OOMGuard), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.OOMGuard)(nil), (*OOMGuard)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_OOMGuard_To_v1_OOMGuard(a.(*platform.OOMGuard), b.(*OOMGuard), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*OOMGuardList)(nil), (*platform.OOMGuardList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_OOMGuardList_To_platform_OOMGuardList(a.(*OOMGuardList), b.(*platform.OOMGuardList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.OOMGuardList)(nil), (*OOMGuardList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_OOMGuardList_To_v1_OOMGuardList(a.(*platform.OOMGuardList), b.(*OOMGuardList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*OOMGuardSpec)(nil), (*platform.OOMGuardSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_OOMGuardSpec_To_platform_OOMGuardSpec(a.(*OOMGuardSpec), b.(*platform.OOMGuardSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.OOMGuardSpec)(nil), (*OOMGuardSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_OOMGuardSpec_To_v1_OOMGuardSpec(a.(*platform.OOMGuardSpec), b.(*OOMGuardSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*OOMGuardStatus)(nil), (*platform.OOMGuardStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_OOMGuardStatus_To_platform_OOMGuardStatus(a.(*OOMGuardStatus), b.(*platform.OOMGuardStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.OOMGuardStatus)(nil), (*OOMGuardStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_OOMGuardStatus_To_v1_OOMGuardStatus(a.(*platform.OOMGuardStatus), b.(*OOMGuardStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*PersistentBackEnd)(nil), (*platform.PersistentBackEnd)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentBackEnd_To_platform_PersistentBackEnd(a.(*PersistentBackEnd), b.(*platform.PersistentBackEnd), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.PersistentBackEnd)(nil), (*PersistentBackEnd)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_PersistentBackEnd_To_v1_PersistentBackEnd(a.(*platform.PersistentBackEnd), b.(*PersistentBackEnd), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*PersistentEvent)(nil), (*platform.PersistentEvent)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentEvent_To_platform_PersistentEvent(a.(*PersistentEvent), b.(*platform.PersistentEvent), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.PersistentEvent)(nil), (*PersistentEvent)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_PersistentEvent_To_v1_PersistentEvent(a.(*platform.PersistentEvent), b.(*PersistentEvent), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*PersistentEventList)(nil), (*platform.PersistentEventList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentEventList_To_platform_PersistentEventList(a.(*PersistentEventList), b.(*platform.PersistentEventList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.PersistentEventList)(nil), (*PersistentEventList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_PersistentEventList_To_v1_PersistentEventList(a.(*platform.PersistentEventList), b.(*PersistentEventList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*PersistentEventSpec)(nil), (*platform.PersistentEventSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentEventSpec_To_platform_PersistentEventSpec(a.(*PersistentEventSpec), b.(*platform.PersistentEventSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.PersistentEventSpec)(nil), (*PersistentEventSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_PersistentEventSpec_To_v1_PersistentEventSpec(a.(*platform.PersistentEventSpec), b.(*PersistentEventSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*PersistentEventStatus)(nil), (*platform.PersistentEventStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentEventStatus_To_platform_PersistentEventStatus(a.(*PersistentEventStatus), b.(*platform.PersistentEventStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.PersistentEventStatus)(nil), (*PersistentEventStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_PersistentEventStatus_To_v1_PersistentEventStatus(a.(*platform.PersistentEventStatus), b.(*PersistentEventStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*Policy)(nil), (*platform.Policy)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Policy_To_platform_Policy(a.(*Policy), b.(*platform.Policy), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.Policy)(nil), (*Policy)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_Policy_To_v1_Policy(a.(*platform.Policy), b.(*Policy), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*Project)(nil), (*platform.Project)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Project_To_platform_Project(a.(*Project), b.(*platform.Project), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.Project)(nil), (*Project)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_Project_To_v1_Project(a.(*platform.Project), b.(*Project), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ProjectList)(nil), (*platform.ProjectList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ProjectList_To_platform_ProjectList(a.(*ProjectList), b.(*platform.ProjectList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ProjectList)(nil), (*ProjectList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ProjectList_To_v1_ProjectList(a.(*platform.ProjectList), b.(*ProjectList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ProjectSpec)(nil), (*platform.ProjectSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ProjectSpec_To_platform_ProjectSpec(a.(*ProjectSpec), b.(*platform.ProjectSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ProjectSpec)(nil), (*ProjectSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ProjectSpec_To_v1_ProjectSpec(a.(*platform.ProjectSpec), b.(*ProjectSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ProjectStatus)(nil), (*platform.ProjectStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ProjectStatus_To_platform_ProjectStatus(a.(*ProjectStatus), b.(*platform.ProjectStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ProjectStatus)(nil), (*ProjectStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ProjectStatus_To_v1_ProjectStatus(a.(*platform.ProjectStatus), b.(*ProjectStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*QGPU)(nil), (*platform.QGPU)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_QGPU_To_platform_QGPU(a.(*QGPU), b.(*platform.QGPU), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.QGPU)(nil), (*QGPU)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_QGPU_To_v1_QGPU(a.(*platform.QGPU), b.(*QGPU), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*QGPUList)(nil), (*platform.QGPUList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_QGPUList_To_platform_QGPUList(a.(*QGPUList), b.(*platform.QGPUList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.QGPUList)(nil), (*QGPUList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_QGPUList_To_v1_QGPUList(a.(*platform.QGPUList), b.(*QGPUList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*QGPUSpec)(nil), (*platform.QGPUSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_QGPUSpec_To_platform_QGPUSpec(a.(*QGPUSpec), b.(*platform.QGPUSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.QGPUSpec)(nil), (*QGPUSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_QGPUSpec_To_v1_QGPUSpec(a.(*platform.QGPUSpec), b.(*QGPUSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*QGPUStatus)(nil), (*platform.QGPUStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_QGPUStatus_To_platform_QGPUStatus(a.(*QGPUStatus), b.(*platform.QGPUStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.QGPUStatus)(nil), (*QGPUStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_QGPUStatus_To_v1_QGPUStatus(a.(*platform.QGPUStatus), b.(*QGPUStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*RecommendationProxyOptions)(nil), (*platform.RecommendationProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_RecommendationProxyOptions_To_platform_RecommendationProxyOptions(a.(*RecommendationProxyOptions), b.(*platform.RecommendationProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.RecommendationProxyOptions)(nil), (*RecommendationProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_RecommendationProxyOptions_To_v1_RecommendationProxyOptions(a.(*platform.RecommendationProxyOptions), b.(*RecommendationProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*Registry)(nil), (*platform.Registry)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Registry_To_platform_Registry(a.(*Registry), b.(*platform.Registry), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.Registry)(nil), (*Registry)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_Registry_To_v1_Registry(a.(*platform.Registry), b.(*Registry), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*RegistryList)(nil), (*platform.RegistryList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_RegistryList_To_platform_RegistryList(a.(*RegistryList), b.(*platform.RegistryList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.RegistryList)(nil), (*RegistryList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_RegistryList_To_v1_RegistryList(a.(*platform.RegistryList), b.(*RegistryList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*RegistrySpec)(nil), (*platform.RegistrySpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_RegistrySpec_To_platform_RegistrySpec(a.(*RegistrySpec), b.(*platform.RegistrySpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.RegistrySpec)(nil), (*RegistrySpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_RegistrySpec_To_v1_RegistrySpec(a.(*platform.RegistrySpec), b.(*RegistrySpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*RunTime)(nil), (*platform.RunTime)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_RunTime_To_platform_RunTime(a.(*RunTime), b.(*platform.RunTime), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.RunTime)(nil), (*RunTime)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_RunTime_To_v1_RunTime(a.(*platform.RunTime), b.(*RunTime), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ScoreThreshold)(nil), (*platform.ScoreThreshold)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ScoreThreshold_To_platform_ScoreThreshold(a.(*ScoreThreshold), b.(*platform.ScoreThreshold), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ScoreThreshold)(nil), (*ScoreThreshold)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ScoreThreshold_To_v1_ScoreThreshold(a.(*platform.ScoreThreshold), b.(*ScoreThreshold), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*StorageBackEndCLS)(nil), (*platform.StorageBackEndCLS)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_StorageBackEndCLS_To_platform_StorageBackEndCLS(a.(*StorageBackEndCLS), b.(*platform.StorageBackEndCLS), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.StorageBackEndCLS)(nil), (*StorageBackEndCLS)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_StorageBackEndCLS_To_v1_StorageBackEndCLS(a.(*platform.StorageBackEndCLS), b.(*StorageBackEndCLS), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*StorageBackEndES)(nil), (*platform.StorageBackEndES)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_StorageBackEndES_To_platform_StorageBackEndES(a.(*StorageBackEndES), b.(*platform.StorageBackEndES), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.StorageBackEndES)(nil), (*StorageBackEndES)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_StorageBackEndES_To_v1_StorageBackEndES(a.(*platform.StorageBackEndES), b.(*StorageBackEndES), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*Tcr)(nil), (*platform.Tcr)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Tcr_To_platform_Tcr(a.(*Tcr), b.(*platform.Tcr), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.Tcr)(nil), (*Tcr)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_Tcr_To_v1_Tcr(a.(*platform.Tcr), b.(*Tcr), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*TcrItem)(nil), (*platform.TcrItem)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TcrItem_To_platform_TcrItem(a.(*TcrItem), b.(*platform.TcrItem), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.TcrItem)(nil), (*TcrItem)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_TcrItem_To_v1_TcrItem(a.(*platform.TcrItem), b.(*TcrItem), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*TcrList)(nil), (*platform.TcrList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TcrList_To_platform_TcrList(a.(*TcrList), b.(*platform.TcrList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.TcrList)(nil), (*TcrList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_TcrList_To_v1_TcrList(a.(*platform.TcrList), b.(*TcrList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*TcrSpec)(nil), (*platform.TcrSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TcrSpec_To_platform_TcrSpec(a.(*TcrSpec), b.(*platform.TcrSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.TcrSpec)(nil), (*TcrSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_TcrSpec_To_v1_TcrSpec(a.(*platform.TcrSpec), b.(*TcrSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*TcrStatus)(nil), (*platform.TcrStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TcrStatus_To_platform_TcrStatus(a.(*TcrStatus), b.(*platform.TcrStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.TcrStatus)(nil), (*TcrStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_TcrStatus_To_v1_TcrStatus(a.(*platform.TcrStatus), b.(*TcrStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*VersionMap)(nil), (*platform.VersionMap)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_VersionMap_To_platform_VersionMap(a.(*VersionMap), b.(*platform.VersionMap), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.VersionMap)(nil), (*VersionMap)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_VersionMap_To_v1_VersionMap(a.(*platform.VersionMap), b.(*VersionMap), scope)
	}); err != nil {
		return err
	}
	return nil
}

func autoConvert_v1_Actions_To_platform_Actions(in *Actions, out *platform.Actions, s conversion.Scope) error {
	if err := Convert_v1_RunTime_To_platform_RunTime(&in.Runtime, &out.Runtime, s); err != nil {
		return err
	}
	if err := Convert_v1_NodePod_To_platform_NodePod(&in.NodePod, &out.NodePod, s); err != nil {
		return err
	}
	if err := Convert_v1_CVM_To_platform_CVM(&in.CVM, &out.CVM, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_Actions_To_platform_Actions is an autogenerated conversion function.
func Convert_v1_Actions_To_platform_Actions(in *Actions, out *platform.Actions, s conversion.Scope) error {
	return autoConvert_v1_Actions_To_platform_Actions(in, out, s)
}

func autoConvert_platform_Actions_To_v1_Actions(in *platform.Actions, out *Actions, s conversion.Scope) error {
	if err := Convert_platform_RunTime_To_v1_RunTime(&in.Runtime, &out.Runtime, s); err != nil {
		return err
	}
	if err := Convert_platform_NodePod_To_v1_NodePod(&in.NodePod, &out.NodePod, s); err != nil {
		return err
	}
	if err := Convert_platform_CVM_To_v1_CVM(&in.CVM, &out.CVM, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_Actions_To_v1_Actions is an autogenerated conversion function.
func Convert_platform_Actions_To_v1_Actions(in *platform.Actions, out *Actions, s conversion.Scope) error {
	return autoConvert_platform_Actions_To_v1_Actions(in, out, s)
}

func autoConvert_v1_AddonType_To_platform_AddonType(in *AddonType, out *platform.AddonType, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_AddonTypeSpec_To_platform_AddonTypeSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_AddonType_To_platform_AddonType is an autogenerated conversion function.
func Convert_v1_AddonType_To_platform_AddonType(in *AddonType, out *platform.AddonType, s conversion.Scope) error {
	return autoConvert_v1_AddonType_To_platform_AddonType(in, out, s)
}

func autoConvert_platform_AddonType_To_v1_AddonType(in *platform.AddonType, out *AddonType, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_AddonTypeSpec_To_v1_AddonTypeSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_AddonType_To_v1_AddonType is an autogenerated conversion function.
func Convert_platform_AddonType_To_v1_AddonType(in *platform.AddonType, out *AddonType, s conversion.Scope) error {
	return autoConvert_platform_AddonType_To_v1_AddonType(in, out, s)
}

func autoConvert_v1_AddonTypeList_To_platform_AddonTypeList(in *AddonTypeList, out *platform.AddonTypeList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.AddonType)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_AddonTypeList_To_platform_AddonTypeList is an autogenerated conversion function.
func Convert_v1_AddonTypeList_To_platform_AddonTypeList(in *AddonTypeList, out *platform.AddonTypeList, s conversion.Scope) error {
	return autoConvert_v1_AddonTypeList_To_platform_AddonTypeList(in, out, s)
}

func autoConvert_platform_AddonTypeList_To_v1_AddonTypeList(in *platform.AddonTypeList, out *AddonTypeList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]AddonType)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_AddonTypeList_To_v1_AddonTypeList is an autogenerated conversion function.
func Convert_platform_AddonTypeList_To_v1_AddonTypeList(in *platform.AddonTypeList, out *AddonTypeList, s conversion.Scope) error {
	return autoConvert_platform_AddonTypeList_To_v1_AddonTypeList(in, out, s)
}

func autoConvert_v1_AddonTypeSpec_To_platform_AddonTypeSpec(in *AddonTypeSpec, out *platform.AddonTypeSpec, s conversion.Scope) error {
	out.Type = in.Type
	out.TenantIDs = *(*[]string)(unsafe.Pointer(&in.TenantIDs))
	out.Level = platform.AddonLevel(in.Level)
	out.LatestVersion = in.LatestVersion
	out.Description = in.Description
	out.VersionSpec = *(*[]platform.VersionMap)(unsafe.Pointer(&in.VersionSpec))
	return nil
}

// Convert_v1_AddonTypeSpec_To_platform_AddonTypeSpec is an autogenerated conversion function.
func Convert_v1_AddonTypeSpec_To_platform_AddonTypeSpec(in *AddonTypeSpec, out *platform.AddonTypeSpec, s conversion.Scope) error {
	return autoConvert_v1_AddonTypeSpec_To_platform_AddonTypeSpec(in, out, s)
}

func autoConvert_platform_AddonTypeSpec_To_v1_AddonTypeSpec(in *platform.AddonTypeSpec, out *AddonTypeSpec, s conversion.Scope) error {
	out.Type = in.Type
	out.TenantIDs = *(*[]string)(unsafe.Pointer(&in.TenantIDs))
	out.Level = AddonLevel(in.Level)
	out.LatestVersion = in.LatestVersion
	out.Description = in.Description
	out.VersionSpec = *(*[]VersionMap)(unsafe.Pointer(&in.VersionSpec))
	return nil
}

// Convert_platform_AddonTypeSpec_To_v1_AddonTypeSpec is an autogenerated conversion function.
func Convert_platform_AddonTypeSpec_To_v1_AddonTypeSpec(in *platform.AddonTypeSpec, out *AddonTypeSpec, s conversion.Scope) error {
	return autoConvert_platform_AddonTypeSpec_To_v1_AddonTypeSpec(in, out, s)
}

func autoConvert_v1_AppSpec_To_platform_AppSpec(in *AppSpec, out *platform.AppSpec, s conversion.Scope) error {
	out.Chart = in.Chart
	out.ChartFrom = in.ChartFrom
	out.ChartNamespace = in.ChartNamespace
	out.ReleaseName = in.ReleaseName
	return nil
}

// Convert_v1_AppSpec_To_platform_AppSpec is an autogenerated conversion function.
func Convert_v1_AppSpec_To_platform_AppSpec(in *AppSpec, out *platform.AppSpec, s conversion.Scope) error {
	return autoConvert_v1_AppSpec_To_platform_AppSpec(in, out, s)
}

func autoConvert_platform_AppSpec_To_v1_AppSpec(in *platform.AppSpec, out *AppSpec, s conversion.Scope) error {
	out.Chart = in.Chart
	out.ChartFrom = in.ChartFrom
	out.ChartNamespace = in.ChartNamespace
	out.ReleaseName = in.ReleaseName
	return nil
}

// Convert_platform_AppSpec_To_v1_AppSpec is an autogenerated conversion function.
func Convert_platform_AppSpec_To_v1_AppSpec(in *platform.AppSpec, out *AppSpec, s conversion.Scope) error {
	return autoConvert_platform_AppSpec_To_v1_AppSpec(in, out, s)
}

func autoConvert_v1_AuthenticationInfo_To_platform_AuthenticationInfo(in *AuthenticationInfo, out *platform.AuthenticationInfo, s conversion.Scope) error {
	out.ClientCertificate = *(*[]byte)(unsafe.Pointer(&in.ClientCertificate))
	out.ClientKey = *(*[]byte)(unsafe.Pointer(&in.ClientKey))
	out.CommonName = in.CommonName
	return nil
}

// Convert_v1_AuthenticationInfo_To_platform_AuthenticationInfo is an autogenerated conversion function.
func Convert_v1_AuthenticationInfo_To_platform_AuthenticationInfo(in *AuthenticationInfo, out *platform.AuthenticationInfo, s conversion.Scope) error {
	return autoConvert_v1_AuthenticationInfo_To_platform_AuthenticationInfo(in, out, s)
}

func autoConvert_platform_AuthenticationInfo_To_v1_AuthenticationInfo(in *platform.AuthenticationInfo, out *AuthenticationInfo, s conversion.Scope) error {
	out.ClientCertificate = *(*[]byte)(unsafe.Pointer(&in.ClientCertificate))
	out.ClientKey = *(*[]byte)(unsafe.Pointer(&in.ClientKey))
	out.CommonName = in.CommonName
	return nil
}

// Convert_platform_AuthenticationInfo_To_v1_AuthenticationInfo is an autogenerated conversion function.
func Convert_platform_AuthenticationInfo_To_v1_AuthenticationInfo(in *platform.AuthenticationInfo, out *AuthenticationInfo, s conversion.Scope) error {
	return autoConvert_platform_AuthenticationInfo_To_v1_AuthenticationInfo(in, out, s)
}

func autoConvert_v1_CBS_To_platform_CBS(in *CBS, out *platform.CBS, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_CBSSpec_To_platform_CBSSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_CBSStatus_To_platform_CBSStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_CBS_To_platform_CBS is an autogenerated conversion function.
func Convert_v1_CBS_To_platform_CBS(in *CBS, out *platform.CBS, s conversion.Scope) error {
	return autoConvert_v1_CBS_To_platform_CBS(in, out, s)
}

func autoConvert_platform_CBS_To_v1_CBS(in *platform.CBS, out *CBS, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_CBSSpec_To_v1_CBSSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_CBSStatus_To_v1_CBSStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_CBS_To_v1_CBS is an autogenerated conversion function.
func Convert_platform_CBS_To_v1_CBS(in *platform.CBS, out *CBS, s conversion.Scope) error {
	return autoConvert_platform_CBS_To_v1_CBS(in, out, s)
}

func autoConvert_v1_CBSList_To_platform_CBSList(in *CBSList, out *platform.CBSList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.CBS)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_CBSList_To_platform_CBSList is an autogenerated conversion function.
func Convert_v1_CBSList_To_platform_CBSList(in *CBSList, out *platform.CBSList, s conversion.Scope) error {
	return autoConvert_v1_CBSList_To_platform_CBSList(in, out, s)
}

func autoConvert_platform_CBSList_To_v1_CBSList(in *platform.CBSList, out *CBSList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]CBS)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_CBSList_To_v1_CBSList is an autogenerated conversion function.
func Convert_platform_CBSList_To_v1_CBSList(in *platform.CBSList, out *CBSList, s conversion.Scope) error {
	return autoConvert_platform_CBSList_To_v1_CBSList(in, out, s)
}

func autoConvert_v1_CBSSpec_To_platform_CBSSpec(in *CBSSpec, out *platform.CBSSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	out.Rootdir = in.Rootdir
	return nil
}

// Convert_v1_CBSSpec_To_platform_CBSSpec is an autogenerated conversion function.
func Convert_v1_CBSSpec_To_platform_CBSSpec(in *CBSSpec, out *platform.CBSSpec, s conversion.Scope) error {
	return autoConvert_v1_CBSSpec_To_platform_CBSSpec(in, out, s)
}

func autoConvert_platform_CBSSpec_To_v1_CBSSpec(in *platform.CBSSpec, out *CBSSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	out.Rootdir = in.Rootdir
	return nil
}

// Convert_platform_CBSSpec_To_v1_CBSSpec is an autogenerated conversion function.
func Convert_platform_CBSSpec_To_v1_CBSSpec(in *platform.CBSSpec, out *CBSSpec, s conversion.Scope) error {
	return autoConvert_platform_CBSSpec_To_v1_CBSSpec(in, out, s)
}

func autoConvert_v1_CBSStatus_To_platform_CBSStatus(in *CBSStatus, out *platform.CBSStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_v1_CBSStatus_To_platform_CBSStatus is an autogenerated conversion function.
func Convert_v1_CBSStatus_To_platform_CBSStatus(in *CBSStatus, out *platform.CBSStatus, s conversion.Scope) error {
	return autoConvert_v1_CBSStatus_To_platform_CBSStatus(in, out, s)
}

func autoConvert_platform_CBSStatus_To_v1_CBSStatus(in *platform.CBSStatus, out *CBSStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_platform_CBSStatus_To_v1_CBSStatus is an autogenerated conversion function.
func Convert_platform_CBSStatus_To_v1_CBSStatus(in *platform.CBSStatus, out *CBSStatus, s conversion.Scope) error {
	return autoConvert_platform_CBSStatus_To_v1_CBSStatus(in, out, s)
}

func autoConvert_v1_CFS_To_platform_CFS(in *CFS, out *platform.CFS, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_CFSSpec_To_platform_CFSSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_CFSStatus_To_platform_CFSStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_CFS_To_platform_CFS is an autogenerated conversion function.
func Convert_v1_CFS_To_platform_CFS(in *CFS, out *platform.CFS, s conversion.Scope) error {
	return autoConvert_v1_CFS_To_platform_CFS(in, out, s)
}

func autoConvert_platform_CFS_To_v1_CFS(in *platform.CFS, out *CFS, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_CFSSpec_To_v1_CFSSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_CFSStatus_To_v1_CFSStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_CFS_To_v1_CFS is an autogenerated conversion function.
func Convert_platform_CFS_To_v1_CFS(in *platform.CFS, out *CFS, s conversion.Scope) error {
	return autoConvert_platform_CFS_To_v1_CFS(in, out, s)
}

func autoConvert_v1_CFSList_To_platform_CFSList(in *CFSList, out *platform.CFSList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.CFS)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_CFSList_To_platform_CFSList is an autogenerated conversion function.
func Convert_v1_CFSList_To_platform_CFSList(in *CFSList, out *platform.CFSList, s conversion.Scope) error {
	return autoConvert_v1_CFSList_To_platform_CFSList(in, out, s)
}

func autoConvert_platform_CFSList_To_v1_CFSList(in *platform.CFSList, out *CFSList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]CFS)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_CFSList_To_v1_CFSList is an autogenerated conversion function.
func Convert_platform_CFSList_To_v1_CFSList(in *platform.CFSList, out *CFSList, s conversion.Scope) error {
	return autoConvert_platform_CFSList_To_v1_CFSList(in, out, s)
}

func autoConvert_v1_CFSSpec_To_platform_CFSSpec(in *CFSSpec, out *platform.CFSSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	out.Rootdir = in.Rootdir
	return nil
}

// Convert_v1_CFSSpec_To_platform_CFSSpec is an autogenerated conversion function.
func Convert_v1_CFSSpec_To_platform_CFSSpec(in *CFSSpec, out *platform.CFSSpec, s conversion.Scope) error {
	return autoConvert_v1_CFSSpec_To_platform_CFSSpec(in, out, s)
}

func autoConvert_platform_CFSSpec_To_v1_CFSSpec(in *platform.CFSSpec, out *CFSSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	out.Rootdir = in.Rootdir
	return nil
}

// Convert_platform_CFSSpec_To_v1_CFSSpec is an autogenerated conversion function.
func Convert_platform_CFSSpec_To_v1_CFSSpec(in *platform.CFSSpec, out *CFSSpec, s conversion.Scope) error {
	return autoConvert_platform_CFSSpec_To_v1_CFSSpec(in, out, s)
}

func autoConvert_v1_CFSStatus_To_platform_CFSStatus(in *CFSStatus, out *platform.CFSStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_v1_CFSStatus_To_platform_CFSStatus is an autogenerated conversion function.
func Convert_v1_CFSStatus_To_platform_CFSStatus(in *CFSStatus, out *platform.CFSStatus, s conversion.Scope) error {
	return autoConvert_v1_CFSStatus_To_platform_CFSStatus(in, out, s)
}

func autoConvert_platform_CFSStatus_To_v1_CFSStatus(in *platform.CFSStatus, out *CFSStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_platform_CFSStatus_To_v1_CFSStatus is an autogenerated conversion function.
func Convert_platform_CFSStatus_To_v1_CFSStatus(in *platform.CFSStatus, out *CFSStatus, s conversion.Scope) error {
	return autoConvert_platform_CFSStatus_To_v1_CFSStatus(in, out, s)
}

func autoConvert_v1_CLSLogConfigProxyOptions_To_platform_CLSLogConfigProxyOptions(in *CLSLogConfigProxyOptions, out *platform.CLSLogConfigProxyOptions, s conversion.Scope) error {
	out.Name = in.Name
	out.Action = in.Action
	out.FieldSelector = in.FieldSelector
	out.Limit = in.Limit
	out.Continue = in.Continue
	return nil
}

// Convert_v1_CLSLogConfigProxyOptions_To_platform_CLSLogConfigProxyOptions is an autogenerated conversion function.
func Convert_v1_CLSLogConfigProxyOptions_To_platform_CLSLogConfigProxyOptions(in *CLSLogConfigProxyOptions, out *platform.CLSLogConfigProxyOptions, s conversion.Scope) error {
	return autoConvert_v1_CLSLogConfigProxyOptions_To_platform_CLSLogConfigProxyOptions(in, out, s)
}

func autoConvert_platform_CLSLogConfigProxyOptions_To_v1_CLSLogConfigProxyOptions(in *platform.CLSLogConfigProxyOptions, out *CLSLogConfigProxyOptions, s conversion.Scope) error {
	out.Name = in.Name
	out.Action = in.Action
	out.FieldSelector = in.FieldSelector
	out.Limit = in.Limit
	out.Continue = in.Continue
	return nil
}

// Convert_platform_CLSLogConfigProxyOptions_To_v1_CLSLogConfigProxyOptions is an autogenerated conversion function.
func Convert_platform_CLSLogConfigProxyOptions_To_v1_CLSLogConfigProxyOptions(in *platform.CLSLogConfigProxyOptions, out *CLSLogConfigProxyOptions, s conversion.Scope) error {
	return autoConvert_platform_CLSLogConfigProxyOptions_To_v1_CLSLogConfigProxyOptions(in, out, s)
}

func autoConvert_v1_COS_To_platform_COS(in *COS, out *platform.COS, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_COSSpec_To_platform_COSSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_COSStatus_To_platform_COSStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_COS_To_platform_COS is an autogenerated conversion function.
func Convert_v1_COS_To_platform_COS(in *COS, out *platform.COS, s conversion.Scope) error {
	return autoConvert_v1_COS_To_platform_COS(in, out, s)
}

func autoConvert_platform_COS_To_v1_COS(in *platform.COS, out *COS, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_COSSpec_To_v1_COSSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_COSStatus_To_v1_COSStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_COS_To_v1_COS is an autogenerated conversion function.
func Convert_platform_COS_To_v1_COS(in *platform.COS, out *COS, s conversion.Scope) error {
	return autoConvert_platform_COS_To_v1_COS(in, out, s)
}

func autoConvert_v1_COSList_To_platform_COSList(in *COSList, out *platform.COSList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.COS)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_COSList_To_platform_COSList is an autogenerated conversion function.
func Convert_v1_COSList_To_platform_COSList(in *COSList, out *platform.COSList, s conversion.Scope) error {
	return autoConvert_v1_COSList_To_platform_COSList(in, out, s)
}

func autoConvert_platform_COSList_To_v1_COSList(in *platform.COSList, out *COSList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]COS)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_COSList_To_v1_COSList is an autogenerated conversion function.
func Convert_platform_COSList_To_v1_COSList(in *platform.COSList, out *COSList, s conversion.Scope) error {
	return autoConvert_platform_COSList_To_v1_COSList(in, out, s)
}

func autoConvert_v1_COSSpec_To_platform_COSSpec(in *COSSpec, out *platform.COSSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	out.Rootdir = in.Rootdir
	return nil
}

// Convert_v1_COSSpec_To_platform_COSSpec is an autogenerated conversion function.
func Convert_v1_COSSpec_To_platform_COSSpec(in *COSSpec, out *platform.COSSpec, s conversion.Scope) error {
	return autoConvert_v1_COSSpec_To_platform_COSSpec(in, out, s)
}

func autoConvert_platform_COSSpec_To_v1_COSSpec(in *platform.COSSpec, out *COSSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	out.Rootdir = in.Rootdir
	return nil
}

// Convert_platform_COSSpec_To_v1_COSSpec is an autogenerated conversion function.
func Convert_platform_COSSpec_To_v1_COSSpec(in *platform.COSSpec, out *COSSpec, s conversion.Scope) error {
	return autoConvert_platform_COSSpec_To_v1_COSSpec(in, out, s)
}

func autoConvert_v1_COSStatus_To_platform_COSStatus(in *COSStatus, out *platform.COSStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_v1_COSStatus_To_platform_COSStatus is an autogenerated conversion function.
func Convert_v1_COSStatus_To_platform_COSStatus(in *COSStatus, out *platform.COSStatus, s conversion.Scope) error {
	return autoConvert_v1_COSStatus_To_platform_COSStatus(in, out, s)
}

func autoConvert_platform_COSStatus_To_v1_COSStatus(in *platform.COSStatus, out *COSStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_platform_COSStatus_To_v1_COSStatus is an autogenerated conversion function.
func Convert_platform_COSStatus_To_v1_COSStatus(in *platform.COSStatus, out *COSStatus, s conversion.Scope) error {
	return autoConvert_platform_COSStatus_To_v1_COSStatus(in, out, s)
}

func autoConvert_v1_CVM_To_platform_CVM(in *CVM, out *platform.CVM, s conversion.Scope) error {
	out.RetryCounts = in.RetryCounts
	out.ReBootCVM = in.ReBootCVM
	return nil
}

// Convert_v1_CVM_To_platform_CVM is an autogenerated conversion function.
func Convert_v1_CVM_To_platform_CVM(in *CVM, out *platform.CVM, s conversion.Scope) error {
	return autoConvert_v1_CVM_To_platform_CVM(in, out, s)
}

func autoConvert_platform_CVM_To_v1_CVM(in *platform.CVM, out *CVM, s conversion.Scope) error {
	out.RetryCounts = in.RetryCounts
	out.ReBootCVM = in.ReBootCVM
	return nil
}

// Convert_platform_CVM_To_v1_CVM is an autogenerated conversion function.
func Convert_platform_CVM_To_v1_CVM(in *platform.CVM, out *CVM, s conversion.Scope) error {
	return autoConvert_platform_CVM_To_v1_CVM(in, out, s)
}

func autoConvert_v1_ChartSpec_To_platform_ChartSpec(in *ChartSpec, out *platform.ChartSpec, s conversion.Scope) error {
	out.Chart = in.Chart
	out.ChartFrom = in.ChartFrom
	out.ChartVersion = in.ChartVersion
	out.ChartNamespace = in.ChartNamespace
	out.ChartInstanceID = in.ChartInstanceID
	return nil
}

// Convert_v1_ChartSpec_To_platform_ChartSpec is an autogenerated conversion function.
func Convert_v1_ChartSpec_To_platform_ChartSpec(in *ChartSpec, out *platform.ChartSpec, s conversion.Scope) error {
	return autoConvert_v1_ChartSpec_To_platform_ChartSpec(in, out, s)
}

func autoConvert_platform_ChartSpec_To_v1_ChartSpec(in *platform.ChartSpec, out *ChartSpec, s conversion.Scope) error {
	out.Chart = in.Chart
	out.ChartFrom = in.ChartFrom
	out.ChartVersion = in.ChartVersion
	out.ChartNamespace = in.ChartNamespace
	out.ChartInstanceID = in.ChartInstanceID
	return nil
}

// Convert_platform_ChartSpec_To_v1_ChartSpec is an autogenerated conversion function.
func Convert_platform_ChartSpec_To_v1_ChartSpec(in *platform.ChartSpec, out *ChartSpec, s conversion.Scope) error {
	return autoConvert_platform_ChartSpec_To_v1_ChartSpec(in, out, s)
}

func autoConvert_v1_Cluster_To_platform_Cluster(in *Cluster, out *platform.Cluster, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_ClusterSpec_To_platform_ClusterSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_ClusterStatus_To_platform_ClusterStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_Cluster_To_platform_Cluster is an autogenerated conversion function.
func Convert_v1_Cluster_To_platform_Cluster(in *Cluster, out *platform.Cluster, s conversion.Scope) error {
	return autoConvert_v1_Cluster_To_platform_Cluster(in, out, s)
}

func autoConvert_platform_Cluster_To_v1_Cluster(in *platform.Cluster, out *Cluster, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_ClusterSpec_To_v1_ClusterSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_ClusterStatus_To_v1_ClusterStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_Cluster_To_v1_Cluster is an autogenerated conversion function.
func Convert_platform_Cluster_To_v1_Cluster(in *platform.Cluster, out *Cluster, s conversion.Scope) error {
	return autoConvert_platform_Cluster_To_v1_Cluster(in, out, s)
}

func autoConvert_v1_ClusterAPIResource_To_platform_ClusterAPIResource(in *ClusterAPIResource, out *platform.ClusterAPIResource, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.GroupVersion = in.GroupVersion
	out.APIResources = *(*[]platform.ClusterGroupAPIResource)(unsafe.Pointer(&in.APIResources))
	return nil
}

// Convert_v1_ClusterAPIResource_To_platform_ClusterAPIResource is an autogenerated conversion function.
func Convert_v1_ClusterAPIResource_To_platform_ClusterAPIResource(in *ClusterAPIResource, out *platform.ClusterAPIResource, s conversion.Scope) error {
	return autoConvert_v1_ClusterAPIResource_To_platform_ClusterAPIResource(in, out, s)
}

func autoConvert_platform_ClusterAPIResource_To_v1_ClusterAPIResource(in *platform.ClusterAPIResource, out *ClusterAPIResource, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.GroupVersion = in.GroupVersion
	out.APIResources = *(*[]ClusterGroupAPIResource)(unsafe.Pointer(&in.APIResources))
	return nil
}

// Convert_platform_ClusterAPIResource_To_v1_ClusterAPIResource is an autogenerated conversion function.
func Convert_platform_ClusterAPIResource_To_v1_ClusterAPIResource(in *platform.ClusterAPIResource, out *ClusterAPIResource, s conversion.Scope) error {
	return autoConvert_platform_ClusterAPIResource_To_v1_ClusterAPIResource(in, out, s)
}

func autoConvert_v1_ClusterAPIResourceList_To_platform_ClusterAPIResourceList(in *ClusterAPIResourceList, out *platform.ClusterAPIResourceList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.ClusterAPIResource)(unsafe.Pointer(&in.Items))
	out.FailedGroupError = in.FailedGroupError
	return nil
}

// Convert_v1_ClusterAPIResourceList_To_platform_ClusterAPIResourceList is an autogenerated conversion function.
func Convert_v1_ClusterAPIResourceList_To_platform_ClusterAPIResourceList(in *ClusterAPIResourceList, out *platform.ClusterAPIResourceList, s conversion.Scope) error {
	return autoConvert_v1_ClusterAPIResourceList_To_platform_ClusterAPIResourceList(in, out, s)
}

func autoConvert_platform_ClusterAPIResourceList_To_v1_ClusterAPIResourceList(in *platform.ClusterAPIResourceList, out *ClusterAPIResourceList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]ClusterAPIResource)(unsafe.Pointer(&in.Items))
	out.FailedGroupError = in.FailedGroupError
	return nil
}

// Convert_platform_ClusterAPIResourceList_To_v1_ClusterAPIResourceList is an autogenerated conversion function.
func Convert_platform_ClusterAPIResourceList_To_v1_ClusterAPIResourceList(in *platform.ClusterAPIResourceList, out *ClusterAPIResourceList, s conversion.Scope) error {
	return autoConvert_platform_ClusterAPIResourceList_To_v1_ClusterAPIResourceList(in, out, s)
}

func autoConvert_v1_ClusterAPIResourceOptions_To_platform_ClusterAPIResourceOptions(in *ClusterAPIResourceOptions, out *platform.ClusterAPIResourceOptions, s conversion.Scope) error {
	out.OnlySecure = in.OnlySecure
	return nil
}

// Convert_v1_ClusterAPIResourceOptions_To_platform_ClusterAPIResourceOptions is an autogenerated conversion function.
func Convert_v1_ClusterAPIResourceOptions_To_platform_ClusterAPIResourceOptions(in *ClusterAPIResourceOptions, out *platform.ClusterAPIResourceOptions, s conversion.Scope) error {
	return autoConvert_v1_ClusterAPIResourceOptions_To_platform_ClusterAPIResourceOptions(in, out, s)
}

func autoConvert_platform_ClusterAPIResourceOptions_To_v1_ClusterAPIResourceOptions(in *platform.ClusterAPIResourceOptions, out *ClusterAPIResourceOptions, s conversion.Scope) error {
	out.OnlySecure = in.OnlySecure
	return nil
}

// Convert_platform_ClusterAPIResourceOptions_To_v1_ClusterAPIResourceOptions is an autogenerated conversion function.
func Convert_platform_ClusterAPIResourceOptions_To_v1_ClusterAPIResourceOptions(in *platform.ClusterAPIResourceOptions, out *ClusterAPIResourceOptions, s conversion.Scope) error {
	return autoConvert_platform_ClusterAPIResourceOptions_To_v1_ClusterAPIResourceOptions(in, out, s)
}

func autoConvert_v1_ClusterAddOn_To_platform_ClusterAddOn(in *ClusterAddOn, out *platform.ClusterAddOn, s conversion.Scope) error {
	return nil
}

// Convert_v1_ClusterAddOn_To_platform_ClusterAddOn is an autogenerated conversion function.
func Convert_v1_ClusterAddOn_To_platform_ClusterAddOn(in *ClusterAddOn, out *platform.ClusterAddOn, s conversion.Scope) error {
	return autoConvert_v1_ClusterAddOn_To_platform_ClusterAddOn(in, out, s)
}

func autoConvert_platform_ClusterAddOn_To_v1_ClusterAddOn(in *platform.ClusterAddOn, out *ClusterAddOn, s conversion.Scope) error {
	return nil
}

// Convert_platform_ClusterAddOn_To_v1_ClusterAddOn is an autogenerated conversion function.
func Convert_platform_ClusterAddOn_To_v1_ClusterAddOn(in *platform.ClusterAddOn, out *ClusterAddOn, s conversion.Scope) error {
	return autoConvert_platform_ClusterAddOn_To_v1_ClusterAddOn(in, out, s)
}

func autoConvert_v1_ClusterAddon_To_platform_ClusterAddon(in *ClusterAddon, out *platform.ClusterAddon, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_ClusterAddonSpec_To_platform_ClusterAddonSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_ClusterAddonStatus_To_platform_ClusterAddonStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_ClusterAddon_To_platform_ClusterAddon is an autogenerated conversion function.
func Convert_v1_ClusterAddon_To_platform_ClusterAddon(in *ClusterAddon, out *platform.ClusterAddon, s conversion.Scope) error {
	return autoConvert_v1_ClusterAddon_To_platform_ClusterAddon(in, out, s)
}

func autoConvert_platform_ClusterAddon_To_v1_ClusterAddon(in *platform.ClusterAddon, out *ClusterAddon, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_ClusterAddonSpec_To_v1_ClusterAddonSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_ClusterAddonStatus_To_v1_ClusterAddonStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_ClusterAddon_To_v1_ClusterAddon is an autogenerated conversion function.
func Convert_platform_ClusterAddon_To_v1_ClusterAddon(in *platform.ClusterAddon, out *ClusterAddon, s conversion.Scope) error {
	return autoConvert_platform_ClusterAddon_To_v1_ClusterAddon(in, out, s)
}

func autoConvert_v1_ClusterAddonKind_To_platform_ClusterAddonKind(in *ClusterAddonKind, out *platform.ClusterAddonKind, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	return nil
}

// Convert_v1_ClusterAddonKind_To_platform_ClusterAddonKind is an autogenerated conversion function.
func Convert_v1_ClusterAddonKind_To_platform_ClusterAddonKind(in *ClusterAddonKind, out *platform.ClusterAddonKind, s conversion.Scope) error {
	return autoConvert_v1_ClusterAddonKind_To_platform_ClusterAddonKind(in, out, s)
}

func autoConvert_platform_ClusterAddonKind_To_v1_ClusterAddonKind(in *platform.ClusterAddonKind, out *ClusterAddonKind, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	return nil
}

// Convert_platform_ClusterAddonKind_To_v1_ClusterAddonKind is an autogenerated conversion function.
func Convert_platform_ClusterAddonKind_To_v1_ClusterAddonKind(in *platform.ClusterAddonKind, out *ClusterAddonKind, s conversion.Scope) error {
	return autoConvert_platform_ClusterAddonKind_To_v1_ClusterAddonKind(in, out, s)
}

func autoConvert_v1_ClusterAddonKindList_To_platform_ClusterAddonKindList(in *ClusterAddonKindList, out *platform.ClusterAddonKindList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.ClusterAddonKind)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_ClusterAddonKindList_To_platform_ClusterAddonKindList is an autogenerated conversion function.
func Convert_v1_ClusterAddonKindList_To_platform_ClusterAddonKindList(in *ClusterAddonKindList, out *platform.ClusterAddonKindList, s conversion.Scope) error {
	return autoConvert_v1_ClusterAddonKindList_To_platform_ClusterAddonKindList(in, out, s)
}

func autoConvert_platform_ClusterAddonKindList_To_v1_ClusterAddonKindList(in *platform.ClusterAddonKindList, out *ClusterAddonKindList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]ClusterAddonKind)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_ClusterAddonKindList_To_v1_ClusterAddonKindList is an autogenerated conversion function.
func Convert_platform_ClusterAddonKindList_To_v1_ClusterAddonKindList(in *platform.ClusterAddonKindList, out *ClusterAddonKindList, s conversion.Scope) error {
	return autoConvert_platform_ClusterAddonKindList_To_v1_ClusterAddonKindList(in, out, s)
}

func autoConvert_v1_ClusterAddonList_To_platform_ClusterAddonList(in *ClusterAddonList, out *platform.ClusterAddonList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.ClusterAddon)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_ClusterAddonList_To_platform_ClusterAddonList is an autogenerated conversion function.
func Convert_v1_ClusterAddonList_To_platform_ClusterAddonList(in *ClusterAddonList, out *platform.ClusterAddonList, s conversion.Scope) error {
	return autoConvert_v1_ClusterAddonList_To_platform_ClusterAddonList(in, out, s)
}

func autoConvert_platform_ClusterAddonList_To_v1_ClusterAddonList(in *platform.ClusterAddonList, out *ClusterAddonList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]ClusterAddon)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_ClusterAddonList_To_v1_ClusterAddonList is an autogenerated conversion function.
func Convert_platform_ClusterAddonList_To_v1_ClusterAddonList(in *platform.ClusterAddonList, out *ClusterAddonList, s conversion.Scope) error {
	return autoConvert_platform_ClusterAddonList_To_v1_ClusterAddonList(in, out, s)
}

func autoConvert_v1_ClusterAddonSpec_To_platform_ClusterAddonSpec(in *ClusterAddonSpec, out *platform.ClusterAddonSpec, s conversion.Scope) error {
	out.Type = in.Type
	out.Level = platform.AddonLevel(in.Level)
	out.Version = in.Version
	return nil
}

// Convert_v1_ClusterAddonSpec_To_platform_ClusterAddonSpec is an autogenerated conversion function.
func Convert_v1_ClusterAddonSpec_To_platform_ClusterAddonSpec(in *ClusterAddonSpec, out *platform.ClusterAddonSpec, s conversion.Scope) error {
	return autoConvert_v1_ClusterAddonSpec_To_platform_ClusterAddonSpec(in, out, s)
}

func autoConvert_platform_ClusterAddonSpec_To_v1_ClusterAddonSpec(in *platform.ClusterAddonSpec, out *ClusterAddonSpec, s conversion.Scope) error {
	out.Type = in.Type
	out.Level = AddonLevel(in.Level)
	out.Version = in.Version
	return nil
}

// Convert_platform_ClusterAddonSpec_To_v1_ClusterAddonSpec is an autogenerated conversion function.
func Convert_platform_ClusterAddonSpec_To_v1_ClusterAddonSpec(in *platform.ClusterAddonSpec, out *ClusterAddonSpec, s conversion.Scope) error {
	return autoConvert_platform_ClusterAddonSpec_To_v1_ClusterAddonSpec(in, out, s)
}

func autoConvert_v1_ClusterAddonStatus_To_platform_ClusterAddonStatus(in *ClusterAddonStatus, out *platform.ClusterAddonStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = in.Phase
	out.Reason = in.Reason
	return nil
}

// Convert_v1_ClusterAddonStatus_To_platform_ClusterAddonStatus is an autogenerated conversion function.
func Convert_v1_ClusterAddonStatus_To_platform_ClusterAddonStatus(in *ClusterAddonStatus, out *platform.ClusterAddonStatus, s conversion.Scope) error {
	return autoConvert_v1_ClusterAddonStatus_To_platform_ClusterAddonStatus(in, out, s)
}

func autoConvert_platform_ClusterAddonStatus_To_v1_ClusterAddonStatus(in *platform.ClusterAddonStatus, out *ClusterAddonStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = in.Phase
	out.Reason = in.Reason
	return nil
}

// Convert_platform_ClusterAddonStatus_To_v1_ClusterAddonStatus is an autogenerated conversion function.
func Convert_platform_ClusterAddonStatus_To_v1_ClusterAddonStatus(in *platform.ClusterAddonStatus, out *ClusterAddonStatus, s conversion.Scope) error {
	return autoConvert_platform_ClusterAddonStatus_To_v1_ClusterAddonStatus(in, out, s)
}

func autoConvert_v1_ClusterAddonType_To_platform_ClusterAddonType(in *ClusterAddonType, out *platform.ClusterAddonType, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Type = in.Type
	out.Level = platform.AddonLevel(in.Level)
	out.LatestVersion = in.LatestVersion
	out.Description = in.Description
	return nil
}

// Convert_v1_ClusterAddonType_To_platform_ClusterAddonType is an autogenerated conversion function.
func Convert_v1_ClusterAddonType_To_platform_ClusterAddonType(in *ClusterAddonType, out *platform.ClusterAddonType, s conversion.Scope) error {
	return autoConvert_v1_ClusterAddonType_To_platform_ClusterAddonType(in, out, s)
}

func autoConvert_platform_ClusterAddonType_To_v1_ClusterAddonType(in *platform.ClusterAddonType, out *ClusterAddonType, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Type = in.Type
	out.Level = AddonLevel(in.Level)
	out.LatestVersion = in.LatestVersion
	out.Description = in.Description
	return nil
}

// Convert_platform_ClusterAddonType_To_v1_ClusterAddonType is an autogenerated conversion function.
func Convert_platform_ClusterAddonType_To_v1_ClusterAddonType(in *platform.ClusterAddonType, out *ClusterAddonType, s conversion.Scope) error {
	return autoConvert_platform_ClusterAddonType_To_v1_ClusterAddonType(in, out, s)
}

func autoConvert_v1_ClusterAddonTypeList_To_platform_ClusterAddonTypeList(in *ClusterAddonTypeList, out *platform.ClusterAddonTypeList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.ClusterAddonType)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_ClusterAddonTypeList_To_platform_ClusterAddonTypeList is an autogenerated conversion function.
func Convert_v1_ClusterAddonTypeList_To_platform_ClusterAddonTypeList(in *ClusterAddonTypeList, out *platform.ClusterAddonTypeList, s conversion.Scope) error {
	return autoConvert_v1_ClusterAddonTypeList_To_platform_ClusterAddonTypeList(in, out, s)
}

func autoConvert_platform_ClusterAddonTypeList_To_v1_ClusterAddonTypeList(in *platform.ClusterAddonTypeList, out *ClusterAddonTypeList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]ClusterAddonType)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_ClusterAddonTypeList_To_v1_ClusterAddonTypeList is an autogenerated conversion function.
func Convert_platform_ClusterAddonTypeList_To_v1_ClusterAddonTypeList(in *platform.ClusterAddonTypeList, out *ClusterAddonTypeList, s conversion.Scope) error {
	return autoConvert_platform_ClusterAddonTypeList_To_v1_ClusterAddonTypeList(in, out, s)
}

func autoConvert_v1_ClusterAddress_To_platform_ClusterAddress(in *ClusterAddress, out *platform.ClusterAddress, s conversion.Scope) error {
	out.Type = platform.ClusterAddressType(in.Type)
	out.IP = in.IP
	out.Port = in.Port
	out.Proxy = in.Proxy
	return nil
}

// Convert_v1_ClusterAddress_To_platform_ClusterAddress is an autogenerated conversion function.
func Convert_v1_ClusterAddress_To_platform_ClusterAddress(in *ClusterAddress, out *platform.ClusterAddress, s conversion.Scope) error {
	return autoConvert_v1_ClusterAddress_To_platform_ClusterAddress(in, out, s)
}

func autoConvert_platform_ClusterAddress_To_v1_ClusterAddress(in *platform.ClusterAddress, out *ClusterAddress, s conversion.Scope) error {
	out.Type = ClusterAddressType(in.Type)
	out.IP = in.IP
	out.Port = in.Port
	out.Proxy = in.Proxy
	return nil
}

// Convert_platform_ClusterAddress_To_v1_ClusterAddress is an autogenerated conversion function.
func Convert_platform_ClusterAddress_To_v1_ClusterAddress(in *platform.ClusterAddress, out *ClusterAddress, s conversion.Scope) error {
	return autoConvert_platform_ClusterAddress_To_v1_ClusterAddress(in, out, s)
}

func autoConvert_v1_ClusterApplyOptions_To_platform_ClusterApplyOptions(in *ClusterApplyOptions, out *platform.ClusterApplyOptions, s conversion.Scope) error {
	out.NotUpdate = in.NotUpdate
	out.IsMultiClusterResources = in.IsMultiClusterResources
	return nil
}

// Convert_v1_ClusterApplyOptions_To_platform_ClusterApplyOptions is an autogenerated conversion function.
func Convert_v1_ClusterApplyOptions_To_platform_ClusterApplyOptions(in *ClusterApplyOptions, out *platform.ClusterApplyOptions, s conversion.Scope) error {
	return autoConvert_v1_ClusterApplyOptions_To_platform_ClusterApplyOptions(in, out, s)
}

func autoConvert_platform_ClusterApplyOptions_To_v1_ClusterApplyOptions(in *platform.ClusterApplyOptions, out *ClusterApplyOptions, s conversion.Scope) error {
	out.NotUpdate = in.NotUpdate
	out.IsMultiClusterResources = in.IsMultiClusterResources
	return nil
}

// Convert_platform_ClusterApplyOptions_To_v1_ClusterApplyOptions is an autogenerated conversion function.
func Convert_platform_ClusterApplyOptions_To_v1_ClusterApplyOptions(in *platform.ClusterApplyOptions, out *ClusterApplyOptions, s conversion.Scope) error {
	return autoConvert_platform_ClusterApplyOptions_To_v1_ClusterApplyOptions(in, out, s)
}

func autoConvert_v1_ClusterAuthentication_To_platform_ClusterAuthentication(in *ClusterAuthentication, out *platform.ClusterAuthentication, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.OwnerUIN = in.OwnerUIN
	out.SubAccountUIN = in.SubAccountUIN
	out.ServiceRole = in.ServiceRole
	if err := Convert_v1_AuthenticationInfo_To_platform_AuthenticationInfo(&in.AuthenticationInfo, &out.AuthenticationInfo, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_ClusterAuthentication_To_platform_ClusterAuthentication is an autogenerated conversion function.
func Convert_v1_ClusterAuthentication_To_platform_ClusterAuthentication(in *ClusterAuthentication, out *platform.ClusterAuthentication, s conversion.Scope) error {
	return autoConvert_v1_ClusterAuthentication_To_platform_ClusterAuthentication(in, out, s)
}

func autoConvert_platform_ClusterAuthentication_To_v1_ClusterAuthentication(in *platform.ClusterAuthentication, out *ClusterAuthentication, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.OwnerUIN = in.OwnerUIN
	out.SubAccountUIN = in.SubAccountUIN
	out.ServiceRole = in.ServiceRole
	if err := Convert_platform_AuthenticationInfo_To_v1_AuthenticationInfo(&in.AuthenticationInfo, &out.AuthenticationInfo, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_ClusterAuthentication_To_v1_ClusterAuthentication is an autogenerated conversion function.
func Convert_platform_ClusterAuthentication_To_v1_ClusterAuthentication(in *platform.ClusterAuthentication, out *ClusterAuthentication, s conversion.Scope) error {
	return autoConvert_platform_ClusterAuthentication_To_v1_ClusterAuthentication(in, out, s)
}

func autoConvert_v1_ClusterAuthenticationList_To_platform_ClusterAuthenticationList(in *ClusterAuthenticationList, out *platform.ClusterAuthenticationList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.ClusterAuthentication)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_ClusterAuthenticationList_To_platform_ClusterAuthenticationList is an autogenerated conversion function.
func Convert_v1_ClusterAuthenticationList_To_platform_ClusterAuthenticationList(in *ClusterAuthenticationList, out *platform.ClusterAuthenticationList, s conversion.Scope) error {
	return autoConvert_v1_ClusterAuthenticationList_To_platform_ClusterAuthenticationList(in, out, s)
}

func autoConvert_platform_ClusterAuthenticationList_To_v1_ClusterAuthenticationList(in *platform.ClusterAuthenticationList, out *ClusterAuthenticationList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]ClusterAuthentication)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_ClusterAuthenticationList_To_v1_ClusterAuthenticationList is an autogenerated conversion function.
func Convert_platform_ClusterAuthenticationList_To_v1_ClusterAuthenticationList(in *platform.ClusterAuthenticationList, out *ClusterAuthenticationList, s conversion.Scope) error {
	return autoConvert_platform_ClusterAuthenticationList_To_v1_ClusterAuthenticationList(in, out, s)
}

func autoConvert_v1_ClusterComponent_To_platform_ClusterComponent(in *ClusterComponent, out *platform.ClusterComponent, s conversion.Scope) error {
	out.Type = in.Type
	if err := Convert_v1_ClusterComponentReplicas_To_platform_ClusterComponentReplicas(&in.Replicas, &out.Replicas, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_ClusterComponent_To_platform_ClusterComponent is an autogenerated conversion function.
func Convert_v1_ClusterComponent_To_platform_ClusterComponent(in *ClusterComponent, out *platform.ClusterComponent, s conversion.Scope) error {
	return autoConvert_v1_ClusterComponent_To_platform_ClusterComponent(in, out, s)
}

func autoConvert_platform_ClusterComponent_To_v1_ClusterComponent(in *platform.ClusterComponent, out *ClusterComponent, s conversion.Scope) error {
	out.Type = in.Type
	if err := Convert_platform_ClusterComponentReplicas_To_v1_ClusterComponentReplicas(&in.Replicas, &out.Replicas, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_ClusterComponent_To_v1_ClusterComponent is an autogenerated conversion function.
func Convert_platform_ClusterComponent_To_v1_ClusterComponent(in *platform.ClusterComponent, out *ClusterComponent, s conversion.Scope) error {
	return autoConvert_platform_ClusterComponent_To_v1_ClusterComponent(in, out, s)
}

func autoConvert_v1_ClusterComponentReplicas_To_platform_ClusterComponentReplicas(in *ClusterComponentReplicas, out *platform.ClusterComponentReplicas, s conversion.Scope) error {
	out.Desired = in.Desired
	out.Current = in.Current
	out.Available = in.Available
	out.Updated = in.Updated
	return nil
}

// Convert_v1_ClusterComponentReplicas_To_platform_ClusterComponentReplicas is an autogenerated conversion function.
func Convert_v1_ClusterComponentReplicas_To_platform_ClusterComponentReplicas(in *ClusterComponentReplicas, out *platform.ClusterComponentReplicas, s conversion.Scope) error {
	return autoConvert_v1_ClusterComponentReplicas_To_platform_ClusterComponentReplicas(in, out, s)
}

func autoConvert_platform_ClusterComponentReplicas_To_v1_ClusterComponentReplicas(in *platform.ClusterComponentReplicas, out *ClusterComponentReplicas, s conversion.Scope) error {
	out.Desired = in.Desired
	out.Current = in.Current
	out.Available = in.Available
	out.Updated = in.Updated
	return nil
}

// Convert_platform_ClusterComponentReplicas_To_v1_ClusterComponentReplicas is an autogenerated conversion function.
func Convert_platform_ClusterComponentReplicas_To_v1_ClusterComponentReplicas(in *platform.ClusterComponentReplicas, out *ClusterComponentReplicas, s conversion.Scope) error {
	return autoConvert_platform_ClusterComponentReplicas_To_v1_ClusterComponentReplicas(in, out, s)
}

func autoConvert_v1_ClusterCondition_To_platform_ClusterCondition(in *ClusterCondition, out *platform.ClusterCondition, s conversion.Scope) error {
	out.Type = in.Type
	out.Status = platform.ConditionStatus(in.Status)
	out.LastProbeTime = in.LastProbeTime
	out.LastTransitionTime = in.LastTransitionTime
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_v1_ClusterCondition_To_platform_ClusterCondition is an autogenerated conversion function.
func Convert_v1_ClusterCondition_To_platform_ClusterCondition(in *ClusterCondition, out *platform.ClusterCondition, s conversion.Scope) error {
	return autoConvert_v1_ClusterCondition_To_platform_ClusterCondition(in, out, s)
}

func autoConvert_platform_ClusterCondition_To_v1_ClusterCondition(in *platform.ClusterCondition, out *ClusterCondition, s conversion.Scope) error {
	out.Type = in.Type
	out.Status = ConditionStatus(in.Status)
	out.LastProbeTime = in.LastProbeTime
	out.LastTransitionTime = in.LastTransitionTime
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_platform_ClusterCondition_To_v1_ClusterCondition is an autogenerated conversion function.
func Convert_platform_ClusterCondition_To_v1_ClusterCondition(in *platform.ClusterCondition, out *ClusterCondition, s conversion.Scope) error {
	return autoConvert_platform_ClusterCondition_To_v1_ClusterCondition(in, out, s)
}

func autoConvert_v1_ClusterCredential_To_platform_ClusterCredential(in *ClusterCredential, out *platform.ClusterCredential, s conversion.Scope) error {
	out.CACert = *(*[]byte)(unsafe.Pointer(&in.CACert))
	out.CAKey = *(*[]byte)(unsafe.Pointer(&in.CAKey))
	out.APIServerCert = *(*[]byte)(unsafe.Pointer(&in.APIServerCert))
	out.APIServerKey = *(*[]byte)(unsafe.Pointer(&in.APIServerKey))
	out.ClientCert = *(*[]byte)(unsafe.Pointer(&in.ClientCert))
	out.ClientKey = *(*[]byte)(unsafe.Pointer(&in.ClientKey))
	out.Privileged = in.Privileged
	out.Token = (*string)(unsafe.Pointer(in.Token))
	out.KubeletToken = (*string)(unsafe.Pointer(in.KubeletToken))
	out.KubeProxyToken = (*string)(unsafe.Pointer(in.KubeProxyToken))
	return nil
}

// Convert_v1_ClusterCredential_To_platform_ClusterCredential is an autogenerated conversion function.
func Convert_v1_ClusterCredential_To_platform_ClusterCredential(in *ClusterCredential, out *platform.ClusterCredential, s conversion.Scope) error {
	return autoConvert_v1_ClusterCredential_To_platform_ClusterCredential(in, out, s)
}

func autoConvert_platform_ClusterCredential_To_v1_ClusterCredential(in *platform.ClusterCredential, out *ClusterCredential, s conversion.Scope) error {
	out.CACert = *(*[]byte)(unsafe.Pointer(&in.CACert))
	out.CAKey = *(*[]byte)(unsafe.Pointer(&in.CAKey))
	out.APIServerCert = *(*[]byte)(unsafe.Pointer(&in.APIServerCert))
	out.APIServerKey = *(*[]byte)(unsafe.Pointer(&in.APIServerKey))
	out.ClientCert = *(*[]byte)(unsafe.Pointer(&in.ClientCert))
	out.ClientKey = *(*[]byte)(unsafe.Pointer(&in.ClientKey))
	out.Privileged = in.Privileged
	out.Token = (*string)(unsafe.Pointer(in.Token))
	out.KubeletToken = (*string)(unsafe.Pointer(in.KubeletToken))
	out.KubeProxyToken = (*string)(unsafe.Pointer(in.KubeProxyToken))
	return nil
}

// Convert_platform_ClusterCredential_To_v1_ClusterCredential is an autogenerated conversion function.
func Convert_platform_ClusterCredential_To_v1_ClusterCredential(in *platform.ClusterCredential, out *ClusterCredential, s conversion.Scope) error {
	return autoConvert_platform_ClusterCredential_To_v1_ClusterCredential(in, out, s)
}

func autoConvert_v1_ClusterFeature_To_platform_ClusterFeature(in *ClusterFeature, out *platform.ClusterFeature, s conversion.Scope) error {
	out.IPVS = (*bool)(unsafe.Pointer(in.IPVS))
	out.Public = (*bool)(unsafe.Pointer(in.Public))
	return nil
}

// Convert_v1_ClusterFeature_To_platform_ClusterFeature is an autogenerated conversion function.
func Convert_v1_ClusterFeature_To_platform_ClusterFeature(in *ClusterFeature, out *platform.ClusterFeature, s conversion.Scope) error {
	return autoConvert_v1_ClusterFeature_To_platform_ClusterFeature(in, out, s)
}

func autoConvert_platform_ClusterFeature_To_v1_ClusterFeature(in *platform.ClusterFeature, out *ClusterFeature, s conversion.Scope) error {
	out.IPVS = (*bool)(unsafe.Pointer(in.IPVS))
	out.Public = (*bool)(unsafe.Pointer(in.Public))
	return nil
}

// Convert_platform_ClusterFeature_To_v1_ClusterFeature is an autogenerated conversion function.
func Convert_platform_ClusterFeature_To_v1_ClusterFeature(in *platform.ClusterFeature, out *ClusterFeature, s conversion.Scope) error {
	return autoConvert_platform_ClusterFeature_To_v1_ClusterFeature(in, out, s)
}

func autoConvert_v1_ClusterGroupAPIResource_To_platform_ClusterGroupAPIResource(in *ClusterGroupAPIResource, out *platform.ClusterGroupAPIResource, s conversion.Scope) error {
	out.Name = in.Name
	out.SingularName = in.SingularName
	out.Namespaced = in.Namespaced
	out.Group = in.Group
	out.Version = in.Version
	out.Kind = in.Kind
	out.Verbs = *(*[]string)(unsafe.Pointer(&in.Verbs))
	out.ShortNames = *(*[]string)(unsafe.Pointer(&in.ShortNames))
	out.Categories = *(*[]string)(unsafe.Pointer(&in.Categories))
	return nil
}

// Convert_v1_ClusterGroupAPIResource_To_platform_ClusterGroupAPIResource is an autogenerated conversion function.
func Convert_v1_ClusterGroupAPIResource_To_platform_ClusterGroupAPIResource(in *ClusterGroupAPIResource, out *platform.ClusterGroupAPIResource, s conversion.Scope) error {
	return autoConvert_v1_ClusterGroupAPIResource_To_platform_ClusterGroupAPIResource(in, out, s)
}

func autoConvert_platform_ClusterGroupAPIResource_To_v1_ClusterGroupAPIResource(in *platform.ClusterGroupAPIResource, out *ClusterGroupAPIResource, s conversion.Scope) error {
	out.Name = in.Name
	out.SingularName = in.SingularName
	out.Namespaced = in.Namespaced
	out.Group = in.Group
	out.Version = in.Version
	out.Kind = in.Kind
	out.Verbs = *(*[]string)(unsafe.Pointer(&in.Verbs))
	out.ShortNames = *(*[]string)(unsafe.Pointer(&in.ShortNames))
	out.Categories = *(*[]string)(unsafe.Pointer(&in.Categories))
	return nil
}

// Convert_platform_ClusterGroupAPIResource_To_v1_ClusterGroupAPIResource is an autogenerated conversion function.
func Convert_platform_ClusterGroupAPIResource_To_v1_ClusterGroupAPIResource(in *platform.ClusterGroupAPIResource, out *ClusterGroupAPIResource, s conversion.Scope) error {
	return autoConvert_platform_ClusterGroupAPIResource_To_v1_ClusterGroupAPIResource(in, out, s)
}

func autoConvert_v1_ClusterList_To_platform_ClusterList(in *ClusterList, out *platform.ClusterList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.Cluster)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_ClusterList_To_platform_ClusterList is an autogenerated conversion function.
func Convert_v1_ClusterList_To_platform_ClusterList(in *ClusterList, out *platform.ClusterList, s conversion.Scope) error {
	return autoConvert_v1_ClusterList_To_platform_ClusterList(in, out, s)
}

func autoConvert_platform_ClusterList_To_v1_ClusterList(in *platform.ClusterList, out *ClusterList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]Cluster)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_ClusterList_To_v1_ClusterList is an autogenerated conversion function.
func Convert_platform_ClusterList_To_v1_ClusterList(in *platform.ClusterList, out *ClusterList, s conversion.Scope) error {
	return autoConvert_platform_ClusterList_To_v1_ClusterList(in, out, s)
}

func autoConvert_v1_ClusterProperty_To_platform_ClusterProperty(in *ClusterProperty, out *platform.ClusterProperty, s conversion.Scope) error {
	out.MaxClusterServiceNum = (*int32)(unsafe.Pointer(in.MaxClusterServiceNum))
	out.MaxNodePodNum = (*int32)(unsafe.Pointer(in.MaxNodePodNum))
	return nil
}

// Convert_v1_ClusterProperty_To_platform_ClusterProperty is an autogenerated conversion function.
func Convert_v1_ClusterProperty_To_platform_ClusterProperty(in *ClusterProperty, out *platform.ClusterProperty, s conversion.Scope) error {
	return autoConvert_v1_ClusterProperty_To_platform_ClusterProperty(in, out, s)
}

func autoConvert_platform_ClusterProperty_To_v1_ClusterProperty(in *platform.ClusterProperty, out *ClusterProperty, s conversion.Scope) error {
	out.MaxClusterServiceNum = (*int32)(unsafe.Pointer(in.MaxClusterServiceNum))
	out.MaxNodePodNum = (*int32)(unsafe.Pointer(in.MaxNodePodNum))
	return nil
}

// Convert_platform_ClusterProperty_To_v1_ClusterProperty is an autogenerated conversion function.
func Convert_platform_ClusterProperty_To_v1_ClusterProperty(in *platform.ClusterProperty, out *ClusterProperty, s conversion.Scope) error {
	return autoConvert_platform_ClusterProperty_To_v1_ClusterProperty(in, out, s)
}

func autoConvert_v1_ClusterResource_To_platform_ClusterResource(in *ClusterResource, out *platform.ClusterResource, s conversion.Scope) error {
	out.Capacity = *(*platform.ResourceList)(unsafe.Pointer(&in.Capacity))
	out.Allocatable = *(*platform.ResourceList)(unsafe.Pointer(&in.Allocatable))
	out.Allocated = *(*platform.ResourceList)(unsafe.Pointer(&in.Allocated))
	return nil
}

// Convert_v1_ClusterResource_To_platform_ClusterResource is an autogenerated conversion function.
func Convert_v1_ClusterResource_To_platform_ClusterResource(in *ClusterResource, out *platform.ClusterResource, s conversion.Scope) error {
	return autoConvert_v1_ClusterResource_To_platform_ClusterResource(in, out, s)
}

func autoConvert_platform_ClusterResource_To_v1_ClusterResource(in *platform.ClusterResource, out *ClusterResource, s conversion.Scope) error {
	out.Capacity = *(*ResourceList)(unsafe.Pointer(&in.Capacity))
	out.Allocatable = *(*ResourceList)(unsafe.Pointer(&in.Allocatable))
	out.Allocated = *(*ResourceList)(unsafe.Pointer(&in.Allocated))
	return nil
}

// Convert_platform_ClusterResource_To_v1_ClusterResource is an autogenerated conversion function.
func Convert_platform_ClusterResource_To_v1_ClusterResource(in *platform.ClusterResource, out *ClusterResource, s conversion.Scope) error {
	return autoConvert_platform_ClusterResource_To_v1_ClusterResource(in, out, s)
}

func autoConvert_v1_ClusterSpec_To_platform_ClusterSpec(in *ClusterSpec, out *platform.ClusterSpec, s conversion.Scope) error {
	out.Finalizers = *(*[]platform.FinalizerName)(unsafe.Pointer(&in.Finalizers))
	out.TenantID = in.TenantID
	out.DisplayName = in.DisplayName
	out.Type = platform.ClusterType(in.Type)
	out.ClusterCIDR = in.ClusterCIDR
	out.PublicAlternativeNames = *(*[]string)(unsafe.Pointer(&in.PublicAlternativeNames))
	if err := Convert_v1_ClusterFeature_To_platform_ClusterFeature(&in.Features, &out.Features, s); err != nil {
		return err
	}
	if err := Convert_v1_ClusterProperty_To_platform_ClusterProperty(&in.Properties, &out.Properties, s); err != nil {
		return err
	}
	out.CreatorUIN = in.CreatorUIN
	out.UseRBAC = (*bool)(unsafe.Pointer(in.UseRBAC))
	return nil
}

// Convert_v1_ClusterSpec_To_platform_ClusterSpec is an autogenerated conversion function.
func Convert_v1_ClusterSpec_To_platform_ClusterSpec(in *ClusterSpec, out *platform.ClusterSpec, s conversion.Scope) error {
	return autoConvert_v1_ClusterSpec_To_platform_ClusterSpec(in, out, s)
}

func autoConvert_platform_ClusterSpec_To_v1_ClusterSpec(in *platform.ClusterSpec, out *ClusterSpec, s conversion.Scope) error {
	out.Finalizers = *(*[]FinalizerName)(unsafe.Pointer(&in.Finalizers))
	out.TenantID = in.TenantID
	out.DisplayName = in.DisplayName
	out.Type = ClusterType(in.Type)
	out.ClusterCIDR = in.ClusterCIDR
	out.PublicAlternativeNames = *(*[]string)(unsafe.Pointer(&in.PublicAlternativeNames))
	if err := Convert_platform_ClusterFeature_To_v1_ClusterFeature(&in.Features, &out.Features, s); err != nil {
		return err
	}
	if err := Convert_platform_ClusterProperty_To_v1_ClusterProperty(&in.Properties, &out.Properties, s); err != nil {
		return err
	}
	out.CreatorUIN = in.CreatorUIN
	out.UseRBAC = (*bool)(unsafe.Pointer(in.UseRBAC))
	return nil
}

// Convert_platform_ClusterSpec_To_v1_ClusterSpec is an autogenerated conversion function.
func Convert_platform_ClusterSpec_To_v1_ClusterSpec(in *platform.ClusterSpec, out *ClusterSpec, s conversion.Scope) error {
	return autoConvert_platform_ClusterSpec_To_v1_ClusterSpec(in, out, s)
}

func autoConvert_v1_ClusterStatus_To_platform_ClusterStatus(in *ClusterStatus, out *platform.ClusterStatus, s conversion.Scope) error {
	out.Locked = (*bool)(unsafe.Pointer(in.Locked))
	out.Version = in.Version
	out.Phase = platform.ClusterPhase(in.Phase)
	out.Conditions = *(*[]platform.ClusterCondition)(unsafe.Pointer(&in.Conditions))
	out.Message = in.Message
	out.Reason = in.Reason
	out.Addresses = *(*[]platform.ClusterAddress)(unsafe.Pointer(&in.Addresses))
	if err := Convert_v1_ClusterCredential_To_platform_ClusterCredential(&in.Credential, &out.Credential, s); err != nil {
		return err
	}
	if err := Convert_v1_ClusterAddOn_To_platform_ClusterAddOn(&in.AddOns, &out.AddOns, s); err != nil {
		return err
	}
	if err := Convert_v1_ClusterResource_To_platform_ClusterResource(&in.Resource, &out.Resource, s); err != nil {
		return err
	}
	out.Components = *(*[]platform.ClusterComponent)(unsafe.Pointer(&in.Components))
	return nil
}

// Convert_v1_ClusterStatus_To_platform_ClusterStatus is an autogenerated conversion function.
func Convert_v1_ClusterStatus_To_platform_ClusterStatus(in *ClusterStatus, out *platform.ClusterStatus, s conversion.Scope) error {
	return autoConvert_v1_ClusterStatus_To_platform_ClusterStatus(in, out, s)
}

func autoConvert_platform_ClusterStatus_To_v1_ClusterStatus(in *platform.ClusterStatus, out *ClusterStatus, s conversion.Scope) error {
	out.Locked = (*bool)(unsafe.Pointer(in.Locked))
	out.Version = in.Version
	out.Phase = ClusterPhase(in.Phase)
	out.Conditions = *(*[]ClusterCondition)(unsafe.Pointer(&in.Conditions))
	out.Message = in.Message
	out.Reason = in.Reason
	out.Addresses = *(*[]ClusterAddress)(unsafe.Pointer(&in.Addresses))
	if err := Convert_platform_ClusterCredential_To_v1_ClusterCredential(&in.Credential, &out.Credential, s); err != nil {
		return err
	}
	if err := Convert_platform_ClusterAddOn_To_v1_ClusterAddOn(&in.AddOns, &out.AddOns, s); err != nil {
		return err
	}
	if err := Convert_platform_ClusterResource_To_v1_ClusterResource(&in.Resource, &out.Resource, s); err != nil {
		return err
	}
	out.Components = *(*[]ClusterComponent)(unsafe.Pointer(&in.Components))
	return nil
}

// Convert_platform_ClusterStatus_To_v1_ClusterStatus is an autogenerated conversion function.
func Convert_platform_ClusterStatus_To_v1_ClusterStatus(in *platform.ClusterStatus, out *ClusterStatus, s conversion.Scope) error {
	return autoConvert_platform_ClusterStatus_To_v1_ClusterStatus(in, out, s)
}

func autoConvert_v1_ComponentStatus_To_platform_ComponentStatus(in *ComponentStatus, out *platform.ComponentStatus, s conversion.Scope) error {
	out.Link = in.Link
	out.Name = in.Name
	out.Kind = in.Kind
	out.Group = in.Group
	out.Status = in.Status
	return nil
}

// Convert_v1_ComponentStatus_To_platform_ComponentStatus is an autogenerated conversion function.
func Convert_v1_ComponentStatus_To_platform_ComponentStatus(in *ComponentStatus, out *platform.ComponentStatus, s conversion.Scope) error {
	return autoConvert_v1_ComponentStatus_To_platform_ComponentStatus(in, out, s)
}

func autoConvert_platform_ComponentStatus_To_v1_ComponentStatus(in *platform.ComponentStatus, out *ComponentStatus, s conversion.Scope) error {
	out.Link = in.Link
	out.Name = in.Name
	out.Kind = in.Kind
	out.Group = in.Group
	out.Status = in.Status
	return nil
}

// Convert_platform_ComponentStatus_To_v1_ComponentStatus is an autogenerated conversion function.
func Convert_platform_ComponentStatus_To_v1_ComponentStatus(in *platform.ComponentStatus, out *ComponentStatus, s conversion.Scope) error {
	return autoConvert_platform_ComponentStatus_To_v1_ComponentStatus(in, out, s)
}

func autoConvert_v1_ConfigMap_To_platform_ConfigMap(in *ConfigMap, out *platform.ConfigMap, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Data = *(*map[string]string)(unsafe.Pointer(&in.Data))
	out.BinaryData = *(*map[string][]byte)(unsafe.Pointer(&in.BinaryData))
	return nil
}

// Convert_v1_ConfigMap_To_platform_ConfigMap is an autogenerated conversion function.
func Convert_v1_ConfigMap_To_platform_ConfigMap(in *ConfigMap, out *platform.ConfigMap, s conversion.Scope) error {
	return autoConvert_v1_ConfigMap_To_platform_ConfigMap(in, out, s)
}

func autoConvert_platform_ConfigMap_To_v1_ConfigMap(in *platform.ConfigMap, out *ConfigMap, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Data = *(*map[string]string)(unsafe.Pointer(&in.Data))
	out.BinaryData = *(*map[string][]byte)(unsafe.Pointer(&in.BinaryData))
	return nil
}

// Convert_platform_ConfigMap_To_v1_ConfigMap is an autogenerated conversion function.
func Convert_platform_ConfigMap_To_v1_ConfigMap(in *platform.ConfigMap, out *ConfigMap, s conversion.Scope) error {
	return autoConvert_platform_ConfigMap_To_v1_ConfigMap(in, out, s)
}

func autoConvert_v1_ConfigMapList_To_platform_ConfigMapList(in *ConfigMapList, out *platform.ConfigMapList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.ConfigMap)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_ConfigMapList_To_platform_ConfigMapList is an autogenerated conversion function.
func Convert_v1_ConfigMapList_To_platform_ConfigMapList(in *ConfigMapList, out *platform.ConfigMapList, s conversion.Scope) error {
	return autoConvert_v1_ConfigMapList_To_platform_ConfigMapList(in, out, s)
}

func autoConvert_platform_ConfigMapList_To_v1_ConfigMapList(in *platform.ConfigMapList, out *ConfigMapList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]ConfigMap)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_ConfigMapList_To_v1_ConfigMapList is an autogenerated conversion function.
func Convert_platform_ConfigMapList_To_v1_ConfigMapList(in *platform.ConfigMapList, out *ConfigMapList, s conversion.Scope) error {
	return autoConvert_platform_ConfigMapList_To_v1_ConfigMapList(in, out, s)
}

func autoConvert_v1_DNSAutoscaler_To_platform_DNSAutoscaler(in *DNSAutoscaler, out *platform.DNSAutoscaler, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_DNSAutoscalerSpec_To_platform_DNSAutoscalerSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_DNSAutoscalerStatus_To_platform_DNSAutoscalerStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_DNSAutoscaler_To_platform_DNSAutoscaler is an autogenerated conversion function.
func Convert_v1_DNSAutoscaler_To_platform_DNSAutoscaler(in *DNSAutoscaler, out *platform.DNSAutoscaler, s conversion.Scope) error {
	return autoConvert_v1_DNSAutoscaler_To_platform_DNSAutoscaler(in, out, s)
}

func autoConvert_platform_DNSAutoscaler_To_v1_DNSAutoscaler(in *platform.DNSAutoscaler, out *DNSAutoscaler, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_DNSAutoscalerSpec_To_v1_DNSAutoscalerSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_DNSAutoscalerStatus_To_v1_DNSAutoscalerStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_DNSAutoscaler_To_v1_DNSAutoscaler is an autogenerated conversion function.
func Convert_platform_DNSAutoscaler_To_v1_DNSAutoscaler(in *platform.DNSAutoscaler, out *DNSAutoscaler, s conversion.Scope) error {
	return autoConvert_platform_DNSAutoscaler_To_v1_DNSAutoscaler(in, out, s)
}

func autoConvert_v1_DNSAutoscalerList_To_platform_DNSAutoscalerList(in *DNSAutoscalerList, out *platform.DNSAutoscalerList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.DNSAutoscaler)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_DNSAutoscalerList_To_platform_DNSAutoscalerList is an autogenerated conversion function.
func Convert_v1_DNSAutoscalerList_To_platform_DNSAutoscalerList(in *DNSAutoscalerList, out *platform.DNSAutoscalerList, s conversion.Scope) error {
	return autoConvert_v1_DNSAutoscalerList_To_platform_DNSAutoscalerList(in, out, s)
}

func autoConvert_platform_DNSAutoscalerList_To_v1_DNSAutoscalerList(in *platform.DNSAutoscalerList, out *DNSAutoscalerList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]DNSAutoscaler)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_DNSAutoscalerList_To_v1_DNSAutoscalerList is an autogenerated conversion function.
func Convert_platform_DNSAutoscalerList_To_v1_DNSAutoscalerList(in *platform.DNSAutoscalerList, out *DNSAutoscalerList, s conversion.Scope) error {
	return autoConvert_platform_DNSAutoscalerList_To_v1_DNSAutoscalerList(in, out, s)
}

func autoConvert_v1_DNSAutoscalerSpec_To_platform_DNSAutoscalerSpec(in *DNSAutoscalerSpec, out *platform.DNSAutoscalerSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	out.Target = in.Target
	return nil
}

// Convert_v1_DNSAutoscalerSpec_To_platform_DNSAutoscalerSpec is an autogenerated conversion function.
func Convert_v1_DNSAutoscalerSpec_To_platform_DNSAutoscalerSpec(in *DNSAutoscalerSpec, out *platform.DNSAutoscalerSpec, s conversion.Scope) error {
	return autoConvert_v1_DNSAutoscalerSpec_To_platform_DNSAutoscalerSpec(in, out, s)
}

func autoConvert_platform_DNSAutoscalerSpec_To_v1_DNSAutoscalerSpec(in *platform.DNSAutoscalerSpec, out *DNSAutoscalerSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	out.Target = in.Target
	return nil
}

// Convert_platform_DNSAutoscalerSpec_To_v1_DNSAutoscalerSpec is an autogenerated conversion function.
func Convert_platform_DNSAutoscalerSpec_To_v1_DNSAutoscalerSpec(in *platform.DNSAutoscalerSpec, out *DNSAutoscalerSpec, s conversion.Scope) error {
	return autoConvert_platform_DNSAutoscalerSpec_To_v1_DNSAutoscalerSpec(in, out, s)
}

func autoConvert_v1_DNSAutoscalerStatus_To_platform_DNSAutoscalerStatus(in *DNSAutoscalerStatus, out *platform.DNSAutoscalerStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	out.Components = *(*[]platform.ComponentStatus)(unsafe.Pointer(&in.Components))
	return nil
}

// Convert_v1_DNSAutoscalerStatus_To_platform_DNSAutoscalerStatus is an autogenerated conversion function.
func Convert_v1_DNSAutoscalerStatus_To_platform_DNSAutoscalerStatus(in *DNSAutoscalerStatus, out *platform.DNSAutoscalerStatus, s conversion.Scope) error {
	return autoConvert_v1_DNSAutoscalerStatus_To_platform_DNSAutoscalerStatus(in, out, s)
}

func autoConvert_platform_DNSAutoscalerStatus_To_v1_DNSAutoscalerStatus(in *platform.DNSAutoscalerStatus, out *DNSAutoscalerStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	out.Components = *(*[]ComponentStatus)(unsafe.Pointer(&in.Components))
	return nil
}

// Convert_platform_DNSAutoscalerStatus_To_v1_DNSAutoscalerStatus is an autogenerated conversion function.
func Convert_platform_DNSAutoscalerStatus_To_v1_DNSAutoscalerStatus(in *platform.DNSAutoscalerStatus, out *DNSAutoscalerStatus, s conversion.Scope) error {
	return autoConvert_platform_DNSAutoscalerStatus_To_v1_DNSAutoscalerStatus(in, out, s)
}

func autoConvert_v1_DeScheduler_To_platform_DeScheduler(in *DeScheduler, out *platform.DeScheduler, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_DeSchedulerSpec_To_platform_DeSchedulerSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_DeSchedulerStatus_To_platform_DeSchedulerStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_DeScheduler_To_platform_DeScheduler is an autogenerated conversion function.
func Convert_v1_DeScheduler_To_platform_DeScheduler(in *DeScheduler, out *platform.DeScheduler, s conversion.Scope) error {
	return autoConvert_v1_DeScheduler_To_platform_DeScheduler(in, out, s)
}

func autoConvert_platform_DeScheduler_To_v1_DeScheduler(in *platform.DeScheduler, out *DeScheduler, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_DeSchedulerSpec_To_v1_DeSchedulerSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_DeSchedulerStatus_To_v1_DeSchedulerStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_DeScheduler_To_v1_DeScheduler is an autogenerated conversion function.
func Convert_platform_DeScheduler_To_v1_DeScheduler(in *platform.DeScheduler, out *DeScheduler, s conversion.Scope) error {
	return autoConvert_platform_DeScheduler_To_v1_DeScheduler(in, out, s)
}

func autoConvert_v1_DeSchedulerList_To_platform_DeSchedulerList(in *DeSchedulerList, out *platform.DeSchedulerList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.DeScheduler)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_DeSchedulerList_To_platform_DeSchedulerList is an autogenerated conversion function.
func Convert_v1_DeSchedulerList_To_platform_DeSchedulerList(in *DeSchedulerList, out *platform.DeSchedulerList, s conversion.Scope) error {
	return autoConvert_v1_DeSchedulerList_To_platform_DeSchedulerList(in, out, s)
}

func autoConvert_platform_DeSchedulerList_To_v1_DeSchedulerList(in *platform.DeSchedulerList, out *DeSchedulerList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]DeScheduler)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_DeSchedulerList_To_v1_DeSchedulerList is an autogenerated conversion function.
func Convert_platform_DeSchedulerList_To_v1_DeSchedulerList(in *platform.DeSchedulerList, out *DeSchedulerList, s conversion.Scope) error {
	return autoConvert_platform_DeSchedulerList_To_v1_DeSchedulerList(in, out, s)
}

func autoConvert_v1_DeSchedulerSpec_To_platform_DeSchedulerSpec(in *DeSchedulerSpec, out *platform.DeSchedulerSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	out.PrometheusBackEnd = in.PrometheusBackEnd
	if err := Convert_v1_LoadThreshold_To_platform_LoadThreshold(&in.LoadThreshold, &out.LoadThreshold, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_DeSchedulerSpec_To_platform_DeSchedulerSpec is an autogenerated conversion function.
func Convert_v1_DeSchedulerSpec_To_platform_DeSchedulerSpec(in *DeSchedulerSpec, out *platform.DeSchedulerSpec, s conversion.Scope) error {
	return autoConvert_v1_DeSchedulerSpec_To_platform_DeSchedulerSpec(in, out, s)
}

func autoConvert_platform_DeSchedulerSpec_To_v1_DeSchedulerSpec(in *platform.DeSchedulerSpec, out *DeSchedulerSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	out.PrometheusBackEnd = in.PrometheusBackEnd
	if err := Convert_platform_LoadThreshold_To_v1_LoadThreshold(&in.LoadThreshold, &out.LoadThreshold, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_DeSchedulerSpec_To_v1_DeSchedulerSpec is an autogenerated conversion function.
func Convert_platform_DeSchedulerSpec_To_v1_DeSchedulerSpec(in *platform.DeSchedulerSpec, out *DeSchedulerSpec, s conversion.Scope) error {
	return autoConvert_platform_DeSchedulerSpec_To_v1_DeSchedulerSpec(in, out, s)
}

func autoConvert_v1_DeSchedulerStatus_To_platform_DeSchedulerStatus(in *DeSchedulerStatus, out *platform.DeSchedulerStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	return nil
}

// Convert_v1_DeSchedulerStatus_To_platform_DeSchedulerStatus is an autogenerated conversion function.
func Convert_v1_DeSchedulerStatus_To_platform_DeSchedulerStatus(in *DeSchedulerStatus, out *platform.DeSchedulerStatus, s conversion.Scope) error {
	return autoConvert_v1_DeSchedulerStatus_To_platform_DeSchedulerStatus(in, out, s)
}

func autoConvert_platform_DeSchedulerStatus_To_v1_DeSchedulerStatus(in *platform.DeSchedulerStatus, out *DeSchedulerStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	return nil
}

// Convert_platform_DeSchedulerStatus_To_v1_DeSchedulerStatus is an autogenerated conversion function.
func Convert_platform_DeSchedulerStatus_To_v1_DeSchedulerStatus(in *platform.DeSchedulerStatus, out *DeSchedulerStatus, s conversion.Scope) error {
	return autoConvert_platform_DeSchedulerStatus_To_v1_DeSchedulerStatus(in, out, s)
}

func autoConvert_v1_DynamicScheduler_To_platform_DynamicScheduler(in *DynamicScheduler, out *platform.DynamicScheduler, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_DynamicSchedulerSpec_To_platform_DynamicSchedulerSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_DynamicSchedulerStatus_To_platform_DynamicSchedulerStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_DynamicScheduler_To_platform_DynamicScheduler is an autogenerated conversion function.
func Convert_v1_DynamicScheduler_To_platform_DynamicScheduler(in *DynamicScheduler, out *platform.DynamicScheduler, s conversion.Scope) error {
	return autoConvert_v1_DynamicScheduler_To_platform_DynamicScheduler(in, out, s)
}

func autoConvert_platform_DynamicScheduler_To_v1_DynamicScheduler(in *platform.DynamicScheduler, out *DynamicScheduler, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_DynamicSchedulerSpec_To_v1_DynamicSchedulerSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_DynamicSchedulerStatus_To_v1_DynamicSchedulerStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_DynamicScheduler_To_v1_DynamicScheduler is an autogenerated conversion function.
func Convert_platform_DynamicScheduler_To_v1_DynamicScheduler(in *platform.DynamicScheduler, out *DynamicScheduler, s conversion.Scope) error {
	return autoConvert_platform_DynamicScheduler_To_v1_DynamicScheduler(in, out, s)
}

func autoConvert_v1_DynamicSchedulerList_To_platform_DynamicSchedulerList(in *DynamicSchedulerList, out *platform.DynamicSchedulerList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.DynamicScheduler)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_DynamicSchedulerList_To_platform_DynamicSchedulerList is an autogenerated conversion function.
func Convert_v1_DynamicSchedulerList_To_platform_DynamicSchedulerList(in *DynamicSchedulerList, out *platform.DynamicSchedulerList, s conversion.Scope) error {
	return autoConvert_v1_DynamicSchedulerList_To_platform_DynamicSchedulerList(in, out, s)
}

func autoConvert_platform_DynamicSchedulerList_To_v1_DynamicSchedulerList(in *platform.DynamicSchedulerList, out *DynamicSchedulerList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]DynamicScheduler)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_DynamicSchedulerList_To_v1_DynamicSchedulerList is an autogenerated conversion function.
func Convert_platform_DynamicSchedulerList_To_v1_DynamicSchedulerList(in *platform.DynamicSchedulerList, out *DynamicSchedulerList, s conversion.Scope) error {
	return autoConvert_platform_DynamicSchedulerList_To_v1_DynamicSchedulerList(in, out, s)
}

func autoConvert_v1_DynamicSchedulerSpec_To_platform_DynamicSchedulerSpec(in *DynamicSchedulerSpec, out *platform.DynamicSchedulerSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	out.PrometheusBackEnd = in.PrometheusBackEnd
	if err := Convert_v1_FilterThreshold_To_platform_FilterThreshold(&in.FilterThreshold, &out.FilterThreshold, s); err != nil {
		return err
	}
	if err := Convert_v1_ScoreThreshold_To_platform_ScoreThreshold(&in.ScoreThreshold, &out.ScoreThreshold, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_DynamicSchedulerSpec_To_platform_DynamicSchedulerSpec is an autogenerated conversion function.
func Convert_v1_DynamicSchedulerSpec_To_platform_DynamicSchedulerSpec(in *DynamicSchedulerSpec, out *platform.DynamicSchedulerSpec, s conversion.Scope) error {
	return autoConvert_v1_DynamicSchedulerSpec_To_platform_DynamicSchedulerSpec(in, out, s)
}

func autoConvert_platform_DynamicSchedulerSpec_To_v1_DynamicSchedulerSpec(in *platform.DynamicSchedulerSpec, out *DynamicSchedulerSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	out.PrometheusBackEnd = in.PrometheusBackEnd
	if err := Convert_platform_FilterThreshold_To_v1_FilterThreshold(&in.FilterThreshold, &out.FilterThreshold, s); err != nil {
		return err
	}
	if err := Convert_platform_ScoreThreshold_To_v1_ScoreThreshold(&in.ScoreThreshold, &out.ScoreThreshold, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_DynamicSchedulerSpec_To_v1_DynamicSchedulerSpec is an autogenerated conversion function.
func Convert_platform_DynamicSchedulerSpec_To_v1_DynamicSchedulerSpec(in *platform.DynamicSchedulerSpec, out *DynamicSchedulerSpec, s conversion.Scope) error {
	return autoConvert_platform_DynamicSchedulerSpec_To_v1_DynamicSchedulerSpec(in, out, s)
}

func autoConvert_v1_DynamicSchedulerStatus_To_platform_DynamicSchedulerStatus(in *DynamicSchedulerStatus, out *platform.DynamicSchedulerStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	return nil
}

// Convert_v1_DynamicSchedulerStatus_To_platform_DynamicSchedulerStatus is an autogenerated conversion function.
func Convert_v1_DynamicSchedulerStatus_To_platform_DynamicSchedulerStatus(in *DynamicSchedulerStatus, out *platform.DynamicSchedulerStatus, s conversion.Scope) error {
	return autoConvert_v1_DynamicSchedulerStatus_To_platform_DynamicSchedulerStatus(in, out, s)
}

func autoConvert_platform_DynamicSchedulerStatus_To_v1_DynamicSchedulerStatus(in *platform.DynamicSchedulerStatus, out *DynamicSchedulerStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	return nil
}

// Convert_platform_DynamicSchedulerStatus_To_v1_DynamicSchedulerStatus is an autogenerated conversion function.
func Convert_platform_DynamicSchedulerStatus_To_v1_DynamicSchedulerStatus(in *platform.DynamicSchedulerStatus, out *DynamicSchedulerStatus, s conversion.Scope) error {
	return autoConvert_platform_DynamicSchedulerStatus_To_v1_DynamicSchedulerStatus(in, out, s)
}

func autoConvert_v1_EniIpamd_To_platform_EniIpamd(in *EniIpamd, out *platform.EniIpamd, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_EniIpamdSpec_To_platform_EniIpamdSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_EniIpamdStatus_To_platform_EniIpamdStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_EniIpamd_To_platform_EniIpamd is an autogenerated conversion function.
func Convert_v1_EniIpamd_To_platform_EniIpamd(in *EniIpamd, out *platform.EniIpamd, s conversion.Scope) error {
	return autoConvert_v1_EniIpamd_To_platform_EniIpamd(in, out, s)
}

func autoConvert_platform_EniIpamd_To_v1_EniIpamd(in *platform.EniIpamd, out *EniIpamd, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_EniIpamdSpec_To_v1_EniIpamdSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_EniIpamdStatus_To_v1_EniIpamdStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_EniIpamd_To_v1_EniIpamd is an autogenerated conversion function.
func Convert_platform_EniIpamd_To_v1_EniIpamd(in *platform.EniIpamd, out *EniIpamd, s conversion.Scope) error {
	return autoConvert_platform_EniIpamd_To_v1_EniIpamd(in, out, s)
}

func autoConvert_v1_EniIpamdCondition_To_platform_EniIpamdCondition(in *EniIpamdCondition, out *platform.EniIpamdCondition, s conversion.Scope) error {
	out.Type = in.Type
	out.Status = platform.ConditionStatus(in.Status)
	out.LastProbeTime = in.LastProbeTime
	out.LastTransitionTime = in.LastTransitionTime
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_v1_EniIpamdCondition_To_platform_EniIpamdCondition is an autogenerated conversion function.
func Convert_v1_EniIpamdCondition_To_platform_EniIpamdCondition(in *EniIpamdCondition, out *platform.EniIpamdCondition, s conversion.Scope) error {
	return autoConvert_v1_EniIpamdCondition_To_platform_EniIpamdCondition(in, out, s)
}

func autoConvert_platform_EniIpamdCondition_To_v1_EniIpamdCondition(in *platform.EniIpamdCondition, out *EniIpamdCondition, s conversion.Scope) error {
	out.Type = in.Type
	out.Status = ConditionStatus(in.Status)
	out.LastProbeTime = in.LastProbeTime
	out.LastTransitionTime = in.LastTransitionTime
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_platform_EniIpamdCondition_To_v1_EniIpamdCondition is an autogenerated conversion function.
func Convert_platform_EniIpamdCondition_To_v1_EniIpamdCondition(in *platform.EniIpamdCondition, out *EniIpamdCondition, s conversion.Scope) error {
	return autoConvert_platform_EniIpamdCondition_To_v1_EniIpamdCondition(in, out, s)
}

func autoConvert_v1_EniIpamdList_To_platform_EniIpamdList(in *EniIpamdList, out *platform.EniIpamdList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.EniIpamd)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_EniIpamdList_To_platform_EniIpamdList is an autogenerated conversion function.
func Convert_v1_EniIpamdList_To_platform_EniIpamdList(in *EniIpamdList, out *platform.EniIpamdList, s conversion.Scope) error {
	return autoConvert_v1_EniIpamdList_To_platform_EniIpamdList(in, out, s)
}

func autoConvert_platform_EniIpamdList_To_v1_EniIpamdList(in *platform.EniIpamdList, out *EniIpamdList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]EniIpamd)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_EniIpamdList_To_v1_EniIpamdList is an autogenerated conversion function.
func Convert_platform_EniIpamdList_To_v1_EniIpamdList(in *platform.EniIpamdList, out *EniIpamdList, s conversion.Scope) error {
	return autoConvert_platform_EniIpamdList_To_v1_EniIpamdList(in, out, s)
}

func autoConvert_v1_EniIpamdSpec_To_platform_EniIpamdSpec(in *EniIpamdSpec, out *platform.EniIpamdSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.VpcID = in.VpcID
	out.SubnetID = in.SubnetID
	out.SubnetCidr = in.SubnetCidr
	out.Zone = in.Zone
	out.VirtualScale = in.VirtualScale
	out.Version = in.Version
	out.ClaimExpiredSeconds = in.ClaimExpiredSeconds
	return nil
}

// Convert_v1_EniIpamdSpec_To_platform_EniIpamdSpec is an autogenerated conversion function.
func Convert_v1_EniIpamdSpec_To_platform_EniIpamdSpec(in *EniIpamdSpec, out *platform.EniIpamdSpec, s conversion.Scope) error {
	return autoConvert_v1_EniIpamdSpec_To_platform_EniIpamdSpec(in, out, s)
}

func autoConvert_platform_EniIpamdSpec_To_v1_EniIpamdSpec(in *platform.EniIpamdSpec, out *EniIpamdSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.VpcID = in.VpcID
	out.SubnetID = in.SubnetID
	out.SubnetCidr = in.SubnetCidr
	out.Zone = in.Zone
	out.VirtualScale = in.VirtualScale
	out.Version = in.Version
	out.ClaimExpiredSeconds = in.ClaimExpiredSeconds
	return nil
}

// Convert_platform_EniIpamdSpec_To_v1_EniIpamdSpec is an autogenerated conversion function.
func Convert_platform_EniIpamdSpec_To_v1_EniIpamdSpec(in *platform.EniIpamdSpec, out *EniIpamdSpec, s conversion.Scope) error {
	return autoConvert_platform_EniIpamdSpec_To_v1_EniIpamdSpec(in, out, s)
}

func autoConvert_v1_EniIpamdStatus_To_platform_EniIpamdStatus(in *EniIpamdStatus, out *platform.EniIpamdStatus, s conversion.Scope) error {
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	out.Conditions = *(*[]platform.EniIpamdCondition)(unsafe.Pointer(&in.Conditions))
	out.Version = in.Version
	return nil
}

// Convert_v1_EniIpamdStatus_To_platform_EniIpamdStatus is an autogenerated conversion function.
func Convert_v1_EniIpamdStatus_To_platform_EniIpamdStatus(in *EniIpamdStatus, out *platform.EniIpamdStatus, s conversion.Scope) error {
	return autoConvert_v1_EniIpamdStatus_To_platform_EniIpamdStatus(in, out, s)
}

func autoConvert_platform_EniIpamdStatus_To_v1_EniIpamdStatus(in *platform.EniIpamdStatus, out *EniIpamdStatus, s conversion.Scope) error {
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	out.Conditions = *(*[]EniIpamdCondition)(unsafe.Pointer(&in.Conditions))
	out.Version = in.Version
	return nil
}

// Convert_platform_EniIpamdStatus_To_v1_EniIpamdStatus is an autogenerated conversion function.
func Convert_platform_EniIpamdStatus_To_v1_EniIpamdStatus(in *platform.EniIpamdStatus, out *EniIpamdStatus, s conversion.Scope) error {
	return autoConvert_platform_EniIpamdStatus_To_v1_EniIpamdStatus(in, out, s)
}

func autoConvert_v1_Environment_To_platform_Environment(in *Environment, out *platform.Environment, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_EnvironmentSpec_To_platform_EnvironmentSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_EnvironmentStatus_To_platform_EnvironmentStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_Environment_To_platform_Environment is an autogenerated conversion function.
func Convert_v1_Environment_To_platform_Environment(in *Environment, out *platform.Environment, s conversion.Scope) error {
	return autoConvert_v1_Environment_To_platform_Environment(in, out, s)
}

func autoConvert_platform_Environment_To_v1_Environment(in *platform.Environment, out *Environment, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_EnvironmentSpec_To_v1_EnvironmentSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_EnvironmentStatus_To_v1_EnvironmentStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_Environment_To_v1_Environment is an autogenerated conversion function.
func Convert_platform_Environment_To_v1_Environment(in *platform.Environment, out *Environment, s conversion.Scope) error {
	return autoConvert_platform_Environment_To_v1_Environment(in, out, s)
}

func autoConvert_v1_EnvironmentList_To_platform_EnvironmentList(in *EnvironmentList, out *platform.EnvironmentList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.Environment)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_EnvironmentList_To_platform_EnvironmentList is an autogenerated conversion function.
func Convert_v1_EnvironmentList_To_platform_EnvironmentList(in *EnvironmentList, out *platform.EnvironmentList, s conversion.Scope) error {
	return autoConvert_v1_EnvironmentList_To_platform_EnvironmentList(in, out, s)
}

func autoConvert_platform_EnvironmentList_To_v1_EnvironmentList(in *platform.EnvironmentList, out *EnvironmentList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]Environment)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_EnvironmentList_To_v1_EnvironmentList is an autogenerated conversion function.
func Convert_platform_EnvironmentList_To_v1_EnvironmentList(in *platform.EnvironmentList, out *EnvironmentList, s conversion.Scope) error {
	return autoConvert_platform_EnvironmentList_To_v1_EnvironmentList(in, out, s)
}

func autoConvert_v1_EnvironmentSpec_To_platform_EnvironmentSpec(in *EnvironmentSpec, out *platform.EnvironmentSpec, s conversion.Scope) error {
	out.EnvType = platform.EnvironmentType(in.EnvType)
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.UIN = in.UIN
	out.SubAccountUin = in.SubAccountUin
	out.Apps = *(*[]platform.AppSpec)(unsafe.Pointer(&in.Apps))
	return nil
}

// Convert_v1_EnvironmentSpec_To_platform_EnvironmentSpec is an autogenerated conversion function.
func Convert_v1_EnvironmentSpec_To_platform_EnvironmentSpec(in *EnvironmentSpec, out *platform.EnvironmentSpec, s conversion.Scope) error {
	return autoConvert_v1_EnvironmentSpec_To_platform_EnvironmentSpec(in, out, s)
}

func autoConvert_platform_EnvironmentSpec_To_v1_EnvironmentSpec(in *platform.EnvironmentSpec, out *EnvironmentSpec, s conversion.Scope) error {
	out.EnvType = EnvironmentType(in.EnvType)
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.UIN = in.UIN
	out.SubAccountUin = in.SubAccountUin
	out.Apps = *(*[]AppSpec)(unsafe.Pointer(&in.Apps))
	return nil
}

// Convert_platform_EnvironmentSpec_To_v1_EnvironmentSpec is an autogenerated conversion function.
func Convert_platform_EnvironmentSpec_To_v1_EnvironmentSpec(in *platform.EnvironmentSpec, out *EnvironmentSpec, s conversion.Scope) error {
	return autoConvert_platform_EnvironmentSpec_To_v1_EnvironmentSpec(in, out, s)
}

func autoConvert_v1_EnvironmentStatus_To_platform_EnvironmentStatus(in *EnvironmentStatus, out *platform.EnvironmentStatus, s conversion.Scope) error {
	out.Supports = *(*map[string]metav1.GroupVersionKind)(unsafe.Pointer(&in.Supports))
	return nil
}

// Convert_v1_EnvironmentStatus_To_platform_EnvironmentStatus is an autogenerated conversion function.
func Convert_v1_EnvironmentStatus_To_platform_EnvironmentStatus(in *EnvironmentStatus, out *platform.EnvironmentStatus, s conversion.Scope) error {
	return autoConvert_v1_EnvironmentStatus_To_platform_EnvironmentStatus(in, out, s)
}

func autoConvert_platform_EnvironmentStatus_To_v1_EnvironmentStatus(in *platform.EnvironmentStatus, out *EnvironmentStatus, s conversion.Scope) error {
	out.Supports = *(*map[string]metav1.GroupVersionKind)(unsafe.Pointer(&in.Supports))
	return nil
}

// Convert_platform_EnvironmentStatus_To_v1_EnvironmentStatus is an autogenerated conversion function.
func Convert_platform_EnvironmentStatus_To_v1_EnvironmentStatus(in *platform.EnvironmentStatus, out *EnvironmentStatus, s conversion.Scope) error {
	return autoConvert_platform_EnvironmentStatus_To_v1_EnvironmentStatus(in, out, s)
}

func autoConvert_v1_FilterThreshold_To_platform_FilterThreshold(in *FilterThreshold, out *platform.FilterThreshold, s conversion.Scope) error {
	out.CpuAvgUsage5M = in.CpuAvgUsage5M
	out.CpuMaxUsage1H = in.CpuMaxUsage1H
	out.MemAvgUsage5M = in.MemAvgUsage5M
	out.MemMaxUsage1H = in.MemMaxUsage1H
	return nil
}

// Convert_v1_FilterThreshold_To_platform_FilterThreshold is an autogenerated conversion function.
func Convert_v1_FilterThreshold_To_platform_FilterThreshold(in *FilterThreshold, out *platform.FilterThreshold, s conversion.Scope) error {
	return autoConvert_v1_FilterThreshold_To_platform_FilterThreshold(in, out, s)
}

func autoConvert_platform_FilterThreshold_To_v1_FilterThreshold(in *platform.FilterThreshold, out *FilterThreshold, s conversion.Scope) error {
	out.CpuAvgUsage5M = in.CpuAvgUsage5M
	out.CpuMaxUsage1H = in.CpuMaxUsage1H
	out.MemAvgUsage5M = in.MemAvgUsage5M
	out.MemMaxUsage1H = in.MemMaxUsage1H
	return nil
}

// Convert_platform_FilterThreshold_To_v1_FilterThreshold is an autogenerated conversion function.
func Convert_platform_FilterThreshold_To_v1_FilterThreshold(in *platform.FilterThreshold, out *FilterThreshold, s conversion.Scope) error {
	return autoConvert_platform_FilterThreshold_To_v1_FilterThreshold(in, out, s)
}

func autoConvert_v1_GPUManager_To_platform_GPUManager(in *GPUManager, out *platform.GPUManager, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_GPUManagerSpec_To_platform_GPUManagerSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_GPUManagerStatus_To_platform_GPUManagerStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_GPUManager_To_platform_GPUManager is an autogenerated conversion function.
func Convert_v1_GPUManager_To_platform_GPUManager(in *GPUManager, out *platform.GPUManager, s conversion.Scope) error {
	return autoConvert_v1_GPUManager_To_platform_GPUManager(in, out, s)
}

func autoConvert_platform_GPUManager_To_v1_GPUManager(in *platform.GPUManager, out *GPUManager, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_GPUManagerSpec_To_v1_GPUManagerSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_GPUManagerStatus_To_v1_GPUManagerStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_GPUManager_To_v1_GPUManager is an autogenerated conversion function.
func Convert_platform_GPUManager_To_v1_GPUManager(in *platform.GPUManager, out *GPUManager, s conversion.Scope) error {
	return autoConvert_platform_GPUManager_To_v1_GPUManager(in, out, s)
}

func autoConvert_v1_GPUManagerList_To_platform_GPUManagerList(in *GPUManagerList, out *platform.GPUManagerList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.GPUManager)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_GPUManagerList_To_platform_GPUManagerList is an autogenerated conversion function.
func Convert_v1_GPUManagerList_To_platform_GPUManagerList(in *GPUManagerList, out *platform.GPUManagerList, s conversion.Scope) error {
	return autoConvert_v1_GPUManagerList_To_platform_GPUManagerList(in, out, s)
}

func autoConvert_platform_GPUManagerList_To_v1_GPUManagerList(in *platform.GPUManagerList, out *GPUManagerList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]GPUManager)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_GPUManagerList_To_v1_GPUManagerList is an autogenerated conversion function.
func Convert_platform_GPUManagerList_To_v1_GPUManagerList(in *platform.GPUManagerList, out *GPUManagerList, s conversion.Scope) error {
	return autoConvert_platform_GPUManagerList_To_v1_GPUManagerList(in, out, s)
}

func autoConvert_v1_GPUManagerSpec_To_platform_GPUManagerSpec(in *GPUManagerSpec, out *platform.GPUManagerSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	return nil
}

// Convert_v1_GPUManagerSpec_To_platform_GPUManagerSpec is an autogenerated conversion function.
func Convert_v1_GPUManagerSpec_To_platform_GPUManagerSpec(in *GPUManagerSpec, out *platform.GPUManagerSpec, s conversion.Scope) error {
	return autoConvert_v1_GPUManagerSpec_To_platform_GPUManagerSpec(in, out, s)
}

func autoConvert_platform_GPUManagerSpec_To_v1_GPUManagerSpec(in *platform.GPUManagerSpec, out *GPUManagerSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	return nil
}

// Convert_platform_GPUManagerSpec_To_v1_GPUManagerSpec is an autogenerated conversion function.
func Convert_platform_GPUManagerSpec_To_v1_GPUManagerSpec(in *platform.GPUManagerSpec, out *GPUManagerSpec, s conversion.Scope) error {
	return autoConvert_platform_GPUManagerSpec_To_v1_GPUManagerSpec(in, out, s)
}

func autoConvert_v1_GPUManagerStatus_To_platform_GPUManagerStatus(in *GPUManagerStatus, out *platform.GPUManagerStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	return nil
}

// Convert_v1_GPUManagerStatus_To_platform_GPUManagerStatus is an autogenerated conversion function.
func Convert_v1_GPUManagerStatus_To_platform_GPUManagerStatus(in *GPUManagerStatus, out *platform.GPUManagerStatus, s conversion.Scope) error {
	return autoConvert_v1_GPUManagerStatus_To_platform_GPUManagerStatus(in, out, s)
}

func autoConvert_platform_GPUManagerStatus_To_v1_GPUManagerStatus(in *platform.GPUManagerStatus, out *GPUManagerStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	return nil
}

// Convert_platform_GPUManagerStatus_To_v1_GPUManagerStatus is an autogenerated conversion function.
func Convert_platform_GPUManagerStatus_To_v1_GPUManagerStatus(in *platform.GPUManagerStatus, out *GPUManagerStatus, s conversion.Scope) error {
	return autoConvert_platform_GPUManagerStatus_To_v1_GPUManagerStatus(in, out, s)
}

func autoConvert_v1_GameApp_To_platform_GameApp(in *GameApp, out *platform.GameApp, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_GameAppSpec_To_platform_GameAppSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_GameAppStatus_To_platform_GameAppStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_GameApp_To_platform_GameApp is an autogenerated conversion function.
func Convert_v1_GameApp_To_platform_GameApp(in *GameApp, out *platform.GameApp, s conversion.Scope) error {
	return autoConvert_v1_GameApp_To_platform_GameApp(in, out, s)
}

func autoConvert_platform_GameApp_To_v1_GameApp(in *platform.GameApp, out *GameApp, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_GameAppSpec_To_v1_GameAppSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_GameAppStatus_To_v1_GameAppStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_GameApp_To_v1_GameApp is an autogenerated conversion function.
func Convert_platform_GameApp_To_v1_GameApp(in *platform.GameApp, out *GameApp, s conversion.Scope) error {
	return autoConvert_platform_GameApp_To_v1_GameApp(in, out, s)
}

func autoConvert_v1_GameAppList_To_platform_GameAppList(in *GameAppList, out *platform.GameAppList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.GameApp)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_GameAppList_To_platform_GameAppList is an autogenerated conversion function.
func Convert_v1_GameAppList_To_platform_GameAppList(in *GameAppList, out *platform.GameAppList, s conversion.Scope) error {
	return autoConvert_v1_GameAppList_To_platform_GameAppList(in, out, s)
}

func autoConvert_platform_GameAppList_To_v1_GameAppList(in *platform.GameAppList, out *GameAppList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]GameApp)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_GameAppList_To_v1_GameAppList is an autogenerated conversion function.
func Convert_platform_GameAppList_To_v1_GameAppList(in *platform.GameAppList, out *GameAppList, s conversion.Scope) error {
	return autoConvert_platform_GameAppList_To_v1_GameAppList(in, out, s)
}

func autoConvert_v1_GameAppProxyOptions_To_platform_GameAppProxyOptions(in *GameAppProxyOptions, out *platform.GameAppProxyOptions, s conversion.Scope) error {
	out.Namespace = in.Namespace
	out.Name = in.Name
	out.Action = in.Action
	return nil
}

// Convert_v1_GameAppProxyOptions_To_platform_GameAppProxyOptions is an autogenerated conversion function.
func Convert_v1_GameAppProxyOptions_To_platform_GameAppProxyOptions(in *GameAppProxyOptions, out *platform.GameAppProxyOptions, s conversion.Scope) error {
	return autoConvert_v1_GameAppProxyOptions_To_platform_GameAppProxyOptions(in, out, s)
}

func autoConvert_platform_GameAppProxyOptions_To_v1_GameAppProxyOptions(in *platform.GameAppProxyOptions, out *GameAppProxyOptions, s conversion.Scope) error {
	out.Namespace = in.Namespace
	out.Name = in.Name
	out.Action = in.Action
	return nil
}

// Convert_platform_GameAppProxyOptions_To_v1_GameAppProxyOptions is an autogenerated conversion function.
func Convert_platform_GameAppProxyOptions_To_v1_GameAppProxyOptions(in *platform.GameAppProxyOptions, out *GameAppProxyOptions, s conversion.Scope) error {
	return autoConvert_platform_GameAppProxyOptions_To_v1_GameAppProxyOptions(in, out, s)
}

func autoConvert_v1_GameAppSpec_To_platform_GameAppSpec(in *GameAppSpec, out *platform.GameAppSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	if err := Convert_v1_GameAppVersion_To_platform_GameAppVersion(&in.VersionDeprecated, &out.VersionDeprecated, s); err != nil {
		return err
	}
	out.Version = in.Version
	return nil
}

// Convert_v1_GameAppSpec_To_platform_GameAppSpec is an autogenerated conversion function.
func Convert_v1_GameAppSpec_To_platform_GameAppSpec(in *GameAppSpec, out *platform.GameAppSpec, s conversion.Scope) error {
	return autoConvert_v1_GameAppSpec_To_platform_GameAppSpec(in, out, s)
}

func autoConvert_platform_GameAppSpec_To_v1_GameAppSpec(in *platform.GameAppSpec, out *GameAppSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	if err := Convert_platform_GameAppVersion_To_v1_GameAppVersion(&in.VersionDeprecated, &out.VersionDeprecated, s); err != nil {
		return err
	}
	out.Version = in.Version
	return nil
}

// Convert_platform_GameAppSpec_To_v1_GameAppSpec is an autogenerated conversion function.
func Convert_platform_GameAppSpec_To_v1_GameAppSpec(in *platform.GameAppSpec, out *GameAppSpec, s conversion.Scope) error {
	return autoConvert_platform_GameAppSpec_To_v1_GameAppSpec(in, out, s)
}

func autoConvert_v1_GameAppStatus_To_platform_GameAppStatus(in *GameAppStatus, out *platform.GameAppStatus, s conversion.Scope) error {
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	out.Version = in.Version
	return nil
}

// Convert_v1_GameAppStatus_To_platform_GameAppStatus is an autogenerated conversion function.
func Convert_v1_GameAppStatus_To_platform_GameAppStatus(in *GameAppStatus, out *platform.GameAppStatus, s conversion.Scope) error {
	return autoConvert_v1_GameAppStatus_To_platform_GameAppStatus(in, out, s)
}

func autoConvert_platform_GameAppStatus_To_v1_GameAppStatus(in *platform.GameAppStatus, out *GameAppStatus, s conversion.Scope) error {
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	out.Version = in.Version
	return nil
}

// Convert_platform_GameAppStatus_To_v1_GameAppStatus is an autogenerated conversion function.
func Convert_platform_GameAppStatus_To_v1_GameAppStatus(in *platform.GameAppStatus, out *GameAppStatus, s conversion.Scope) error {
	return autoConvert_platform_GameAppStatus_To_v1_GameAppStatus(in, out, s)
}

func autoConvert_v1_GameAppVersion_To_platform_GameAppVersion(in *GameAppVersion, out *platform.GameAppVersion, s conversion.Scope) error {
	out.GameAppOperator = in.GameAppOperator
	return nil
}

// Convert_v1_GameAppVersion_To_platform_GameAppVersion is an autogenerated conversion function.
func Convert_v1_GameAppVersion_To_platform_GameAppVersion(in *GameAppVersion, out *platform.GameAppVersion, s conversion.Scope) error {
	return autoConvert_v1_GameAppVersion_To_platform_GameAppVersion(in, out, s)
}

func autoConvert_platform_GameAppVersion_To_v1_GameAppVersion(in *platform.GameAppVersion, out *GameAppVersion, s conversion.Scope) error {
	out.GameAppOperator = in.GameAppOperator
	return nil
}

// Convert_platform_GameAppVersion_To_v1_GameAppVersion is an autogenerated conversion function.
func Convert_platform_GameAppVersion_To_v1_GameAppVersion(in *platform.GameAppVersion, out *GameAppVersion, s conversion.Scope) error {
	return autoConvert_platform_GameAppVersion_To_v1_GameAppVersion(in, out, s)
}

func autoConvert_v1_HPC_To_platform_HPC(in *HPC, out *platform.HPC, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_HPCSpec_To_platform_HPCSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_HPCStatus_To_platform_HPCStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_HPC_To_platform_HPC is an autogenerated conversion function.
func Convert_v1_HPC_To_platform_HPC(in *HPC, out *platform.HPC, s conversion.Scope) error {
	return autoConvert_v1_HPC_To_platform_HPC(in, out, s)
}

func autoConvert_platform_HPC_To_v1_HPC(in *platform.HPC, out *HPC, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_HPCSpec_To_v1_HPCSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_HPCStatus_To_v1_HPCStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_HPC_To_v1_HPC is an autogenerated conversion function.
func Convert_platform_HPC_To_v1_HPC(in *platform.HPC, out *HPC, s conversion.Scope) error {
	return autoConvert_platform_HPC_To_v1_HPC(in, out, s)
}

func autoConvert_v1_HPCList_To_platform_HPCList(in *HPCList, out *platform.HPCList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.HPC)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_HPCList_To_platform_HPCList is an autogenerated conversion function.
func Convert_v1_HPCList_To_platform_HPCList(in *HPCList, out *platform.HPCList, s conversion.Scope) error {
	return autoConvert_v1_HPCList_To_platform_HPCList(in, out, s)
}

func autoConvert_platform_HPCList_To_v1_HPCList(in *platform.HPCList, out *HPCList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]HPC)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_HPCList_To_v1_HPCList is an autogenerated conversion function.
func Convert_platform_HPCList_To_v1_HPCList(in *platform.HPCList, out *HPCList, s conversion.Scope) error {
	return autoConvert_platform_HPCList_To_v1_HPCList(in, out, s)
}

func autoConvert_v1_HPCProxyOptions_To_platform_HPCProxyOptions(in *HPCProxyOptions, out *platform.HPCProxyOptions, s conversion.Scope) error {
	out.Namespace = in.Namespace
	out.Name = in.Name
	out.Action = in.Action
	return nil
}

// Convert_v1_HPCProxyOptions_To_platform_HPCProxyOptions is an autogenerated conversion function.
func Convert_v1_HPCProxyOptions_To_platform_HPCProxyOptions(in *HPCProxyOptions, out *platform.HPCProxyOptions, s conversion.Scope) error {
	return autoConvert_v1_HPCProxyOptions_To_platform_HPCProxyOptions(in, out, s)
}

func autoConvert_platform_HPCProxyOptions_To_v1_HPCProxyOptions(in *platform.HPCProxyOptions, out *HPCProxyOptions, s conversion.Scope) error {
	out.Namespace = in.Namespace
	out.Name = in.Name
	out.Action = in.Action
	return nil
}

// Convert_platform_HPCProxyOptions_To_v1_HPCProxyOptions is an autogenerated conversion function.
func Convert_platform_HPCProxyOptions_To_v1_HPCProxyOptions(in *platform.HPCProxyOptions, out *HPCProxyOptions, s conversion.Scope) error {
	return autoConvert_platform_HPCProxyOptions_To_v1_HPCProxyOptions(in, out, s)
}

func autoConvert_v1_HPCSpec_To_platform_HPCSpec(in *HPCSpec, out *platform.HPCSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	if err := Convert_v1_ChartSpec_To_platform_ChartSpec(&in.ChartInfo, &out.ChartInfo, s); err != nil {
		return err
	}
	out.UIN = in.UIN
	out.SubUin = in.SubUin
	return nil
}

// Convert_v1_HPCSpec_To_platform_HPCSpec is an autogenerated conversion function.
func Convert_v1_HPCSpec_To_platform_HPCSpec(in *HPCSpec, out *platform.HPCSpec, s conversion.Scope) error {
	return autoConvert_v1_HPCSpec_To_platform_HPCSpec(in, out, s)
}

func autoConvert_platform_HPCSpec_To_v1_HPCSpec(in *platform.HPCSpec, out *HPCSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	if err := Convert_platform_ChartSpec_To_v1_ChartSpec(&in.ChartInfo, &out.ChartInfo, s); err != nil {
		return err
	}
	out.UIN = in.UIN
	out.SubUin = in.SubUin
	return nil
}

// Convert_platform_HPCSpec_To_v1_HPCSpec is an autogenerated conversion function.
func Convert_platform_HPCSpec_To_v1_HPCSpec(in *platform.HPCSpec, out *HPCSpec, s conversion.Scope) error {
	return autoConvert_platform_HPCSpec_To_v1_HPCSpec(in, out, s)
}

func autoConvert_v1_HPCStatus_To_platform_HPCStatus(in *HPCStatus, out *platform.HPCStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	out.Components = *(*[]platform.ComponentStatus)(unsafe.Pointer(&in.Components))
	return nil
}

// Convert_v1_HPCStatus_To_platform_HPCStatus is an autogenerated conversion function.
func Convert_v1_HPCStatus_To_platform_HPCStatus(in *HPCStatus, out *platform.HPCStatus, s conversion.Scope) error {
	return autoConvert_v1_HPCStatus_To_platform_HPCStatus(in, out, s)
}

func autoConvert_platform_HPCStatus_To_v1_HPCStatus(in *platform.HPCStatus, out *HPCStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	out.Components = *(*[]ComponentStatus)(unsafe.Pointer(&in.Components))
	return nil
}

// Convert_platform_HPCStatus_To_v1_HPCStatus is an autogenerated conversion function.
func Convert_platform_HPCStatus_To_v1_HPCStatus(in *platform.HPCStatus, out *HPCStatus, s conversion.Scope) error {
	return autoConvert_platform_HPCStatus_To_v1_HPCStatus(in, out, s)
}

func autoConvert_v1_Helm_To_platform_Helm(in *Helm, out *platform.Helm, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_HelmSpec_To_platform_HelmSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_HelmStatus_To_platform_HelmStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_Helm_To_platform_Helm is an autogenerated conversion function.
func Convert_v1_Helm_To_platform_Helm(in *Helm, out *platform.Helm, s conversion.Scope) error {
	return autoConvert_v1_Helm_To_platform_Helm(in, out, s)
}

func autoConvert_platform_Helm_To_v1_Helm(in *platform.Helm, out *Helm, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_HelmSpec_To_v1_HelmSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_HelmStatus_To_v1_HelmStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_Helm_To_v1_Helm is an autogenerated conversion function.
func Convert_platform_Helm_To_v1_Helm(in *platform.Helm, out *Helm, s conversion.Scope) error {
	return autoConvert_platform_Helm_To_v1_Helm(in, out, s)
}

func autoConvert_v1_HelmList_To_platform_HelmList(in *HelmList, out *platform.HelmList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]platform.Helm, len(*in))
		for i := range *in {
			if err := Convert_v1_Helm_To_platform_Helm(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_v1_HelmList_To_platform_HelmList is an autogenerated conversion function.
func Convert_v1_HelmList_To_platform_HelmList(in *HelmList, out *platform.HelmList, s conversion.Scope) error {
	return autoConvert_v1_HelmList_To_platform_HelmList(in, out, s)
}

func autoConvert_platform_HelmList_To_v1_HelmList(in *platform.HelmList, out *HelmList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]Helm, len(*in))
		for i := range *in {
			if err := Convert_platform_Helm_To_v1_Helm(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_platform_HelmList_To_v1_HelmList is an autogenerated conversion function.
func Convert_platform_HelmList_To_v1_HelmList(in *platform.HelmList, out *HelmList, s conversion.Scope) error {
	return autoConvert_platform_HelmList_To_v1_HelmList(in, out, s)
}

func autoConvert_v1_HelmProxyOptions_To_platform_HelmProxyOptions(in *HelmProxyOptions, out *platform.HelmProxyOptions, s conversion.Scope) error {
	out.Path = in.Path
	return nil
}

// Convert_v1_HelmProxyOptions_To_platform_HelmProxyOptions is an autogenerated conversion function.
func Convert_v1_HelmProxyOptions_To_platform_HelmProxyOptions(in *HelmProxyOptions, out *platform.HelmProxyOptions, s conversion.Scope) error {
	return autoConvert_v1_HelmProxyOptions_To_platform_HelmProxyOptions(in, out, s)
}

func autoConvert_platform_HelmProxyOptions_To_v1_HelmProxyOptions(in *platform.HelmProxyOptions, out *HelmProxyOptions, s conversion.Scope) error {
	out.Path = in.Path
	return nil
}

// Convert_platform_HelmProxyOptions_To_v1_HelmProxyOptions is an autogenerated conversion function.
func Convert_platform_HelmProxyOptions_To_v1_HelmProxyOptions(in *platform.HelmProxyOptions, out *HelmProxyOptions, s conversion.Scope) error {
	return autoConvert_platform_HelmProxyOptions_To_v1_HelmProxyOptions(in, out, s)
}

func autoConvert_v1_HelmSpec_To_platform_HelmSpec(in *HelmSpec, out *platform.HelmSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	if err := Convert_v1_HelmVersion_To_platform_HelmVersion(&in.VersionDeprecated, &out.VersionDeprecated, s); err != nil {
		return err
	}
	out.Version = in.Version
	return nil
}

// Convert_v1_HelmSpec_To_platform_HelmSpec is an autogenerated conversion function.
func Convert_v1_HelmSpec_To_platform_HelmSpec(in *HelmSpec, out *platform.HelmSpec, s conversion.Scope) error {
	return autoConvert_v1_HelmSpec_To_platform_HelmSpec(in, out, s)
}

func autoConvert_platform_HelmSpec_To_v1_HelmSpec(in *platform.HelmSpec, out *HelmSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	if err := Convert_platform_HelmVersion_To_v1_HelmVersion(&in.VersionDeprecated, &out.VersionDeprecated, s); err != nil {
		return err
	}
	out.Version = in.Version
	return nil
}

// Convert_platform_HelmSpec_To_v1_HelmSpec is an autogenerated conversion function.
func Convert_platform_HelmSpec_To_v1_HelmSpec(in *platform.HelmSpec, out *HelmSpec, s conversion.Scope) error {
	return autoConvert_platform_HelmSpec_To_v1_HelmSpec(in, out, s)
}

func autoConvert_v1_HelmStatus_To_platform_HelmStatus(in *HelmStatus, out *platform.HelmStatus, s conversion.Scope) error {
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = int(in.RetryCount)
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	out.Version = in.Version
	return nil
}

// Convert_v1_HelmStatus_To_platform_HelmStatus is an autogenerated conversion function.
func Convert_v1_HelmStatus_To_platform_HelmStatus(in *HelmStatus, out *platform.HelmStatus, s conversion.Scope) error {
	return autoConvert_v1_HelmStatus_To_platform_HelmStatus(in, out, s)
}

func autoConvert_platform_HelmStatus_To_v1_HelmStatus(in *platform.HelmStatus, out *HelmStatus, s conversion.Scope) error {
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = int32(in.RetryCount)
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	out.Version = in.Version
	return nil
}

// Convert_platform_HelmStatus_To_v1_HelmStatus is an autogenerated conversion function.
func Convert_platform_HelmStatus_To_v1_HelmStatus(in *platform.HelmStatus, out *HelmStatus, s conversion.Scope) error {
	return autoConvert_platform_HelmStatus_To_v1_HelmStatus(in, out, s)
}

func autoConvert_v1_HelmVersion_To_platform_HelmVersion(in *HelmVersion, out *platform.HelmVersion, s conversion.Scope) error {
	out.Tiller = in.Tiller
	out.Swift = in.Swift
	out.HelmAPI = in.HelmAPI
	return nil
}

// Convert_v1_HelmVersion_To_platform_HelmVersion is an autogenerated conversion function.
func Convert_v1_HelmVersion_To_platform_HelmVersion(in *HelmVersion, out *platform.HelmVersion, s conversion.Scope) error {
	return autoConvert_v1_HelmVersion_To_platform_HelmVersion(in, out, s)
}

func autoConvert_platform_HelmVersion_To_v1_HelmVersion(in *platform.HelmVersion, out *HelmVersion, s conversion.Scope) error {
	out.Tiller = in.Tiller
	out.Swift = in.Swift
	out.HelmAPI = in.HelmAPI
	return nil
}

// Convert_platform_HelmVersion_To_v1_HelmVersion is an autogenerated conversion function.
func Convert_platform_HelmVersion_To_v1_HelmVersion(in *platform.HelmVersion, out *HelmVersion, s conversion.Scope) error {
	return autoConvert_platform_HelmVersion_To_v1_HelmVersion(in, out, s)
}

func autoConvert_v1_HostItem_To_platform_HostItem(in *HostItem, out *platform.HostItem, s conversion.Scope) error {
	out.Domain = in.Domain
	out.IP = in.IP
	out.Disabled = in.Disabled
	out.V6 = in.V6
	return nil
}

// Convert_v1_HostItem_To_platform_HostItem is an autogenerated conversion function.
func Convert_v1_HostItem_To_platform_HostItem(in *HostItem, out *platform.HostItem, s conversion.Scope) error {
	return autoConvert_v1_HostItem_To_platform_HostItem(in, out, s)
}

func autoConvert_platform_HostItem_To_v1_HostItem(in *platform.HostItem, out *HostItem, s conversion.Scope) error {
	out.Domain = in.Domain
	out.IP = in.IP
	out.Disabled = in.Disabled
	out.V6 = in.V6
	return nil
}

// Convert_platform_HostItem_To_v1_HostItem is an autogenerated conversion function.
func Convert_platform_HostItem_To_v1_HostItem(in *platform.HostItem, out *HostItem, s conversion.Scope) error {
	return autoConvert_platform_HostItem_To_v1_HostItem(in, out, s)
}

func autoConvert_v1_ImageP2P_To_platform_ImageP2P(in *ImageP2P, out *platform.ImageP2P, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_ImageP2PSpec_To_platform_ImageP2PSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_ImageP2PStatus_To_platform_ImageP2PStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_ImageP2P_To_platform_ImageP2P is an autogenerated conversion function.
func Convert_v1_ImageP2P_To_platform_ImageP2P(in *ImageP2P, out *platform.ImageP2P, s conversion.Scope) error {
	return autoConvert_v1_ImageP2P_To_platform_ImageP2P(in, out, s)
}

func autoConvert_platform_ImageP2P_To_v1_ImageP2P(in *platform.ImageP2P, out *ImageP2P, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_ImageP2PSpec_To_v1_ImageP2PSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_ImageP2PStatus_To_v1_ImageP2PStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_ImageP2P_To_v1_ImageP2P is an autogenerated conversion function.
func Convert_platform_ImageP2P_To_v1_ImageP2P(in *platform.ImageP2P, out *ImageP2P, s conversion.Scope) error {
	return autoConvert_platform_ImageP2P_To_v1_ImageP2P(in, out, s)
}

func autoConvert_v1_ImageP2PList_To_platform_ImageP2PList(in *ImageP2PList, out *platform.ImageP2PList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]platform.ImageP2P, len(*in))
		for i := range *in {
			if err := Convert_v1_ImageP2P_To_platform_ImageP2P(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_v1_ImageP2PList_To_platform_ImageP2PList is an autogenerated conversion function.
func Convert_v1_ImageP2PList_To_platform_ImageP2PList(in *ImageP2PList, out *platform.ImageP2PList, s conversion.Scope) error {
	return autoConvert_v1_ImageP2PList_To_platform_ImageP2PList(in, out, s)
}

func autoConvert_platform_ImageP2PList_To_v1_ImageP2PList(in *platform.ImageP2PList, out *ImageP2PList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]ImageP2P, len(*in))
		for i := range *in {
			if err := Convert_platform_ImageP2P_To_v1_ImageP2P(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_platform_ImageP2PList_To_v1_ImageP2PList is an autogenerated conversion function.
func Convert_platform_ImageP2PList_To_v1_ImageP2PList(in *platform.ImageP2PList, out *ImageP2PList, s conversion.Scope) error {
	return autoConvert_platform_ImageP2PList_To_v1_ImageP2PList(in, out, s)
}

func autoConvert_v1_ImageP2PSpec_To_platform_ImageP2PSpec(in *ImageP2PSpec, out *platform.ImageP2PSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.RegistryAddress = in.RegistryAddress
	out.AgentDownloadRate = in.AgentDownloadRate
	out.Version = in.Version
	out.ProxyReplicas = in.ProxyReplicas
	out.TrackerReplicas = in.TrackerReplicas
	out.P2PAgentWorkDir = in.P2PAgentWorkDir
	out.P2PProxyWorkDir = in.P2PProxyWorkDir
	return nil
}

// Convert_v1_ImageP2PSpec_To_platform_ImageP2PSpec is an autogenerated conversion function.
func Convert_v1_ImageP2PSpec_To_platform_ImageP2PSpec(in *ImageP2PSpec, out *platform.ImageP2PSpec, s conversion.Scope) error {
	return autoConvert_v1_ImageP2PSpec_To_platform_ImageP2PSpec(in, out, s)
}

func autoConvert_platform_ImageP2PSpec_To_v1_ImageP2PSpec(in *platform.ImageP2PSpec, out *ImageP2PSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.RegistryAddress = in.RegistryAddress
	out.AgentDownloadRate = in.AgentDownloadRate
	out.ProxyReplicas = in.ProxyReplicas
	out.TrackerReplicas = in.TrackerReplicas
	out.Version = in.Version
	out.P2PAgentWorkDir = in.P2PAgentWorkDir
	out.P2PProxyWorkDir = in.P2PProxyWorkDir
	return nil
}

// Convert_platform_ImageP2PSpec_To_v1_ImageP2PSpec is an autogenerated conversion function.
func Convert_platform_ImageP2PSpec_To_v1_ImageP2PSpec(in *platform.ImageP2PSpec, out *ImageP2PSpec, s conversion.Scope) error {
	return autoConvert_platform_ImageP2PSpec_To_v1_ImageP2PSpec(in, out, s)
}

func autoConvert_v1_ImageP2PStatus_To_platform_ImageP2PStatus(in *ImageP2PStatus, out *platform.ImageP2PStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_v1_ImageP2PStatus_To_platform_ImageP2PStatus is an autogenerated conversion function.
func Convert_v1_ImageP2PStatus_To_platform_ImageP2PStatus(in *ImageP2PStatus, out *platform.ImageP2PStatus, s conversion.Scope) error {
	return autoConvert_v1_ImageP2PStatus_To_platform_ImageP2PStatus(in, out, s)
}

func autoConvert_platform_ImageP2PStatus_To_v1_ImageP2PStatus(in *platform.ImageP2PStatus, out *ImageP2PStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_platform_ImageP2PStatus_To_v1_ImageP2PStatus is an autogenerated conversion function.
func Convert_platform_ImageP2PStatus_To_v1_ImageP2PStatus(in *platform.ImageP2PStatus, out *ImageP2PStatus, s conversion.Scope) error {
	return autoConvert_platform_ImageP2PStatus_To_v1_ImageP2PStatus(in, out, s)
}

func autoConvert_v1_LBCF_To_platform_LBCF(in *LBCF, out *platform.LBCF, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_LBCFSpec_To_platform_LBCFSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_LBCFStatus_To_platform_LBCFStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_LBCF_To_platform_LBCF is an autogenerated conversion function.
func Convert_v1_LBCF_To_platform_LBCF(in *LBCF, out *platform.LBCF, s conversion.Scope) error {
	return autoConvert_v1_LBCF_To_platform_LBCF(in, out, s)
}

func autoConvert_platform_LBCF_To_v1_LBCF(in *platform.LBCF, out *LBCF, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_LBCFSpec_To_v1_LBCFSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_LBCFStatus_To_v1_LBCFStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_LBCF_To_v1_LBCF is an autogenerated conversion function.
func Convert_platform_LBCF_To_v1_LBCF(in *platform.LBCF, out *LBCF, s conversion.Scope) error {
	return autoConvert_platform_LBCF_To_v1_LBCF(in, out, s)
}

func autoConvert_v1_LBCFList_To_platform_LBCFList(in *LBCFList, out *platform.LBCFList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.LBCF)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_LBCFList_To_platform_LBCFList is an autogenerated conversion function.
func Convert_v1_LBCFList_To_platform_LBCFList(in *LBCFList, out *platform.LBCFList, s conversion.Scope) error {
	return autoConvert_v1_LBCFList_To_platform_LBCFList(in, out, s)
}

func autoConvert_platform_LBCFList_To_v1_LBCFList(in *platform.LBCFList, out *LBCFList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]LBCF)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_LBCFList_To_v1_LBCFList is an autogenerated conversion function.
func Convert_platform_LBCFList_To_v1_LBCFList(in *platform.LBCFList, out *LBCFList, s conversion.Scope) error {
	return autoConvert_platform_LBCFList_To_v1_LBCFList(in, out, s)
}

func autoConvert_v1_LBCFProxyOptions_To_platform_LBCFProxyOptions(in *LBCFProxyOptions, out *platform.LBCFProxyOptions, s conversion.Scope) error {
	out.Namespace = in.Namespace
	out.Name = in.Name
	out.Action = in.Action
	return nil
}

// Convert_v1_LBCFProxyOptions_To_platform_LBCFProxyOptions is an autogenerated conversion function.
func Convert_v1_LBCFProxyOptions_To_platform_LBCFProxyOptions(in *LBCFProxyOptions, out *platform.LBCFProxyOptions, s conversion.Scope) error {
	return autoConvert_v1_LBCFProxyOptions_To_platform_LBCFProxyOptions(in, out, s)
}

func autoConvert_platform_LBCFProxyOptions_To_v1_LBCFProxyOptions(in *platform.LBCFProxyOptions, out *LBCFProxyOptions, s conversion.Scope) error {
	out.Namespace = in.Namespace
	out.Name = in.Name
	out.Action = in.Action
	return nil
}

// Convert_platform_LBCFProxyOptions_To_v1_LBCFProxyOptions is an autogenerated conversion function.
func Convert_platform_LBCFProxyOptions_To_v1_LBCFProxyOptions(in *platform.LBCFProxyOptions, out *LBCFProxyOptions, s conversion.Scope) error {
	return autoConvert_platform_LBCFProxyOptions_To_v1_LBCFProxyOptions(in, out, s)
}

func autoConvert_v1_LBCFSpec_To_platform_LBCFSpec(in *LBCFSpec, out *platform.LBCFSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	return nil
}

// Convert_v1_LBCFSpec_To_platform_LBCFSpec is an autogenerated conversion function.
func Convert_v1_LBCFSpec_To_platform_LBCFSpec(in *LBCFSpec, out *platform.LBCFSpec, s conversion.Scope) error {
	return autoConvert_v1_LBCFSpec_To_platform_LBCFSpec(in, out, s)
}

func autoConvert_platform_LBCFSpec_To_v1_LBCFSpec(in *platform.LBCFSpec, out *LBCFSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	return nil
}

// Convert_platform_LBCFSpec_To_v1_LBCFSpec is an autogenerated conversion function.
func Convert_platform_LBCFSpec_To_v1_LBCFSpec(in *platform.LBCFSpec, out *LBCFSpec, s conversion.Scope) error {
	return autoConvert_platform_LBCFSpec_To_v1_LBCFSpec(in, out, s)
}

func autoConvert_v1_LBCFStatus_To_platform_LBCFStatus(in *LBCFStatus, out *platform.LBCFStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_v1_LBCFStatus_To_platform_LBCFStatus is an autogenerated conversion function.
func Convert_v1_LBCFStatus_To_platform_LBCFStatus(in *LBCFStatus, out *platform.LBCFStatus, s conversion.Scope) error {
	return autoConvert_v1_LBCFStatus_To_platform_LBCFStatus(in, out, s)
}

func autoConvert_platform_LBCFStatus_To_v1_LBCFStatus(in *platform.LBCFStatus, out *LBCFStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_platform_LBCFStatus_To_v1_LBCFStatus is an autogenerated conversion function.
func Convert_platform_LBCFStatus_To_v1_LBCFStatus(in *platform.LBCFStatus, out *LBCFStatus, s conversion.Scope) error {
	return autoConvert_platform_LBCFStatus_To_v1_LBCFStatus(in, out, s)
}

func autoConvert_v1_LoadThreshold_To_platform_LoadThreshold(in *LoadThreshold, out *platform.LoadThreshold, s conversion.Scope) error {
	out.CpuAvgUsage5M = in.CpuAvgUsage5M
	out.CpuTargetUsage = in.CpuTargetUsage
	out.MemAvgUsage5M = in.MemAvgUsage5M
	out.MemTargetUsage = in.MemTargetUsage
	return nil
}

// Convert_v1_LoadThreshold_To_platform_LoadThreshold is an autogenerated conversion function.
func Convert_v1_LoadThreshold_To_platform_LoadThreshold(in *LoadThreshold, out *platform.LoadThreshold, s conversion.Scope) error {
	return autoConvert_v1_LoadThreshold_To_platform_LoadThreshold(in, out, s)
}

func autoConvert_platform_LoadThreshold_To_v1_LoadThreshold(in *platform.LoadThreshold, out *LoadThreshold, s conversion.Scope) error {
	out.CpuAvgUsage5M = in.CpuAvgUsage5M
	out.CpuTargetUsage = in.CpuTargetUsage
	out.MemAvgUsage5M = in.MemAvgUsage5M
	out.MemTargetUsage = in.MemTargetUsage
	return nil
}

// Convert_platform_LoadThreshold_To_v1_LoadThreshold is an autogenerated conversion function.
func Convert_platform_LoadThreshold_To_v1_LoadThreshold(in *platform.LoadThreshold, out *LoadThreshold, s conversion.Scope) error {
	return autoConvert_platform_LoadThreshold_To_v1_LoadThreshold(in, out, s)
}

func autoConvert_v1_LogCollector_To_platform_LogCollector(in *LogCollector, out *platform.LogCollector, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_LogCollectorSpec_To_platform_LogCollectorSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_LogCollectorStatus_To_platform_LogCollectorStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_LogCollector_To_platform_LogCollector is an autogenerated conversion function.
func Convert_v1_LogCollector_To_platform_LogCollector(in *LogCollector, out *platform.LogCollector, s conversion.Scope) error {
	return autoConvert_v1_LogCollector_To_platform_LogCollector(in, out, s)
}

func autoConvert_platform_LogCollector_To_v1_LogCollector(in *platform.LogCollector, out *LogCollector, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_LogCollectorSpec_To_v1_LogCollectorSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_LogCollectorStatus_To_v1_LogCollectorStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_LogCollector_To_v1_LogCollector is an autogenerated conversion function.
func Convert_platform_LogCollector_To_v1_LogCollector(in *platform.LogCollector, out *LogCollector, s conversion.Scope) error {
	return autoConvert_platform_LogCollector_To_v1_LogCollector(in, out, s)
}

func autoConvert_v1_LogCollectorList_To_platform_LogCollectorList(in *LogCollectorList, out *platform.LogCollectorList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.LogCollector)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_LogCollectorList_To_platform_LogCollectorList is an autogenerated conversion function.
func Convert_v1_LogCollectorList_To_platform_LogCollectorList(in *LogCollectorList, out *platform.LogCollectorList, s conversion.Scope) error {
	return autoConvert_v1_LogCollectorList_To_platform_LogCollectorList(in, out, s)
}

func autoConvert_platform_LogCollectorList_To_v1_LogCollectorList(in *platform.LogCollectorList, out *LogCollectorList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]LogCollector)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_LogCollectorList_To_v1_LogCollectorList is an autogenerated conversion function.
func Convert_platform_LogCollectorList_To_v1_LogCollectorList(in *platform.LogCollectorList, out *LogCollectorList, s conversion.Scope) error {
	return autoConvert_platform_LogCollectorList_To_v1_LogCollectorList(in, out, s)
}

func autoConvert_v1_LogCollectorProxyOptions_To_platform_LogCollectorProxyOptions(in *LogCollectorProxyOptions, out *platform.LogCollectorProxyOptions, s conversion.Scope) error {
	out.Namespace = in.Namespace
	out.Name = in.Name
	return nil
}

// Convert_v1_LogCollectorProxyOptions_To_platform_LogCollectorProxyOptions is an autogenerated conversion function.
func Convert_v1_LogCollectorProxyOptions_To_platform_LogCollectorProxyOptions(in *LogCollectorProxyOptions, out *platform.LogCollectorProxyOptions, s conversion.Scope) error {
	return autoConvert_v1_LogCollectorProxyOptions_To_platform_LogCollectorProxyOptions(in, out, s)
}

func autoConvert_platform_LogCollectorProxyOptions_To_v1_LogCollectorProxyOptions(in *platform.LogCollectorProxyOptions, out *LogCollectorProxyOptions, s conversion.Scope) error {
	out.Namespace = in.Namespace
	out.Name = in.Name
	return nil
}

// Convert_platform_LogCollectorProxyOptions_To_v1_LogCollectorProxyOptions is an autogenerated conversion function.
func Convert_platform_LogCollectorProxyOptions_To_v1_LogCollectorProxyOptions(in *platform.LogCollectorProxyOptions, out *LogCollectorProxyOptions, s conversion.Scope) error {
	return autoConvert_platform_LogCollectorProxyOptions_To_v1_LogCollectorProxyOptions(in, out, s)
}

func autoConvert_v1_LogCollectorSpec_To_platform_LogCollectorSpec(in *LogCollectorSpec, out *platform.LogCollectorSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	return nil
}

// Convert_v1_LogCollectorSpec_To_platform_LogCollectorSpec is an autogenerated conversion function.
func Convert_v1_LogCollectorSpec_To_platform_LogCollectorSpec(in *LogCollectorSpec, out *platform.LogCollectorSpec, s conversion.Scope) error {
	return autoConvert_v1_LogCollectorSpec_To_platform_LogCollectorSpec(in, out, s)
}

func autoConvert_platform_LogCollectorSpec_To_v1_LogCollectorSpec(in *platform.LogCollectorSpec, out *LogCollectorSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	return nil
}

// Convert_platform_LogCollectorSpec_To_v1_LogCollectorSpec is an autogenerated conversion function.
func Convert_platform_LogCollectorSpec_To_v1_LogCollectorSpec(in *platform.LogCollectorSpec, out *LogCollectorSpec, s conversion.Scope) error {
	return autoConvert_platform_LogCollectorSpec_To_v1_LogCollectorSpec(in, out, s)
}

func autoConvert_v1_LogCollectorStatus_To_platform_LogCollectorStatus(in *LogCollectorStatus, out *platform.LogCollectorStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_v1_LogCollectorStatus_To_platform_LogCollectorStatus is an autogenerated conversion function.
func Convert_v1_LogCollectorStatus_To_platform_LogCollectorStatus(in *LogCollectorStatus, out *platform.LogCollectorStatus, s conversion.Scope) error {
	return autoConvert_v1_LogCollectorStatus_To_platform_LogCollectorStatus(in, out, s)
}

func autoConvert_platform_LogCollectorStatus_To_v1_LogCollectorStatus(in *platform.LogCollectorStatus, out *LogCollectorStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_platform_LogCollectorStatus_To_v1_LogCollectorStatus is an autogenerated conversion function.
func Convert_platform_LogCollectorStatus_To_v1_LogCollectorStatus(in *platform.LogCollectorStatus, out *LogCollectorStatus, s conversion.Scope) error {
	return autoConvert_platform_LogCollectorStatus_To_v1_LogCollectorStatus(in, out, s)
}

func autoConvert_v1_Manager_To_platform_Manager(in *Manager, out *platform.Manager, s conversion.Scope) error {
	out.Name = in.Name
	out.DisplayName = in.DisplayName
	return nil
}

// Convert_v1_Manager_To_platform_Manager is an autogenerated conversion function.
func Convert_v1_Manager_To_platform_Manager(in *Manager, out *platform.Manager, s conversion.Scope) error {
	return autoConvert_v1_Manager_To_platform_Manager(in, out, s)
}

func autoConvert_platform_Manager_To_v1_Manager(in *platform.Manager, out *Manager, s conversion.Scope) error {
	out.Name = in.Name
	out.DisplayName = in.DisplayName
	return nil
}

// Convert_platform_Manager_To_v1_Manager is an autogenerated conversion function.
func Convert_platform_Manager_To_v1_Manager(in *platform.Manager, out *Manager, s conversion.Scope) error {
	return autoConvert_platform_Manager_To_v1_Manager(in, out, s)
}

func autoConvert_v1_NamespaceSet_To_platform_NamespaceSet(in *NamespaceSet, out *platform.NamespaceSet, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_NamespaceSetSpec_To_platform_NamespaceSetSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_NamespaceSetStatus_To_platform_NamespaceSetStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_NamespaceSet_To_platform_NamespaceSet is an autogenerated conversion function.
func Convert_v1_NamespaceSet_To_platform_NamespaceSet(in *NamespaceSet, out *platform.NamespaceSet, s conversion.Scope) error {
	return autoConvert_v1_NamespaceSet_To_platform_NamespaceSet(in, out, s)
}

func autoConvert_platform_NamespaceSet_To_v1_NamespaceSet(in *platform.NamespaceSet, out *NamespaceSet, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_NamespaceSetSpec_To_v1_NamespaceSetSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_NamespaceSetStatus_To_v1_NamespaceSetStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_NamespaceSet_To_v1_NamespaceSet is an autogenerated conversion function.
func Convert_platform_NamespaceSet_To_v1_NamespaceSet(in *platform.NamespaceSet, out *NamespaceSet, s conversion.Scope) error {
	return autoConvert_platform_NamespaceSet_To_v1_NamespaceSet(in, out, s)
}

func autoConvert_v1_NamespaceSetList_To_platform_NamespaceSetList(in *NamespaceSetList, out *platform.NamespaceSetList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.NamespaceSet)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_NamespaceSetList_To_platform_NamespaceSetList is an autogenerated conversion function.
func Convert_v1_NamespaceSetList_To_platform_NamespaceSetList(in *NamespaceSetList, out *platform.NamespaceSetList, s conversion.Scope) error {
	return autoConvert_v1_NamespaceSetList_To_platform_NamespaceSetList(in, out, s)
}

func autoConvert_platform_NamespaceSetList_To_v1_NamespaceSetList(in *platform.NamespaceSetList, out *NamespaceSetList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]NamespaceSet)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_NamespaceSetList_To_v1_NamespaceSetList is an autogenerated conversion function.
func Convert_platform_NamespaceSetList_To_v1_NamespaceSetList(in *platform.NamespaceSetList, out *NamespaceSetList, s conversion.Scope) error {
	return autoConvert_platform_NamespaceSetList_To_v1_NamespaceSetList(in, out, s)
}

func autoConvert_v1_NamespaceSetSpec_To_platform_NamespaceSetSpec(in *NamespaceSetSpec, out *platform.NamespaceSetSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ProjectName = in.ProjectName
	out.ClusterName = in.ClusterName
	out.NamespaceName = in.NamespaceName
	return nil
}

// Convert_v1_NamespaceSetSpec_To_platform_NamespaceSetSpec is an autogenerated conversion function.
func Convert_v1_NamespaceSetSpec_To_platform_NamespaceSetSpec(in *NamespaceSetSpec, out *platform.NamespaceSetSpec, s conversion.Scope) error {
	return autoConvert_v1_NamespaceSetSpec_To_platform_NamespaceSetSpec(in, out, s)
}

func autoConvert_platform_NamespaceSetSpec_To_v1_NamespaceSetSpec(in *platform.NamespaceSetSpec, out *NamespaceSetSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ProjectName = in.ProjectName
	out.ClusterName = in.ClusterName
	out.NamespaceName = in.NamespaceName
	return nil
}

// Convert_platform_NamespaceSetSpec_To_v1_NamespaceSetSpec is an autogenerated conversion function.
func Convert_platform_NamespaceSetSpec_To_v1_NamespaceSetSpec(in *platform.NamespaceSetSpec, out *NamespaceSetSpec, s conversion.Scope) error {
	return autoConvert_platform_NamespaceSetSpec_To_v1_NamespaceSetSpec(in, out, s)
}

func autoConvert_v1_NamespaceSetStatus_To_platform_NamespaceSetStatus(in *NamespaceSetStatus, out *platform.NamespaceSetStatus, s conversion.Scope) error {
	out.Phase = platform.NamespaceSetPhase(in.Phase)
	out.Reason = in.Reason
	out.Locked = (*bool)(unsafe.Pointer(in.Locked))
	return nil
}

// Convert_v1_NamespaceSetStatus_To_platform_NamespaceSetStatus is an autogenerated conversion function.
func Convert_v1_NamespaceSetStatus_To_platform_NamespaceSetStatus(in *NamespaceSetStatus, out *platform.NamespaceSetStatus, s conversion.Scope) error {
	return autoConvert_v1_NamespaceSetStatus_To_platform_NamespaceSetStatus(in, out, s)
}

func autoConvert_platform_NamespaceSetStatus_To_v1_NamespaceSetStatus(in *platform.NamespaceSetStatus, out *NamespaceSetStatus, s conversion.Scope) error {
	out.Phase = NamespaceSetPhase(in.Phase)
	out.Reason = in.Reason
	out.Locked = (*bool)(unsafe.Pointer(in.Locked))
	return nil
}

// Convert_platform_NamespaceSetStatus_To_v1_NamespaceSetStatus is an autogenerated conversion function.
func Convert_platform_NamespaceSetStatus_To_v1_NamespaceSetStatus(in *platform.NamespaceSetStatus, out *NamespaceSetStatus, s conversion.Scope) error {
	return autoConvert_platform_NamespaceSetStatus_To_v1_NamespaceSetStatus(in, out, s)
}

func autoConvert_v1_NetworkPolicy_To_platform_NetworkPolicy(in *NetworkPolicy, out *platform.NetworkPolicy, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_NetworkPolicySpec_To_platform_NetworkPolicySpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_NetworkPolicyStatus_To_platform_NetworkPolicyStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_NetworkPolicy_To_platform_NetworkPolicy is an autogenerated conversion function.
func Convert_v1_NetworkPolicy_To_platform_NetworkPolicy(in *NetworkPolicy, out *platform.NetworkPolicy, s conversion.Scope) error {
	return autoConvert_v1_NetworkPolicy_To_platform_NetworkPolicy(in, out, s)
}

func autoConvert_platform_NetworkPolicy_To_v1_NetworkPolicy(in *platform.NetworkPolicy, out *NetworkPolicy, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_NetworkPolicySpec_To_v1_NetworkPolicySpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_NetworkPolicyStatus_To_v1_NetworkPolicyStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_NetworkPolicy_To_v1_NetworkPolicy is an autogenerated conversion function.
func Convert_platform_NetworkPolicy_To_v1_NetworkPolicy(in *platform.NetworkPolicy, out *NetworkPolicy, s conversion.Scope) error {
	return autoConvert_platform_NetworkPolicy_To_v1_NetworkPolicy(in, out, s)
}

func autoConvert_v1_NetworkPolicyList_To_platform_NetworkPolicyList(in *NetworkPolicyList, out *platform.NetworkPolicyList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.NetworkPolicy)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_NetworkPolicyList_To_platform_NetworkPolicyList is an autogenerated conversion function.
func Convert_v1_NetworkPolicyList_To_platform_NetworkPolicyList(in *NetworkPolicyList, out *platform.NetworkPolicyList, s conversion.Scope) error {
	return autoConvert_v1_NetworkPolicyList_To_platform_NetworkPolicyList(in, out, s)
}

func autoConvert_platform_NetworkPolicyList_To_v1_NetworkPolicyList(in *platform.NetworkPolicyList, out *NetworkPolicyList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]NetworkPolicy)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_NetworkPolicyList_To_v1_NetworkPolicyList is an autogenerated conversion function.
func Convert_platform_NetworkPolicyList_To_v1_NetworkPolicyList(in *platform.NetworkPolicyList, out *NetworkPolicyList, s conversion.Scope) error {
	return autoConvert_platform_NetworkPolicyList_To_v1_NetworkPolicyList(in, out, s)
}

func autoConvert_v1_NetworkPolicySpec_To_platform_NetworkPolicySpec(in *NetworkPolicySpec, out *platform.NetworkPolicySpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	return nil
}

// Convert_v1_NetworkPolicySpec_To_platform_NetworkPolicySpec is an autogenerated conversion function.
func Convert_v1_NetworkPolicySpec_To_platform_NetworkPolicySpec(in *NetworkPolicySpec, out *platform.NetworkPolicySpec, s conversion.Scope) error {
	return autoConvert_v1_NetworkPolicySpec_To_platform_NetworkPolicySpec(in, out, s)
}

func autoConvert_platform_NetworkPolicySpec_To_v1_NetworkPolicySpec(in *platform.NetworkPolicySpec, out *NetworkPolicySpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	return nil
}

// Convert_platform_NetworkPolicySpec_To_v1_NetworkPolicySpec is an autogenerated conversion function.
func Convert_platform_NetworkPolicySpec_To_v1_NetworkPolicySpec(in *platform.NetworkPolicySpec, out *NetworkPolicySpec, s conversion.Scope) error {
	return autoConvert_platform_NetworkPolicySpec_To_v1_NetworkPolicySpec(in, out, s)
}

func autoConvert_v1_NetworkPolicyStatus_To_platform_NetworkPolicyStatus(in *NetworkPolicyStatus, out *platform.NetworkPolicyStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	return nil
}

// Convert_v1_NetworkPolicyStatus_To_platform_NetworkPolicyStatus is an autogenerated conversion function.
func Convert_v1_NetworkPolicyStatus_To_platform_NetworkPolicyStatus(in *NetworkPolicyStatus, out *platform.NetworkPolicyStatus, s conversion.Scope) error {
	return autoConvert_v1_NetworkPolicyStatus_To_platform_NetworkPolicyStatus(in, out, s)
}

func autoConvert_platform_NetworkPolicyStatus_To_v1_NetworkPolicyStatus(in *platform.NetworkPolicyStatus, out *NetworkPolicyStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	return nil
}

// Convert_platform_NetworkPolicyStatus_To_v1_NetworkPolicyStatus is an autogenerated conversion function.
func Convert_platform_NetworkPolicyStatus_To_v1_NetworkPolicyStatus(in *platform.NetworkPolicyStatus, out *NetworkPolicyStatus, s conversion.Scope) error {
	return autoConvert_platform_NetworkPolicyStatus_To_v1_NetworkPolicyStatus(in, out, s)
}

func autoConvert_v1_NginxControllerVersion_To_platform_NginxControllerVersion(in *NginxControllerVersion, out *platform.NginxControllerVersion, s conversion.Scope) error {
	out.Controller = in.Controller
	return nil
}

// Convert_v1_NginxControllerVersion_To_platform_NginxControllerVersion is an autogenerated conversion function.
func Convert_v1_NginxControllerVersion_To_platform_NginxControllerVersion(in *NginxControllerVersion, out *platform.NginxControllerVersion, s conversion.Scope) error {
	return autoConvert_v1_NginxControllerVersion_To_platform_NginxControllerVersion(in, out, s)
}

func autoConvert_platform_NginxControllerVersion_To_v1_NginxControllerVersion(in *platform.NginxControllerVersion, out *NginxControllerVersion, s conversion.Scope) error {
	out.Controller = in.Controller
	return nil
}

// Convert_platform_NginxControllerVersion_To_v1_NginxControllerVersion is an autogenerated conversion function.
func Convert_platform_NginxControllerVersion_To_v1_NginxControllerVersion(in *platform.NginxControllerVersion, out *NginxControllerVersion, s conversion.Scope) error {
	return autoConvert_platform_NginxControllerVersion_To_v1_NginxControllerVersion(in, out, s)
}

func autoConvert_v1_NginxIngress_To_platform_NginxIngress(in *NginxIngress, out *platform.NginxIngress, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_NginxIngressSpec_To_platform_NginxIngressSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_NginxIngressStatus_To_platform_NginxIngressStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_NginxIngress_To_platform_NginxIngress is an autogenerated conversion function.
func Convert_v1_NginxIngress_To_platform_NginxIngress(in *NginxIngress, out *platform.NginxIngress, s conversion.Scope) error {
	return autoConvert_v1_NginxIngress_To_platform_NginxIngress(in, out, s)
}

func autoConvert_platform_NginxIngress_To_v1_NginxIngress(in *platform.NginxIngress, out *NginxIngress, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_NginxIngressSpec_To_v1_NginxIngressSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_NginxIngressStatus_To_v1_NginxIngressStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_NginxIngress_To_v1_NginxIngress is an autogenerated conversion function.
func Convert_platform_NginxIngress_To_v1_NginxIngress(in *platform.NginxIngress, out *NginxIngress, s conversion.Scope) error {
	return autoConvert_platform_NginxIngress_To_v1_NginxIngress(in, out, s)
}

func autoConvert_v1_NginxIngressList_To_platform_NginxIngressList(in *NginxIngressList, out *platform.NginxIngressList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.NginxIngress)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_NginxIngressList_To_platform_NginxIngressList is an autogenerated conversion function.
func Convert_v1_NginxIngressList_To_platform_NginxIngressList(in *NginxIngressList, out *platform.NginxIngressList, s conversion.Scope) error {
	return autoConvert_v1_NginxIngressList_To_platform_NginxIngressList(in, out, s)
}

func autoConvert_platform_NginxIngressList_To_v1_NginxIngressList(in *platform.NginxIngressList, out *NginxIngressList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]NginxIngress)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_NginxIngressList_To_v1_NginxIngressList is an autogenerated conversion function.
func Convert_platform_NginxIngressList_To_v1_NginxIngressList(in *platform.NginxIngressList, out *NginxIngressList, s conversion.Scope) error {
	return autoConvert_platform_NginxIngressList_To_v1_NginxIngressList(in, out, s)
}

func autoConvert_v1_NginxIngressProxyOptions_To_platform_NginxIngressProxyOptions(in *NginxIngressProxyOptions, out *platform.NginxIngressProxyOptions, s conversion.Scope) error {
	out.Name = in.Name
	return nil
}

// Convert_v1_NginxIngressProxyOptions_To_platform_NginxIngressProxyOptions is an autogenerated conversion function.
func Convert_v1_NginxIngressProxyOptions_To_platform_NginxIngressProxyOptions(in *NginxIngressProxyOptions, out *platform.NginxIngressProxyOptions, s conversion.Scope) error {
	return autoConvert_v1_NginxIngressProxyOptions_To_platform_NginxIngressProxyOptions(in, out, s)
}

func autoConvert_platform_NginxIngressProxyOptions_To_v1_NginxIngressProxyOptions(in *platform.NginxIngressProxyOptions, out *NginxIngressProxyOptions, s conversion.Scope) error {
	out.Name = in.Name
	return nil
}

// Convert_platform_NginxIngressProxyOptions_To_v1_NginxIngressProxyOptions is an autogenerated conversion function.
func Convert_platform_NginxIngressProxyOptions_To_v1_NginxIngressProxyOptions(in *platform.NginxIngressProxyOptions, out *NginxIngressProxyOptions, s conversion.Scope) error {
	return autoConvert_platform_NginxIngressProxyOptions_To_v1_NginxIngressProxyOptions(in, out, s)
}

func autoConvert_v1_NginxIngressSpec_To_platform_NginxIngressSpec(in *NginxIngressSpec, out *platform.NginxIngressSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	if err := Convert_v1_NginxControllerVersion_To_platform_NginxControllerVersion(&in.VersionDeprecated, &out.VersionDeprecated, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_NginxIngressSpec_To_platform_NginxIngressSpec is an autogenerated conversion function.
func Convert_v1_NginxIngressSpec_To_platform_NginxIngressSpec(in *NginxIngressSpec, out *platform.NginxIngressSpec, s conversion.Scope) error {
	return autoConvert_v1_NginxIngressSpec_To_platform_NginxIngressSpec(in, out, s)
}

func autoConvert_platform_NginxIngressSpec_To_v1_NginxIngressSpec(in *platform.NginxIngressSpec, out *NginxIngressSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	if err := Convert_platform_NginxControllerVersion_To_v1_NginxControllerVersion(&in.VersionDeprecated, &out.VersionDeprecated, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_NginxIngressSpec_To_v1_NginxIngressSpec is an autogenerated conversion function.
func Convert_platform_NginxIngressSpec_To_v1_NginxIngressSpec(in *platform.NginxIngressSpec, out *NginxIngressSpec, s conversion.Scope) error {
	return autoConvert_platform_NginxIngressSpec_To_v1_NginxIngressSpec(in, out, s)
}

func autoConvert_v1_NginxIngressStatus_To_platform_NginxIngressStatus(in *NginxIngressStatus, out *platform.NginxIngressStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_v1_NginxIngressStatus_To_platform_NginxIngressStatus is an autogenerated conversion function.
func Convert_v1_NginxIngressStatus_To_platform_NginxIngressStatus(in *NginxIngressStatus, out *platform.NginxIngressStatus, s conversion.Scope) error {
	return autoConvert_v1_NginxIngressStatus_To_platform_NginxIngressStatus(in, out, s)
}

func autoConvert_platform_NginxIngressStatus_To_v1_NginxIngressStatus(in *platform.NginxIngressStatus, out *NginxIngressStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_platform_NginxIngressStatus_To_v1_NginxIngressStatus is an autogenerated conversion function.
func Convert_platform_NginxIngressStatus_To_v1_NginxIngressStatus(in *platform.NginxIngressStatus, out *NginxIngressStatus, s conversion.Scope) error {
	return autoConvert_platform_NginxIngressStatus_To_v1_NginxIngressStatus(in, out, s)
}

func autoConvert_v1_NodeLocalDNSCache_To_platform_NodeLocalDNSCache(in *NodeLocalDNSCache, out *platform.NodeLocalDNSCache, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_NodeLocalDNSCacheSpec_To_platform_NodeLocalDNSCacheSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_NodeLocalDNSCacheStatus_To_platform_NodeLocalDNSCacheStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_NodeLocalDNSCache_To_platform_NodeLocalDNSCache is an autogenerated conversion function.
func Convert_v1_NodeLocalDNSCache_To_platform_NodeLocalDNSCache(in *NodeLocalDNSCache, out *platform.NodeLocalDNSCache, s conversion.Scope) error {
	return autoConvert_v1_NodeLocalDNSCache_To_platform_NodeLocalDNSCache(in, out, s)
}

func autoConvert_platform_NodeLocalDNSCache_To_v1_NodeLocalDNSCache(in *platform.NodeLocalDNSCache, out *NodeLocalDNSCache, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_NodeLocalDNSCacheSpec_To_v1_NodeLocalDNSCacheSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_NodeLocalDNSCacheStatus_To_v1_NodeLocalDNSCacheStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_NodeLocalDNSCache_To_v1_NodeLocalDNSCache is an autogenerated conversion function.
func Convert_platform_NodeLocalDNSCache_To_v1_NodeLocalDNSCache(in *platform.NodeLocalDNSCache, out *NodeLocalDNSCache, s conversion.Scope) error {
	return autoConvert_platform_NodeLocalDNSCache_To_v1_NodeLocalDNSCache(in, out, s)
}

func autoConvert_v1_NodeLocalDNSCacheList_To_platform_NodeLocalDNSCacheList(in *NodeLocalDNSCacheList, out *platform.NodeLocalDNSCacheList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.NodeLocalDNSCache)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_NodeLocalDNSCacheList_To_platform_NodeLocalDNSCacheList is an autogenerated conversion function.
func Convert_v1_NodeLocalDNSCacheList_To_platform_NodeLocalDNSCacheList(in *NodeLocalDNSCacheList, out *platform.NodeLocalDNSCacheList, s conversion.Scope) error {
	return autoConvert_v1_NodeLocalDNSCacheList_To_platform_NodeLocalDNSCacheList(in, out, s)
}

func autoConvert_platform_NodeLocalDNSCacheList_To_v1_NodeLocalDNSCacheList(in *platform.NodeLocalDNSCacheList, out *NodeLocalDNSCacheList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]NodeLocalDNSCache)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_NodeLocalDNSCacheList_To_v1_NodeLocalDNSCacheList is an autogenerated conversion function.
func Convert_platform_NodeLocalDNSCacheList_To_v1_NodeLocalDNSCacheList(in *platform.NodeLocalDNSCacheList, out *NodeLocalDNSCacheList, s conversion.Scope) error {
	return autoConvert_platform_NodeLocalDNSCacheList_To_v1_NodeLocalDNSCacheList(in, out, s)
}

func autoConvert_v1_NodeLocalDNSCacheSpec_To_platform_NodeLocalDNSCacheSpec(in *NodeLocalDNSCacheSpec, out *platform.NodeLocalDNSCacheSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	out.Target = in.Target
	return nil
}

// Convert_v1_NodeLocalDNSCacheSpec_To_platform_NodeLocalDNSCacheSpec is an autogenerated conversion function.
func Convert_v1_NodeLocalDNSCacheSpec_To_platform_NodeLocalDNSCacheSpec(in *NodeLocalDNSCacheSpec, out *platform.NodeLocalDNSCacheSpec, s conversion.Scope) error {
	return autoConvert_v1_NodeLocalDNSCacheSpec_To_platform_NodeLocalDNSCacheSpec(in, out, s)
}

func autoConvert_platform_NodeLocalDNSCacheSpec_To_v1_NodeLocalDNSCacheSpec(in *platform.NodeLocalDNSCacheSpec, out *NodeLocalDNSCacheSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	out.Target = in.Target
	return nil
}

// Convert_platform_NodeLocalDNSCacheSpec_To_v1_NodeLocalDNSCacheSpec is an autogenerated conversion function.
func Convert_platform_NodeLocalDNSCacheSpec_To_v1_NodeLocalDNSCacheSpec(in *platform.NodeLocalDNSCacheSpec, out *NodeLocalDNSCacheSpec, s conversion.Scope) error {
	return autoConvert_platform_NodeLocalDNSCacheSpec_To_v1_NodeLocalDNSCacheSpec(in, out, s)
}

func autoConvert_v1_NodeLocalDNSCacheStatus_To_platform_NodeLocalDNSCacheStatus(in *NodeLocalDNSCacheStatus, out *platform.NodeLocalDNSCacheStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	out.Components = *(*[]platform.ComponentStatus)(unsafe.Pointer(&in.Components))
	return nil
}

// Convert_v1_NodeLocalDNSCacheStatus_To_platform_NodeLocalDNSCacheStatus is an autogenerated conversion function.
func Convert_v1_NodeLocalDNSCacheStatus_To_platform_NodeLocalDNSCacheStatus(in *NodeLocalDNSCacheStatus, out *platform.NodeLocalDNSCacheStatus, s conversion.Scope) error {
	return autoConvert_v1_NodeLocalDNSCacheStatus_To_platform_NodeLocalDNSCacheStatus(in, out, s)
}

func autoConvert_platform_NodeLocalDNSCacheStatus_To_v1_NodeLocalDNSCacheStatus(in *platform.NodeLocalDNSCacheStatus, out *NodeLocalDNSCacheStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	out.Components = *(*[]ComponentStatus)(unsafe.Pointer(&in.Components))
	return nil
}

// Convert_platform_NodeLocalDNSCacheStatus_To_v1_NodeLocalDNSCacheStatus is an autogenerated conversion function.
func Convert_platform_NodeLocalDNSCacheStatus_To_v1_NodeLocalDNSCacheStatus(in *platform.NodeLocalDNSCacheStatus, out *NodeLocalDNSCacheStatus, s conversion.Scope) error {
	return autoConvert_platform_NodeLocalDNSCacheStatus_To_v1_NodeLocalDNSCacheStatus(in, out, s)
}

func autoConvert_v1_NodePod_To_platform_NodePod(in *NodePod, out *platform.NodePod, s conversion.Scope) error {
	out.RetryCounts = in.RetryCounts
	out.Evict = in.Evict
	return nil
}

// Convert_v1_NodePod_To_platform_NodePod is an autogenerated conversion function.
func Convert_v1_NodePod_To_platform_NodePod(in *NodePod, out *platform.NodePod, s conversion.Scope) error {
	return autoConvert_v1_NodePod_To_platform_NodePod(in, out, s)
}

func autoConvert_platform_NodePod_To_v1_NodePod(in *platform.NodePod, out *NodePod, s conversion.Scope) error {
	out.RetryCounts = in.RetryCounts
	out.Evict = in.Evict
	return nil
}

// Convert_platform_NodePod_To_v1_NodePod is an autogenerated conversion function.
func Convert_platform_NodePod_To_v1_NodePod(in *platform.NodePod, out *NodePod, s conversion.Scope) error {
	return autoConvert_platform_NodePod_To_v1_NodePod(in, out, s)
}

func autoConvert_v1_NodeProblemDetector_To_platform_NodeProblemDetector(in *NodeProblemDetector, out *platform.NodeProblemDetector, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_NodeProblemDetectorSpec_To_platform_NodeProblemDetectorSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_NodeProblemDetectorStatus_To_platform_NodeProblemDetectorStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_NodeProblemDetector_To_platform_NodeProblemDetector is an autogenerated conversion function.
func Convert_v1_NodeProblemDetector_To_platform_NodeProblemDetector(in *NodeProblemDetector, out *platform.NodeProblemDetector, s conversion.Scope) error {
	return autoConvert_v1_NodeProblemDetector_To_platform_NodeProblemDetector(in, out, s)
}

func autoConvert_platform_NodeProblemDetector_To_v1_NodeProblemDetector(in *platform.NodeProblemDetector, out *NodeProblemDetector, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_NodeProblemDetectorSpec_To_v1_NodeProblemDetectorSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_NodeProblemDetectorStatus_To_v1_NodeProblemDetectorStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_NodeProblemDetector_To_v1_NodeProblemDetector is an autogenerated conversion function.
func Convert_platform_NodeProblemDetector_To_v1_NodeProblemDetector(in *platform.NodeProblemDetector, out *NodeProblemDetector, s conversion.Scope) error {
	return autoConvert_platform_NodeProblemDetector_To_v1_NodeProblemDetector(in, out, s)
}

func autoConvert_v1_NodeProblemDetectorList_To_platform_NodeProblemDetectorList(in *NodeProblemDetectorList, out *platform.NodeProblemDetectorList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.NodeProblemDetector)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_NodeProblemDetectorList_To_platform_NodeProblemDetectorList is an autogenerated conversion function.
func Convert_v1_NodeProblemDetectorList_To_platform_NodeProblemDetectorList(in *NodeProblemDetectorList, out *platform.NodeProblemDetectorList, s conversion.Scope) error {
	return autoConvert_v1_NodeProblemDetectorList_To_platform_NodeProblemDetectorList(in, out, s)
}

func autoConvert_platform_NodeProblemDetectorList_To_v1_NodeProblemDetectorList(in *platform.NodeProblemDetectorList, out *NodeProblemDetectorList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]NodeProblemDetector)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_NodeProblemDetectorList_To_v1_NodeProblemDetectorList is an autogenerated conversion function.
func Convert_platform_NodeProblemDetectorList_To_v1_NodeProblemDetectorList(in *platform.NodeProblemDetectorList, out *NodeProblemDetectorList, s conversion.Scope) error {
	return autoConvert_platform_NodeProblemDetectorList_To_v1_NodeProblemDetectorList(in, out, s)
}

func autoConvert_v1_NodeProblemDetectorSpec_To_platform_NodeProblemDetectorSpec(in *NodeProblemDetectorSpec, out *platform.NodeProblemDetectorSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	out.SelfCure = in.SelfCure
	out.UIN = in.UIN
	out.SubUin = in.SubUin
	out.Policys = *(*[]platform.Policy)(unsafe.Pointer(&in.Policys))
	return nil
}

// Convert_v1_NodeProblemDetectorSpec_To_platform_NodeProblemDetectorSpec is an autogenerated conversion function.
func Convert_v1_NodeProblemDetectorSpec_To_platform_NodeProblemDetectorSpec(in *NodeProblemDetectorSpec, out *platform.NodeProblemDetectorSpec, s conversion.Scope) error {
	return autoConvert_v1_NodeProblemDetectorSpec_To_platform_NodeProblemDetectorSpec(in, out, s)
}

func autoConvert_platform_NodeProblemDetectorSpec_To_v1_NodeProblemDetectorSpec(in *platform.NodeProblemDetectorSpec, out *NodeProblemDetectorSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	out.SelfCure = in.SelfCure
	out.UIN = in.UIN
	out.SubUin = in.SubUin
	out.Policys = *(*[]Policy)(unsafe.Pointer(&in.Policys))
	return nil
}

// Convert_platform_NodeProblemDetectorSpec_To_v1_NodeProblemDetectorSpec is an autogenerated conversion function.
func Convert_platform_NodeProblemDetectorSpec_To_v1_NodeProblemDetectorSpec(in *platform.NodeProblemDetectorSpec, out *NodeProblemDetectorSpec, s conversion.Scope) error {
	return autoConvert_platform_NodeProblemDetectorSpec_To_v1_NodeProblemDetectorSpec(in, out, s)
}

func autoConvert_v1_NodeProblemDetectorStatus_To_platform_NodeProblemDetectorStatus(in *NodeProblemDetectorStatus, out *platform.NodeProblemDetectorStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	return nil
}

// Convert_v1_NodeProblemDetectorStatus_To_platform_NodeProblemDetectorStatus is an autogenerated conversion function.
func Convert_v1_NodeProblemDetectorStatus_To_platform_NodeProblemDetectorStatus(in *NodeProblemDetectorStatus, out *platform.NodeProblemDetectorStatus, s conversion.Scope) error {
	return autoConvert_v1_NodeProblemDetectorStatus_To_platform_NodeProblemDetectorStatus(in, out, s)
}

func autoConvert_platform_NodeProblemDetectorStatus_To_v1_NodeProblemDetectorStatus(in *platform.NodeProblemDetectorStatus, out *NodeProblemDetectorStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	return nil
}

// Convert_platform_NodeProblemDetectorStatus_To_v1_NodeProblemDetectorStatus is an autogenerated conversion function.
func Convert_platform_NodeProblemDetectorStatus_To_v1_NodeProblemDetectorStatus(in *platform.NodeProblemDetectorStatus, out *NodeProblemDetectorStatus, s conversion.Scope) error {
	return autoConvert_platform_NodeProblemDetectorStatus_To_v1_NodeProblemDetectorStatus(in, out, s)
}

func autoConvert_v1_OLM_To_platform_OLM(in *OLM, out *platform.OLM, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_OLMSpec_To_platform_OLMSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_OLMStatus_To_platform_OLMStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_OLM_To_platform_OLM is an autogenerated conversion function.
func Convert_v1_OLM_To_platform_OLM(in *OLM, out *platform.OLM, s conversion.Scope) error {
	return autoConvert_v1_OLM_To_platform_OLM(in, out, s)
}

func autoConvert_platform_OLM_To_v1_OLM(in *platform.OLM, out *OLM, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_OLMSpec_To_v1_OLMSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_OLMStatus_To_v1_OLMStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_OLM_To_v1_OLM is an autogenerated conversion function.
func Convert_platform_OLM_To_v1_OLM(in *platform.OLM, out *OLM, s conversion.Scope) error {
	return autoConvert_platform_OLM_To_v1_OLM(in, out, s)
}

func autoConvert_v1_OLMList_To_platform_OLMList(in *OLMList, out *platform.OLMList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.OLM)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_OLMList_To_platform_OLMList is an autogenerated conversion function.
func Convert_v1_OLMList_To_platform_OLMList(in *OLMList, out *platform.OLMList, s conversion.Scope) error {
	return autoConvert_v1_OLMList_To_platform_OLMList(in, out, s)
}

func autoConvert_platform_OLMList_To_v1_OLMList(in *platform.OLMList, out *OLMList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]OLM)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_OLMList_To_v1_OLMList is an autogenerated conversion function.
func Convert_platform_OLMList_To_v1_OLMList(in *platform.OLMList, out *OLMList, s conversion.Scope) error {
	return autoConvert_platform_OLMList_To_v1_OLMList(in, out, s)
}

func autoConvert_v1_OLMProxyOptions_To_platform_OLMProxyOptions(in *OLMProxyOptions, out *platform.OLMProxyOptions, s conversion.Scope) error {
	out.Namespace = in.Namespace
	out.Name = in.Name
	out.Action = in.Action
	return nil
}

// Convert_v1_OLMProxyOptions_To_platform_OLMProxyOptions is an autogenerated conversion function.
func Convert_v1_OLMProxyOptions_To_platform_OLMProxyOptions(in *OLMProxyOptions, out *platform.OLMProxyOptions, s conversion.Scope) error {
	return autoConvert_v1_OLMProxyOptions_To_platform_OLMProxyOptions(in, out, s)
}

func autoConvert_platform_OLMProxyOptions_To_v1_OLMProxyOptions(in *platform.OLMProxyOptions, out *OLMProxyOptions, s conversion.Scope) error {
	out.Namespace = in.Namespace
	out.Name = in.Name
	out.Action = in.Action
	return nil
}

// Convert_platform_OLMProxyOptions_To_v1_OLMProxyOptions is an autogenerated conversion function.
func Convert_platform_OLMProxyOptions_To_v1_OLMProxyOptions(in *platform.OLMProxyOptions, out *OLMProxyOptions, s conversion.Scope) error {
	return autoConvert_platform_OLMProxyOptions_To_v1_OLMProxyOptions(in, out, s)
}

func autoConvert_v1_OLMSpec_To_platform_OLMSpec(in *OLMSpec, out *platform.OLMSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	if err := Convert_v1_ChartSpec_To_platform_ChartSpec(&in.ChartInfo, &out.ChartInfo, s); err != nil {
		return err
	}
	out.UIN = in.UIN
	out.SubUin = in.SubUin
	return nil
}

// Convert_v1_OLMSpec_To_platform_OLMSpec is an autogenerated conversion function.
func Convert_v1_OLMSpec_To_platform_OLMSpec(in *OLMSpec, out *platform.OLMSpec, s conversion.Scope) error {
	return autoConvert_v1_OLMSpec_To_platform_OLMSpec(in, out, s)
}

func autoConvert_platform_OLMSpec_To_v1_OLMSpec(in *platform.OLMSpec, out *OLMSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	if err := Convert_platform_ChartSpec_To_v1_ChartSpec(&in.ChartInfo, &out.ChartInfo, s); err != nil {
		return err
	}
	out.UIN = in.UIN
	out.SubUin = in.SubUin
	return nil
}

// Convert_platform_OLMSpec_To_v1_OLMSpec is an autogenerated conversion function.
func Convert_platform_OLMSpec_To_v1_OLMSpec(in *platform.OLMSpec, out *OLMSpec, s conversion.Scope) error {
	return autoConvert_platform_OLMSpec_To_v1_OLMSpec(in, out, s)
}

func autoConvert_v1_OLMStatus_To_platform_OLMStatus(in *OLMStatus, out *platform.OLMStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	out.Components = *(*[]platform.ComponentStatus)(unsafe.Pointer(&in.Components))
	return nil
}

// Convert_v1_OLMStatus_To_platform_OLMStatus is an autogenerated conversion function.
func Convert_v1_OLMStatus_To_platform_OLMStatus(in *OLMStatus, out *platform.OLMStatus, s conversion.Scope) error {
	return autoConvert_v1_OLMStatus_To_platform_OLMStatus(in, out, s)
}

func autoConvert_platform_OLMStatus_To_v1_OLMStatus(in *platform.OLMStatus, out *OLMStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	out.Components = *(*[]ComponentStatus)(unsafe.Pointer(&in.Components))
	return nil
}

// Convert_platform_OLMStatus_To_v1_OLMStatus is an autogenerated conversion function.
func Convert_platform_OLMStatus_To_v1_OLMStatus(in *platform.OLMStatus, out *OLMStatus, s conversion.Scope) error {
	return autoConvert_platform_OLMStatus_To_v1_OLMStatus(in, out, s)
}

func autoConvert_v1_OOMGuard_To_platform_OOMGuard(in *OOMGuard, out *platform.OOMGuard, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_OOMGuardSpec_To_platform_OOMGuardSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_OOMGuardStatus_To_platform_OOMGuardStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_OOMGuard_To_platform_OOMGuard is an autogenerated conversion function.
func Convert_v1_OOMGuard_To_platform_OOMGuard(in *OOMGuard, out *platform.OOMGuard, s conversion.Scope) error {
	return autoConvert_v1_OOMGuard_To_platform_OOMGuard(in, out, s)
}

func autoConvert_platform_OOMGuard_To_v1_OOMGuard(in *platform.OOMGuard, out *OOMGuard, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_OOMGuardSpec_To_v1_OOMGuardSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_OOMGuardStatus_To_v1_OOMGuardStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_OOMGuard_To_v1_OOMGuard is an autogenerated conversion function.
func Convert_platform_OOMGuard_To_v1_OOMGuard(in *platform.OOMGuard, out *OOMGuard, s conversion.Scope) error {
	return autoConvert_platform_OOMGuard_To_v1_OOMGuard(in, out, s)
}

func autoConvert_v1_OOMGuardList_To_platform_OOMGuardList(in *OOMGuardList, out *platform.OOMGuardList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.OOMGuard)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_OOMGuardList_To_platform_OOMGuardList is an autogenerated conversion function.
func Convert_v1_OOMGuardList_To_platform_OOMGuardList(in *OOMGuardList, out *platform.OOMGuardList, s conversion.Scope) error {
	return autoConvert_v1_OOMGuardList_To_platform_OOMGuardList(in, out, s)
}

func autoConvert_platform_OOMGuardList_To_v1_OOMGuardList(in *platform.OOMGuardList, out *OOMGuardList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]OOMGuard)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_OOMGuardList_To_v1_OOMGuardList is an autogenerated conversion function.
func Convert_platform_OOMGuardList_To_v1_OOMGuardList(in *platform.OOMGuardList, out *OOMGuardList, s conversion.Scope) error {
	return autoConvert_platform_OOMGuardList_To_v1_OOMGuardList(in, out, s)
}

func autoConvert_v1_OOMGuardSpec_To_platform_OOMGuardSpec(in *OOMGuardSpec, out *platform.OOMGuardSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	return nil
}

// Convert_v1_OOMGuardSpec_To_platform_OOMGuardSpec is an autogenerated conversion function.
func Convert_v1_OOMGuardSpec_To_platform_OOMGuardSpec(in *OOMGuardSpec, out *platform.OOMGuardSpec, s conversion.Scope) error {
	return autoConvert_v1_OOMGuardSpec_To_platform_OOMGuardSpec(in, out, s)
}

func autoConvert_platform_OOMGuardSpec_To_v1_OOMGuardSpec(in *platform.OOMGuardSpec, out *OOMGuardSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	return nil
}

// Convert_platform_OOMGuardSpec_To_v1_OOMGuardSpec is an autogenerated conversion function.
func Convert_platform_OOMGuardSpec_To_v1_OOMGuardSpec(in *platform.OOMGuardSpec, out *OOMGuardSpec, s conversion.Scope) error {
	return autoConvert_platform_OOMGuardSpec_To_v1_OOMGuardSpec(in, out, s)
}

func autoConvert_v1_OOMGuardStatus_To_platform_OOMGuardStatus(in *OOMGuardStatus, out *platform.OOMGuardStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	return nil
}

// Convert_v1_OOMGuardStatus_To_platform_OOMGuardStatus is an autogenerated conversion function.
func Convert_v1_OOMGuardStatus_To_platform_OOMGuardStatus(in *OOMGuardStatus, out *platform.OOMGuardStatus, s conversion.Scope) error {
	return autoConvert_v1_OOMGuardStatus_To_platform_OOMGuardStatus(in, out, s)
}

func autoConvert_platform_OOMGuardStatus_To_v1_OOMGuardStatus(in *platform.OOMGuardStatus, out *OOMGuardStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	return nil
}

// Convert_platform_OOMGuardStatus_To_v1_OOMGuardStatus is an autogenerated conversion function.
func Convert_platform_OOMGuardStatus_To_v1_OOMGuardStatus(in *platform.OOMGuardStatus, out *OOMGuardStatus, s conversion.Scope) error {
	return autoConvert_platform_OOMGuardStatus_To_v1_OOMGuardStatus(in, out, s)
}

func autoConvert_v1_PersistentBackEnd_To_platform_PersistentBackEnd(in *PersistentBackEnd, out *platform.PersistentBackEnd, s conversion.Scope) error {
	out.CLS = (*platform.StorageBackEndCLS)(unsafe.Pointer(in.CLS))
	if in.ES != nil {
		in, out := &in.ES, &out.ES
		*out = new(platform.StorageBackEndES)
		if err := Convert_v1_StorageBackEndES_To_platform_StorageBackEndES(*in, *out, s); err != nil {
			return err
		}
	} else {
		out.ES = nil
	}
	return nil
}

// Convert_v1_PersistentBackEnd_To_platform_PersistentBackEnd is an autogenerated conversion function.
func Convert_v1_PersistentBackEnd_To_platform_PersistentBackEnd(in *PersistentBackEnd, out *platform.PersistentBackEnd, s conversion.Scope) error {
	return autoConvert_v1_PersistentBackEnd_To_platform_PersistentBackEnd(in, out, s)
}

func autoConvert_platform_PersistentBackEnd_To_v1_PersistentBackEnd(in *platform.PersistentBackEnd, out *PersistentBackEnd, s conversion.Scope) error {
	out.CLS = (*StorageBackEndCLS)(unsafe.Pointer(in.CLS))
	if in.ES != nil {
		in, out := &in.ES, &out.ES
		*out = new(StorageBackEndES)
		if err := Convert_platform_StorageBackEndES_To_v1_StorageBackEndES(*in, *out, s); err != nil {
			return err
		}
	} else {
		out.ES = nil
	}
	return nil
}

// Convert_platform_PersistentBackEnd_To_v1_PersistentBackEnd is an autogenerated conversion function.
func Convert_platform_PersistentBackEnd_To_v1_PersistentBackEnd(in *platform.PersistentBackEnd, out *PersistentBackEnd, s conversion.Scope) error {
	return autoConvert_platform_PersistentBackEnd_To_v1_PersistentBackEnd(in, out, s)
}

func autoConvert_v1_PersistentEvent_To_platform_PersistentEvent(in *PersistentEvent, out *platform.PersistentEvent, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_PersistentEventSpec_To_platform_PersistentEventSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_PersistentEventStatus_To_platform_PersistentEventStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_PersistentEvent_To_platform_PersistentEvent is an autogenerated conversion function.
func Convert_v1_PersistentEvent_To_platform_PersistentEvent(in *PersistentEvent, out *platform.PersistentEvent, s conversion.Scope) error {
	return autoConvert_v1_PersistentEvent_To_platform_PersistentEvent(in, out, s)
}

func autoConvert_platform_PersistentEvent_To_v1_PersistentEvent(in *platform.PersistentEvent, out *PersistentEvent, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_PersistentEventSpec_To_v1_PersistentEventSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_PersistentEventStatus_To_v1_PersistentEventStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_PersistentEvent_To_v1_PersistentEvent is an autogenerated conversion function.
func Convert_platform_PersistentEvent_To_v1_PersistentEvent(in *platform.PersistentEvent, out *PersistentEvent, s conversion.Scope) error {
	return autoConvert_platform_PersistentEvent_To_v1_PersistentEvent(in, out, s)
}

func autoConvert_v1_PersistentEventList_To_platform_PersistentEventList(in *PersistentEventList, out *platform.PersistentEventList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]platform.PersistentEvent, len(*in))
		for i := range *in {
			if err := Convert_v1_PersistentEvent_To_platform_PersistentEvent(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_v1_PersistentEventList_To_platform_PersistentEventList is an autogenerated conversion function.
func Convert_v1_PersistentEventList_To_platform_PersistentEventList(in *PersistentEventList, out *platform.PersistentEventList, s conversion.Scope) error {
	return autoConvert_v1_PersistentEventList_To_platform_PersistentEventList(in, out, s)
}

func autoConvert_platform_PersistentEventList_To_v1_PersistentEventList(in *platform.PersistentEventList, out *PersistentEventList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]PersistentEvent, len(*in))
		for i := range *in {
			if err := Convert_platform_PersistentEvent_To_v1_PersistentEvent(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_platform_PersistentEventList_To_v1_PersistentEventList is an autogenerated conversion function.
func Convert_platform_PersistentEventList_To_v1_PersistentEventList(in *platform.PersistentEventList, out *PersistentEventList, s conversion.Scope) error {
	return autoConvert_platform_PersistentEventList_To_v1_PersistentEventList(in, out, s)
}

func autoConvert_v1_PersistentEventSpec_To_platform_PersistentEventSpec(in *PersistentEventSpec, out *platform.PersistentEventSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	if err := Convert_v1_PersistentBackEnd_To_platform_PersistentBackEnd(&in.PersistentBackEnd, &out.PersistentBackEnd, s); err != nil {
		return err
	}
	out.Version = in.Version
	return nil
}

// Convert_v1_PersistentEventSpec_To_platform_PersistentEventSpec is an autogenerated conversion function.
func Convert_v1_PersistentEventSpec_To_platform_PersistentEventSpec(in *PersistentEventSpec, out *platform.PersistentEventSpec, s conversion.Scope) error {
	return autoConvert_v1_PersistentEventSpec_To_platform_PersistentEventSpec(in, out, s)
}

func autoConvert_platform_PersistentEventSpec_To_v1_PersistentEventSpec(in *platform.PersistentEventSpec, out *PersistentEventSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	if err := Convert_platform_PersistentBackEnd_To_v1_PersistentBackEnd(&in.PersistentBackEnd, &out.PersistentBackEnd, s); err != nil {
		return err
	}
	out.Version = in.Version
	return nil
}

// Convert_platform_PersistentEventSpec_To_v1_PersistentEventSpec is an autogenerated conversion function.
func Convert_platform_PersistentEventSpec_To_v1_PersistentEventSpec(in *platform.PersistentEventSpec, out *PersistentEventSpec, s conversion.Scope) error {
	return autoConvert_platform_PersistentEventSpec_To_v1_PersistentEventSpec(in, out, s)
}

func autoConvert_v1_PersistentEventStatus_To_platform_PersistentEventStatus(in *PersistentEventStatus, out *platform.PersistentEventStatus, s conversion.Scope) error {
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = int(in.RetryCount)
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	out.Version = in.Version
	return nil
}

// Convert_v1_PersistentEventStatus_To_platform_PersistentEventStatus is an autogenerated conversion function.
func Convert_v1_PersistentEventStatus_To_platform_PersistentEventStatus(in *PersistentEventStatus, out *platform.PersistentEventStatus, s conversion.Scope) error {
	return autoConvert_v1_PersistentEventStatus_To_platform_PersistentEventStatus(in, out, s)
}

func autoConvert_platform_PersistentEventStatus_To_v1_PersistentEventStatus(in *platform.PersistentEventStatus, out *PersistentEventStatus, s conversion.Scope) error {
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = int32(in.RetryCount)
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	out.Version = in.Version
	return nil
}

// Convert_platform_PersistentEventStatus_To_v1_PersistentEventStatus is an autogenerated conversion function.
func Convert_platform_PersistentEventStatus_To_v1_PersistentEventStatus(in *platform.PersistentEventStatus, out *PersistentEventStatus, s conversion.Scope) error {
	return autoConvert_platform_PersistentEventStatus_To_v1_PersistentEventStatus(in, out, s)
}

func autoConvert_v1_Policy_To_platform_Policy(in *Policy, out *platform.Policy, s conversion.Scope) error {
	out.ConditionType = in.ConditionType
	if err := Convert_v1_Actions_To_platform_Actions(&in.Actions, &out.Actions, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_Policy_To_platform_Policy is an autogenerated conversion function.
func Convert_v1_Policy_To_platform_Policy(in *Policy, out *platform.Policy, s conversion.Scope) error {
	return autoConvert_v1_Policy_To_platform_Policy(in, out, s)
}

func autoConvert_platform_Policy_To_v1_Policy(in *platform.Policy, out *Policy, s conversion.Scope) error {
	out.ConditionType = in.ConditionType
	if err := Convert_platform_Actions_To_v1_Actions(&in.Actions, &out.Actions, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_Policy_To_v1_Policy is an autogenerated conversion function.
func Convert_platform_Policy_To_v1_Policy(in *platform.Policy, out *Policy, s conversion.Scope) error {
	return autoConvert_platform_Policy_To_v1_Policy(in, out, s)
}

func autoConvert_v1_Project_To_platform_Project(in *Project, out *platform.Project, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_ProjectSpec_To_platform_ProjectSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_ProjectStatus_To_platform_ProjectStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_Project_To_platform_Project is an autogenerated conversion function.
func Convert_v1_Project_To_platform_Project(in *Project, out *platform.Project, s conversion.Scope) error {
	return autoConvert_v1_Project_To_platform_Project(in, out, s)
}

func autoConvert_platform_Project_To_v1_Project(in *platform.Project, out *Project, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_ProjectSpec_To_v1_ProjectSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_ProjectStatus_To_v1_ProjectStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_Project_To_v1_Project is an autogenerated conversion function.
func Convert_platform_Project_To_v1_Project(in *platform.Project, out *Project, s conversion.Scope) error {
	return autoConvert_platform_Project_To_v1_Project(in, out, s)
}

func autoConvert_v1_ProjectList_To_platform_ProjectList(in *ProjectList, out *platform.ProjectList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.Project)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_ProjectList_To_platform_ProjectList is an autogenerated conversion function.
func Convert_v1_ProjectList_To_platform_ProjectList(in *ProjectList, out *platform.ProjectList, s conversion.Scope) error {
	return autoConvert_v1_ProjectList_To_platform_ProjectList(in, out, s)
}

func autoConvert_platform_ProjectList_To_v1_ProjectList(in *platform.ProjectList, out *ProjectList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]Project)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_ProjectList_To_v1_ProjectList is an autogenerated conversion function.
func Convert_platform_ProjectList_To_v1_ProjectList(in *platform.ProjectList, out *ProjectList, s conversion.Scope) error {
	return autoConvert_platform_ProjectList_To_v1_ProjectList(in, out, s)
}

func autoConvert_v1_ProjectSpec_To_platform_ProjectSpec(in *ProjectSpec, out *platform.ProjectSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.DisplayName = in.DisplayName
	out.Description = in.Description
	out.Managers = *(*[]platform.Manager)(unsafe.Pointer(&in.Managers))
	return nil
}

// Convert_v1_ProjectSpec_To_platform_ProjectSpec is an autogenerated conversion function.
func Convert_v1_ProjectSpec_To_platform_ProjectSpec(in *ProjectSpec, out *platform.ProjectSpec, s conversion.Scope) error {
	return autoConvert_v1_ProjectSpec_To_platform_ProjectSpec(in, out, s)
}

func autoConvert_platform_ProjectSpec_To_v1_ProjectSpec(in *platform.ProjectSpec, out *ProjectSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.DisplayName = in.DisplayName
	out.Description = in.Description
	out.Managers = *(*[]Manager)(unsafe.Pointer(&in.Managers))
	return nil
}

// Convert_platform_ProjectSpec_To_v1_ProjectSpec is an autogenerated conversion function.
func Convert_platform_ProjectSpec_To_v1_ProjectSpec(in *platform.ProjectSpec, out *ProjectSpec, s conversion.Scope) error {
	return autoConvert_platform_ProjectSpec_To_v1_ProjectSpec(in, out, s)
}

func autoConvert_v1_ProjectStatus_To_platform_ProjectStatus(in *ProjectStatus, out *platform.ProjectStatus, s conversion.Scope) error {
	out.Locked = (*bool)(unsafe.Pointer(in.Locked))
	return nil
}

// Convert_v1_ProjectStatus_To_platform_ProjectStatus is an autogenerated conversion function.
func Convert_v1_ProjectStatus_To_platform_ProjectStatus(in *ProjectStatus, out *platform.ProjectStatus, s conversion.Scope) error {
	return autoConvert_v1_ProjectStatus_To_platform_ProjectStatus(in, out, s)
}

func autoConvert_platform_ProjectStatus_To_v1_ProjectStatus(in *platform.ProjectStatus, out *ProjectStatus, s conversion.Scope) error {
	out.Locked = (*bool)(unsafe.Pointer(in.Locked))
	return nil
}

// Convert_platform_ProjectStatus_To_v1_ProjectStatus is an autogenerated conversion function.
func Convert_platform_ProjectStatus_To_v1_ProjectStatus(in *platform.ProjectStatus, out *ProjectStatus, s conversion.Scope) error {
	return autoConvert_platform_ProjectStatus_To_v1_ProjectStatus(in, out, s)
}

func autoConvert_v1_QGPU_To_platform_QGPU(in *QGPU, out *platform.QGPU, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_QGPUSpec_To_platform_QGPUSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_QGPUStatus_To_platform_QGPUStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_QGPU_To_platform_QGPU is an autogenerated conversion function.
func Convert_v1_QGPU_To_platform_QGPU(in *QGPU, out *platform.QGPU, s conversion.Scope) error {
	return autoConvert_v1_QGPU_To_platform_QGPU(in, out, s)
}

func autoConvert_platform_QGPU_To_v1_QGPU(in *platform.QGPU, out *QGPU, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_QGPUSpec_To_v1_QGPUSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_QGPUStatus_To_v1_QGPUStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_QGPU_To_v1_QGPU is an autogenerated conversion function.
func Convert_platform_QGPU_To_v1_QGPU(in *platform.QGPU, out *QGPU, s conversion.Scope) error {
	return autoConvert_platform_QGPU_To_v1_QGPU(in, out, s)
}

func autoConvert_v1_QGPUList_To_platform_QGPUList(in *QGPUList, out *platform.QGPUList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.QGPU)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_QGPUList_To_platform_QGPUList is an autogenerated conversion function.
func Convert_v1_QGPUList_To_platform_QGPUList(in *QGPUList, out *platform.QGPUList, s conversion.Scope) error {
	return autoConvert_v1_QGPUList_To_platform_QGPUList(in, out, s)
}

func autoConvert_platform_QGPUList_To_v1_QGPUList(in *platform.QGPUList, out *QGPUList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]QGPU)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_QGPUList_To_v1_QGPUList is an autogenerated conversion function.
func Convert_platform_QGPUList_To_v1_QGPUList(in *platform.QGPUList, out *QGPUList, s conversion.Scope) error {
	return autoConvert_platform_QGPUList_To_v1_QGPUList(in, out, s)
}

func autoConvert_v1_QGPUSpec_To_platform_QGPUSpec(in *QGPUSpec, out *platform.QGPUSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Priority = in.Priority
	out.Version = in.Version
	return nil
}

// Convert_v1_QGPUSpec_To_platform_QGPUSpec is an autogenerated conversion function.
func Convert_v1_QGPUSpec_To_platform_QGPUSpec(in *QGPUSpec, out *platform.QGPUSpec, s conversion.Scope) error {
	return autoConvert_v1_QGPUSpec_To_platform_QGPUSpec(in, out, s)
}

func autoConvert_platform_QGPUSpec_To_v1_QGPUSpec(in *platform.QGPUSpec, out *QGPUSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	out.Priority = in.Priority
	return nil
}

// Convert_platform_QGPUSpec_To_v1_QGPUSpec is an autogenerated conversion function.
func Convert_platform_QGPUSpec_To_v1_QGPUSpec(in *platform.QGPUSpec, out *QGPUSpec, s conversion.Scope) error {
	return autoConvert_platform_QGPUSpec_To_v1_QGPUSpec(in, out, s)
}

func autoConvert_v1_QGPUStatus_To_platform_QGPUStatus(in *QGPUStatus, out *platform.QGPUStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	return nil
}

// Convert_v1_QGPUStatus_To_platform_QGPUStatus is an autogenerated conversion function.
func Convert_v1_QGPUStatus_To_platform_QGPUStatus(in *QGPUStatus, out *platform.QGPUStatus, s conversion.Scope) error {
	return autoConvert_v1_QGPUStatus_To_platform_QGPUStatus(in, out, s)
}

func autoConvert_platform_QGPUStatus_To_v1_QGPUStatus(in *platform.QGPUStatus, out *QGPUStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	return nil
}

// Convert_platform_QGPUStatus_To_v1_QGPUStatus is an autogenerated conversion function.
func Convert_platform_QGPUStatus_To_v1_QGPUStatus(in *platform.QGPUStatus, out *QGPUStatus, s conversion.Scope) error {
	return autoConvert_platform_QGPUStatus_To_v1_QGPUStatus(in, out, s)
}

func autoConvert_v1_RecommendationProxyOptions_To_platform_RecommendationProxyOptions(in *RecommendationProxyOptions, out *platform.RecommendationProxyOptions, s conversion.Scope) error {
	out.Path = in.Path
	return nil
}

// Convert_v1_RecommendationProxyOptions_To_platform_RecommendationProxyOptions is an autogenerated conversion function.
func Convert_v1_RecommendationProxyOptions_To_platform_RecommendationProxyOptions(in *RecommendationProxyOptions, out *platform.RecommendationProxyOptions, s conversion.Scope) error {
	return autoConvert_v1_RecommendationProxyOptions_To_platform_RecommendationProxyOptions(in, out, s)
}

func autoConvert_platform_RecommendationProxyOptions_To_v1_RecommendationProxyOptions(in *platform.RecommendationProxyOptions, out *RecommendationProxyOptions, s conversion.Scope) error {
	out.Path = in.Path
	return nil
}

// Convert_platform_RecommendationProxyOptions_To_v1_RecommendationProxyOptions is an autogenerated conversion function.
func Convert_platform_RecommendationProxyOptions_To_v1_RecommendationProxyOptions(in *platform.RecommendationProxyOptions, out *RecommendationProxyOptions, s conversion.Scope) error {
	return autoConvert_platform_RecommendationProxyOptions_To_v1_RecommendationProxyOptions(in, out, s)
}

func autoConvert_v1_Registry_To_platform_Registry(in *Registry, out *platform.Registry, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_RegistrySpec_To_platform_RegistrySpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_Registry_To_platform_Registry is an autogenerated conversion function.
func Convert_v1_Registry_To_platform_Registry(in *Registry, out *platform.Registry, s conversion.Scope) error {
	return autoConvert_v1_Registry_To_platform_Registry(in, out, s)
}

func autoConvert_platform_Registry_To_v1_Registry(in *platform.Registry, out *Registry, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_RegistrySpec_To_v1_RegistrySpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_Registry_To_v1_Registry is an autogenerated conversion function.
func Convert_platform_Registry_To_v1_Registry(in *platform.Registry, out *Registry, s conversion.Scope) error {
	return autoConvert_platform_Registry_To_v1_Registry(in, out, s)
}

func autoConvert_v1_RegistryList_To_platform_RegistryList(in *RegistryList, out *platform.RegistryList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.Registry)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_RegistryList_To_platform_RegistryList is an autogenerated conversion function.
func Convert_v1_RegistryList_To_platform_RegistryList(in *RegistryList, out *platform.RegistryList, s conversion.Scope) error {
	return autoConvert_v1_RegistryList_To_platform_RegistryList(in, out, s)
}

func autoConvert_platform_RegistryList_To_v1_RegistryList(in *platform.RegistryList, out *RegistryList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]Registry)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_RegistryList_To_v1_RegistryList is an autogenerated conversion function.
func Convert_platform_RegistryList_To_v1_RegistryList(in *platform.RegistryList, out *RegistryList, s conversion.Scope) error {
	return autoConvert_platform_RegistryList_To_v1_RegistryList(in, out, s)
}

func autoConvert_v1_RegistrySpec_To_platform_RegistrySpec(in *RegistrySpec, out *platform.RegistrySpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.DisplayName = in.DisplayName
	out.ClusterName = in.ClusterName
	out.URL = in.URL
	out.UserName = (*string)(unsafe.Pointer(in.UserName))
	out.Password = (*string)(unsafe.Pointer(in.Password))
	return nil
}

// Convert_v1_RegistrySpec_To_platform_RegistrySpec is an autogenerated conversion function.
func Convert_v1_RegistrySpec_To_platform_RegistrySpec(in *RegistrySpec, out *platform.RegistrySpec, s conversion.Scope) error {
	return autoConvert_v1_RegistrySpec_To_platform_RegistrySpec(in, out, s)
}

func autoConvert_platform_RegistrySpec_To_v1_RegistrySpec(in *platform.RegistrySpec, out *RegistrySpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.DisplayName = in.DisplayName
	out.ClusterName = in.ClusterName
	out.URL = in.URL
	out.UserName = (*string)(unsafe.Pointer(in.UserName))
	out.Password = (*string)(unsafe.Pointer(in.Password))
	return nil
}

// Convert_platform_RegistrySpec_To_v1_RegistrySpec is an autogenerated conversion function.
func Convert_platform_RegistrySpec_To_v1_RegistrySpec(in *platform.RegistrySpec, out *RegistrySpec, s conversion.Scope) error {
	return autoConvert_platform_RegistrySpec_To_v1_RegistrySpec(in, out, s)
}

func autoConvert_v1_RunTime_To_platform_RunTime(in *RunTime, out *platform.RunTime, s conversion.Scope) error {
	out.RetryCounts = in.RetryCounts
	out.ReStartDokcer = in.ReStartDokcer
	out.ReStartKubelet = in.ReStartKubelet
	return nil
}

// Convert_v1_RunTime_To_platform_RunTime is an autogenerated conversion function.
func Convert_v1_RunTime_To_platform_RunTime(in *RunTime, out *platform.RunTime, s conversion.Scope) error {
	return autoConvert_v1_RunTime_To_platform_RunTime(in, out, s)
}

func autoConvert_platform_RunTime_To_v1_RunTime(in *platform.RunTime, out *RunTime, s conversion.Scope) error {
	out.RetryCounts = in.RetryCounts
	out.ReStartDokcer = in.ReStartDokcer
	out.ReStartKubelet = in.ReStartKubelet
	return nil
}

// Convert_platform_RunTime_To_v1_RunTime is an autogenerated conversion function.
func Convert_platform_RunTime_To_v1_RunTime(in *platform.RunTime, out *RunTime, s conversion.Scope) error {
	return autoConvert_platform_RunTime_To_v1_RunTime(in, out, s)
}

func autoConvert_v1_ScoreThreshold_To_platform_ScoreThreshold(in *ScoreThreshold, out *platform.ScoreThreshold, s conversion.Scope) error {
	out.CpuAvgUsage5M = in.CpuAvgUsage5M
	out.CpuMaxUsage1H = in.CpuMaxUsage1H
	out.CpuMaxUsage1D = in.CpuMaxUsage1D
	out.MemAvgUsage5M = in.MemAvgUsage5M
	out.MemMaxUsage1H = in.MemMaxUsage1H
	out.MemMaxUsage1D = in.MemMaxUsage1D
	return nil
}

// Convert_v1_ScoreThreshold_To_platform_ScoreThreshold is an autogenerated conversion function.
func Convert_v1_ScoreThreshold_To_platform_ScoreThreshold(in *ScoreThreshold, out *platform.ScoreThreshold, s conversion.Scope) error {
	return autoConvert_v1_ScoreThreshold_To_platform_ScoreThreshold(in, out, s)
}

func autoConvert_platform_ScoreThreshold_To_v1_ScoreThreshold(in *platform.ScoreThreshold, out *ScoreThreshold, s conversion.Scope) error {
	out.CpuAvgUsage5M = in.CpuAvgUsage5M
	out.CpuMaxUsage1H = in.CpuMaxUsage1H
	out.CpuMaxUsage1D = in.CpuMaxUsage1D
	out.MemAvgUsage5M = in.MemAvgUsage5M
	out.MemMaxUsage1H = in.MemMaxUsage1H
	out.MemMaxUsage1D = in.MemMaxUsage1D
	return nil
}

// Convert_platform_ScoreThreshold_To_v1_ScoreThreshold is an autogenerated conversion function.
func Convert_platform_ScoreThreshold_To_v1_ScoreThreshold(in *platform.ScoreThreshold, out *ScoreThreshold, s conversion.Scope) error {
	return autoConvert_platform_ScoreThreshold_To_v1_ScoreThreshold(in, out, s)
}

func autoConvert_v1_StorageBackEndCLS_To_platform_StorageBackEndCLS(in *StorageBackEndCLS, out *platform.StorageBackEndCLS, s conversion.Scope) error {
	out.LogSetID = in.LogSetID
	out.TopicID = in.TopicID
	return nil
}

// Convert_v1_StorageBackEndCLS_To_platform_StorageBackEndCLS is an autogenerated conversion function.
func Convert_v1_StorageBackEndCLS_To_platform_StorageBackEndCLS(in *StorageBackEndCLS, out *platform.StorageBackEndCLS, s conversion.Scope) error {
	return autoConvert_v1_StorageBackEndCLS_To_platform_StorageBackEndCLS(in, out, s)
}

func autoConvert_platform_StorageBackEndCLS_To_v1_StorageBackEndCLS(in *platform.StorageBackEndCLS, out *StorageBackEndCLS, s conversion.Scope) error {
	out.LogSetID = in.LogSetID
	out.TopicID = in.TopicID
	return nil
}

// Convert_platform_StorageBackEndCLS_To_v1_StorageBackEndCLS is an autogenerated conversion function.
func Convert_platform_StorageBackEndCLS_To_v1_StorageBackEndCLS(in *platform.StorageBackEndCLS, out *StorageBackEndCLS, s conversion.Scope) error {
	return autoConvert_platform_StorageBackEndCLS_To_v1_StorageBackEndCLS(in, out, s)
}

func autoConvert_v1_StorageBackEndES_To_platform_StorageBackEndES(in *StorageBackEndES, out *platform.StorageBackEndES, s conversion.Scope) error {
	out.IP = in.IP
	out.Port = int(in.Port)
	out.Scheme = in.Scheme
	out.IndexName = in.IndexName
	return nil
}

// Convert_v1_StorageBackEndES_To_platform_StorageBackEndES is an autogenerated conversion function.
func Convert_v1_StorageBackEndES_To_platform_StorageBackEndES(in *StorageBackEndES, out *platform.StorageBackEndES, s conversion.Scope) error {
	return autoConvert_v1_StorageBackEndES_To_platform_StorageBackEndES(in, out, s)
}

func autoConvert_platform_StorageBackEndES_To_v1_StorageBackEndES(in *platform.StorageBackEndES, out *StorageBackEndES, s conversion.Scope) error {
	out.IP = in.IP
	out.Port = int32(in.Port)
	out.Scheme = in.Scheme
	out.IndexName = in.IndexName
	return nil
}

// Convert_platform_StorageBackEndES_To_v1_StorageBackEndES is an autogenerated conversion function.
func Convert_platform_StorageBackEndES_To_v1_StorageBackEndES(in *platform.StorageBackEndES, out *StorageBackEndES, s conversion.Scope) error {
	return autoConvert_platform_StorageBackEndES_To_v1_StorageBackEndES(in, out, s)
}

func autoConvert_v1_Tcr_To_platform_Tcr(in *Tcr, out *platform.Tcr, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_TcrSpec_To_platform_TcrSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_TcrStatus_To_platform_TcrStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_Tcr_To_platform_Tcr is an autogenerated conversion function.
func Convert_v1_Tcr_To_platform_Tcr(in *Tcr, out *platform.Tcr, s conversion.Scope) error {
	return autoConvert_v1_Tcr_To_platform_Tcr(in, out, s)
}

func autoConvert_platform_Tcr_To_v1_Tcr(in *platform.Tcr, out *Tcr, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_TcrSpec_To_v1_TcrSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_TcrStatus_To_v1_TcrStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_Tcr_To_v1_Tcr is an autogenerated conversion function.
func Convert_platform_Tcr_To_v1_Tcr(in *platform.Tcr, out *Tcr, s conversion.Scope) error {
	return autoConvert_platform_Tcr_To_v1_Tcr(in, out, s)
}

func autoConvert_v1_TcrItem_To_platform_TcrItem(in *TcrItem, out *platform.TcrItem, s conversion.Scope) error {
	out.Name = in.Name
	out.RegistrySource = in.RegistrySource
	out.Username = in.Username
	out.Password = in.Password
	out.Server = in.Server
	out.NamespacesString = in.NamespacesString
	out.ServiceAccountsString = in.ServiceAccountsString
	return nil
}

// Convert_v1_TcrItem_To_platform_TcrItem is an autogenerated conversion function.
func Convert_v1_TcrItem_To_platform_TcrItem(in *TcrItem, out *platform.TcrItem, s conversion.Scope) error {
	return autoConvert_v1_TcrItem_To_platform_TcrItem(in, out, s)
}

func autoConvert_platform_TcrItem_To_v1_TcrItem(in *platform.TcrItem, out *TcrItem, s conversion.Scope) error {
	out.Name = in.Name
	out.RegistrySource = in.RegistrySource
	out.Username = in.Username
	out.Password = in.Password
	out.Server = in.Server
	out.NamespacesString = in.NamespacesString
	out.ServiceAccountsString = in.ServiceAccountsString
	return nil
}

// Convert_platform_TcrItem_To_v1_TcrItem is an autogenerated conversion function.
func Convert_platform_TcrItem_To_v1_TcrItem(in *platform.TcrItem, out *TcrItem, s conversion.Scope) error {
	return autoConvert_platform_TcrItem_To_v1_TcrItem(in, out, s)
}

func autoConvert_v1_TcrList_To_platform_TcrList(in *TcrList, out *platform.TcrList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.Tcr)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_TcrList_To_platform_TcrList is an autogenerated conversion function.
func Convert_v1_TcrList_To_platform_TcrList(in *TcrList, out *platform.TcrList, s conversion.Scope) error {
	return autoConvert_v1_TcrList_To_platform_TcrList(in, out, s)
}

func autoConvert_platform_TcrList_To_v1_TcrList(in *platform.TcrList, out *TcrList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]Tcr)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_TcrList_To_v1_TcrList is an autogenerated conversion function.
func Convert_platform_TcrList_To_v1_TcrList(in *platform.TcrList, out *TcrList, s conversion.Scope) error {
	return autoConvert_platform_TcrList_To_v1_TcrList(in, out, s)
}

func autoConvert_v1_TcrSpec_To_platform_TcrSpec(in *TcrSpec, out *platform.TcrSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	out.Hosts = *(*[]platform.HostItem)(unsafe.Pointer(&in.Hosts))
	out.Tcrs = *(*[]platform.TcrItem)(unsafe.Pointer(&in.Tcrs))
	out.Accelerated = in.Accelerated
	out.AcceleratedNamespaces = in.AcceleratedNamespaces
	return nil
}

// Convert_v1_TcrSpec_To_platform_TcrSpec is an autogenerated conversion function.
func Convert_v1_TcrSpec_To_platform_TcrSpec(in *TcrSpec, out *platform.TcrSpec, s conversion.Scope) error {
	return autoConvert_v1_TcrSpec_To_platform_TcrSpec(in, out, s)
}

func autoConvert_platform_TcrSpec_To_v1_TcrSpec(in *platform.TcrSpec, out *TcrSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	out.Hosts = *(*[]HostItem)(unsafe.Pointer(&in.Hosts))
	out.Tcrs = *(*[]TcrItem)(unsafe.Pointer(&in.Tcrs))
	out.Accelerated = in.Accelerated
	out.AcceleratedNamespaces = in.AcceleratedNamespaces
	return nil
}

// Convert_platform_TcrSpec_To_v1_TcrSpec is an autogenerated conversion function.
func Convert_platform_TcrSpec_To_v1_TcrSpec(in *platform.TcrSpec, out *TcrSpec, s conversion.Scope) error {
	return autoConvert_platform_TcrSpec_To_v1_TcrSpec(in, out, s)
}

func autoConvert_v1_TcrStatus_To_platform_TcrStatus(in *TcrStatus, out *platform.TcrStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_v1_TcrStatus_To_platform_TcrStatus is an autogenerated conversion function.
func Convert_v1_TcrStatus_To_platform_TcrStatus(in *TcrStatus, out *platform.TcrStatus, s conversion.Scope) error {
	return autoConvert_v1_TcrStatus_To_platform_TcrStatus(in, out, s)
}

func autoConvert_platform_TcrStatus_To_v1_TcrStatus(in *platform.TcrStatus, out *TcrStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_platform_TcrStatus_To_v1_TcrStatus is an autogenerated conversion function.
func Convert_platform_TcrStatus_To_v1_TcrStatus(in *platform.TcrStatus, out *TcrStatus, s conversion.Scope) error {
	return autoConvert_platform_TcrStatus_To_v1_TcrStatus(in, out, s)
}

func autoConvert_v1_VersionMap_To_platform_VersionMap(in *VersionMap, out *platform.VersionMap, s conversion.Scope) error {
	out.Name = in.Name
	out.Tag = in.Tag
	out.FullImagePath = in.FullImagePath
	return nil
}

// Convert_v1_VersionMap_To_platform_VersionMap is an autogenerated conversion function.
func Convert_v1_VersionMap_To_platform_VersionMap(in *VersionMap, out *platform.VersionMap, s conversion.Scope) error {
	return autoConvert_v1_VersionMap_To_platform_VersionMap(in, out, s)
}

func autoConvert_platform_VersionMap_To_v1_VersionMap(in *platform.VersionMap, out *VersionMap, s conversion.Scope) error {
	out.Name = in.Name
	out.Tag = in.Tag
	out.FullImagePath = in.FullImagePath
	return nil
}

// Convert_platform_VersionMap_To_v1_VersionMap is an autogenerated conversion function.
func Convert_platform_VersionMap_To_v1_VersionMap(in *platform.VersionMap, out *VersionMap, s conversion.Scope) error {
	return autoConvert_platform_VersionMap_To_v1_VersionMap(in, out, s)
}
