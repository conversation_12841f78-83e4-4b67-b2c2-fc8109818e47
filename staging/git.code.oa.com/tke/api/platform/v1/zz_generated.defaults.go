// +build !ignore_autogenerated

/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by defaulter-gen. DO NOT EDIT.

package v1

import (
	runtime "k8s.io/apimachinery/pkg/runtime"
)

// RegisterDefaults adds defaulters functions to the given scheme.
// Public to allow building arbitrary schemes.
// All generated defaulters are covering - they call all nested defaulters.
func RegisterDefaults(scheme *runtime.Scheme) error {
	scheme.AddTypeDefaultingFunc(&CBS{}, func(obj interface{}) { SetObjectDefaults_CBS(obj.(*CBS)) })
	scheme.AddTypeDefaultingFunc(&CBSList{}, func(obj interface{}) { SetObjectDefaults_CBSList(obj.(*CBSList)) })
	scheme.AddTypeDefaultingFunc(&CFS{}, func(obj interface{}) { SetObjectDefaults_CFS(obj.(*CFS)) })
	scheme.AddTypeDefaultingFunc(&CFSList{}, func(obj interface{}) { SetObjectDefaults_CFSList(obj.(*CFSList)) })
	scheme.AddTypeDefaultingFunc(&COS{}, func(obj interface{}) { SetObjectDefaults_COS(obj.(*COS)) })
	scheme.AddTypeDefaultingFunc(&COSList{}, func(obj interface{}) { SetObjectDefaults_COSList(obj.(*COSList)) })
	scheme.AddTypeDefaultingFunc(&Cluster{}, func(obj interface{}) { SetObjectDefaults_Cluster(obj.(*Cluster)) })
	scheme.AddTypeDefaultingFunc(&ClusterList{}, func(obj interface{}) { SetObjectDefaults_ClusterList(obj.(*ClusterList)) })
	scheme.AddTypeDefaultingFunc(&ConfigMap{}, func(obj interface{}) { SetObjectDefaults_ConfigMap(obj.(*ConfigMap)) })
	scheme.AddTypeDefaultingFunc(&ConfigMapList{}, func(obj interface{}) { SetObjectDefaults_ConfigMapList(obj.(*ConfigMapList)) })
	scheme.AddTypeDefaultingFunc(&DNSAutoscaler{}, func(obj interface{}) { SetObjectDefaults_DNSAutoscaler(obj.(*DNSAutoscaler)) })
	scheme.AddTypeDefaultingFunc(&DNSAutoscalerList{}, func(obj interface{}) { SetObjectDefaults_DNSAutoscalerList(obj.(*DNSAutoscalerList)) })
	scheme.AddTypeDefaultingFunc(&DeScheduler{}, func(obj interface{}) { SetObjectDefaults_DeScheduler(obj.(*DeScheduler)) })
	scheme.AddTypeDefaultingFunc(&DeSchedulerList{}, func(obj interface{}) { SetObjectDefaults_DeSchedulerList(obj.(*DeSchedulerList)) })
	scheme.AddTypeDefaultingFunc(&DynamicScheduler{}, func(obj interface{}) { SetObjectDefaults_DynamicScheduler(obj.(*DynamicScheduler)) })
	scheme.AddTypeDefaultingFunc(&DynamicSchedulerList{}, func(obj interface{}) { SetObjectDefaults_DynamicSchedulerList(obj.(*DynamicSchedulerList)) })
	scheme.AddTypeDefaultingFunc(&EniIpamd{}, func(obj interface{}) { SetObjectDefaults_EniIpamd(obj.(*EniIpamd)) })
	scheme.AddTypeDefaultingFunc(&EniIpamdList{}, func(obj interface{}) { SetObjectDefaults_EniIpamdList(obj.(*EniIpamdList)) })
	scheme.AddTypeDefaultingFunc(&GPUManager{}, func(obj interface{}) { SetObjectDefaults_GPUManager(obj.(*GPUManager)) })
	scheme.AddTypeDefaultingFunc(&GPUManagerList{}, func(obj interface{}) { SetObjectDefaults_GPUManagerList(obj.(*GPUManagerList)) })
	scheme.AddTypeDefaultingFunc(&GameApp{}, func(obj interface{}) { SetObjectDefaults_GameApp(obj.(*GameApp)) })
	scheme.AddTypeDefaultingFunc(&GameAppList{}, func(obj interface{}) { SetObjectDefaults_GameAppList(obj.(*GameAppList)) })
	scheme.AddTypeDefaultingFunc(&HPC{}, func(obj interface{}) { SetObjectDefaults_HPC(obj.(*HPC)) })
	scheme.AddTypeDefaultingFunc(&HPCList{}, func(obj interface{}) { SetObjectDefaults_HPCList(obj.(*HPCList)) })
	scheme.AddTypeDefaultingFunc(&Helm{}, func(obj interface{}) { SetObjectDefaults_Helm(obj.(*Helm)) })
	scheme.AddTypeDefaultingFunc(&HelmList{}, func(obj interface{}) { SetObjectDefaults_HelmList(obj.(*HelmList)) })
	scheme.AddTypeDefaultingFunc(&ImageP2P{}, func(obj interface{}) { SetObjectDefaults_ImageP2P(obj.(*ImageP2P)) })
	scheme.AddTypeDefaultingFunc(&ImageP2PList{}, func(obj interface{}) { SetObjectDefaults_ImageP2PList(obj.(*ImageP2PList)) })
	scheme.AddTypeDefaultingFunc(&LBCF{}, func(obj interface{}) { SetObjectDefaults_LBCF(obj.(*LBCF)) })
	scheme.AddTypeDefaultingFunc(&LBCFList{}, func(obj interface{}) { SetObjectDefaults_LBCFList(obj.(*LBCFList)) })
	scheme.AddTypeDefaultingFunc(&LogCollector{}, func(obj interface{}) { SetObjectDefaults_LogCollector(obj.(*LogCollector)) })
	scheme.AddTypeDefaultingFunc(&LogCollectorList{}, func(obj interface{}) { SetObjectDefaults_LogCollectorList(obj.(*LogCollectorList)) })
	scheme.AddTypeDefaultingFunc(&NamespaceSet{}, func(obj interface{}) { SetObjectDefaults_NamespaceSet(obj.(*NamespaceSet)) })
	scheme.AddTypeDefaultingFunc(&NamespaceSetList{}, func(obj interface{}) { SetObjectDefaults_NamespaceSetList(obj.(*NamespaceSetList)) })
	scheme.AddTypeDefaultingFunc(&NetworkPolicy{}, func(obj interface{}) { SetObjectDefaults_NetworkPolicy(obj.(*NetworkPolicy)) })
	scheme.AddTypeDefaultingFunc(&NetworkPolicyList{}, func(obj interface{}) { SetObjectDefaults_NetworkPolicyList(obj.(*NetworkPolicyList)) })
	scheme.AddTypeDefaultingFunc(&NginxIngress{}, func(obj interface{}) { SetObjectDefaults_NginxIngress(obj.(*NginxIngress)) })
	scheme.AddTypeDefaultingFunc(&NginxIngressList{}, func(obj interface{}) { SetObjectDefaults_NginxIngressList(obj.(*NginxIngressList)) })
	scheme.AddTypeDefaultingFunc(&NodeLocalDNSCache{}, func(obj interface{}) { SetObjectDefaults_NodeLocalDNSCache(obj.(*NodeLocalDNSCache)) })
	scheme.AddTypeDefaultingFunc(&NodeLocalDNSCacheList{}, func(obj interface{}) { SetObjectDefaults_NodeLocalDNSCacheList(obj.(*NodeLocalDNSCacheList)) })
	scheme.AddTypeDefaultingFunc(&NodeProblemDetector{}, func(obj interface{}) { SetObjectDefaults_NodeProblemDetector(obj.(*NodeProblemDetector)) })
	scheme.AddTypeDefaultingFunc(&NodeProblemDetectorList{}, func(obj interface{}) { SetObjectDefaults_NodeProblemDetectorList(obj.(*NodeProblemDetectorList)) })
	scheme.AddTypeDefaultingFunc(&OLM{}, func(obj interface{}) { SetObjectDefaults_OLM(obj.(*OLM)) })
	scheme.AddTypeDefaultingFunc(&OLMList{}, func(obj interface{}) { SetObjectDefaults_OLMList(obj.(*OLMList)) })
	scheme.AddTypeDefaultingFunc(&OOMGuard{}, func(obj interface{}) { SetObjectDefaults_OOMGuard(obj.(*OOMGuard)) })
	scheme.AddTypeDefaultingFunc(&OOMGuardList{}, func(obj interface{}) { SetObjectDefaults_OOMGuardList(obj.(*OOMGuardList)) })
	scheme.AddTypeDefaultingFunc(&PersistentEvent{}, func(obj interface{}) { SetObjectDefaults_PersistentEvent(obj.(*PersistentEvent)) })
	scheme.AddTypeDefaultingFunc(&PersistentEventList{}, func(obj interface{}) { SetObjectDefaults_PersistentEventList(obj.(*PersistentEventList)) })
	scheme.AddTypeDefaultingFunc(&QGPU{}, func(obj interface{}) { SetObjectDefaults_QGPU(obj.(*QGPU)) })
	scheme.AddTypeDefaultingFunc(&QGPUList{}, func(obj interface{}) { SetObjectDefaults_QGPUList(obj.(*QGPUList)) })
	scheme.AddTypeDefaultingFunc(&Tcr{}, func(obj interface{}) { SetObjectDefaults_Tcr(obj.(*Tcr)) })
	scheme.AddTypeDefaultingFunc(&TcrList{}, func(obj interface{}) { SetObjectDefaults_TcrList(obj.(*TcrList)) })
	return nil
}

func SetObjectDefaults_CBS(in *CBS) {
	SetDefaults_CBSStatus(&in.Status)
}

func SetObjectDefaults_CBSList(in *CBSList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_CBS(a)
	}
}

func SetObjectDefaults_CFS(in *CFS) {
	SetDefaults_CFSStatus(&in.Status)
}

func SetObjectDefaults_CFSList(in *CFSList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_CFS(a)
	}
}

func SetObjectDefaults_COS(in *COS) {
	SetDefaults_COSStatus(&in.Status)
}

func SetObjectDefaults_COSList(in *COSList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_COS(a)
	}
}

func SetObjectDefaults_Cluster(in *Cluster) {
	SetDefaults_ClusterStatus(&in.Status)
}

func SetObjectDefaults_ClusterList(in *ClusterList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_Cluster(a)
	}
}

func SetObjectDefaults_ConfigMap(in *ConfigMap) {
	SetDefaults_ConfigMap(in)
}

func SetObjectDefaults_ConfigMapList(in *ConfigMapList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_ConfigMap(a)
	}
}

func SetObjectDefaults_DNSAutoscaler(in *DNSAutoscaler) {
	SetDefaults_DNSAutoscalerStatus(&in.Status)
}

func SetObjectDefaults_DNSAutoscalerList(in *DNSAutoscalerList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_DNSAutoscaler(a)
	}
}

func SetObjectDefaults_DeScheduler(in *DeScheduler) {
	SetDefaults_DeSchedulerStatus(&in.Status)
}

func SetObjectDefaults_DeSchedulerList(in *DeSchedulerList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_DeScheduler(a)
	}
}

func SetObjectDefaults_DynamicScheduler(in *DynamicScheduler) {
	SetDefaults_DynamicSchedulerStatus(&in.Status)
}

func SetObjectDefaults_DynamicSchedulerList(in *DynamicSchedulerList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_DynamicScheduler(a)
	}
}

func SetObjectDefaults_EniIpamd(in *EniIpamd) {
	SetDefaults_EniIpamdSpec(&in.Spec)
	SetDefaults_EniIpamdStatus(&in.Status)
}

func SetObjectDefaults_EniIpamdList(in *EniIpamdList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_EniIpamd(a)
	}
}

func SetObjectDefaults_GPUManager(in *GPUManager) {
	SetDefaults_GPUManagerStatus(&in.Status)
}

func SetObjectDefaults_GPUManagerList(in *GPUManagerList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_GPUManager(a)
	}
}

func SetObjectDefaults_GameApp(in *GameApp) {
	SetDefaults_GameAppStatus(&in.Status)
}

func SetObjectDefaults_GameAppList(in *GameAppList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_GameApp(a)
	}
}

func SetObjectDefaults_HPC(in *HPC) {
	SetDefaults_HPCStatus(&in.Status)
}

func SetObjectDefaults_HPCList(in *HPCList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_HPC(a)
	}
}

func SetObjectDefaults_Helm(in *Helm) {
	SetDefaults_HelmStatus(&in.Status)
}

func SetObjectDefaults_HelmList(in *HelmList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_Helm(a)
	}
}

func SetObjectDefaults_ImageP2P(in *ImageP2P) {
	SetDefaults_ImageP2PStatus(&in.Status)
}

func SetObjectDefaults_ImageP2PList(in *ImageP2PList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_ImageP2P(a)
	}
}

func SetObjectDefaults_LBCF(in *LBCF) {
	SetDefaults_LBCFStatus(&in.Status)
}

func SetObjectDefaults_LBCFList(in *LBCFList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_LBCF(a)
	}
}

func SetObjectDefaults_LogCollector(in *LogCollector) {
	SetDefaults_LogCollectorStatus(&in.Status)
}

func SetObjectDefaults_LogCollectorList(in *LogCollectorList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_LogCollector(a)
	}
}

func SetObjectDefaults_NamespaceSet(in *NamespaceSet) {
	SetDefaults_NamespaceSetStatus(&in.Status)
}

func SetObjectDefaults_NamespaceSetList(in *NamespaceSetList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_NamespaceSet(a)
	}
}

func SetObjectDefaults_NetworkPolicy(in *NetworkPolicy) {
	SetDefaults_NetworkPolicyStatus(&in.Status)
}

func SetObjectDefaults_NetworkPolicyList(in *NetworkPolicyList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_NetworkPolicy(a)
	}
}

func SetObjectDefaults_NginxIngress(in *NginxIngress) {
	SetDefaults_NginxIngressStatus(&in.Status)
}

func SetObjectDefaults_NginxIngressList(in *NginxIngressList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_NginxIngress(a)
	}
}

func SetObjectDefaults_NodeLocalDNSCache(in *NodeLocalDNSCache) {
	SetDefaults_NodeLocalDNSCacheStatus(&in.Status)
}

func SetObjectDefaults_NodeLocalDNSCacheList(in *NodeLocalDNSCacheList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_NodeLocalDNSCache(a)
	}
}

func SetObjectDefaults_NodeProblemDetector(in *NodeProblemDetector) {
	SetDefaults_NodeProblemDetectorStatus(&in.Status)
}

func SetObjectDefaults_NodeProblemDetectorList(in *NodeProblemDetectorList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_NodeProblemDetector(a)
	}
}

func SetObjectDefaults_OLM(in *OLM) {
	SetDefaults_OLMStatus(&in.Status)
}

func SetObjectDefaults_OLMList(in *OLMList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_OLM(a)
	}
}

func SetObjectDefaults_OOMGuard(in *OOMGuard) {
	SetDefaults_OOMGuardStatus(&in.Status)
}

func SetObjectDefaults_OOMGuardList(in *OOMGuardList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_OOMGuard(a)
	}
}

func SetObjectDefaults_PersistentEvent(in *PersistentEvent) {
	SetDefaults_PersistentEventStatus(&in.Status)
}

func SetObjectDefaults_PersistentEventList(in *PersistentEventList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_PersistentEvent(a)
	}
}

func SetObjectDefaults_QGPU(in *QGPU) {
	SetDefaults_QGPUStatus(&in.Status)
}

func SetObjectDefaults_QGPUList(in *QGPUList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_QGPU(a)
	}
}

func SetObjectDefaults_Tcr(in *Tcr) {
	SetDefaults_TcrStatus(&in.Status)
}

func SetObjectDefaults_TcrList(in *TcrList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_Tcr(a)
	}
}
