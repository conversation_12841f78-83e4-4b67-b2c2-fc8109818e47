/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKE
 * available.
 *
 * Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License"); you may not use this
 * file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

package v1

import (
	metaV1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
)

// GroupName is the group name use in this package.
const GroupName = "platform.tke"

// SchemeGroupVersion is group version used to register these objects
var SchemeGroupVersion = schema.GroupVersion{Group: GroupName, Version: "v1"}

var (
	// SchemeBuilder collects functions that add things to a scheme.
	SchemeBuilder      runtime.SchemeBuilder
	localSchemeBuilder = &SchemeBuilder
	// AddToScheme applies all the stored functions to the scheme.
	AddToScheme = localSchemeBuilder.AddToScheme
)

func init() {
	localSchemeBuilder.Register(addKnownTypes, addConversionFuncs, addDefaultingFuncs)
}

// Adds the list of known types to the given scheme.
func addKnownTypes(scheme *runtime.Scheme) error {
	scheme.AddKnownTypes(SchemeGroupVersion,
		&Cluster{},
		&ClusterList{},
		&ClusterApplyOptions{},
		&Project{},
		&ProjectList{},
		&NamespaceSet{},
		&NamespaceSetList{},
		&PersistentEvent{},
		&PersistentEventList{},
		&Helm{},
		&HelmList{},
		&GameApp{},
		&GameAppList{},
		&GameAppProxyOptions{},
		&HelmProxyOptions{},
		&ConfigMap{},
		&ConfigMapList{},
		&Registry{},
		&RegistryList{},
		&EniIpamd{},
		&EniIpamdList{},
		&AddonType{},
		&AddonTypeList{},
		&ClusterAPIResource{},
		&ClusterAPIResourceList{},
		&ClusterAPIResourceOptions{},
		&ClusterAddon{},
		&ClusterAddonList{},
		&ClusterAddonType{},
		&ClusterAddonTypeList{},
		&GPUManager{},
		&GPUManagerList{},
		&LogCollector{},
		&LogCollectorList{},
		&LogCollectorProxyOptions{},
		&CLSLogConfigProxyOptions{},
		&RecommendationProxyOptions{},
		&LBCF{},
		&LBCFList{},
		&LBCFProxyOptions{},
		&CFS{},
		&CFSList{},
		&COS{},
		&COSList{},
		&NodeProblemDetector{},
		&NodeProblemDetectorList{},
		&ImageP2P{},
		&ImageP2PList{},
		&ClusterAuthentication{},
		&ClusterAuthenticationList{},
		&OOMGuard{},
		&OOMGuardList{},
		&DNSAutoscaler{},
		&DNSAutoscalerList{},
		&NodeLocalDNSCache{},
		&NodeLocalDNSCacheList{},
		&Tcr{},
		&TcrList{},
		&ClusterAddonKind{},
		&ClusterAddonKindList{},
		&NginxIngressProxyOptions{},
		&NginxIngress{},
		&NginxIngressList{},
		&DynamicScheduler{},
		&DynamicSchedulerList{},
		&CBS{},
		&CBSList{},
		&HPC{},
		&HPCList{},
		&HPCProxyOptions{},
		&NetworkPolicy{},
		&NetworkPolicyList{},
		&DeScheduler{},
		&DeSchedulerList{},
		&OLM{},
		&OLMList{},
		&OLMProxyOptions{},
		&Environment{},
		&EnvironmentList{},
		&QGPU{},
		&QGPUList{},
	)
	metaV1.AddToGroupVersion(scheme, SchemeGroupVersion)
	return nil
}

// Resource takes an unqualified resource and returns a Group qualified
// GroupResource
func Resource(resource string) schema.GroupResource {
	return SchemeGroupVersion.WithResource(resource).GroupResource()
}
