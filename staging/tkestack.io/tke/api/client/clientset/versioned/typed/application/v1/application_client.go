/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	rest "k8s.io/client-go/rest"
	v1 "tkestack.io/tke/api/application/v1"
	"tkestack.io/tke/api/client/clientset/versioned/scheme"
)

type ApplicationV1Interface interface {
	RESTClient() rest.Interface
	AppsGetter
	AppHistoriesGetter
	AppResourcesGetter
	ConfigMapsGetter
}

// ApplicationV1Client is used to interact with features provided by the application.tkestack.io group.
type ApplicationV1Client struct {
	restClient rest.Interface
}

func (c *ApplicationV1Client) Apps(namespace string) AppInterface {
	return newApps(c, namespace)
}

func (c *ApplicationV1Client) AppHistories(namespace string) AppHistoryInterface {
	return newAppHistories(c, namespace)
}

func (c *ApplicationV1Client) AppResources(namespace string) AppResourceInterface {
	return newAppResources(c, namespace)
}

func (c *ApplicationV1Client) ConfigMaps() ConfigMapInterface {
	return newConfigMaps(c)
}

// NewForConfig creates a new ApplicationV1Client for the given config.
func NewForConfig(c *rest.Config) (*ApplicationV1Client, error) {
	config := *c
	if err := setConfigDefaults(&config); err != nil {
		return nil, err
	}
	client, err := rest.RESTClientFor(&config)
	if err != nil {
		return nil, err
	}
	return &ApplicationV1Client{client}, nil
}

// NewForConfigOrDie creates a new ApplicationV1Client for the given config and
// panics if there is an error in the config.
func NewForConfigOrDie(c *rest.Config) *ApplicationV1Client {
	client, err := NewForConfig(c)
	if err != nil {
		panic(err)
	}
	return client
}

// New creates a new ApplicationV1Client for the given RESTClient.
func New(c rest.Interface) *ApplicationV1Client {
	return &ApplicationV1Client{c}
}

func setConfigDefaults(config *rest.Config) error {
	gv := v1.SchemeGroupVersion
	config.GroupVersion = &gv
	config.APIPath = "/apis"
	config.NegotiatedSerializer = scheme.Codecs.WithoutConversion()

	if config.UserAgent == "" {
		config.UserAgent = rest.DefaultKubernetesUserAgent()
	}

	return nil
}

// RESTClient returns a RESTClient that is used to communicate
// with API server by this client implementation.
func (c *ApplicationV1Client) RESTClient() rest.Interface {
	if c == nil {
		return nil
	}
	return c.restClient
}
