/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
	platformv1 "tkestack.io/tke/api/platform/v1"
)

// FakeTappControllers implements TappControllerInterface
type FakeTappControllers struct {
	Fake *FakePlatformV1
}

var tappcontrollersResource = schema.GroupVersionResource{Group: "platform.tkestack.io", Version: "v1", Resource: "tappcontrollers"}

var tappcontrollersKind = schema.GroupVersionKind{Group: "platform.tkestack.io", Version: "v1", Kind: "TappController"}

// Get takes name of the tappController, and returns the corresponding tappController object, and an error if there is any.
func (c *FakeTappControllers) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.TappController, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(tappcontrollersResource, name), &platformv1.TappController{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.TappController), err
}

// List takes label and field selectors, and returns the list of TappControllers that match those selectors.
func (c *FakeTappControllers) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.TappControllerList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(tappcontrollersResource, tappcontrollersKind, opts), &platformv1.TappControllerList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.TappControllerList{ListMeta: obj.(*platformv1.TappControllerList).ListMeta}
	for _, item := range obj.(*platformv1.TappControllerList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested tappControllers.
func (c *FakeTappControllers) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(tappcontrollersResource, opts))
}

// Create takes the representation of a tappController and creates it.  Returns the server's representation of the tappController, and an error, if there is any.
func (c *FakeTappControllers) Create(ctx context.Context, tappController *platformv1.TappController, opts v1.CreateOptions) (result *platformv1.TappController, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(tappcontrollersResource, tappController), &platformv1.TappController{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.TappController), err
}

// Update takes the representation of a tappController and updates it. Returns the server's representation of the tappController, and an error, if there is any.
func (c *FakeTappControllers) Update(ctx context.Context, tappController *platformv1.TappController, opts v1.UpdateOptions) (result *platformv1.TappController, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(tappcontrollersResource, tappController), &platformv1.TappController{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.TappController), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeTappControllers) UpdateStatus(ctx context.Context, tappController *platformv1.TappController, opts v1.UpdateOptions) (*platformv1.TappController, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(tappcontrollersResource, "status", tappController), &platformv1.TappController{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.TappController), err
}

// Delete takes name of the tappController and deletes it. Returns an error if one occurs.
func (c *FakeTappControllers) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(tappcontrollersResource, name), &platformv1.TappController{})
	return err
}

// Patch applies the patch and returns the patched tappController.
func (c *FakeTappControllers) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.TappController, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(tappcontrollersResource, name, pt, data, subresources...), &platformv1.TappController{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.TappController), err
}
