/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	rest "k8s.io/client-go/rest"
)

// AppHistoriesGetter has a method to return a AppHistoryInterface.
// A group's client should implement this interface.
type AppHistoriesGetter interface {
	AppHistories(namespace string) AppHistoryInterface
}

// AppHistoryInterface has methods to work with AppHistory resources.
type AppHistoryInterface interface {
	AppHistoryExpansion
}

// appHistories implements AppHistoryInterface
type appHistories struct {
	client rest.Interface
	ns     string
}

// newAppHistories returns a AppHistories
func newAppHistories(c *ApplicationV1Client, namespace string) *appHistories {
	return &appHistories{
		client: c.RESTClient(),
		ns:     namespace,
	}
}
