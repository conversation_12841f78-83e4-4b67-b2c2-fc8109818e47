/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by lister-gen. DO NOT EDIT.

package v1

// AppListerExpansion allows custom methods to be added to
// AppLister.
type AppListerExpansion interface{}

// AppNamespaceListerExpansion allows custom methods to be added to
// AppNamespaceLister.
type AppNamespaceListerExpansion interface{}

// AppHistoryListerExpansion allows custom methods to be added to
// AppHistoryLister.
type AppHistoryListerExpansion interface{}

// AppHistoryNamespaceListerExpansion allows custom methods to be added to
// AppHistoryNamespaceLister.
type AppHistoryNamespaceListerExpansion interface{}

// AppResourceListerExpansion allows custom methods to be added to
// AppResourceLister.
type AppResourceListerExpansion interface{}

// AppResourceNamespaceListerExpansion allows custom methods to be added to
// AppResourceNamespaceLister.
type AppResourceNamespaceListerExpansion interface{}

// ConfigMapListerExpansion allows custom methods to be added to
// ConfigMapLister.
type ConfigMapListerExpansion interface{}
