// +build !ignore_autogenerated

/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by defaulter-gen. DO NOT EDIT.

package v1

import (
	runtime "k8s.io/apimachinery/pkg/runtime"
)

// RegisterDefaults adds defaulters functions to the given scheme.
// Public to allow building arbitrary schemes.
// All generated defaulters are covering - they call all nested defaulters.
func RegisterDefaults(scheme *runtime.Scheme) error {
	scheme.AddTypeDefaultingFunc(&App{}, func(obj interface{}) { SetObjectDefaults_App(obj.(*App)) })
	scheme.AddTypeDefaultingFunc(&AppHistory{}, func(obj interface{}) { SetObjectDefaults_AppHistory(obj.(*AppHistory)) })
	scheme.AddTypeDefaultingFunc(&AppList{}, func(obj interface{}) { SetObjectDefaults_AppList(obj.(*AppList)) })
	scheme.AddTypeDefaultingFunc(&ConfigMap{}, func(obj interface{}) { SetObjectDefaults_ConfigMap(obj.(*ConfigMap)) })
	scheme.AddTypeDefaultingFunc(&ConfigMapList{}, func(obj interface{}) { SetObjectDefaults_ConfigMapList(obj.(*ConfigMapList)) })
	return nil
}

func SetObjectDefaults_App(in *App) {
	SetDefaults_App(in)
	SetDefaults_AppSpec(&in.Spec)
	SetDefaults_AppStatus(&in.Status)
}

func SetObjectDefaults_AppHistory(in *AppHistory) {
	SetDefaults_AppHistorySpec(&in.Spec)
}

func SetObjectDefaults_AppList(in *AppList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_App(a)
	}
}

func SetObjectDefaults_ConfigMap(in *ConfigMap) {
	SetDefaults_ConfigMap(in)
}

func SetObjectDefaults_ConfigMapList(in *ConfigMapList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_ConfigMap(a)
	}
}
