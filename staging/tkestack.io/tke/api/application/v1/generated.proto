/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2020 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package tkestack.io.tke.api.application.v1;

import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "v1";

// App is a app bootstrap in TKE.
message App {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of bootstrap in this set.
  // +optional
  optional AppSpec spec = 2;

  // +optional
  optional AppStatus status = 3;
}

// AppHistory is a app history in TKE.
message AppHistory {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of bootstrap in this set.
  // +optional
  optional AppHistorySpec spec = 2;
}

// AppHistorySpec is a description of a AppHistory.
message AppHistorySpec {
  optional string type = 1;

  optional string tenantID = 2;

  optional string name = 3;

  optional string targetCluster = 4;

  optional string targetNamespace = 5;

  // +optional
  repeated History histories = 6;
}

// AppList is the whole list of all bootstraps.
message AppList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of bootstraps
  repeated App items = 2;
}

// AppResource is a app resource in TKE.
message AppResource {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of bootstrap in this set.
  // +optional
  optional AppResourceSpec spec = 2;
}

// AppResourceSpec is a description of a AppResource.
message AppResourceSpec {
  optional string type = 1;

  optional string tenantID = 2;

  optional string name = 3;

  optional string targetCluster = 4;

  optional string targetNamespace = 5;

  // +optional
  map<string, ResourceValues> resources = 6;
}

// AppSpec is a description of a project.
message AppSpec {
  optional string type = 1;

  optional string tenantID = 2;

  optional string name = 3;

  optional string targetCluster = 4;

  optional string targetNamespace = 5;

  // +optional
  optional Chart chart = 6;

  // Values holds the values for this app.
  // +optional
  optional AppValues values = 7;

  // +optional
  repeated string finalizers = 8;

  optional bool dryRun = 9;
}

// AppStatus represents information about the status of a bootstrap.
message AppStatus {
  // Phase the release is in, one of ('ChartFetched',
  // 'ChartFetchFailed', 'Installing', 'Upgrading', 'Succeeded',
  // 'RollingBack', 'RolledBack', 'RollbackFailed')
  // +optional
  optional string phase = 1;

  // ObservedGeneration is the most recent generation observed by
  // the operator.
  // +optional
  optional int64 observedGeneration = 2;

  // ReleaseStatus is the status as given by Helm for the release
  // managed by this resource.
  // +optional
  optional string releaseStatus = 3;

  // ReleaseLastUpdated is the last updated time for the release
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time releaseLastUpdated = 4;

  // Revision holds the Git hash or version of the chart currently
  // deployed.
  // +optional
  optional int64 revision = 5;

  // RollbackRevision specify the target rollback version of the chart
  // +optional
  optional int64 rollbackRevision = 6;

  // The last time the condition transitioned from one status to another.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 7;

  // The reason for the condition's last transition.
  // +optional
  optional string reason = 8;

  // A human readable message indicating details about the transition.
  // +optional
  optional string message = 9;

  // Dryrun result.
  // +optional
  optional string manifest = 10;
}

// AppValues string the values for this app.
message AppValues {
  optional string rawValuesType = 1;

  optional string rawValues = 2;

  repeated string values = 3;
}

// Chart is a description of a chart.
message Chart {
  optional string tenantID = 1;

  optional string chartGroupName = 2;

  optional string chartName = 3;

  optional string chartVersion = 4;

  optional string repoURL = 5;

  optional string repoUsername = 6;

  optional string repoPassword = 7;

  optional bool importedRepo = 8;
}

// ConfigMap holds configuration data for tke to consume.
message ConfigMap {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Data contains the configuration data.
  // Each key must consist of alphanumeric characters, '-', '_' or '.'.
  // Values with non-UTF-8 byte sequences must use the BinaryData field.
  // The keys stored in Data must not overlap with the keys in
  // the BinaryData field, this is enforced during validation process.
  // +optional
  map<string, string> data = 2;

  // BinaryData contains the binary data.
  // Each key must consist of alphanumeric characters, '-', '_' or '.'.
  // BinaryData can contain byte sequences that are not in the UTF-8 range.
  // The keys stored in BinaryData must not overlap with the ones in
  // the Data field, this is enforced during validation process.
  // +optional
  map<string, bytes> binaryData = 3;
}

// ConfigMapList is a resource containing a list of ConfigMap objects.
message ConfigMapList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is the list of ConfigMaps.
  repeated ConfigMap items = 2;
}

// History is a history of a app.
message History {
  optional int64 revision = 1;

  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time updated = 2;

  optional string status = 3;

  optional string chart = 4;

  optional string appVersion = 5;

  optional string description = 6;

  optional string manifest = 7;
}

// ResourceValues masks the value so protobuf can generate
// You can view related issues : https://github.com/kubernetes/kubernetes/issues/46024
// +protobuf.nullable=true
// +protobuf.options.(gogoproto.goproto_stringer)=false
message ResourceValues {
  // items, if empty, will result in an empty slice

  repeated string items = 1;
}

// RollbackProxyOptions is the query options to an app rollback proxy call.
message RollbackProxyOptions {
  // +optional
  optional int64 revision = 1;

  // +optional
  optional string cluster = 2;
}

