/*
Copyright 2018 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package config

type FeatureGates interface {
	EnableMasterOperator() bool
	SupportIndependentCluster() bool
}

type TKEFeatureGates struct {
}

// NewTKEFeatureGates returns new instance of a FeatureGates
func NewTKEFeatureGates() FeatureGates {
	return &TKEFeatureGates{}
}

func (tke *TKEFeatureGates) EnableMasterOperator() bool {
	return true
}

func (tke *TKEFeatureGates) SupportIndependentCluster() bool {
	return true
}

type EKSFeatureGates struct {
}

// NewEKSFeatureGates returns new instance of a FeatureGates
func NewEKSFeatureGates() FeatureGates {
	return &EKSFeatureGates{}
}

func (eks *EKSFeatureGates) EnableMasterOperator() bool {
	return false
}

func (eks *EKSFeatureGates) SupportIndependentCluster() bool {
	return false
}

// NewGenericFeatureGatesProvider returns new instance of a FeatureGates
func NewGenericFeatureGatesProvider(product string) FeatureGates {
	switch product {
	case TKEProductName:
		return NewTKEFeatureGates()
	case EKSProductName:
		return NewEKSFeatureGates()
	}
	return nil
}
