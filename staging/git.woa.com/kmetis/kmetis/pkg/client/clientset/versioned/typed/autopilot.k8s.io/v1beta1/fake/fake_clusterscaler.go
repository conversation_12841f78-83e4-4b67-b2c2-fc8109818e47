/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	v1beta1 "git.woa.com/kmetis/kmetis/pkg/apis/autopilot.k8s.io/v1beta1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeClusterScalers implements ClusterScalerInterface
type FakeClusterScalers struct {
	Fake *FakeAutopilotV1beta1
	ns   string
}

var clusterscalersResource = schema.GroupVersionResource{Group: "autopilot.k8s.io", Version: "v1beta1", Resource: "clusterscalers"}

var clusterscalersKind = schema.GroupVersionKind{Group: "autopilot.k8s.io", Version: "v1beta1", Kind: "ClusterScaler"}

// Get takes name of the clusterScaler, and returns the corresponding clusterScaler object, and an error if there is any.
func (c *FakeClusterScalers) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1beta1.ClusterScaler, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewGetAction(clusterscalersResource, c.ns, name), &v1beta1.ClusterScaler{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.ClusterScaler), err
}

// List takes label and field selectors, and returns the list of ClusterScalers that match those selectors.
func (c *FakeClusterScalers) List(ctx context.Context, opts v1.ListOptions) (result *v1beta1.ClusterScalerList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewListAction(clusterscalersResource, clusterscalersKind, c.ns, opts), &v1beta1.ClusterScalerList{})

	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &v1beta1.ClusterScalerList{ListMeta: obj.(*v1beta1.ClusterScalerList).ListMeta}
	for _, item := range obj.(*v1beta1.ClusterScalerList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested clusterScalers.
func (c *FakeClusterScalers) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchAction(clusterscalersResource, c.ns, opts))

}

// Create takes the representation of a clusterScaler and creates it.  Returns the server's representation of the clusterScaler, and an error, if there is any.
func (c *FakeClusterScalers) Create(ctx context.Context, clusterScaler *v1beta1.ClusterScaler, opts v1.CreateOptions) (result *v1beta1.ClusterScaler, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewCreateAction(clusterscalersResource, c.ns, clusterScaler), &v1beta1.ClusterScaler{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.ClusterScaler), err
}

// Update takes the representation of a clusterScaler and updates it. Returns the server's representation of the clusterScaler, and an error, if there is any.
func (c *FakeClusterScalers) Update(ctx context.Context, clusterScaler *v1beta1.ClusterScaler, opts v1.UpdateOptions) (result *v1beta1.ClusterScaler, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateAction(clusterscalersResource, c.ns, clusterScaler), &v1beta1.ClusterScaler{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.ClusterScaler), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeClusterScalers) UpdateStatus(ctx context.Context, clusterScaler *v1beta1.ClusterScaler, opts v1.UpdateOptions) (*v1beta1.ClusterScaler, error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateSubresourceAction(clusterscalersResource, "status", c.ns, clusterScaler), &v1beta1.ClusterScaler{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.ClusterScaler), err
}

// Delete takes name of the clusterScaler and deletes it. Returns an error if one occurs.
func (c *FakeClusterScalers) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteAction(clusterscalersResource, c.ns, name), &v1beta1.ClusterScaler{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeClusterScalers) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	action := testing.NewDeleteCollectionAction(clusterscalersResource, c.ns, listOpts)

	_, err := c.Fake.Invokes(action, &v1beta1.ClusterScalerList{})
	return err
}

// Patch applies the patch and returns the patched clusterScaler.
func (c *FakeClusterScalers) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1beta1.ClusterScaler, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceAction(clusterscalersResource, c.ns, name, pt, data, subresources...), &v1beta1.ClusterScaler{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.ClusterScaler), err
}
