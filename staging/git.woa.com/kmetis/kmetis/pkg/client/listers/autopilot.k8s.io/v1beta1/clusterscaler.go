/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by lister-gen. DO NOT EDIT.

package v1beta1

import (
	v1beta1 "git.woa.com/kmetis/kmetis/pkg/apis/autopilot.k8s.io/v1beta1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
)

// ClusterScalerLister helps list ClusterScalers.
// All objects returned here must be treated as read-only.
type ClusterScalerLister interface {
	// List lists all ClusterScalers in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1beta1.ClusterScaler, err error)
	// ClusterScalers returns an object that can list and get ClusterScalers.
	ClusterScalers(namespace string) ClusterScalerNamespaceLister
	ClusterScalerListerExpansion
}

// clusterScalerLister implements the ClusterScalerLister interface.
type clusterScalerLister struct {
	indexer cache.Indexer
}

// NewClusterScalerLister returns a new ClusterScalerLister.
func NewClusterScalerLister(indexer cache.Indexer) ClusterScalerLister {
	return &clusterScalerLister{indexer: indexer}
}

// List lists all ClusterScalers in the indexer.
func (s *clusterScalerLister) List(selector labels.Selector) (ret []*v1beta1.ClusterScaler, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1beta1.ClusterScaler))
	})
	return ret, err
}

// ClusterScalers returns an object that can list and get ClusterScalers.
func (s *clusterScalerLister) ClusterScalers(namespace string) ClusterScalerNamespaceLister {
	return clusterScalerNamespaceLister{indexer: s.indexer, namespace: namespace}
}

// ClusterScalerNamespaceLister helps list and get ClusterScalers.
// All objects returned here must be treated as read-only.
type ClusterScalerNamespaceLister interface {
	// List lists all ClusterScalers in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1beta1.ClusterScaler, err error)
	// Get retrieves the ClusterScaler from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*v1beta1.ClusterScaler, error)
	ClusterScalerNamespaceListerExpansion
}

// clusterScalerNamespaceLister implements the ClusterScalerNamespaceLister
// interface.
type clusterScalerNamespaceLister struct {
	indexer   cache.Indexer
	namespace string
}

// List lists all ClusterScalers in the indexer for a given namespace.
func (s clusterScalerNamespaceLister) List(selector labels.Selector) (ret []*v1beta1.ClusterScaler, err error) {
	err = cache.ListAllByNamespace(s.indexer, s.namespace, selector, func(m interface{}) {
		ret = append(ret, m.(*v1beta1.ClusterScaler))
	})
	return ret, err
}

// Get retrieves the ClusterScaler from the indexer for a given namespace and name.
func (s clusterScalerNamespaceLister) Get(name string) (*v1beta1.ClusterScaler, error) {
	obj, exists, err := s.indexer.GetByKey(s.namespace + "/" + name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1beta1.Resource("clusterscaler"), name)
	}
	return obj.(*v1beta1.ClusterScaler), nil
}
